// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/card/activity/physicalcarddispatch/activity.proto

package physicalcarddispatch

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	order "github.com/epifi/gamma/api/order"

	provisioning "github.com/epifi/gamma/api/card/provisioning"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = order.OrderStatus(0)

	_ = provisioning.UIEntryPoint(0)

	_ = typesv2.AddressType(0)
)

// Validate checks the field values on UpdateShippingAddressActivityRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateShippingAddressActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateShippingAddressActivityRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateShippingAddressActivityRequestMultiError, or nil if none found.
func (m *UpdateShippingAddressActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateShippingAddressActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateShippingAddressActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateShippingAddressActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateShippingAddressActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	if len(errors) > 0 {
		return UpdateShippingAddressActivityRequestMultiError(errors)
	}

	return nil
}

// UpdateShippingAddressActivityRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateShippingAddressActivityRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateShippingAddressActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateShippingAddressActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateShippingAddressActivityRequestMultiError) AllErrors() []error { return m }

// UpdateShippingAddressActivityRequestValidationError is the validation error
// returned by UpdateShippingAddressActivityRequest.Validate if the designated
// constraints aren't met.
type UpdateShippingAddressActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateShippingAddressActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateShippingAddressActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateShippingAddressActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateShippingAddressActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateShippingAddressActivityRequestValidationError) ErrorName() string {
	return "UpdateShippingAddressActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateShippingAddressActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateShippingAddressActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateShippingAddressActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateShippingAddressActivityRequestValidationError{}

// Validate checks the field values on UpdateShippingAddressActivityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateShippingAddressActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateShippingAddressActivityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateShippingAddressActivityResponseMultiError, or nil if none found.
func (m *UpdateShippingAddressActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateShippingAddressActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateShippingAddressActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateShippingAddressActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateShippingAddressActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RetriggerShippingAddressUpdate

	if len(errors) > 0 {
		return UpdateShippingAddressActivityResponseMultiError(errors)
	}

	return nil
}

// UpdateShippingAddressActivityResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateShippingAddressActivityResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateShippingAddressActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateShippingAddressActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateShippingAddressActivityResponseMultiError) AllErrors() []error { return m }

// UpdateShippingAddressActivityResponseValidationError is the validation error
// returned by UpdateShippingAddressActivityResponse.Validate if the
// designated constraints aren't met.
type UpdateShippingAddressActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateShippingAddressActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateShippingAddressActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateShippingAddressActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateShippingAddressActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateShippingAddressActivityResponseValidationError) ErrorName() string {
	return "UpdateShippingAddressActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateShippingAddressActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateShippingAddressActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateShippingAddressActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateShippingAddressActivityResponseValidationError{}

// Validate checks the field values on PhysicalCardDispatchActivityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PhysicalCardDispatchActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhysicalCardDispatchActivityRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PhysicalCardDispatchActivityRequestMultiError, or nil if none found.
func (m *PhysicalCardDispatchActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalCardDispatchActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for EntryPoint

	if len(errors) > 0 {
		return PhysicalCardDispatchActivityRequestMultiError(errors)
	}

	return nil
}

// PhysicalCardDispatchActivityRequestMultiError is an error wrapping multiple
// validation errors returned by
// PhysicalCardDispatchActivityRequest.ValidateAll() if the designated
// constraints aren't met.
type PhysicalCardDispatchActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalCardDispatchActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalCardDispatchActivityRequestMultiError) AllErrors() []error { return m }

// PhysicalCardDispatchActivityRequestValidationError is the validation error
// returned by PhysicalCardDispatchActivityRequest.Validate if the designated
// constraints aren't met.
type PhysicalCardDispatchActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalCardDispatchActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhysicalCardDispatchActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhysicalCardDispatchActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhysicalCardDispatchActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhysicalCardDispatchActivityRequestValidationError) ErrorName() string {
	return "PhysicalCardDispatchActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalCardDispatchActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalCardDispatchActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalCardDispatchActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalCardDispatchActivityRequestValidationError{}

// Validate checks the field values on PhysicalCardDispatchActivityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *PhysicalCardDispatchActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhysicalCardDispatchActivityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PhysicalCardDispatchActivityResponseMultiError, or nil if none found.
func (m *PhysicalCardDispatchActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PhysicalCardDispatchActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PhysicalCardDispatchActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PhysicalCardDispatchActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PhysicalCardDispatchActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PhysicalCardDispatchActivityResponseMultiError(errors)
	}

	return nil
}

// PhysicalCardDispatchActivityResponseMultiError is an error wrapping multiple
// validation errors returned by
// PhysicalCardDispatchActivityResponse.ValidateAll() if the designated
// constraints aren't met.
type PhysicalCardDispatchActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhysicalCardDispatchActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhysicalCardDispatchActivityResponseMultiError) AllErrors() []error { return m }

// PhysicalCardDispatchActivityResponseValidationError is the validation error
// returned by PhysicalCardDispatchActivityResponse.Validate if the designated
// constraints aren't met.
type PhysicalCardDispatchActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhysicalCardDispatchActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhysicalCardDispatchActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhysicalCardDispatchActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhysicalCardDispatchActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhysicalCardDispatchActivityResponseValidationError) ErrorName() string {
	return "PhysicalCardDispatchActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PhysicalCardDispatchActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhysicalCardDispatchActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhysicalCardDispatchActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhysicalCardDispatchActivityResponseValidationError{}

// Validate checks the field values on CreateShippingPreferenceActivityRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateShippingPreferenceActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateShippingPreferenceActivityRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CreateShippingPreferenceActivityRequestMultiError, or nil if none found.
func (m *CreateShippingPreferenceActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateShippingPreferenceActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateShippingPreferenceActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateShippingPreferenceActivityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateShippingPreferenceActivityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for ActorId

	// no validation rules for AddressType

	if len(errors) > 0 {
		return CreateShippingPreferenceActivityRequestMultiError(errors)
	}

	return nil
}

// CreateShippingPreferenceActivityRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreateShippingPreferenceActivityRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateShippingPreferenceActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateShippingPreferenceActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateShippingPreferenceActivityRequestMultiError) AllErrors() []error { return m }

// CreateShippingPreferenceActivityRequestValidationError is the validation
// error returned by CreateShippingPreferenceActivityRequest.Validate if the
// designated constraints aren't met.
type CreateShippingPreferenceActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateShippingPreferenceActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateShippingPreferenceActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateShippingPreferenceActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateShippingPreferenceActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateShippingPreferenceActivityRequestValidationError) ErrorName() string {
	return "CreateShippingPreferenceActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateShippingPreferenceActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateShippingPreferenceActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateShippingPreferenceActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateShippingPreferenceActivityRequestValidationError{}

// Validate checks the field values on CreateShippingPreferenceActivityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateShippingPreferenceActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateShippingPreferenceActivityResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateShippingPreferenceActivityResponseMultiError, or nil if none found.
func (m *CreateShippingPreferenceActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateShippingPreferenceActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateShippingPreferenceActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateShippingPreferenceActivityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateShippingPreferenceActivityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateShippingPreferenceActivityResponseMultiError(errors)
	}

	return nil
}

// CreateShippingPreferenceActivityResponseMultiError is an error wrapping
// multiple validation errors returned by
// CreateShippingPreferenceActivityResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateShippingPreferenceActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateShippingPreferenceActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateShippingPreferenceActivityResponseMultiError) AllErrors() []error { return m }

// CreateShippingPreferenceActivityResponseValidationError is the validation
// error returned by CreateShippingPreferenceActivityResponse.Validate if the
// designated constraints aren't met.
type CreateShippingPreferenceActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateShippingPreferenceActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateShippingPreferenceActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateShippingPreferenceActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateShippingPreferenceActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateShippingPreferenceActivityResponseValidationError) ErrorName() string {
	return "CreateShippingPreferenceActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateShippingPreferenceActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateShippingPreferenceActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateShippingPreferenceActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateShippingPreferenceActivityResponseValidationError{}

// Validate checks the field values on
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError, or
// nil if none found.
func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for AddressType

	if len(errors) > 0 {
		return InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError(errors)
	}

	return nil
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError is an
// error wrapping multiple validation errors returned by
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest.ValidateAll()
// if the designated constraints aren't met.
type InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestMultiError) AllErrors() []error {
	return m
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError
// is the validation error returned by
// InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest.Validate if the
// designated constraints aren't met.
type InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) ErrorName() string {
	return "InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateShippingAddressUpdateAndDispatchPhysicalCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateShippingAddressUpdateAndDispatchPhysicalCardRequestValidationError{}

// Validate checks the field values on
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError, or
// nil if none found.
func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError(errors)
	}

	return nil
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError is an
// error wrapping multiple validation errors returned by
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse.ValidateAll()
// if the designated constraints aren't met.
type InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseMultiError) AllErrors() []error {
	return m
}

// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError
// is the validation error returned by
// InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse.Validate if
// the designated constraints aren't met.
type InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) ErrorName() string {
	return "InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateShippingAddressUpdateAndDispatchPhysicalCardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateShippingAddressUpdateAndDispatchPhysicalCardResponseValidationError{}

// Validate checks the field values on UpdatePhysicalCardDispatchRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdatePhysicalCardDispatchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePhysicalCardDispatchRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdatePhysicalCardDispatchRequestMultiError, or nil if none found.
func (m *UpdatePhysicalCardDispatchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePhysicalCardDispatchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdatePhysicalCardDispatchRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdatePhysicalCardDispatchRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdatePhysicalCardDispatchRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for StateToUpdate

	// no validation rules for SubStatusToUpdate

	// no validation rules for StageToBeUpdated

	if len(errors) > 0 {
		return UpdatePhysicalCardDispatchRequestMultiError(errors)
	}

	return nil
}

// UpdatePhysicalCardDispatchRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdatePhysicalCardDispatchRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdatePhysicalCardDispatchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePhysicalCardDispatchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePhysicalCardDispatchRequestMultiError) AllErrors() []error { return m }

// UpdatePhysicalCardDispatchRequestValidationError is the validation error
// returned by UpdatePhysicalCardDispatchRequest.Validate if the designated
// constraints aren't met.
type UpdatePhysicalCardDispatchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePhysicalCardDispatchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePhysicalCardDispatchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePhysicalCardDispatchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePhysicalCardDispatchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePhysicalCardDispatchRequestValidationError) ErrorName() string {
	return "UpdatePhysicalCardDispatchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePhysicalCardDispatchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePhysicalCardDispatchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePhysicalCardDispatchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePhysicalCardDispatchRequestValidationError{}

// Validate checks the field values on UpdatePhysicalCardDispatchResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdatePhysicalCardDispatchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePhysicalCardDispatchResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdatePhysicalCardDispatchResponseMultiError, or nil if none found.
func (m *UpdatePhysicalCardDispatchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePhysicalCardDispatchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdatePhysicalCardDispatchResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdatePhysicalCardDispatchResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdatePhysicalCardDispatchResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdatePhysicalCardDispatchResponseMultiError(errors)
	}

	return nil
}

// UpdatePhysicalCardDispatchResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdatePhysicalCardDispatchResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdatePhysicalCardDispatchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePhysicalCardDispatchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePhysicalCardDispatchResponseMultiError) AllErrors() []error { return m }

// UpdatePhysicalCardDispatchResponseValidationError is the validation error
// returned by UpdatePhysicalCardDispatchResponse.Validate if the designated
// constraints aren't met.
type UpdatePhysicalCardDispatchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePhysicalCardDispatchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePhysicalCardDispatchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePhysicalCardDispatchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePhysicalCardDispatchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePhysicalCardDispatchResponseValidationError) ErrorName() string {
	return "UpdatePhysicalCardDispatchResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePhysicalCardDispatchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePhysicalCardDispatchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePhysicalCardDispatchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePhysicalCardDispatchResponseValidationError{}

// Validate checks the field values on CheckPaymentStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckPaymentStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckPaymentStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckPaymentStatusRequestMultiError, or nil if none found.
func (m *CheckPaymentStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckPaymentStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckPaymentStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckPaymentStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckPaymentStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentClientReqId

	// no validation rules for CardId

	// no validation rules for EntryPoint

	// no validation rules for ActorId

	if len(errors) > 0 {
		return CheckPaymentStatusRequestMultiError(errors)
	}

	return nil
}

// CheckPaymentStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CheckPaymentStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type CheckPaymentStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckPaymentStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckPaymentStatusRequestMultiError) AllErrors() []error { return m }

// CheckPaymentStatusRequestValidationError is the validation error returned by
// CheckPaymentStatusRequest.Validate if the designated constraints aren't met.
type CheckPaymentStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckPaymentStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckPaymentStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckPaymentStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckPaymentStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckPaymentStatusRequestValidationError) ErrorName() string {
	return "CheckPaymentStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckPaymentStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckPaymentStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckPaymentStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckPaymentStatusRequestValidationError{}

// Validate checks the field values on CheckPaymentStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckPaymentStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckPaymentStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckPaymentStatusResponseMultiError, or nil if none found.
func (m *CheckPaymentStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckPaymentStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckPaymentStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckPaymentStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckPaymentStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentStatus

	if len(errors) > 0 {
		return CheckPaymentStatusResponseMultiError(errors)
	}

	return nil
}

// CheckPaymentStatusResponseMultiError is an error wrapping multiple
// validation errors returned by CheckPaymentStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckPaymentStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckPaymentStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckPaymentStatusResponseMultiError) AllErrors() []error { return m }

// CheckPaymentStatusResponseValidationError is the validation error returned
// by CheckPaymentStatusResponse.Validate if the designated constraints aren't met.
type CheckPaymentStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckPaymentStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckPaymentStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckPaymentStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckPaymentStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckPaymentStatusResponseValidationError) ErrorName() string {
	return "CheckPaymentStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckPaymentStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckPaymentStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckPaymentStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckPaymentStatusResponseValidationError{}

// Validate checks the field values on CollectDebitCardChargesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectDebitCardChargesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectDebitCardChargesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CollectDebitCardChargesRequestMultiError, or nil if none found.
func (m *CollectDebitCardChargesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectDebitCardChargesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDebitCardChargesRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDebitCardChargesRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDebitCardChargesRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for EntryPoint

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDebitCardChargesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDebitCardChargesRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDebitCardChargesRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollectDebitCardChargesRequestMultiError(errors)
	}

	return nil
}

// CollectDebitCardChargesRequestMultiError is an error wrapping multiple
// validation errors returned by CollectDebitCardChargesRequest.ValidateAll()
// if the designated constraints aren't met.
type CollectDebitCardChargesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectDebitCardChargesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectDebitCardChargesRequestMultiError) AllErrors() []error { return m }

// CollectDebitCardChargesRequestValidationError is the validation error
// returned by CollectDebitCardChargesRequest.Validate if the designated
// constraints aren't met.
type CollectDebitCardChargesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectDebitCardChargesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectDebitCardChargesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectDebitCardChargesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectDebitCardChargesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectDebitCardChargesRequestValidationError) ErrorName() string {
	return "CollectDebitCardChargesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CollectDebitCardChargesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectDebitCardChargesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectDebitCardChargesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectDebitCardChargesRequestValidationError{}

// Validate checks the field values on CollectDebitCardChargesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectDebitCardChargesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectDebitCardChargesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CollectDebitCardChargesResponseMultiError, or nil if none found.
func (m *CollectDebitCardChargesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectDebitCardChargesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectDebitCardChargesResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectDebitCardChargesResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectDebitCardChargesResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollectDebitCardChargesResponseMultiError(errors)
	}

	return nil
}

// CollectDebitCardChargesResponseMultiError is an error wrapping multiple
// validation errors returned by CollectDebitCardChargesResponse.ValidateAll()
// if the designated constraints aren't met.
type CollectDebitCardChargesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectDebitCardChargesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectDebitCardChargesResponseMultiError) AllErrors() []error { return m }

// CollectDebitCardChargesResponseValidationError is the validation error
// returned by CollectDebitCardChargesResponse.Validate if the designated
// constraints aren't met.
type CollectDebitCardChargesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectDebitCardChargesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectDebitCardChargesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectDebitCardChargesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectDebitCardChargesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectDebitCardChargesResponseValidationError) ErrorName() string {
	return "CollectDebitCardChargesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CollectDebitCardChargesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectDebitCardChargesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectDebitCardChargesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectDebitCardChargesResponseValidationError{}

// Validate checks the field values on InitiateChargesCollectionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateChargesCollectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateChargesCollectionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// InitiateChargesCollectionRequestMultiError, or nil if none found.
func (m *InitiateChargesCollectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateChargesCollectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateChargesCollectionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateChargesCollectionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateChargesCollectionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for EntryPoint

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateChargesCollectionRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateChargesCollectionRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateChargesCollectionRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateChargesCollectionRequestMultiError(errors)
	}

	return nil
}

// InitiateChargesCollectionRequestMultiError is an error wrapping multiple
// validation errors returned by
// InitiateChargesCollectionRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateChargesCollectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateChargesCollectionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateChargesCollectionRequestMultiError) AllErrors() []error { return m }

// InitiateChargesCollectionRequestValidationError is the validation error
// returned by InitiateChargesCollectionRequest.Validate if the designated
// constraints aren't met.
type InitiateChargesCollectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateChargesCollectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateChargesCollectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateChargesCollectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateChargesCollectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateChargesCollectionRequestValidationError) ErrorName() string {
	return "InitiateChargesCollectionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateChargesCollectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateChargesCollectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateChargesCollectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateChargesCollectionRequestValidationError{}

// Validate checks the field values on InitiateChargesCollectionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateChargesCollectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateChargesCollectionResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InitiateChargesCollectionResponseMultiError, or nil if none found.
func (m *InitiateChargesCollectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateChargesCollectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateChargesCollectionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateChargesCollectionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateChargesCollectionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateChargesCollectionResponseMultiError(errors)
	}

	return nil
}

// InitiateChargesCollectionResponseMultiError is an error wrapping multiple
// validation errors returned by
// InitiateChargesCollectionResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateChargesCollectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateChargesCollectionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateChargesCollectionResponseMultiError) AllErrors() []error { return m }

// InitiateChargesCollectionResponseValidationError is the validation error
// returned by InitiateChargesCollectionResponse.Validate if the designated
// constraints aren't met.
type InitiateChargesCollectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateChargesCollectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateChargesCollectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateChargesCollectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateChargesCollectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateChargesCollectionResponseValidationError) ErrorName() string {
	return "InitiateChargesCollectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateChargesCollectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateChargesCollectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateChargesCollectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateChargesCollectionResponseValidationError{}

// Validate checks the field values on PollChargesCollectionStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PollChargesCollectionStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PollChargesCollectionStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PollChargesCollectionStatusRequestMultiError, or nil if none found.
func (m *PollChargesCollectionStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PollChargesCollectionStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PollChargesCollectionStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PollChargesCollectionStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PollChargesCollectionStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for EntryPoint

	if len(errors) > 0 {
		return PollChargesCollectionStatusRequestMultiError(errors)
	}

	return nil
}

// PollChargesCollectionStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// PollChargesCollectionStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type PollChargesCollectionStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PollChargesCollectionStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PollChargesCollectionStatusRequestMultiError) AllErrors() []error { return m }

// PollChargesCollectionStatusRequestValidationError is the validation error
// returned by PollChargesCollectionStatusRequest.Validate if the designated
// constraints aren't met.
type PollChargesCollectionStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PollChargesCollectionStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PollChargesCollectionStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PollChargesCollectionStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PollChargesCollectionStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PollChargesCollectionStatusRequestValidationError) ErrorName() string {
	return "PollChargesCollectionStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PollChargesCollectionStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPollChargesCollectionStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PollChargesCollectionStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PollChargesCollectionStatusRequestValidationError{}

// Validate checks the field values on PollChargesCollectionStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PollChargesCollectionStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PollChargesCollectionStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PollChargesCollectionStatusResponseMultiError, or nil if none found.
func (m *PollChargesCollectionStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PollChargesCollectionStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PollChargesCollectionStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PollChargesCollectionStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PollChargesCollectionStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PollChargesCollectionStatusResponseMultiError(errors)
	}

	return nil
}

// PollChargesCollectionStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// PollChargesCollectionStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type PollChargesCollectionStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PollChargesCollectionStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PollChargesCollectionStatusResponseMultiError) AllErrors() []error { return m }

// PollChargesCollectionStatusResponseValidationError is the validation error
// returned by PollChargesCollectionStatusResponse.Validate if the designated
// constraints aren't met.
type PollChargesCollectionStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PollChargesCollectionStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PollChargesCollectionStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PollChargesCollectionStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PollChargesCollectionStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PollChargesCollectionStatusResponseValidationError) ErrorName() string {
	return "PollChargesCollectionStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PollChargesCollectionStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPollChargesCollectionStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PollChargesCollectionStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PollChargesCollectionStatusResponseValidationError{}
