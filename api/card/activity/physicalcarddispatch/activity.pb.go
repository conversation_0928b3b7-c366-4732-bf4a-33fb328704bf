// This files defines request response contracts for various activity types to be used in physical card dispatch flow

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/activity/physicalcarddispatch/activity.proto

package physicalcarddispatch

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	order "github.com/epifi/gamma/api/order"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateShippingAddressActivityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                  `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *UpdateShippingAddressActivityRequest) Reset() {
	*x = UpdateShippingAddressActivityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateShippingAddressActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateShippingAddressActivityRequest) ProtoMessage() {}

func (x *UpdateShippingAddressActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateShippingAddressActivityRequest.ProtoReflect.Descriptor instead.
func (*UpdateShippingAddressActivityRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateShippingAddressActivityRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpdateShippingAddressActivityRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type UpdateShippingAddressActivityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// boolean to determine if shipping address update stage has to be triggered again
	RetriggerShippingAddressUpdate bool `protobuf:"varint,2,opt,name=retrigger_shipping_address_update,json=retriggerShippingAddressUpdate,proto3" json:"retrigger_shipping_address_update,omitempty"`
}

func (x *UpdateShippingAddressActivityResponse) Reset() {
	*x = UpdateShippingAddressActivityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateShippingAddressActivityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateShippingAddressActivityResponse) ProtoMessage() {}

func (x *UpdateShippingAddressActivityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateShippingAddressActivityResponse.ProtoReflect.Descriptor instead.
func (*UpdateShippingAddressActivityResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateShippingAddressActivityResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *UpdateShippingAddressActivityResponse) GetRetriggerShippingAddressUpdate() bool {
	if x != nil {
		return x.RetriggerShippingAddressUpdate
	}
	return false
}

type PhysicalCardDispatchActivityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                  `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// entry_point is used to identify from which flow physical dispatch was trigger
	EntryPoint provisioning.UIEntryPoint `protobuf:"varint,4,opt,name=entry_point,json=entryPoint,proto3,enum=card.provisioning.UIEntryPoint" json:"entry_point,omitempty"`
}

func (x *PhysicalCardDispatchActivityRequest) Reset() {
	*x = PhysicalCardDispatchActivityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalCardDispatchActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalCardDispatchActivityRequest) ProtoMessage() {}

func (x *PhysicalCardDispatchActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalCardDispatchActivityRequest.ProtoReflect.Descriptor instead.
func (*PhysicalCardDispatchActivityRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{2}
}

func (x *PhysicalCardDispatchActivityRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *PhysicalCardDispatchActivityRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *PhysicalCardDispatchActivityRequest) GetEntryPoint() provisioning.UIEntryPoint {
	if x != nil {
		return x.EntryPoint
	}
	return provisioning.UIEntryPoint(0)
}

type PhysicalCardDispatchActivityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *PhysicalCardDispatchActivityResponse) Reset() {
	*x = PhysicalCardDispatchActivityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhysicalCardDispatchActivityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhysicalCardDispatchActivityResponse) ProtoMessage() {}

func (x *PhysicalCardDispatchActivityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhysicalCardDispatchActivityResponse.ProtoReflect.Descriptor instead.
func (*PhysicalCardDispatchActivityResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{3}
}

func (x *PhysicalCardDispatchActivityResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CreateShippingPreferenceActivityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                  `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	ActorId       string                  `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AddressType   typesv2.AddressType     `protobuf:"varint,4,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
}

func (x *CreateShippingPreferenceActivityRequest) Reset() {
	*x = CreateShippingPreferenceActivityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateShippingPreferenceActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateShippingPreferenceActivityRequest) ProtoMessage() {}

func (x *CreateShippingPreferenceActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateShippingPreferenceActivityRequest.ProtoReflect.Descriptor instead.
func (*CreateShippingPreferenceActivityRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{4}
}

func (x *CreateShippingPreferenceActivityRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CreateShippingPreferenceActivityRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CreateShippingPreferenceActivityRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateShippingPreferenceActivityRequest) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

type CreateShippingPreferenceActivityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CreateShippingPreferenceActivityResponse) Reset() {
	*x = CreateShippingPreferenceActivityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateShippingPreferenceActivityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateShippingPreferenceActivityResponse) ProtoMessage() {}

func (x *CreateShippingPreferenceActivityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateShippingPreferenceActivityResponse.ProtoReflect.Descriptor instead.
func (*CreateShippingPreferenceActivityResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{5}
}

func (x *CreateShippingPreferenceActivityResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// Request message for physical card dispatch activities
type InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card id of the card which needs to be dispatched
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// address type of the user's address where card needs to be delivered
	AddressType typesv2.AddressType `protobuf:"varint,3,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) Reset() {
	*x = InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) ProtoMessage() {}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest.ProtoReflect.Descriptor instead.
func (*InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{6}
}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

type InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) Reset() {
	*x = InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) ProtoMessage() {}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse.ProtoReflect.Descriptor instead.
func (*InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{7}
}

func (x *InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type UpdatePhysicalCardDispatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card id of the card which needs to be dispatched
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// state to which physical card dispatch request needs to be updated
	StateToUpdate provisioning.RequestState `protobuf:"varint,3,opt,name=state_to_update,json=stateToUpdate,proto3,enum=card.provisioning.RequestState" json:"state_to_update,omitempty"`
	// sub status of the physical card dispatch request need to be updated
	SubStatusToUpdate provisioning.RequestSubStatus `protobuf:"varint,4,opt,name=sub_status_to_update,json=subStatusToUpdate,proto3,enum=card.provisioning.RequestSubStatus" json:"sub_status_to_update,omitempty"`
	// last stage executed of during physical card dispatch request need to be updated
	StageToBeUpdated provisioning.DCRequestStage `protobuf:"varint,5,opt,name=stage_to_be_updated,json=stageToBeUpdated,proto3,enum=card.provisioning.DCRequestStage" json:"stage_to_be_updated,omitempty"`
}

func (x *UpdatePhysicalCardDispatchRequest) Reset() {
	*x = UpdatePhysicalCardDispatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePhysicalCardDispatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePhysicalCardDispatchRequest) ProtoMessage() {}

func (x *UpdatePhysicalCardDispatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePhysicalCardDispatchRequest.ProtoReflect.Descriptor instead.
func (*UpdatePhysicalCardDispatchRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{8}
}

func (x *UpdatePhysicalCardDispatchRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpdatePhysicalCardDispatchRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *UpdatePhysicalCardDispatchRequest) GetStateToUpdate() provisioning.RequestState {
	if x != nil {
		return x.StateToUpdate
	}
	return provisioning.RequestState(0)
}

func (x *UpdatePhysicalCardDispatchRequest) GetSubStatusToUpdate() provisioning.RequestSubStatus {
	if x != nil {
		return x.SubStatusToUpdate
	}
	return provisioning.RequestSubStatus(0)
}

func (x *UpdatePhysicalCardDispatchRequest) GetStageToBeUpdated() provisioning.DCRequestStage {
	if x != nil {
		return x.StageToBeUpdated
	}
	return provisioning.DCRequestStage(0)
}

type UpdatePhysicalCardDispatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *UpdatePhysicalCardDispatchResponse) Reset() {
	*x = UpdatePhysicalCardDispatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePhysicalCardDispatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePhysicalCardDispatchResponse) ProtoMessage() {}

func (x *UpdatePhysicalCardDispatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePhysicalCardDispatchResponse.ProtoReflect.Descriptor instead.
func (*UpdatePhysicalCardDispatchResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{9}
}

func (x *UpdatePhysicalCardDispatchResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CheckPaymentStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader      *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PaymentClientReqId string                  `protobuf:"bytes,2,opt,name=payment_client_req_id,json=paymentClientReqId,proto3" json:"payment_client_req_id,omitempty"`
	CardId             string                  `protobuf:"bytes,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// entry_point is used to identify from which flow physical dispatch was trigger
	EntryPoint provisioning.UIEntryPoint `protobuf:"varint,4,opt,name=entry_point,json=entryPoint,proto3,enum=card.provisioning.UIEntryPoint" json:"entry_point,omitempty"`
	ActorId    string                    `protobuf:"bytes,5,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *CheckPaymentStatusRequest) Reset() {
	*x = CheckPaymentStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPaymentStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPaymentStatusRequest) ProtoMessage() {}

func (x *CheckPaymentStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPaymentStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckPaymentStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{10}
}

func (x *CheckPaymentStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CheckPaymentStatusRequest) GetPaymentClientReqId() string {
	if x != nil {
		return x.PaymentClientReqId
	}
	return ""
}

func (x *CheckPaymentStatusRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CheckPaymentStatusRequest) GetEntryPoint() provisioning.UIEntryPoint {
	if x != nil {
		return x.EntryPoint
	}
	return provisioning.UIEntryPoint(0)
}

func (x *CheckPaymentStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type CheckPaymentStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	PaymentStatus  order.OrderStatus        `protobuf:"varint,2,opt,name=payment_status,json=paymentStatus,proto3,enum=order.OrderStatus" json:"payment_status,omitempty"`
}

func (x *CheckPaymentStatusResponse) Reset() {
	*x = CheckPaymentStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPaymentStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPaymentStatusResponse) ProtoMessage() {}

func (x *CheckPaymentStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPaymentStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckPaymentStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{11}
}

func (x *CheckPaymentStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CheckPaymentStatusResponse) GetPaymentStatus() order.OrderStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return order.OrderStatus(0)
}

type CollectDebitCardChargesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                  `protobuf:"bytes,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// entry_point is used to identify from which flow physical dispatch was trigger
	EntryPoint provisioning.UIEntryPoint `protobuf:"varint,4,opt,name=entry_point,json=entryPoint,proto3,enum=card.provisioning.UIEntryPoint" json:"entry_point,omitempty"`
	// amount to be paid to execute transaction
	Amount *typesv2.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *CollectDebitCardChargesRequest) Reset() {
	*x = CollectDebitCardChargesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectDebitCardChargesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectDebitCardChargesRequest) ProtoMessage() {}

func (x *CollectDebitCardChargesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectDebitCardChargesRequest.ProtoReflect.Descriptor instead.
func (*CollectDebitCardChargesRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{12}
}

func (x *CollectDebitCardChargesRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CollectDebitCardChargesRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CollectDebitCardChargesRequest) GetEntryPoint() provisioning.UIEntryPoint {
	if x != nil {
		return x.EntryPoint
	}
	return provisioning.UIEntryPoint(0)
}

func (x *CollectDebitCardChargesRequest) GetAmount() *typesv2.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type CollectDebitCardChargesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CollectDebitCardChargesResponse) Reset() {
	*x = CollectDebitCardChargesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectDebitCardChargesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectDebitCardChargesResponse) ProtoMessage() {}

func (x *CollectDebitCardChargesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectDebitCardChargesResponse.ProtoReflect.Descriptor instead.
func (*CollectDebitCardChargesResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{13}
}

func (x *CollectDebitCardChargesResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type InitiateChargesCollectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                  `protobuf:"bytes,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// entry_point is used to identify from which flow physical dispatch was trigger
	EntryPoint provisioning.UIEntryPoint `protobuf:"varint,4,opt,name=entry_point,json=entryPoint,proto3,enum=card.provisioning.UIEntryPoint" json:"entry_point,omitempty"`
	// amount(without GST applied) to be paid to execute transaction
	Amount *typesv2.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *InitiateChargesCollectionRequest) Reset() {
	*x = InitiateChargesCollectionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateChargesCollectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateChargesCollectionRequest) ProtoMessage() {}

func (x *InitiateChargesCollectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateChargesCollectionRequest.ProtoReflect.Descriptor instead.
func (*InitiateChargesCollectionRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{14}
}

func (x *InitiateChargesCollectionRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *InitiateChargesCollectionRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *InitiateChargesCollectionRequest) GetEntryPoint() provisioning.UIEntryPoint {
	if x != nil {
		return x.EntryPoint
	}
	return provisioning.UIEntryPoint(0)
}

func (x *InitiateChargesCollectionRequest) GetAmount() *typesv2.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type InitiateChargesCollectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *InitiateChargesCollectionResponse) Reset() {
	*x = InitiateChargesCollectionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateChargesCollectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateChargesCollectionResponse) ProtoMessage() {}

func (x *InitiateChargesCollectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateChargesCollectionResponse.ProtoReflect.Descriptor instead.
func (*InitiateChargesCollectionResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{15}
}

func (x *InitiateChargesCollectionResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type PollChargesCollectionStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	CardId        string                  `protobuf:"bytes,3,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// entry_point is used to identify from which flow physical dispatch was trigger
	EntryPoint provisioning.UIEntryPoint `protobuf:"varint,4,opt,name=entry_point,json=entryPoint,proto3,enum=card.provisioning.UIEntryPoint" json:"entry_point,omitempty"`
}

func (x *PollChargesCollectionStatusRequest) Reset() {
	*x = PollChargesCollectionStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollChargesCollectionStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollChargesCollectionStatusRequest) ProtoMessage() {}

func (x *PollChargesCollectionStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollChargesCollectionStatusRequest.ProtoReflect.Descriptor instead.
func (*PollChargesCollectionStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{16}
}

func (x *PollChargesCollectionStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *PollChargesCollectionStatusRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *PollChargesCollectionStatusRequest) GetEntryPoint() provisioning.UIEntryPoint {
	if x != nil {
		return x.EntryPoint
	}
	return provisioning.UIEntryPoint(0)
}

type PollChargesCollectionStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *PollChargesCollectionStatusResponse) Reset() {
	*x = PollChargesCollectionStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollChargesCollectionStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollChargesCollectionStatusResponse) ProtoMessage() {}

func (x *PollChargesCollectionStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollChargesCollectionStatusResponse.ProtoReflect.Descriptor instead.
func (*PollChargesCollectionStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP(), []int{17}
}

func (x *PollChargesCollectionStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_card_activity_physicalcarddispatch_activity_proto protoreflect.FileDescriptor

var file_api_card_activity_physicalcarddispatch_activity_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x63, 0x61, 0x72, 0x64,
	0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x63,
	0x61, 0x72, 0x64, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x1a, 0x28, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x15, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x01, 0x0a, 0x24, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0xbf, 0x01, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x49, 0x0a,
	0x21, 0x72, 0x65, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x73, 0x68, 0x69, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x72, 0x65, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0xca, 0x01, 0x0a, 0x23, 0x50, 0x68, 0x79,
	0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x49, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0x73, 0x0a, 0x24, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61,
	0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xe4, 0x01, 0x0a, 0x27, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x77, 0x0a, 0x28, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x68, 0x69, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xdd, 0x01, 0x0a, 0x3b, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x44,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x3b, 0x0a,
	0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x3c, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x53, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x64, 0x44,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xf7, 0x02, 0x0a, 0x21, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49,
	0x64, 0x12, 0x47, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x54, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x54, 0x0a, 0x14, 0x73, 0x75,
	0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x73,
	0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x50, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x65, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e,
	0x67, 0x2e, 0x44, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x10, 0x73, 0x74, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x42, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x22, 0x71, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x68, 0x79, 0x73,
	0x69, 0x63, 0x61, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x8e, 0x02, 0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x31, 0x0a,
	0x15, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x55, 0x49, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52,
	0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xa4, 0x01, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xf1, 0x01,
	0x0a, 0x1e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x65, 0x62, 0x69, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x49, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x6e, 0x0a, 0x1f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x44, 0x65, 0x62, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0xf3, 0x01, 0x0a, 0x20, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x6e, 0x67, 0x2e, 0x55, 0x49, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52,
	0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x70, 0x0a, 0x21, 0x49, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xc9, 0x01, 0x0a, 0x22, 0x50, 0x6f,
	0x6c, 0x6c, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x49, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0x72, 0x0a, 0x23, 0x50, 0x6f, 0x6c, 0x6c, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61,
	0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x7e, 0x0a, 0x3d, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x63, 0x61,
	0x72, 0x64, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2f, 0x70, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x63, 0x61, 0x72,
	0x64, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_card_activity_physicalcarddispatch_activity_proto_rawDescOnce sync.Once
	file_api_card_activity_physicalcarddispatch_activity_proto_rawDescData = file_api_card_activity_physicalcarddispatch_activity_proto_rawDesc
)

func file_api_card_activity_physicalcarddispatch_activity_proto_rawDescGZIP() []byte {
	file_api_card_activity_physicalcarddispatch_activity_proto_rawDescOnce.Do(func() {
		file_api_card_activity_physicalcarddispatch_activity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_activity_physicalcarddispatch_activity_proto_rawDescData)
	})
	return file_api_card_activity_physicalcarddispatch_activity_proto_rawDescData
}

var file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_api_card_activity_physicalcarddispatch_activity_proto_goTypes = []interface{}{
	(*UpdateShippingAddressActivityRequest)(nil),                         // 0: card.activity.physicalcarddispatch.UpdateShippingAddressActivityRequest
	(*UpdateShippingAddressActivityResponse)(nil),                        // 1: card.activity.physicalcarddispatch.UpdateShippingAddressActivityResponse
	(*PhysicalCardDispatchActivityRequest)(nil),                          // 2: card.activity.physicalcarddispatch.PhysicalCardDispatchActivityRequest
	(*PhysicalCardDispatchActivityResponse)(nil),                         // 3: card.activity.physicalcarddispatch.PhysicalCardDispatchActivityResponse
	(*CreateShippingPreferenceActivityRequest)(nil),                      // 4: card.activity.physicalcarddispatch.CreateShippingPreferenceActivityRequest
	(*CreateShippingPreferenceActivityResponse)(nil),                     // 5: card.activity.physicalcarddispatch.CreateShippingPreferenceActivityResponse
	(*InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest)(nil),  // 6: card.activity.physicalcarddispatch.InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest
	(*InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse)(nil), // 7: card.activity.physicalcarddispatch.InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse
	(*UpdatePhysicalCardDispatchRequest)(nil),                            // 8: card.activity.physicalcarddispatch.UpdatePhysicalCardDispatchRequest
	(*UpdatePhysicalCardDispatchResponse)(nil),                           // 9: card.activity.physicalcarddispatch.UpdatePhysicalCardDispatchResponse
	(*CheckPaymentStatusRequest)(nil),                                    // 10: card.activity.physicalcarddispatch.CheckPaymentStatusRequest
	(*CheckPaymentStatusResponse)(nil),                                   // 11: card.activity.physicalcarddispatch.CheckPaymentStatusResponse
	(*CollectDebitCardChargesRequest)(nil),                               // 12: card.activity.physicalcarddispatch.CollectDebitCardChargesRequest
	(*CollectDebitCardChargesResponse)(nil),                              // 13: card.activity.physicalcarddispatch.CollectDebitCardChargesResponse
	(*InitiateChargesCollectionRequest)(nil),                             // 14: card.activity.physicalcarddispatch.InitiateChargesCollectionRequest
	(*InitiateChargesCollectionResponse)(nil),                            // 15: card.activity.physicalcarddispatch.InitiateChargesCollectionResponse
	(*PollChargesCollectionStatusRequest)(nil),                           // 16: card.activity.physicalcarddispatch.PollChargesCollectionStatusRequest
	(*PollChargesCollectionStatusResponse)(nil),                          // 17: card.activity.physicalcarddispatch.PollChargesCollectionStatusResponse
	(*activity.RequestHeader)(nil),                                       // 18: celestial.activity.RequestHeader
	(*activity.ResponseHeader)(nil),                                      // 19: celestial.activity.ResponseHeader
	(provisioning.UIEntryPoint)(0),                                       // 20: card.provisioning.UIEntryPoint
	(typesv2.AddressType)(0),                                             // 21: api.typesv2.AddressType
	(provisioning.RequestState)(0),                                       // 22: card.provisioning.RequestState
	(provisioning.RequestSubStatus)(0),                                   // 23: card.provisioning.RequestSubStatus
	(provisioning.DCRequestStage)(0),                                     // 24: card.provisioning.DCRequestStage
	(order.OrderStatus)(0),                                               // 25: order.OrderStatus
	(*typesv2.Money)(nil),                                                // 26: api.typesv2.Money
}
var file_api_card_activity_physicalcarddispatch_activity_proto_depIdxs = []int32{
	18, // 0: card.activity.physicalcarddispatch.UpdateShippingAddressActivityRequest.request_header:type_name -> celestial.activity.RequestHeader
	19, // 1: card.activity.physicalcarddispatch.UpdateShippingAddressActivityResponse.response_header:type_name -> celestial.activity.ResponseHeader
	18, // 2: card.activity.physicalcarddispatch.PhysicalCardDispatchActivityRequest.request_header:type_name -> celestial.activity.RequestHeader
	20, // 3: card.activity.physicalcarddispatch.PhysicalCardDispatchActivityRequest.entry_point:type_name -> card.provisioning.UIEntryPoint
	19, // 4: card.activity.physicalcarddispatch.PhysicalCardDispatchActivityResponse.response_header:type_name -> celestial.activity.ResponseHeader
	18, // 5: card.activity.physicalcarddispatch.CreateShippingPreferenceActivityRequest.request_header:type_name -> celestial.activity.RequestHeader
	21, // 6: card.activity.physicalcarddispatch.CreateShippingPreferenceActivityRequest.address_type:type_name -> api.typesv2.AddressType
	19, // 7: card.activity.physicalcarddispatch.CreateShippingPreferenceActivityResponse.response_header:type_name -> celestial.activity.ResponseHeader
	18, // 8: card.activity.physicalcarddispatch.InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest.request_header:type_name -> celestial.activity.RequestHeader
	21, // 9: card.activity.physicalcarddispatch.InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest.address_type:type_name -> api.typesv2.AddressType
	19, // 10: card.activity.physicalcarddispatch.InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse.response_header:type_name -> celestial.activity.ResponseHeader
	18, // 11: card.activity.physicalcarddispatch.UpdatePhysicalCardDispatchRequest.request_header:type_name -> celestial.activity.RequestHeader
	22, // 12: card.activity.physicalcarddispatch.UpdatePhysicalCardDispatchRequest.state_to_update:type_name -> card.provisioning.RequestState
	23, // 13: card.activity.physicalcarddispatch.UpdatePhysicalCardDispatchRequest.sub_status_to_update:type_name -> card.provisioning.RequestSubStatus
	24, // 14: card.activity.physicalcarddispatch.UpdatePhysicalCardDispatchRequest.stage_to_be_updated:type_name -> card.provisioning.DCRequestStage
	19, // 15: card.activity.physicalcarddispatch.UpdatePhysicalCardDispatchResponse.response_header:type_name -> celestial.activity.ResponseHeader
	18, // 16: card.activity.physicalcarddispatch.CheckPaymentStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	20, // 17: card.activity.physicalcarddispatch.CheckPaymentStatusRequest.entry_point:type_name -> card.provisioning.UIEntryPoint
	19, // 18: card.activity.physicalcarddispatch.CheckPaymentStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	25, // 19: card.activity.physicalcarddispatch.CheckPaymentStatusResponse.payment_status:type_name -> order.OrderStatus
	18, // 20: card.activity.physicalcarddispatch.CollectDebitCardChargesRequest.request_header:type_name -> celestial.activity.RequestHeader
	20, // 21: card.activity.physicalcarddispatch.CollectDebitCardChargesRequest.entry_point:type_name -> card.provisioning.UIEntryPoint
	26, // 22: card.activity.physicalcarddispatch.CollectDebitCardChargesRequest.amount:type_name -> api.typesv2.Money
	19, // 23: card.activity.physicalcarddispatch.CollectDebitCardChargesResponse.response_header:type_name -> celestial.activity.ResponseHeader
	18, // 24: card.activity.physicalcarddispatch.InitiateChargesCollectionRequest.request_header:type_name -> celestial.activity.RequestHeader
	20, // 25: card.activity.physicalcarddispatch.InitiateChargesCollectionRequest.entry_point:type_name -> card.provisioning.UIEntryPoint
	26, // 26: card.activity.physicalcarddispatch.InitiateChargesCollectionRequest.amount:type_name -> api.typesv2.Money
	19, // 27: card.activity.physicalcarddispatch.InitiateChargesCollectionResponse.response_header:type_name -> celestial.activity.ResponseHeader
	18, // 28: card.activity.physicalcarddispatch.PollChargesCollectionStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	20, // 29: card.activity.physicalcarddispatch.PollChargesCollectionStatusRequest.entry_point:type_name -> card.provisioning.UIEntryPoint
	19, // 30: card.activity.physicalcarddispatch.PollChargesCollectionStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_api_card_activity_physicalcarddispatch_activity_proto_init() }
func file_api_card_activity_physicalcarddispatch_activity_proto_init() {
	if File_api_card_activity_physicalcarddispatch_activity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateShippingAddressActivityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateShippingAddressActivityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalCardDispatchActivityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhysicalCardDispatchActivityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateShippingPreferenceActivityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateShippingPreferenceActivityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePhysicalCardDispatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePhysicalCardDispatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPaymentStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPaymentStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectDebitCardChargesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectDebitCardChargesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateChargesCollectionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateChargesCollectionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollChargesCollectionStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollChargesCollectionStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_activity_physicalcarddispatch_activity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_activity_physicalcarddispatch_activity_proto_goTypes,
		DependencyIndexes: file_api_card_activity_physicalcarddispatch_activity_proto_depIdxs,
		MessageInfos:      file_api_card_activity_physicalcarddispatch_activity_proto_msgTypes,
	}.Build()
	File_api_card_activity_physicalcarddispatch_activity_proto = out.File
	file_api_card_activity_physicalcarddispatch_activity_proto_rawDesc = nil
	file_api_card_activity_physicalcarddispatch_activity_proto_goTypes = nil
	file_api_card_activity_physicalcarddispatch_activity_proto_depIdxs = nil
}
