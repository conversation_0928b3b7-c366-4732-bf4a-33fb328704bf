// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/card/activity/activity.proto

package activity

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	workflow "github.com/epifi/be-common/api/celestial/workflow"
	enums "github.com/epifi/gamma/api/card/enums"
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BlockCardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card id of the card which needs to be blocked
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *BlockCardRequest) Reset() {
	*x = BlockCardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockCardRequest) ProtoMessage() {}

func (x *BlockCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockCardRequest.ProtoReflect.Descriptor instead.
func (*BlockCardRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{0}
}

func (x *BlockCardRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *BlockCardRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type BlockCardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *BlockCardResponse) Reset() {
	*x = BlockCardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockCardResponse) ProtoMessage() {}

func (x *BlockCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockCardResponse.ProtoReflect.Descriptor instead.
func (*BlockCardResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{1}
}

func (x *BlockCardResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CardRequestUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Next action deeplink can be sent in here for update
	NextAction *deeplink.Deeplink           `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	Status     enums.CardRequestStatus      `protobuf:"varint,3,opt,name=status,proto3,enum=card.enums.CardRequestStatus" json:"status,omitempty"`
	FieldMasks []enums.CardRequestFieldMask `protobuf:"varint,4,rep,packed,name=field_masks,json=fieldMasks,proto3,enum=card.enums.CardRequestFieldMask" json:"field_masks,omitempty"`
	// Workflow stage can be sent in when next action needs to be generated
	WorkflowStageEnum *workflow.StageEnum `protobuf:"bytes,5,opt,name=workflow_stage_enum,json=workflowStageEnum,proto3" json:"workflow_stage_enum,omitempty"`
	// Request Details
	CardRequestDetails *provisioning.CardRequestDetails `protobuf:"bytes,6,opt,name=card_request_details,json=cardRequestDetails,proto3" json:"card_request_details,omitempty"`
	// Workflow stage can be sent in when next action needs to be generated
	CardRequestStage enums.CardRequestStageName `protobuf:"varint,7,opt,name=card_request_stage,json=cardRequestStage,proto3,enum=card.enums.CardRequestStageName" json:"card_request_stage,omitempty"`
}

func (x *CardRequestUpdateRequest) Reset() {
	*x = CardRequestUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardRequestUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardRequestUpdateRequest) ProtoMessage() {}

func (x *CardRequestUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardRequestUpdateRequest.ProtoReflect.Descriptor instead.
func (*CardRequestUpdateRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{2}
}

func (x *CardRequestUpdateRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CardRequestUpdateRequest) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *CardRequestUpdateRequest) GetStatus() enums.CardRequestStatus {
	if x != nil {
		return x.Status
	}
	return enums.CardRequestStatus(0)
}

func (x *CardRequestUpdateRequest) GetFieldMasks() []enums.CardRequestFieldMask {
	if x != nil {
		return x.FieldMasks
	}
	return nil
}

func (x *CardRequestUpdateRequest) GetWorkflowStageEnum() *workflow.StageEnum {
	if x != nil {
		return x.WorkflowStageEnum
	}
	return nil
}

func (x *CardRequestUpdateRequest) GetCardRequestDetails() *provisioning.CardRequestDetails {
	if x != nil {
		return x.CardRequestDetails
	}
	return nil
}

func (x *CardRequestUpdateRequest) GetCardRequestStage() enums.CardRequestStageName {
	if x != nil {
		return x.CardRequestStage
	}
	return enums.CardRequestStageName(0)
}

type CardRequestUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// updated card request
	CardRequest *provisioning.CardRequest `protobuf:"bytes,2,opt,name=card_request,json=cardRequest,proto3" json:"card_request,omitempty"`
}

func (x *CardRequestUpdateResponse) Reset() {
	*x = CardRequestUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardRequestUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardRequestUpdateResponse) ProtoMessage() {}

func (x *CardRequestUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardRequestUpdateResponse.ProtoReflect.Descriptor instead.
func (*CardRequestUpdateResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{3}
}

func (x *CardRequestUpdateResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CardRequestUpdateResponse) GetCardRequest() *provisioning.CardRequest {
	if x != nil {
		return x.CardRequest
	}
	return nil
}

type CreateNewCardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *CreateNewCardRequest) Reset() {
	*x = CreateNewCardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNewCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNewCardRequest) ProtoMessage() {}

func (x *CreateNewCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNewCardRequest.ProtoReflect.Descriptor instead.
func (*CreateNewCardRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{4}
}

func (x *CreateNewCardRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type CreateNewCardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// card id of the newly created card in reissue flow
	NewCardId string `protobuf:"bytes,2,opt,name=new_card_id,json=newCardId,proto3" json:"new_card_id,omitempty"`
}

func (x *CreateNewCardResponse) Reset() {
	*x = CreateNewCardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNewCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNewCardResponse) ProtoMessage() {}

func (x *CreateNewCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNewCardResponse.ProtoReflect.Descriptor instead.
func (*CreateNewCardResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{5}
}

func (x *CreateNewCardResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CreateNewCardResponse) GetNewCardId() string {
	if x != nil {
		return x.NewCardId
	}
	return ""
}

type PollCardCreationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card_id for which card creation was initiated
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *PollCardCreationStatusRequest) Reset() {
	*x = PollCardCreationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollCardCreationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollCardCreationStatusRequest) ProtoMessage() {}

func (x *PollCardCreationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollCardCreationStatusRequest.ProtoReflect.Descriptor instead.
func (*PollCardCreationStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{6}
}

func (x *PollCardCreationStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *PollCardCreationStatusRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type PollCardCreationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *PollCardCreationStatusResponse) Reset() {
	*x = PollCardCreationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollCardCreationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollCardCreationStatusResponse) ProtoMessage() {}

func (x *PollCardCreationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollCardCreationStatusResponse.ProtoReflect.Descriptor instead.
func (*PollCardCreationStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{7}
}

func (x *PollCardCreationStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type InitiatePhysicalCardDispatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card id for which physical dispatch needs to be initiated
	CardId  string         `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	ActorId string         `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Amount  *typesv2.Money `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *InitiatePhysicalCardDispatchRequest) Reset() {
	*x = InitiatePhysicalCardDispatchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiatePhysicalCardDispatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiatePhysicalCardDispatchRequest) ProtoMessage() {}

func (x *InitiatePhysicalCardDispatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiatePhysicalCardDispatchRequest.ProtoReflect.Descriptor instead.
func (*InitiatePhysicalCardDispatchRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{8}
}

func (x *InitiatePhysicalCardDispatchRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *InitiatePhysicalCardDispatchRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *InitiatePhysicalCardDispatchRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InitiatePhysicalCardDispatchRequest) GetAmount() *typesv2.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type InitiatePhysicalCardDispatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// flag to check, if should start poll for physical dispatch status
	ShouldSkipDispatchStatusPoll bool `protobuf:"varint,2,opt,name=Should_skip_dispatch_status_poll,json=ShouldSkipDispatchStatusPoll,proto3" json:"Should_skip_dispatch_status_poll,omitempty"`
}

func (x *InitiatePhysicalCardDispatchResponse) Reset() {
	*x = InitiatePhysicalCardDispatchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiatePhysicalCardDispatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiatePhysicalCardDispatchResponse) ProtoMessage() {}

func (x *InitiatePhysicalCardDispatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiatePhysicalCardDispatchResponse.ProtoReflect.Descriptor instead.
func (*InitiatePhysicalCardDispatchResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{9}
}

func (x *InitiatePhysicalCardDispatchResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *InitiatePhysicalCardDispatchResponse) GetShouldSkipDispatchStatusPoll() bool {
	if x != nil {
		return x.ShouldSkipDispatchStatusPoll
	}
	return false
}

type PollPhysicalDispatchStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// card id to poll dispatch status
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
}

func (x *PollPhysicalDispatchStatusRequest) Reset() {
	*x = PollPhysicalDispatchStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollPhysicalDispatchStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollPhysicalDispatchStatusRequest) ProtoMessage() {}

func (x *PollPhysicalDispatchStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollPhysicalDispatchStatusRequest.ProtoReflect.Descriptor instead.
func (*PollPhysicalDispatchStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{10}
}

func (x *PollPhysicalDispatchStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *PollPhysicalDispatchStatusRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type PollPhysicalDispatchStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *PollPhysicalDispatchStatusResponse) Reset() {
	*x = PollPhysicalDispatchStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_card_activity_activity_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollPhysicalDispatchStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollPhysicalDispatchStatusResponse) ProtoMessage() {}

func (x *PollPhysicalDispatchStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_card_activity_activity_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollPhysicalDispatchStatusResponse.ProtoReflect.Descriptor instead.
func (*PollPhysicalDispatchStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_card_activity_activity_proto_rawDescGZIP(), []int{11}
}

func (x *PollPhysicalDispatchStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_card_activity_activity_proto protoreflect.FileDescriptor

var file_api_card_activity_activity_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x75, 0x0a, 0x10, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0x60, 0x0a, 0x11, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x94, 0x04, 0x0a, 0x18, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x63, 0x61, 0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61,
	0x73, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x4d, 0x0a, 0x13, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x11, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x57, 0x0a, 0x14, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x63, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x4e, 0x0a, 0x12, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x63, 0x61,
	0x72, 0x64, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x10, 0x63,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x22,
	0xab, 0x01, 0x0a, 0x19, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0c, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x60, 0x0a,
	0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x77, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22,
	0x84, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x77, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x65, 0x77,
	0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x1d, 0x50, 0x6f, 0x6c, 0x6c, 0x43,
	0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0x6d, 0x0a, 0x1e, 0x50,
	0x6f, 0x6c, 0x6c, 0x43, 0x61, 0x72, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xcf, 0x01, 0x0a, 0x23, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x43,
	0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xbb, 0x01, 0x0a,
	0x24, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61,
	0x6c, 0x43, 0x61, 0x72, 0x64, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x46, 0x0a, 0x20, 0x53, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x73, 0x6b, 0x69,
	0x70, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x70, 0x6f, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x53, 0x68,
	0x6f, 0x75, 0x6c, 0x64, 0x53, 0x6b, 0x69, 0x70, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x6f, 0x6c, 0x6c, 0x22, 0x86, 0x01, 0x0a, 0x21, 0x50,
	0x6f, 0x6c, 0x6c, 0x50, 0x68, 0x79, 0x73, 0x69, 0x63, 0x61, 0x6c, 0x44, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x22, 0x71, 0x0a, 0x22, 0x50, 0x6f, 0x6c, 0x6c, 0x50, 0x68, 0x79, 0x73, 0x69,
	0x63, 0x61, 0x6c, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x61, 0x72, 0x64, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_card_activity_activity_proto_rawDescOnce sync.Once
	file_api_card_activity_activity_proto_rawDescData = file_api_card_activity_activity_proto_rawDesc
)

func file_api_card_activity_activity_proto_rawDescGZIP() []byte {
	file_api_card_activity_activity_proto_rawDescOnce.Do(func() {
		file_api_card_activity_activity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_card_activity_activity_proto_rawDescData)
	})
	return file_api_card_activity_activity_proto_rawDescData
}

var file_api_card_activity_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_card_activity_activity_proto_goTypes = []interface{}{
	(*BlockCardRequest)(nil),                     // 0: card.activity.BlockCardRequest
	(*BlockCardResponse)(nil),                    // 1: card.activity.BlockCardResponse
	(*CardRequestUpdateRequest)(nil),             // 2: card.activity.CardRequestUpdateRequest
	(*CardRequestUpdateResponse)(nil),            // 3: card.activity.CardRequestUpdateResponse
	(*CreateNewCardRequest)(nil),                 // 4: card.activity.CreateNewCardRequest
	(*CreateNewCardResponse)(nil),                // 5: card.activity.CreateNewCardResponse
	(*PollCardCreationStatusRequest)(nil),        // 6: card.activity.PollCardCreationStatusRequest
	(*PollCardCreationStatusResponse)(nil),       // 7: card.activity.PollCardCreationStatusResponse
	(*InitiatePhysicalCardDispatchRequest)(nil),  // 8: card.activity.InitiatePhysicalCardDispatchRequest
	(*InitiatePhysicalCardDispatchResponse)(nil), // 9: card.activity.InitiatePhysicalCardDispatchResponse
	(*PollPhysicalDispatchStatusRequest)(nil),    // 10: card.activity.PollPhysicalDispatchStatusRequest
	(*PollPhysicalDispatchStatusResponse)(nil),   // 11: card.activity.PollPhysicalDispatchStatusResponse
	(*activity.RequestHeader)(nil),               // 12: celestial.activity.RequestHeader
	(*activity.ResponseHeader)(nil),              // 13: celestial.activity.ResponseHeader
	(*deeplink.Deeplink)(nil),                    // 14: frontend.deeplink.Deeplink
	(enums.CardRequestStatus)(0),                 // 15: card.enums.CardRequestStatus
	(enums.CardRequestFieldMask)(0),              // 16: card.enums.CardRequestFieldMask
	(*workflow.StageEnum)(nil),                   // 17: celestial.workflow.StageEnum
	(*provisioning.CardRequestDetails)(nil),      // 18: card.provisioning.CardRequestDetails
	(enums.CardRequestStageName)(0),              // 19: card.enums.CardRequestStageName
	(*provisioning.CardRequest)(nil),             // 20: card.provisioning.CardRequest
	(*typesv2.Money)(nil),                        // 21: api.typesv2.Money
}
var file_api_card_activity_activity_proto_depIdxs = []int32{
	12, // 0: card.activity.BlockCardRequest.request_header:type_name -> celestial.activity.RequestHeader
	13, // 1: card.activity.BlockCardResponse.response_header:type_name -> celestial.activity.ResponseHeader
	12, // 2: card.activity.CardRequestUpdateRequest.request_header:type_name -> celestial.activity.RequestHeader
	14, // 3: card.activity.CardRequestUpdateRequest.next_action:type_name -> frontend.deeplink.Deeplink
	15, // 4: card.activity.CardRequestUpdateRequest.status:type_name -> card.enums.CardRequestStatus
	16, // 5: card.activity.CardRequestUpdateRequest.field_masks:type_name -> card.enums.CardRequestFieldMask
	17, // 6: card.activity.CardRequestUpdateRequest.workflow_stage_enum:type_name -> celestial.workflow.StageEnum
	18, // 7: card.activity.CardRequestUpdateRequest.card_request_details:type_name -> card.provisioning.CardRequestDetails
	19, // 8: card.activity.CardRequestUpdateRequest.card_request_stage:type_name -> card.enums.CardRequestStageName
	13, // 9: card.activity.CardRequestUpdateResponse.response_header:type_name -> celestial.activity.ResponseHeader
	20, // 10: card.activity.CardRequestUpdateResponse.card_request:type_name -> card.provisioning.CardRequest
	12, // 11: card.activity.CreateNewCardRequest.request_header:type_name -> celestial.activity.RequestHeader
	13, // 12: card.activity.CreateNewCardResponse.response_header:type_name -> celestial.activity.ResponseHeader
	12, // 13: card.activity.PollCardCreationStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	13, // 14: card.activity.PollCardCreationStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	12, // 15: card.activity.InitiatePhysicalCardDispatchRequest.request_header:type_name -> celestial.activity.RequestHeader
	21, // 16: card.activity.InitiatePhysicalCardDispatchRequest.amount:type_name -> api.typesv2.Money
	13, // 17: card.activity.InitiatePhysicalCardDispatchResponse.response_header:type_name -> celestial.activity.ResponseHeader
	12, // 18: card.activity.PollPhysicalDispatchStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	13, // 19: card.activity.PollPhysicalDispatchStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_api_card_activity_activity_proto_init() }
func file_api_card_activity_activity_proto_init() {
	if File_api_card_activity_activity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_card_activity_activity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockCardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockCardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardRequestUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardRequestUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNewCardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNewCardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollCardCreationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollCardCreationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiatePhysicalCardDispatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiatePhysicalCardDispatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollPhysicalDispatchStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_card_activity_activity_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollPhysicalDispatchStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_card_activity_activity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_card_activity_activity_proto_goTypes,
		DependencyIndexes: file_api_card_activity_activity_proto_depIdxs,
		MessageInfos:      file_api_card_activity_activity_proto_msgTypes,
	}.Build()
	File_api_card_activity_activity_proto = out.File
	file_api_card_activity_activity_proto_rawDesc = nil
	file_api_card_activity_activity_proto_goTypes = nil
	file_api_card_activity_activity_proto_depIdxs = nil
}
