package card

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardState_value[val]
	*x = CardState(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardType_value[val]
	*x = CardType(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardForm) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardForm) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardForm_value[val]
	*x = CardForm(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardNetworkType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardNetworkType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardNetworkType_value[val]
	*x = CardNetworkType(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x CardIssueType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *CardIssueType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := CardIssueType_value[val]
	*x = CardIssueType(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (m *BasicCardInfo) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return protojson.Marshal(m)
}

// Scanner interface implementation for parsing data while reading from DB
func (m *BasicCardInfo) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, m)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}

// Valuer interface implementation for storing the data in string format in DB
func (m *PinSetOtpToken) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return protojson.Marshal(m)
}

// Scanner interface implementation for parsing data while reading from DB
func (m *PinSetOtpToken) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, m)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}
