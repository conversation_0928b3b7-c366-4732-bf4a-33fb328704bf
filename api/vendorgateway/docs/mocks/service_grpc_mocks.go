// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/docs/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	docs "github.com/epifi/gamma/api/vendorgateway/docs"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDocsClient is a mock of DocsClient interface.
type MockDocsClient struct {
	ctrl     *gomock.Controller
	recorder *MockDocsClientMockRecorder
}

// MockDocsClientMockRecorder is the mock recorder for MockDocsClient.
type MockDocsClientMockRecorder struct {
	mock *MockDocsClient
}

// NewMockDocsClient creates a new mock instance.
func NewMockDocsClient(ctrl *gomock.Controller) *MockDocsClient {
	mock := &MockDocsClient{ctrl: ctrl}
	mock.recorder = &MockDocsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocsClient) EXPECT() *MockDocsClientMockRecorder {
	return m.recorder
}

// PassportVerification mocks base method.
func (m *MockDocsClient) PassportVerification(ctx context.Context, in *docs.PassportVerificationRequest, opts ...grpc.CallOption) (*docs.PassportVerificationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PassportVerification", varargs...)
	ret0, _ := ret[0].(*docs.PassportVerificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PassportVerification indicates an expected call of PassportVerification.
func (mr *MockDocsClientMockRecorder) PassportVerification(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PassportVerification", reflect.TypeOf((*MockDocsClient)(nil).PassportVerification), varargs...)
}

// ShareDocWithVendor mocks base method.
func (m *MockDocsClient) ShareDocWithVendor(ctx context.Context, in *docs.ShareDocWithVendorRequest, opts ...grpc.CallOption) (*docs.ShareDocWithVendorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ShareDocWithVendor", varargs...)
	ret0, _ := ret[0].(*docs.ShareDocWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShareDocWithVendor indicates an expected call of ShareDocWithVendor.
func (mr *MockDocsClientMockRecorder) ShareDocWithVendor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShareDocWithVendor", reflect.TypeOf((*MockDocsClient)(nil).ShareDocWithVendor), varargs...)
}

// MockDocsServer is a mock of DocsServer interface.
type MockDocsServer struct {
	ctrl     *gomock.Controller
	recorder *MockDocsServerMockRecorder
}

// MockDocsServerMockRecorder is the mock recorder for MockDocsServer.
type MockDocsServerMockRecorder struct {
	mock *MockDocsServer
}

// NewMockDocsServer creates a new mock instance.
func NewMockDocsServer(ctrl *gomock.Controller) *MockDocsServer {
	mock := &MockDocsServer{ctrl: ctrl}
	mock.recorder = &MockDocsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocsServer) EXPECT() *MockDocsServerMockRecorder {
	return m.recorder
}

// PassportVerification mocks base method.
func (m *MockDocsServer) PassportVerification(arg0 context.Context, arg1 *docs.PassportVerificationRequest) (*docs.PassportVerificationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PassportVerification", arg0, arg1)
	ret0, _ := ret[0].(*docs.PassportVerificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PassportVerification indicates an expected call of PassportVerification.
func (mr *MockDocsServerMockRecorder) PassportVerification(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PassportVerification", reflect.TypeOf((*MockDocsServer)(nil).PassportVerification), arg0, arg1)
}

// ShareDocWithVendor mocks base method.
func (m *MockDocsServer) ShareDocWithVendor(arg0 context.Context, arg1 *docs.ShareDocWithVendorRequest) (*docs.ShareDocWithVendorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShareDocWithVendor", arg0, arg1)
	ret0, _ := ret[0].(*docs.ShareDocWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ShareDocWithVendor indicates an expected call of ShareDocWithVendor.
func (mr *MockDocsServerMockRecorder) ShareDocWithVendor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShareDocWithVendor", reflect.TypeOf((*MockDocsServer)(nil).ShareDocWithVendor), arg0, arg1)
}

// MockUnsafeDocsServer is a mock of UnsafeDocsServer interface.
type MockUnsafeDocsServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDocsServerMockRecorder
}

// MockUnsafeDocsServerMockRecorder is the mock recorder for MockUnsafeDocsServer.
type MockUnsafeDocsServerMockRecorder struct {
	mock *MockUnsafeDocsServer
}

// NewMockUnsafeDocsServer creates a new mock instance.
func NewMockUnsafeDocsServer(ctrl *gomock.Controller) *MockUnsafeDocsServer {
	mock := &MockUnsafeDocsServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDocsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDocsServer) EXPECT() *MockUnsafeDocsServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDocsServer mocks base method.
func (m *MockUnsafeDocsServer) mustEmbedUnimplementedDocsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDocsServer")
}

// mustEmbedUnimplementedDocsServer indicates an expected call of mustEmbedUnimplementedDocsServer.
func (mr *MockUnsafeDocsServerMockRecorder) mustEmbedUnimplementedDocsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDocsServer", reflect.TypeOf((*MockUnsafeDocsServer)(nil).mustEmbedUnimplementedDocsServer))
}
