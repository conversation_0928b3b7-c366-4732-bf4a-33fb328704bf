// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/consumer_service.proto

package upi

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on ProcessReqAuthRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessReqAuthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReqAuthRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessReqAuthRequestMultiError, or nil if none found.
func (m *ProcessReqAuthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqAuthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqAuthRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqAuthRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqAuthRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for RawData

	if len(errors) > 0 {
		return ProcessReqAuthRequestMultiError(errors)
	}

	return nil
}

// ProcessReqAuthRequestMultiError is an error wrapping multiple validation
// errors returned by ProcessReqAuthRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessReqAuthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqAuthRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqAuthRequestMultiError) AllErrors() []error { return m }

// ProcessReqAuthRequestValidationError is the validation error returned by
// ProcessReqAuthRequest.Validate if the designated constraints aren't met.
type ProcessReqAuthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqAuthRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqAuthRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqAuthRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqAuthRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqAuthRequestValidationError) ErrorName() string {
	return "ProcessReqAuthRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqAuthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqAuthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqAuthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqAuthRequestValidationError{}

// Validate checks the field values on ProcessReqAuthResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessReqAuthResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReqAuthResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessReqAuthResponseMultiError, or nil if none found.
func (m *ProcessReqAuthResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqAuthResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqAuthResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqAuthResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqAuthResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessReqAuthResponseMultiError(errors)
	}

	return nil
}

// ProcessReqAuthResponseMultiError is an error wrapping multiple validation
// errors returned by ProcessReqAuthResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessReqAuthResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqAuthResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqAuthResponseMultiError) AllErrors() []error { return m }

// ProcessReqAuthResponseValidationError is the validation error returned by
// ProcessReqAuthResponse.Validate if the designated constraints aren't met.
type ProcessReqAuthResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqAuthResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqAuthResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqAuthResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqAuthResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqAuthResponseValidationError) ErrorName() string {
	return "ProcessReqAuthResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqAuthResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqAuthResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqAuthResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqAuthResponseValidationError{}

// Validate checks the field values on ProcessResPayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessResPayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessResPayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessResPayRequestMultiError, or nil if none found.
func (m *ProcessResPayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessResPayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessResPayRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessResPayRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessResPayRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for RawData

	if len(errors) > 0 {
		return ProcessResPayRequestMultiError(errors)
	}

	return nil
}

// ProcessResPayRequestMultiError is an error wrapping multiple validation
// errors returned by ProcessResPayRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessResPayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessResPayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessResPayRequestMultiError) AllErrors() []error { return m }

// ProcessResPayRequestValidationError is the validation error returned by
// ProcessResPayRequest.Validate if the designated constraints aren't met.
type ProcessResPayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessResPayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessResPayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessResPayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessResPayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessResPayRequestValidationError) ErrorName() string {
	return "ProcessResPayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessResPayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessResPayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessResPayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessResPayRequestValidationError{}

// Validate checks the field values on ProcessResPayResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessResPayResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessResPayResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessResPayResponseMultiError, or nil if none found.
func (m *ProcessResPayResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessResPayResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessResPayResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessResPayResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessResPayResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessResPayResponseMultiError(errors)
	}

	return nil
}

// ProcessResPayResponseMultiError is an error wrapping multiple validation
// errors returned by ProcessResPayResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessResPayResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessResPayResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessResPayResponseMultiError) AllErrors() []error { return m }

// ProcessResPayResponseValidationError is the validation error returned by
// ProcessResPayResponse.Validate if the designated constraints aren't met.
type ProcessResPayResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessResPayResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessResPayResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessResPayResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessResPayResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessResPayResponseValidationError) ErrorName() string {
	return "ProcessResPayResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessResPayResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessResPayResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessResPayResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessResPayResponseValidationError{}

// Validate checks the field values on ProcessReqTxnConfirmationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessReqTxnConfirmationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReqTxnConfirmationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessReqTxnConfirmationRequestMultiError, or nil if none found.
func (m *ProcessReqTxnConfirmationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqTxnConfirmationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqTxnConfirmationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqTxnConfirmationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqTxnConfirmationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for RawData

	if len(errors) > 0 {
		return ProcessReqTxnConfirmationRequestMultiError(errors)
	}

	return nil
}

// ProcessReqTxnConfirmationRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessReqTxnConfirmationRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessReqTxnConfirmationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqTxnConfirmationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqTxnConfirmationRequestMultiError) AllErrors() []error { return m }

// ProcessReqTxnConfirmationRequestValidationError is the validation error
// returned by ProcessReqTxnConfirmationRequest.Validate if the designated
// constraints aren't met.
type ProcessReqTxnConfirmationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqTxnConfirmationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqTxnConfirmationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqTxnConfirmationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqTxnConfirmationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqTxnConfirmationRequestValidationError) ErrorName() string {
	return "ProcessReqTxnConfirmationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqTxnConfirmationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqTxnConfirmationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqTxnConfirmationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqTxnConfirmationRequestValidationError{}

// Validate checks the field values on ProcessReqTxnConfirmationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessReqTxnConfirmationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReqTxnConfirmationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessReqTxnConfirmationResponseMultiError, or nil if none found.
func (m *ProcessReqTxnConfirmationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqTxnConfirmationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqTxnConfirmationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqTxnConfirmationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqTxnConfirmationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessReqTxnConfirmationResponseMultiError(errors)
	}

	return nil
}

// ProcessReqTxnConfirmationResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessReqTxnConfirmationResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessReqTxnConfirmationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqTxnConfirmationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqTxnConfirmationResponseMultiError) AllErrors() []error { return m }

// ProcessReqTxnConfirmationResponseValidationError is the validation error
// returned by ProcessReqTxnConfirmationResponse.Validate if the designated
// constraints aren't met.
type ProcessReqTxnConfirmationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqTxnConfirmationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqTxnConfirmationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqTxnConfirmationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqTxnConfirmationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqTxnConfirmationResponseValidationError) ErrorName() string {
	return "ProcessReqTxnConfirmationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqTxnConfirmationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqTxnConfirmationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqTxnConfirmationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqTxnConfirmationResponseValidationError{}

// Validate checks the field values on ProcessReqValAddressRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessReqValAddressRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReqValAddressRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessReqValAddressRequestMultiError, or nil if none found.
func (m *ProcessReqValAddressRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqValAddressRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqValAddressRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqValAddressRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqValAddressRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for RawData

	if len(errors) > 0 {
		return ProcessReqValAddressRequestMultiError(errors)
	}

	return nil
}

// ProcessReqValAddressRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessReqValAddressRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessReqValAddressRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqValAddressRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqValAddressRequestMultiError) AllErrors() []error { return m }

// ProcessReqValAddressRequestValidationError is the validation error returned
// by ProcessReqValAddressRequest.Validate if the designated constraints
// aren't met.
type ProcessReqValAddressRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqValAddressRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqValAddressRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqValAddressRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqValAddressRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqValAddressRequestValidationError) ErrorName() string {
	return "ProcessReqValAddressRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqValAddressRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqValAddressRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqValAddressRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqValAddressRequestValidationError{}

// Validate checks the field values on ProcessReqValAddressResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessReqValAddressResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReqValAddressResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessReqValAddressResponseMultiError, or nil if none found.
func (m *ProcessReqValAddressResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqValAddressResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqValAddressResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqValAddressResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqValAddressResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessReqValAddressResponseMultiError(errors)
	}

	return nil
}

// ProcessReqValAddressResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessReqValAddressResponse.ValidateAll() if
// the designated constraints aren't met.
type ProcessReqValAddressResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqValAddressResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqValAddressResponseMultiError) AllErrors() []error { return m }

// ProcessReqValAddressResponseValidationError is the validation error returned
// by ProcessReqValAddressResponse.Validate if the designated constraints
// aren't met.
type ProcessReqValAddressResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqValAddressResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqValAddressResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqValAddressResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqValAddressResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqValAddressResponseValidationError) ErrorName() string {
	return "ProcessReqValAddressResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqValAddressResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqValAddressResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqValAddressResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqValAddressResponseValidationError{}

// Validate checks the field values on ProcessListPspKeysRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessListPspKeysRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessListPspKeysRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessListPspKeysRequestMultiError, or nil if none found.
func (m *ProcessListPspKeysRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessListPspKeysRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessListPspKeysRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessListPspKeysRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessListPspKeysRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for RawData

	if len(errors) > 0 {
		return ProcessListPspKeysRequestMultiError(errors)
	}

	return nil
}

// ProcessListPspKeysRequestMultiError is an error wrapping multiple validation
// errors returned by ProcessListPspKeysRequest.ValidateAll() if the
// designated constraints aren't met.
type ProcessListPspKeysRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessListPspKeysRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessListPspKeysRequestMultiError) AllErrors() []error { return m }

// ProcessListPspKeysRequestValidationError is the validation error returned by
// ProcessListPspKeysRequest.Validate if the designated constraints aren't met.
type ProcessListPspKeysRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessListPspKeysRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessListPspKeysRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessListPspKeysRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessListPspKeysRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessListPspKeysRequestValidationError) ErrorName() string {
	return "ProcessListPspKeysRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessListPspKeysRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessListPspKeysRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessListPspKeysRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessListPspKeysRequestValidationError{}

// Validate checks the field values on ProcessListPspKeysResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessListPspKeysResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessListPspKeysResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessListPspKeysResponseMultiError, or nil if none found.
func (m *ProcessListPspKeysResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessListPspKeysResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessListPspKeysResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessListPspKeysResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessListPspKeysResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessListPspKeysResponseMultiError(errors)
	}

	return nil
}

// ProcessListPspKeysResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessListPspKeysResponse.ValidateAll() if
// the designated constraints aren't met.
type ProcessListPspKeysResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessListPspKeysResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessListPspKeysResponseMultiError) AllErrors() []error { return m }

// ProcessListPspKeysResponseValidationError is the validation error returned
// by ProcessListPspKeysResponse.Validate if the designated constraints aren't met.
type ProcessListPspKeysResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessListPspKeysResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessListPspKeysResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessListPspKeysResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessListPspKeysResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessListPspKeysResponseValidationError) ErrorName() string {
	return "ProcessListPspKeysResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessListPspKeysResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessListPspKeysResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessListPspKeysResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessListPspKeysResponseValidationError{}

// Validate checks the field values on ProcessListVaeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessListVaeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessListVaeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessListVaeRequestMultiError, or nil if none found.
func (m *ProcessListVaeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessListVaeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessListVaeRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessListVaeRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessListVaeRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for RawData

	if len(errors) > 0 {
		return ProcessListVaeRequestMultiError(errors)
	}

	return nil
}

// ProcessListVaeRequestMultiError is an error wrapping multiple validation
// errors returned by ProcessListVaeRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessListVaeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessListVaeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessListVaeRequestMultiError) AllErrors() []error { return m }

// ProcessListVaeRequestValidationError is the validation error returned by
// ProcessListVaeRequest.Validate if the designated constraints aren't met.
type ProcessListVaeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessListVaeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessListVaeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessListVaeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessListVaeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessListVaeRequestValidationError) ErrorName() string {
	return "ProcessListVaeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessListVaeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessListVaeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessListVaeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessListVaeRequestValidationError{}

// Validate checks the field values on ProcessListVaeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessListVaeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessListVaeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessListVaeResponseMultiError, or nil if none found.
func (m *ProcessListVaeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessListVaeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessListVaeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessListVaeResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessListVaeResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessListVaeResponseMultiError(errors)
	}

	return nil
}

// ProcessListVaeResponseMultiError is an error wrapping multiple validation
// errors returned by ProcessListVaeResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessListVaeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessListVaeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessListVaeResponseMultiError) AllErrors() []error { return m }

// ProcessListVaeResponseValidationError is the validation error returned by
// ProcessListVaeResponse.Validate if the designated constraints aren't met.
type ProcessListVaeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessListVaeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessListVaeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessListVaeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessListVaeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessListVaeResponseValidationError) ErrorName() string {
	return "ProcessListVaeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessListVaeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessListVaeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessListVaeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessListVaeResponseValidationError{}

// Validate checks the field values on ProcessOnboardingEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessOnboardingEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessOnboardingEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessOnboardingEventResponseMultiError, or nil if none found.
func (m *ProcessOnboardingEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessOnboardingEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessOnboardingEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessOnboardingEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessOnboardingEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessOnboardingEventResponseMultiError(errors)
	}

	return nil
}

// ProcessOnboardingEventResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessOnboardingEventResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessOnboardingEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessOnboardingEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessOnboardingEventResponseMultiError) AllErrors() []error { return m }

// ProcessOnboardingEventResponseValidationError is the validation error
// returned by ProcessOnboardingEventResponse.Validate if the designated
// constraints aren't met.
type ProcessOnboardingEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessOnboardingEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessOnboardingEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessOnboardingEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessOnboardingEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessOnboardingEventResponseValidationError) ErrorName() string {
	return "ProcessOnboardingEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessOnboardingEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessOnboardingEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessOnboardingEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessOnboardingEventResponseValidationError{}

// Validate checks the field values on ProcessAuthFactorUpdateEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessAuthFactorUpdateEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAuthFactorUpdateEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessAuthFactorUpdateEventResponseMultiError, or nil if none found.
func (m *ProcessAuthFactorUpdateEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAuthFactorUpdateEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAuthFactorUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAuthFactorUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAuthFactorUpdateEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessAuthFactorUpdateEventResponseMultiError(errors)
	}

	return nil
}

// ProcessAuthFactorUpdateEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessAuthFactorUpdateEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessAuthFactorUpdateEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAuthFactorUpdateEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAuthFactorUpdateEventResponseMultiError) AllErrors() []error { return m }

// ProcessAuthFactorUpdateEventResponseValidationError is the validation error
// returned by ProcessAuthFactorUpdateEventResponse.Validate if the designated
// constraints aren't met.
type ProcessAuthFactorUpdateEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAuthFactorUpdateEventResponseValidationError) ErrorName() string {
	return "ProcessAuthFactorUpdateEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAuthFactorUpdateEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAuthFactorUpdateEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAuthFactorUpdateEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAuthFactorUpdateEventResponseValidationError{}

// Validate checks the field values on
// ProcessReqTxnConfirmationComplaintRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessReqTxnConfirmationComplaintRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessReqTxnConfirmationComplaintRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessReqTxnConfirmationComplaintRequestMultiError, or nil if none found.
func (m *ProcessReqTxnConfirmationComplaintRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqTxnConfirmationComplaintRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqTxnConfirmationComplaintRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqTxnConfirmationComplaintRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqTxnConfirmationComplaintRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for RawData

	if len(errors) > 0 {
		return ProcessReqTxnConfirmationComplaintRequestMultiError(errors)
	}

	return nil
}

// ProcessReqTxnConfirmationComplaintRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessReqTxnConfirmationComplaintRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessReqTxnConfirmationComplaintRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqTxnConfirmationComplaintRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqTxnConfirmationComplaintRequestMultiError) AllErrors() []error { return m }

// ProcessReqTxnConfirmationComplaintRequestValidationError is the validation
// error returned by ProcessReqTxnConfirmationComplaintRequest.Validate if the
// designated constraints aren't met.
type ProcessReqTxnConfirmationComplaintRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqTxnConfirmationComplaintRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqTxnConfirmationComplaintRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqTxnConfirmationComplaintRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqTxnConfirmationComplaintRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqTxnConfirmationComplaintRequestValidationError) ErrorName() string {
	return "ProcessReqTxnConfirmationComplaintRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqTxnConfirmationComplaintRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqTxnConfirmationComplaintRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqTxnConfirmationComplaintRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqTxnConfirmationComplaintRequestValidationError{}

// Validate checks the field values on
// ProcessReqTxnConfirmationComplaintResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessReqTxnConfirmationComplaintResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessReqTxnConfirmationComplaintResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessReqTxnConfirmationComplaintResponseMultiError, or nil if none found.
func (m *ProcessReqTxnConfirmationComplaintResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqTxnConfirmationComplaintResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqTxnConfirmationComplaintResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqTxnConfirmationComplaintResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqTxnConfirmationComplaintResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessReqTxnConfirmationComplaintResponseMultiError(errors)
	}

	return nil
}

// ProcessReqTxnConfirmationComplaintResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessReqTxnConfirmationComplaintResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessReqTxnConfirmationComplaintResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqTxnConfirmationComplaintResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqTxnConfirmationComplaintResponseMultiError) AllErrors() []error { return m }

// ProcessReqTxnConfirmationComplaintResponseValidationError is the validation
// error returned by ProcessReqTxnConfirmationComplaintResponse.Validate if
// the designated constraints aren't met.
type ProcessReqTxnConfirmationComplaintResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqTxnConfirmationComplaintResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqTxnConfirmationComplaintResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqTxnConfirmationComplaintResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqTxnConfirmationComplaintResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqTxnConfirmationComplaintResponseValidationError) ErrorName() string {
	return "ProcessReqTxnConfirmationComplaintResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqTxnConfirmationComplaintResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqTxnConfirmationComplaintResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqTxnConfirmationComplaintResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqTxnConfirmationComplaintResponseValidationError{}

// Validate checks the field values on ProcessRespComplaintRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessRespComplaintRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessRespComplaintRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessRespComplaintRequestMultiError, or nil if none found.
func (m *ProcessRespComplaintRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessRespComplaintRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessRespComplaintRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessRespComplaintRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessRespComplaintRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for RawData

	if len(errors) > 0 {
		return ProcessRespComplaintRequestMultiError(errors)
	}

	return nil
}

// ProcessRespComplaintRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessRespComplaintRequest.ValidateAll() if
// the designated constraints aren't met.
type ProcessRespComplaintRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessRespComplaintRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessRespComplaintRequestMultiError) AllErrors() []error { return m }

// ProcessRespComplaintRequestValidationError is the validation error returned
// by ProcessRespComplaintRequest.Validate if the designated constraints
// aren't met.
type ProcessRespComplaintRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessRespComplaintRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessRespComplaintRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessRespComplaintRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessRespComplaintRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessRespComplaintRequestValidationError) ErrorName() string {
	return "ProcessRespComplaintRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessRespComplaintRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessRespComplaintRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessRespComplaintRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessRespComplaintRequestValidationError{}

// Validate checks the field values on ProcessRespComplaintResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessRespComplaintResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessRespComplaintResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessRespComplaintResponseMultiError, or nil if none found.
func (m *ProcessRespComplaintResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessRespComplaintResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessRespComplaintResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessRespComplaintResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessRespComplaintResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessRespComplaintResponseMultiError(errors)
	}

	return nil
}

// ProcessRespComplaintResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessRespComplaintResponse.ValidateAll() if
// the designated constraints aren't met.
type ProcessRespComplaintResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessRespComplaintResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessRespComplaintResponseMultiError) AllErrors() []error { return m }

// ProcessRespComplaintResponseValidationError is the validation error returned
// by ProcessRespComplaintResponse.Validate if the designated constraints
// aren't met.
type ProcessRespComplaintResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessRespComplaintResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessRespComplaintResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessRespComplaintResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessRespComplaintResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessRespComplaintResponseValidationError) ErrorName() string {
	return "ProcessRespComplaintResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessRespComplaintResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessRespComplaintResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessRespComplaintResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessRespComplaintResponseValidationError{}
