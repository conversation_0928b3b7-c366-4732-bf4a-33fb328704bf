// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/upi_events.proto

package upi

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpiEvent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpiEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiEvent with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpiEventMultiError, or nil
// if none found.
func (m *UpiEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for AccountId

	// no validation rules for EventType

	if all {
		switch v := interface{}(m.GetActionTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiEventValidationError{
					field:  "ActionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiEventValidationError{
					field:  "ActionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiEventValidationError{
				field:  "ActionTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.EventInfo.(type) {
	case *UpiEvent_UpiPinSetInfo:
		if v == nil {
			err := UpiEventValidationError{
				field:  "EventInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpiPinSetInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpiEventValidationError{
						field:  "UpiPinSetInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpiEventValidationError{
						field:  "UpiPinSetInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpiPinSetInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpiEventValidationError{
					field:  "UpiPinSetInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UpiEventMultiError(errors)
	}

	return nil
}

// UpiEventMultiError is an error wrapping multiple validation errors returned
// by UpiEvent.ValidateAll() if the designated constraints aren't met.
type UpiEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiEventMultiError) AllErrors() []error { return m }

// UpiEventValidationError is the validation error returned by
// UpiEvent.Validate if the designated constraints aren't met.
type UpiEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiEventValidationError) ErrorName() string { return "UpiEventValidationError" }

// Error satisfies the builtin error interface
func (e UpiEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiEventValidationError{}

// Validate checks the field values on UpiPinSetInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpiPinSetInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiPinSetInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpiPinSetInfoMultiError, or
// nil if none found.
func (m *UpiPinSetInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiPinSetInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpiPinSetInfoMultiError(errors)
	}

	return nil
}

// UpiPinSetInfoMultiError is an error wrapping multiple validation errors
// returned by UpiPinSetInfo.ValidateAll() if the designated constraints
// aren't met.
type UpiPinSetInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiPinSetInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiPinSetInfoMultiError) AllErrors() []error { return m }

// UpiPinSetInfoValidationError is the validation error returned by
// UpiPinSetInfo.Validate if the designated constraints aren't met.
type UpiPinSetInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiPinSetInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiPinSetInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiPinSetInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiPinSetInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiPinSetInfoValidationError) ErrorName() string { return "UpiPinSetInfoValidationError" }

// Error satisfies the builtin error interface
func (e UpiPinSetInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiPinSetInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiPinSetInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiPinSetInfoValidationError{}
