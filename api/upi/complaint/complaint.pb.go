// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/complaint/complaint.proto

package complaint

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// denotes the complaint action for raising a complaint request
type RequestComplaintAction int32

const (
	RequestComplaintAction_REQUEST_COMPLAINT_ACTION_UNSPECIFIED RequestComplaintAction = 0
	// complaint action to raise a complain
	RequestComplaintAction_COMPLAINT_RAISED RequestComplaintAction = 1
)

// Enum value maps for RequestComplaintAction.
var (
	RequestComplaintAction_name = map[int32]string{
		0: "REQUEST_COMPLAINT_ACTION_UNSPECIFIED",
		1: "COMPLAINT_RAISED",
	}
	RequestComplaintAction_value = map[string]int32{
		"REQUEST_COMPLAINT_ACTION_UNSPECIFIED": 0,
		"COMPLAINT_RAISED":                     1,
	}
)

func (x RequestComplaintAction) Enum() *RequestComplaintAction {
	p := new(RequestComplaintAction)
	*p = x
	return p
}

func (x RequestComplaintAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestComplaintAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_complaint_complaint_proto_enumTypes[0].Descriptor()
}

func (RequestComplaintAction) Type() protoreflect.EnumType {
	return &file_api_upi_complaint_complaint_proto_enumTypes[0]
}

func (x RequestComplaintAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestComplaintAction.Descriptor instead.
func (RequestComplaintAction) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_complaint_complaint_proto_rawDescGZIP(), []int{0}
}

// denotes the complaint reason for raising a complaint request
type RequestComplaintReason int32

const (
	RequestComplaintReason_REQUEST_COMPLAINT_REASON_UNSPECIFIED RequestComplaintReason = 0
	// Beneficiary account is not credited for a pending / timeout transaction
	RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_PENDING_TRANSACTION RequestComplaintReason = 1
	// Goods/services are not provided
	RequestComplaintReason_GOODS_NOT_PROVIDED RequestComplaintReason = 2
	// Credit not processed for cancelled or returned goods and services
	RequestComplaintReason_CREDIT_NOT_PROCESSED_FOR_CANCELLED_GOODS RequestComplaintReason = 3
	// account debited but transaction confirmation not received by merchant
	RequestComplaintReason_TRANSACTION_CONFIRMATION_NOT_RECEIVED RequestComplaintReason = 4
	// Paid by alternate means / Duplicate payment
	RequestComplaintReason_DUPLICATE_PAYMENT RequestComplaintReason = 5
	// Customer account not credited back for failed merchant transaction
	RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_MERCHANT_TRANSACTION RequestComplaintReason = 6
	// Customer account not credited back for failed P2P transaction
	RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_P2P_TRANSACTION RequestComplaintReason = 7
	// Beneficiary account is not credited for successful pay transaction
	RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_SUCCESSFUL_TRANSACTION RequestComplaintReason = 8
	// Customer account not credited back for declined transaction
	RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_DECLINED_TRANSACTION RequestComplaintReason = 9
	// Customer account has not yet reversed for a declined pay transaction
	RequestComplaintReason_ACCOUNT_NOT_REVERSED_FOR_DECLINED_TRANSACTION RequestComplaintReason = 10
)

// Enum value maps for RequestComplaintReason.
var (
	RequestComplaintReason_name = map[int32]string{
		0:  "REQUEST_COMPLAINT_REASON_UNSPECIFIED",
		1:  "ACCOUNT_NOT_CREDITED_FOR_PENDING_TRANSACTION",
		2:  "GOODS_NOT_PROVIDED",
		3:  "CREDIT_NOT_PROCESSED_FOR_CANCELLED_GOODS",
		4:  "TRANSACTION_CONFIRMATION_NOT_RECEIVED",
		5:  "DUPLICATE_PAYMENT",
		6:  "ACCOUNT_NOT_CREDITED_FOR_MERCHANT_TRANSACTION",
		7:  "ACCOUNT_NOT_CREDITED_FOR_P2P_TRANSACTION",
		8:  "ACCOUNT_NOT_CREDITED_FOR_SUCCESSFUL_TRANSACTION",
		9:  "ACCOUNT_NOT_CREDITED_FOR_DECLINED_TRANSACTION",
		10: "ACCOUNT_NOT_REVERSED_FOR_DECLINED_TRANSACTION",
	}
	RequestComplaintReason_value = map[string]int32{
		"REQUEST_COMPLAINT_REASON_UNSPECIFIED":            0,
		"ACCOUNT_NOT_CREDITED_FOR_PENDING_TRANSACTION":    1,
		"GOODS_NOT_PROVIDED":                              2,
		"CREDIT_NOT_PROCESSED_FOR_CANCELLED_GOODS":        3,
		"TRANSACTION_CONFIRMATION_NOT_RECEIVED":           4,
		"DUPLICATE_PAYMENT":                               5,
		"ACCOUNT_NOT_CREDITED_FOR_MERCHANT_TRANSACTION":   6,
		"ACCOUNT_NOT_CREDITED_FOR_P2P_TRANSACTION":        7,
		"ACCOUNT_NOT_CREDITED_FOR_SUCCESSFUL_TRANSACTION": 8,
		"ACCOUNT_NOT_CREDITED_FOR_DECLINED_TRANSACTION":   9,
		"ACCOUNT_NOT_REVERSED_FOR_DECLINED_TRANSACTION":   10,
	}
)

func (x RequestComplaintReason) Enum() *RequestComplaintReason {
	p := new(RequestComplaintReason)
	*p = x
	return p
}

func (x RequestComplaintReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestComplaintReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_complaint_complaint_proto_enumTypes[1].Descriptor()
}

func (RequestComplaintReason) Type() protoreflect.EnumType {
	return &file_api_upi_complaint_complaint_proto_enumTypes[1]
}

func (x RequestComplaintReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestComplaintReason.Descriptor instead.
func (RequestComplaintReason) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_complaint_complaint_proto_rawDescGZIP(), []int{1}
}

// denotes the complaint action received in complaint response
type ResponseComplaintAction int32

const (
	ResponseComplaintAction_RESPONSE_COMPLAINT_ACTION_UNSPECIFIED ResponseComplaintAction = 0
	// refund reversal confirmation
	ResponseComplaintAction_REFUND_REVERSAL_CONFIRMATION        ResponseComplaintAction = 1
	ResponseComplaintAction_CHARGEBACK_ACCEPTANCE               ResponseComplaintAction = 2
	ResponseComplaintAction_ARBITRATION_ACCEPTANCE              ResponseComplaintAction = 3
	ResponseComplaintAction_ARBITRATION_CONTINUATION            ResponseComplaintAction = 4
	ResponseComplaintAction_ARBITRATION_VERDICT                 ResponseComplaintAction = 5
	ResponseComplaintAction_ARBITRATION_WITHDRAWN               ResponseComplaintAction = 6
	ResponseComplaintAction_PRE_ARBITRATION_ACCEPTANCE          ResponseComplaintAction = 7
	ResponseComplaintAction_ARBITRATION_RAISE                   ResponseComplaintAction = 8
	ResponseComplaintAction_CHARGEBACK_RAISE                    ResponseComplaintAction = 9
	ResponseComplaintAction_CREDIT_ADJUSTMENT                   ResponseComplaintAction = 10
	ResponseComplaintAction_DEBIT_REVERSAL_CONFIRMATION         ResponseComplaintAction = 11
	ResponseComplaintAction_DIFFERED_CHARGEBACK_ACCEPTANCE      ResponseComplaintAction = 12
	ResponseComplaintAction_DIFFERED_PRE_ARBITRATION_ACCEPTANCE ResponseComplaintAction = 13
	ResponseComplaintAction_DIFFERED_ARBITRATION_RAISE          ResponseComplaintAction = 14
	ResponseComplaintAction_DIFFERED_CHARGEBACK_RAISE           ResponseComplaintAction = 15
	ResponseComplaintAction_FRAUD_CHARGEBACK_RAISE              ResponseComplaintAction = 16
	ResponseComplaintAction_FRAUD_CHARGEBACK_ACCEPT             ResponseComplaintAction = 17
	ResponseComplaintAction_FRAUD_CHARGEBACK_REPRESENTMENT      ResponseComplaintAction = 18
	ResponseComplaintAction_DIFFERED_PRE_ARBITRATION_RAISE      ResponseComplaintAction = 19
	ResponseComplaintAction_DIFFERED_PRE_ARBITRATION_DECLINED   ResponseComplaintAction = 20
	ResponseComplaintAction_DIFFERED_RE_PRESENTMENT_RAISE       ResponseComplaintAction = 21
	ResponseComplaintAction_MANUAL_ADJUSTMENT                   ResponseComplaintAction = 22
	ResponseComplaintAction_PRE_ARBITRATION_RAISE               ResponseComplaintAction = 23
	ResponseComplaintAction_PRE_ARBITRATION_DECLINED            ResponseComplaintAction = 24
	ResponseComplaintAction_RESPONSE_TO_COMPLAINT               ResponseComplaintAction = 25
	ResponseComplaintAction_RE_PRESENTMENT_RAISE                ResponseComplaintAction = 26
	ResponseComplaintAction_RET                                 ResponseComplaintAction = 27
	ResponseComplaintAction_TCC                                 ResponseComplaintAction = 28
	ResponseComplaintAction_WRONG_CREDIT_CHARGEBACK_ACCEPTANCE  ResponseComplaintAction = 29
	ResponseComplaintAction_WRONG_CREDIT_REPRESENTMENT          ResponseComplaintAction = 30
	ResponseComplaintAction_WRONG_CREDIT_CHARGEBACK_RAISE       ResponseComplaintAction = 31
)

// Enum value maps for ResponseComplaintAction.
var (
	ResponseComplaintAction_name = map[int32]string{
		0:  "RESPONSE_COMPLAINT_ACTION_UNSPECIFIED",
		1:  "REFUND_REVERSAL_CONFIRMATION",
		2:  "CHARGEBACK_ACCEPTANCE",
		3:  "ARBITRATION_ACCEPTANCE",
		4:  "ARBITRATION_CONTINUATION",
		5:  "ARBITRATION_VERDICT",
		6:  "ARBITRATION_WITHDRAWN",
		7:  "PRE_ARBITRATION_ACCEPTANCE",
		8:  "ARBITRATION_RAISE",
		9:  "CHARGEBACK_RAISE",
		10: "CREDIT_ADJUSTMENT",
		11: "DEBIT_REVERSAL_CONFIRMATION",
		12: "DIFFERED_CHARGEBACK_ACCEPTANCE",
		13: "DIFFERED_PRE_ARBITRATION_ACCEPTANCE",
		14: "DIFFERED_ARBITRATION_RAISE",
		15: "DIFFERED_CHARGEBACK_RAISE",
		16: "FRAUD_CHARGEBACK_RAISE",
		17: "FRAUD_CHARGEBACK_ACCEPT",
		18: "FRAUD_CHARGEBACK_REPRESENTMENT",
		19: "DIFFERED_PRE_ARBITRATION_RAISE",
		20: "DIFFERED_PRE_ARBITRATION_DECLINED",
		21: "DIFFERED_RE_PRESENTMENT_RAISE",
		22: "MANUAL_ADJUSTMENT",
		23: "PRE_ARBITRATION_RAISE",
		24: "PRE_ARBITRATION_DECLINED",
		25: "RESPONSE_TO_COMPLAINT",
		26: "RE_PRESENTMENT_RAISE",
		27: "RET",
		28: "TCC",
		29: "WRONG_CREDIT_CHARGEBACK_ACCEPTANCE",
		30: "WRONG_CREDIT_REPRESENTMENT",
		31: "WRONG_CREDIT_CHARGEBACK_RAISE",
	}
	ResponseComplaintAction_value = map[string]int32{
		"RESPONSE_COMPLAINT_ACTION_UNSPECIFIED": 0,
		"REFUND_REVERSAL_CONFIRMATION":          1,
		"CHARGEBACK_ACCEPTANCE":                 2,
		"ARBITRATION_ACCEPTANCE":                3,
		"ARBITRATION_CONTINUATION":              4,
		"ARBITRATION_VERDICT":                   5,
		"ARBITRATION_WITHDRAWN":                 6,
		"PRE_ARBITRATION_ACCEPTANCE":            7,
		"ARBITRATION_RAISE":                     8,
		"CHARGEBACK_RAISE":                      9,
		"CREDIT_ADJUSTMENT":                     10,
		"DEBIT_REVERSAL_CONFIRMATION":           11,
		"DIFFERED_CHARGEBACK_ACCEPTANCE":        12,
		"DIFFERED_PRE_ARBITRATION_ACCEPTANCE":   13,
		"DIFFERED_ARBITRATION_RAISE":            14,
		"DIFFERED_CHARGEBACK_RAISE":             15,
		"FRAUD_CHARGEBACK_RAISE":                16,
		"FRAUD_CHARGEBACK_ACCEPT":               17,
		"FRAUD_CHARGEBACK_REPRESENTMENT":        18,
		"DIFFERED_PRE_ARBITRATION_RAISE":        19,
		"DIFFERED_PRE_ARBITRATION_DECLINED":     20,
		"DIFFERED_RE_PRESENTMENT_RAISE":         21,
		"MANUAL_ADJUSTMENT":                     22,
		"PRE_ARBITRATION_RAISE":                 23,
		"PRE_ARBITRATION_DECLINED":              24,
		"RESPONSE_TO_COMPLAINT":                 25,
		"RE_PRESENTMENT_RAISE":                  26,
		"RET":                                   27,
		"TCC":                                   28,
		"WRONG_CREDIT_CHARGEBACK_ACCEPTANCE":    29,
		"WRONG_CREDIT_REPRESENTMENT":            30,
		"WRONG_CREDIT_CHARGEBACK_RAISE":         31,
	}
)

func (x ResponseComplaintAction) Enum() *ResponseComplaintAction {
	p := new(ResponseComplaintAction)
	*p = x
	return p
}

func (x ResponseComplaintAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResponseComplaintAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_complaint_complaint_proto_enumTypes[2].Descriptor()
}

func (ResponseComplaintAction) Type() protoreflect.EnumType {
	return &file_api_upi_complaint_complaint_proto_enumTypes[2]
}

func (x ResponseComplaintAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResponseComplaintAction.Descriptor instead.
func (ResponseComplaintAction) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_complaint_complaint_proto_rawDescGZIP(), []int{2}
}

// denotes the complaint reason received in complaint response
type ResponseComplaintReason int32

const (
	ResponseComplaintReason_RESPONSE_COMPLAINT_REASON_UNSPECIFIED ResponseComplaintReason = 0
	// customer account has been credited
	ResponseComplaintReason_CUSTOMER_ACCOUNT_CREDITED ResponseComplaintReason = 1
)

// Enum value maps for ResponseComplaintReason.
var (
	ResponseComplaintReason_name = map[int32]string{
		0: "RESPONSE_COMPLAINT_REASON_UNSPECIFIED",
		1: "CUSTOMER_ACCOUNT_CREDITED",
	}
	ResponseComplaintReason_value = map[string]int32{
		"RESPONSE_COMPLAINT_REASON_UNSPECIFIED": 0,
		"CUSTOMER_ACCOUNT_CREDITED":             1,
	}
)

func (x ResponseComplaintReason) Enum() *ResponseComplaintReason {
	p := new(ResponseComplaintReason)
	*p = x
	return p
}

func (x ResponseComplaintReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResponseComplaintReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_complaint_complaint_proto_enumTypes[3].Descriptor()
}

func (ResponseComplaintReason) Type() protoreflect.EnumType {
	return &file_api_upi_complaint_complaint_proto_enumTypes[3]
}

func (x ResponseComplaintReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResponseComplaintReason.Descriptor instead.
func (ResponseComplaintReason) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_complaint_complaint_proto_rawDescGZIP(), []int{3}
}

// ComplaintDisputeState is the state of the raised dispute
type ComplaintDisputeState int32

const (
	ComplaintDisputeState_COMPLAINT_DISPUTE_STATE_UNSPECIFIED ComplaintDisputeState = 0
	// request to raise dispute is initiated and is IN_PROGRESS
	ComplaintDisputeState_COMPLAINT_DISPUTE_STATE_RAISE_IN_PROGRESS ComplaintDisputeState = 1
	// request to raise dispute failed
	ComplaintDisputeState_COMPLAINT_DISPUTE_STATE_RAISE_FAILED ComplaintDisputeState = 2
	// current status of raised dispute is in progress
	ComplaintDisputeState_COMPLAINT_DISPUTE_STATE_IN_PROGRESS ComplaintDisputeState = 3
	// raised dispute is resolved
	ComplaintDisputeState_COMPLAINT_DISPUTE_STATE_RESOLVED ComplaintDisputeState = 4
)

// Enum value maps for ComplaintDisputeState.
var (
	ComplaintDisputeState_name = map[int32]string{
		0: "COMPLAINT_DISPUTE_STATE_UNSPECIFIED",
		1: "COMPLAINT_DISPUTE_STATE_RAISE_IN_PROGRESS",
		2: "COMPLAINT_DISPUTE_STATE_RAISE_FAILED",
		3: "COMPLAINT_DISPUTE_STATE_IN_PROGRESS",
		4: "COMPLAINT_DISPUTE_STATE_RESOLVED",
	}
	ComplaintDisputeState_value = map[string]int32{
		"COMPLAINT_DISPUTE_STATE_UNSPECIFIED":       0,
		"COMPLAINT_DISPUTE_STATE_RAISE_IN_PROGRESS": 1,
		"COMPLAINT_DISPUTE_STATE_RAISE_FAILED":      2,
		"COMPLAINT_DISPUTE_STATE_IN_PROGRESS":       3,
		"COMPLAINT_DISPUTE_STATE_RESOLVED":          4,
	}
)

func (x ComplaintDisputeState) Enum() *ComplaintDisputeState {
	p := new(ComplaintDisputeState)
	*p = x
	return p
}

func (x ComplaintDisputeState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ComplaintDisputeState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_complaint_complaint_proto_enumTypes[4].Descriptor()
}

func (ComplaintDisputeState) Type() protoreflect.EnumType {
	return &file_api_upi_complaint_complaint_proto_enumTypes[4]
}

func (x ComplaintDisputeState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ComplaintDisputeState.Descriptor instead.
func (ComplaintDisputeState) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_complaint_complaint_proto_rawDescGZIP(), []int{4}
}

type Complaint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// defines the complaint action
	ComplaintAction RequestComplaintAction `protobuf:"varint,1,opt,name=complaint_action,json=complaintAction,proto3,enum=upi.complaint.RequestComplaintAction" json:"complaint_action,omitempty"`
	// defines the complaint reason
	ComplaintReason RequestComplaintReason `protobuf:"varint,2,opt,name=complaint_reason,json=complaintReason,proto3,enum=upi.complaint.RequestComplaintReason" json:"complaint_reason,omitempty"`
	// defines the adjustment amount
	ComplaintAmount *money.Money `protobuf:"bytes,3,opt,name=complaint_amount,json=complaintAmount,proto3" json:"complaint_amount,omitempty"`
}

func (x *Complaint) Reset() {
	*x = Complaint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_complaint_complaint_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Complaint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Complaint) ProtoMessage() {}

func (x *Complaint) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_complaint_complaint_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Complaint.ProtoReflect.Descriptor instead.
func (*Complaint) Descriptor() ([]byte, []int) {
	return file_api_upi_complaint_complaint_proto_rawDescGZIP(), []int{0}
}

func (x *Complaint) GetComplaintAction() RequestComplaintAction {
	if x != nil {
		return x.ComplaintAction
	}
	return RequestComplaintAction_REQUEST_COMPLAINT_ACTION_UNSPECIFIED
}

func (x *Complaint) GetComplaintReason() RequestComplaintReason {
	if x != nil {
		return x.ComplaintReason
	}
	return RequestComplaintReason_REQUEST_COMPLAINT_REASON_UNSPECIFIED
}

func (x *Complaint) GetComplaintAmount() *money.Money {
	if x != nil {
		return x.ComplaintAmount
	}
	return nil
}

var File_api_upi_complaint_complaint_proto protoreflect.FileDescriptor

var file_api_upi_complaint_complaint_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x75, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xee, 0x01, 0x0a, 0x09,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x10, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x10, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0f, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3d, 0x0a,
	0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x2a, 0x58, 0x0a, 0x16,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x24, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x41,
	0x49, 0x53, 0x45, 0x44, 0x10, 0x01, 0x2a, 0xf8, 0x03, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x28, 0x0a, 0x24, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x47, 0x4f, 0x4f, 0x44, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49,
	0x44, 0x45, 0x44, 0x10, 0x02, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x47, 0x4f, 0x4f, 0x44,
	0x53, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x10, 0x04, 0x12, 0x15,
	0x0a, 0x11, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x31, 0x0a, 0x2d, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x45, 0x44,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50, 0x32, 0x50, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x33, 0x0a, 0x2f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x45, 0x44, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x31, 0x0a, 0x2d, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x44,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x31,
	0x0a, 0x2d, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45,
	0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49,
	0x4e, 0x45, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x0a, 0x2a, 0xcd, 0x07, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a,
	0x25, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41,
	0x49, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10,
	0x03, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x49, 0x4e, 0x55, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12,
	0x17, 0x0a, 0x13, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x56,
	0x45, 0x52, 0x44, 0x49, 0x43, 0x54, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x52, 0x42, 0x49,
	0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x44, 0x52, 0x41, 0x57,
	0x4e, 0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43,
	0x45, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x08, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x09,
	0x12, 0x15, 0x0a, 0x11, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x41, 0x44, 0x4a, 0x55, 0x53,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x0a, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x42, 0x49, 0x54,
	0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52,
	0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x49, 0x46, 0x46,
	0x45, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x0c, 0x12, 0x27, 0x0a, 0x23,
	0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42,
	0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x0d, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45,
	0x44, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41,
	0x49, 0x53, 0x45, 0x10, 0x0e, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45,
	0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x41, 0x49,
	0x53, 0x45, 0x10, 0x0f, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x52, 0x41, 0x55, 0x44, 0x5f, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x10,
	0x12, 0x1b, 0x0a, 0x17, 0x46, 0x52, 0x41, 0x55, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x10, 0x11, 0x12, 0x22, 0x0a,
	0x1e, 0x46, 0x52, 0x41, 0x55, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x52, 0x45, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x12, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x50, 0x52,
	0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41,
	0x49, 0x53, 0x45, 0x10, 0x13, 0x12, 0x25, 0x0a, 0x21, 0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45,
	0x44, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x14, 0x12, 0x21, 0x0a, 0x1d,
	0x44, 0x49, 0x46, 0x46, 0x45, 0x52, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x53,
	0x45, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10, 0x15, 0x12,
	0x15, 0x0a, 0x11, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x44, 0x4a, 0x55, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x16, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52,
	0x42, 0x49, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10,
	0x17, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x52, 0x42, 0x49, 0x54, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x18, 0x12,
	0x19, 0x0a, 0x15, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x54, 0x4f, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x10, 0x19, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45,
	0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x41, 0x49,
	0x53, 0x45, 0x10, 0x1a, 0x12, 0x07, 0x0a, 0x03, 0x52, 0x45, 0x54, 0x10, 0x1b, 0x12, 0x07, 0x0a,
	0x03, 0x54, 0x43, 0x43, 0x10, 0x1c, 0x12, 0x26, 0x0a, 0x22, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x1d, 0x12, 0x1e,
	0x0a, 0x1a, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52,
	0x45, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1e, 0x12, 0x21,
	0x0a, 0x1d, 0x57, 0x52, 0x4f, 0x4e, 0x47, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43,
	0x48, 0x41, 0x52, 0x47, 0x45, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x10,
	0x1f, 0x2a, 0x63, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x25,
	0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49,
	0x4e, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x45, 0x44, 0x10, 0x01, 0x2a, 0xe8, 0x01, 0x0a, 0x15, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49,
	0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e,
	0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x56, 0x45, 0x44, 0x10,
	0x04, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5a, 0x28, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_complaint_complaint_proto_rawDescOnce sync.Once
	file_api_upi_complaint_complaint_proto_rawDescData = file_api_upi_complaint_complaint_proto_rawDesc
)

func file_api_upi_complaint_complaint_proto_rawDescGZIP() []byte {
	file_api_upi_complaint_complaint_proto_rawDescOnce.Do(func() {
		file_api_upi_complaint_complaint_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_complaint_complaint_proto_rawDescData)
	})
	return file_api_upi_complaint_complaint_proto_rawDescData
}

var file_api_upi_complaint_complaint_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_upi_complaint_complaint_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_upi_complaint_complaint_proto_goTypes = []interface{}{
	(RequestComplaintAction)(0),  // 0: upi.complaint.RequestComplaintAction
	(RequestComplaintReason)(0),  // 1: upi.complaint.RequestComplaintReason
	(ResponseComplaintAction)(0), // 2: upi.complaint.ResponseComplaintAction
	(ResponseComplaintReason)(0), // 3: upi.complaint.ResponseComplaintReason
	(ComplaintDisputeState)(0),   // 4: upi.complaint.ComplaintDisputeState
	(*Complaint)(nil),            // 5: upi.complaint.Complaint
	(*money.Money)(nil),          // 6: google.type.Money
}
var file_api_upi_complaint_complaint_proto_depIdxs = []int32{
	0, // 0: upi.complaint.Complaint.complaint_action:type_name -> upi.complaint.RequestComplaintAction
	1, // 1: upi.complaint.Complaint.complaint_reason:type_name -> upi.complaint.RequestComplaintReason
	6, // 2: upi.complaint.Complaint.complaint_amount:type_name -> google.type.Money
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_upi_complaint_complaint_proto_init() }
func file_api_upi_complaint_complaint_proto_init() {
	if File_api_upi_complaint_complaint_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_complaint_complaint_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Complaint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_complaint_complaint_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_complaint_complaint_proto_goTypes,
		DependencyIndexes: file_api_upi_complaint_complaint_proto_depIdxs,
		EnumInfos:         file_api_upi_complaint_complaint_proto_enumTypes,
		MessageInfos:      file_api_upi_complaint_complaint_proto_msgTypes,
	}.Build()
	File_api_upi_complaint_complaint_proto = out.File
	file_api_upi_complaint_complaint_proto_rawDesc = nil
	file_api_upi_complaint_complaint_proto_goTypes = nil
	file_api_upi_complaint_complaint_proto_depIdxs = nil
}
