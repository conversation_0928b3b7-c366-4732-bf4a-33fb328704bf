package upi

import (
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/vendors"
)

func ConvertToPIMerchantDetails(md *MerchantDetails, mcc string) *piPb.Upi_MerchantDetails {
	if mcc == vendors.UPIPersonMCCCode || mcc == "" {
		return nil
	}

	res := &piPb.Upi_MerchantDetails{
		Mcc:                mcc,
		MerchantId:         md.GetMerchantId(),
		MerchantStoreId:    md.GetMerchantStoreId(),
		MerchantTerminalId: md.GetMerchantTerminalId(),
		BrandName:          md.GetBrandName(),
		LegalName:          md.GetLegalName(),
		FranchiseName:      md.GetFranchiseName(),
		SubCode:            md.GetSubCode(),
	}

	if md.GetGenre() != MerchantGenre_MERCHANT_GENRE_UNSPECIFIED {
		res.MerchantGenre = md.GetGenre().String()
	}

	if md.GetOwnershipType() != MerchantOwnershipType_MERCHANT_OWNERSHIP_UNSPECIFIED {
		res.OwnershipType = md.GetOwnershipType().String()
	}

	if md.GetOnboardingType() != MerchantOnboardingType_MERCHANT_ONBOARDING_TYPE_UNSPECIFIED {
		res.OnboardingType = md.GetOnboardingType().String()
	}

	if md.GetMerchantType() != MerchantType_MERCHANT_TYPE_UNSPECIFIED {
		res.MerchantType = md.GetMerchantType().String()
	}

	return res
}

func ParsePIMerchantDetails(md *piPb.Upi_MerchantDetails) *MerchantDetails {
	if md == nil {
		return nil
	}

	return &MerchantDetails{
		MerchantId:         md.GetMerchantId(),
		MerchantStoreId:    md.GetMerchantStoreId(),
		MerchantTerminalId: md.GetMerchantTerminalId(),
		Genre:              MerchantGenre(MerchantGenre_value[md.GetMerchantGenre()]),
		BrandName:          md.GetBrandName(),
		LegalName:          md.GetLegalName(),
		FranchiseName:      md.GetFranchiseName(),
		OwnershipType:      MerchantOwnershipType(MerchantOwnershipType_value[md.GetOwnershipType()]),
		OnboardingType:     MerchantOnboardingType(MerchantOnboardingType_value[md.GetOnboardingType()]),
		MerchantType:       MerchantType(MerchantType_value[md.GetMerchantType()]),
		SubCode:            md.GetSubCode(),
	}
}
