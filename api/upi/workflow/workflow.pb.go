// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/workflow/workflow.proto

package workflow

import (
	workflow "github.com/epifi/be-common/api/celestial/workflow"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActivateUpiLiteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *ActivateUpiLiteRequest) Reset() {
	*x = ActivateUpiLiteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivateUpiLiteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateUpiLiteRequest) ProtoMessage() {}

func (x *ActivateUpiLiteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateUpiLiteRequest.ProtoReflect.Descriptor instead.
func (*ActivateUpiLiteRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{0}
}

func (x *ActivateUpiLiteRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type DeactivateUpiLiteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *DeactivateUpiLiteRequest) Reset() {
	*x = DeactivateUpiLiteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeactivateUpiLiteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateUpiLiteRequest) ProtoMessage() {}

func (x *DeactivateUpiLiteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateUpiLiteRequest.ProtoReflect.Descriptor instead.
func (*DeactivateUpiLiteRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{1}
}

func (x *DeactivateUpiLiteRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type DeactivateZeroBalanceUpiLiteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *DeactivateZeroBalanceUpiLiteRequest) Reset() {
	*x = DeactivateZeroBalanceUpiLiteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeactivateZeroBalanceUpiLiteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateZeroBalanceUpiLiteRequest) ProtoMessage() {}

func (x *DeactivateZeroBalanceUpiLiteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateZeroBalanceUpiLiteRequest.ProtoReflect.Descriptor instead.
func (*DeactivateZeroBalanceUpiLiteRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{2}
}

func (x *DeactivateZeroBalanceUpiLiteRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type ActivateInternationalUpiPaymentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *ActivateInternationalUpiPaymentsRequest) Reset() {
	*x = ActivateInternationalUpiPaymentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivateInternationalUpiPaymentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateInternationalUpiPaymentsRequest) ProtoMessage() {}

func (x *ActivateInternationalUpiPaymentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateInternationalUpiPaymentsRequest.ProtoReflect.Descriptor instead.
func (*ActivateInternationalUpiPaymentsRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{3}
}

func (x *ActivateInternationalUpiPaymentsRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type DeactivateInternationalUpiPaymentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *DeactivateInternationalUpiPaymentsRequest) Reset() {
	*x = DeactivateInternationalUpiPaymentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeactivateInternationalUpiPaymentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateInternationalUpiPaymentsRequest) ProtoMessage() {}

func (x *DeactivateInternationalUpiPaymentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateInternationalUpiPaymentsRequest.ProtoReflect.Descriptor instead.
func (*DeactivateInternationalUpiPaymentsRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{4}
}

func (x *DeactivateInternationalUpiPaymentsRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type LinkUpiAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *LinkUpiAccountRequest) Reset() {
	*x = LinkUpiAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkUpiAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkUpiAccountRequest) ProtoMessage() {}

func (x *LinkUpiAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkUpiAccountRequest.ProtoReflect.Descriptor instead.
func (*LinkUpiAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{5}
}

func (x *LinkUpiAccountRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type DelinkUpiAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *DelinkUpiAccountRequest) Reset() {
	*x = DelinkUpiAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelinkUpiAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelinkUpiAccountRequest) ProtoMessage() {}

func (x *DelinkUpiAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelinkUpiAccountRequest.ProtoReflect.Descriptor instead.
func (*DelinkUpiAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{6}
}

func (x *DelinkUpiAccountRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type DelinkUpiAccountWithVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// workflow specific request payload
	Payload *anypb.Any `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *DelinkUpiAccountWithVendorRequest) Reset() {
	*x = DelinkUpiAccountWithVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelinkUpiAccountWithVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelinkUpiAccountWithVendorRequest) ProtoMessage() {}

func (x *DelinkUpiAccountWithVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelinkUpiAccountWithVendorRequest.ProtoReflect.Descriptor instead.
func (*DelinkUpiAccountWithVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{7}
}

func (x *DelinkUpiAccountWithVendorRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *DelinkUpiAccountWithVendorRequest) GetPayload() *anypb.Any {
	if x != nil {
		return x.Payload
	}
	return nil
}

type DelinkUpiAccountWithVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the workflows
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// response data returned by workflow
	// This might vary based on the workflow, hence using protobuf.Any as the field type
	Data *anypb.Any `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DelinkUpiAccountWithVendorResponse) Reset() {
	*x = DelinkUpiAccountWithVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelinkUpiAccountWithVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelinkUpiAccountWithVendorResponse) ProtoMessage() {}

func (x *DelinkUpiAccountWithVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelinkUpiAccountWithVendorResponse.ProtoReflect.Descriptor instead.
func (*DelinkUpiAccountWithVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{8}
}

func (x *DelinkUpiAccountWithVendorResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *DelinkUpiAccountWithVendorResponse) GetData() *anypb.Any {
	if x != nil {
		return x.Data
	}
	return nil
}

type LinkUpiNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *LinkUpiNumberRequest) Reset() {
	*x = LinkUpiNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkUpiNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkUpiNumberRequest) ProtoMessage() {}

func (x *LinkUpiNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkUpiNumberRequest.ProtoReflect.Descriptor instead.
func (*LinkUpiNumberRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{9}
}

func (x *LinkUpiNumberRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type DelinkUpiNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the workflows
	RequestHeader *workflow.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// workflow specific request payload
	Payload *anypb.Any `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *DelinkUpiNumberRequest) Reset() {
	*x = DelinkUpiNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelinkUpiNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelinkUpiNumberRequest) ProtoMessage() {}

func (x *DelinkUpiNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelinkUpiNumberRequest.ProtoReflect.Descriptor instead.
func (*DelinkUpiNumberRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{10}
}

func (x *DelinkUpiNumberRequest) GetRequestHeader() *workflow.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *DelinkUpiNumberRequest) GetPayload() *anypb.Any {
	if x != nil {
		return x.Payload
	}
	return nil
}

type DelinkUpiNumberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the workflows
	ResponseHeader *workflow.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// response data returned by workflow
	// This might vary based on the workflow, hence using protobuf.Any as the field type
	Data *anypb.Any `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DelinkUpiNumberResponse) Reset() {
	*x = DelinkUpiNumberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_workflow_workflow_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelinkUpiNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelinkUpiNumberResponse) ProtoMessage() {}

func (x *DelinkUpiNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_workflow_workflow_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelinkUpiNumberResponse.ProtoReflect.Descriptor instead.
func (*DelinkUpiNumberResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_workflow_workflow_proto_rawDescGZIP(), []int{11}
}

func (x *DelinkUpiNumberResponse) GetResponseHeader() *workflow.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *DelinkUpiNumberResponse) GetData() *anypb.Any {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_api_upi_workflow_workflow_proto protoreflect.FileDescriptor

var file_api_upi_workflow_workflow_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0c, 0x75, 0x70, 0x69, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x1a,
	0x23, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x62, 0x0a, 0x16, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4c, 0x69,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x22, 0x64, 0x0a, 0x18, 0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6f, 0x0a, 0x23, 0x44, 0x65, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5a, 0x65, 0x72, 0x6f, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x73, 0x0a, 0x27, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x55, 0x70, 0x69, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22,
	0x75, 0x0a, 0x29, 0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x70, 0x69, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c,
	0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x61, 0x0a, 0x15, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x70,
	0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x63, 0x0a, 0x17, 0x44, 0x65, 0x6c,
	0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x9d,
	0x01, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x9b,
	0x01, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x28, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x60, 0x0a, 0x14,
	0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x92,
	0x01, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x22, 0x90, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70,
	0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73,
	0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70,
	0x69, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_upi_workflow_workflow_proto_rawDescOnce sync.Once
	file_api_upi_workflow_workflow_proto_rawDescData = file_api_upi_workflow_workflow_proto_rawDesc
)

func file_api_upi_workflow_workflow_proto_rawDescGZIP() []byte {
	file_api_upi_workflow_workflow_proto_rawDescOnce.Do(func() {
		file_api_upi_workflow_workflow_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_workflow_workflow_proto_rawDescData)
	})
	return file_api_upi_workflow_workflow_proto_rawDescData
}

var file_api_upi_workflow_workflow_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_upi_workflow_workflow_proto_goTypes = []interface{}{
	(*ActivateUpiLiteRequest)(nil),                    // 0: upi.workflow.ActivateUpiLiteRequest
	(*DeactivateUpiLiteRequest)(nil),                  // 1: upi.workflow.DeactivateUpiLiteRequest
	(*DeactivateZeroBalanceUpiLiteRequest)(nil),       // 2: upi.workflow.DeactivateZeroBalanceUpiLiteRequest
	(*ActivateInternationalUpiPaymentsRequest)(nil),   // 3: upi.workflow.ActivateInternationalUpiPaymentsRequest
	(*DeactivateInternationalUpiPaymentsRequest)(nil), // 4: upi.workflow.DeactivateInternationalUpiPaymentsRequest
	(*LinkUpiAccountRequest)(nil),                     // 5: upi.workflow.LinkUpiAccountRequest
	(*DelinkUpiAccountRequest)(nil),                   // 6: upi.workflow.DelinkUpiAccountRequest
	(*DelinkUpiAccountWithVendorRequest)(nil),         // 7: upi.workflow.DelinkUpiAccountWithVendorRequest
	(*DelinkUpiAccountWithVendorResponse)(nil),        // 8: upi.workflow.DelinkUpiAccountWithVendorResponse
	(*LinkUpiNumberRequest)(nil),                      // 9: upi.workflow.LinkUpiNumberRequest
	(*DelinkUpiNumberRequest)(nil),                    // 10: upi.workflow.DelinkUpiNumberRequest
	(*DelinkUpiNumberResponse)(nil),                   // 11: upi.workflow.DelinkUpiNumberResponse
	(*workflow.RequestHeader)(nil),                    // 12: celestial.workflow.RequestHeader
	(*anypb.Any)(nil),                                 // 13: google.protobuf.Any
	(*workflow.ResponseHeader)(nil),                   // 14: celestial.workflow.ResponseHeader
}
var file_api_upi_workflow_workflow_proto_depIdxs = []int32{
	12, // 0: upi.workflow.ActivateUpiLiteRequest.request_header:type_name -> celestial.workflow.RequestHeader
	12, // 1: upi.workflow.DeactivateUpiLiteRequest.request_header:type_name -> celestial.workflow.RequestHeader
	12, // 2: upi.workflow.DeactivateZeroBalanceUpiLiteRequest.request_header:type_name -> celestial.workflow.RequestHeader
	12, // 3: upi.workflow.ActivateInternationalUpiPaymentsRequest.request_header:type_name -> celestial.workflow.RequestHeader
	12, // 4: upi.workflow.DeactivateInternationalUpiPaymentsRequest.request_header:type_name -> celestial.workflow.RequestHeader
	12, // 5: upi.workflow.LinkUpiAccountRequest.request_header:type_name -> celestial.workflow.RequestHeader
	12, // 6: upi.workflow.DelinkUpiAccountRequest.request_header:type_name -> celestial.workflow.RequestHeader
	12, // 7: upi.workflow.DelinkUpiAccountWithVendorRequest.request_header:type_name -> celestial.workflow.RequestHeader
	13, // 8: upi.workflow.DelinkUpiAccountWithVendorRequest.payload:type_name -> google.protobuf.Any
	14, // 9: upi.workflow.DelinkUpiAccountWithVendorResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	13, // 10: upi.workflow.DelinkUpiAccountWithVendorResponse.data:type_name -> google.protobuf.Any
	12, // 11: upi.workflow.LinkUpiNumberRequest.request_header:type_name -> celestial.workflow.RequestHeader
	12, // 12: upi.workflow.DelinkUpiNumberRequest.request_header:type_name -> celestial.workflow.RequestHeader
	13, // 13: upi.workflow.DelinkUpiNumberRequest.payload:type_name -> google.protobuf.Any
	14, // 14: upi.workflow.DelinkUpiNumberResponse.response_header:type_name -> celestial.workflow.ResponseHeader
	13, // 15: upi.workflow.DelinkUpiNumberResponse.data:type_name -> google.protobuf.Any
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_api_upi_workflow_workflow_proto_init() }
func file_api_upi_workflow_workflow_proto_init() {
	if File_api_upi_workflow_workflow_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_workflow_workflow_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivateUpiLiteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeactivateUpiLiteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeactivateZeroBalanceUpiLiteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivateInternationalUpiPaymentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeactivateInternationalUpiPaymentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LinkUpiAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelinkUpiAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelinkUpiAccountWithVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelinkUpiAccountWithVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LinkUpiNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelinkUpiNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_workflow_workflow_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelinkUpiNumberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_workflow_workflow_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_workflow_workflow_proto_goTypes,
		DependencyIndexes: file_api_upi_workflow_workflow_proto_depIdxs,
		MessageInfos:      file_api_upi_workflow_workflow_proto_msgTypes,
	}.Build()
	File_api_upi_workflow_workflow_proto = out.File
	file_api_upi_workflow_workflow_proto_rawDesc = nil
	file_api_upi_workflow_workflow_proto_goTypes = nil
	file_api_upi_workflow_workflow_proto_depIdxs = nil
}
