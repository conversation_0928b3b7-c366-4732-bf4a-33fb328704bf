// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/workflow/workflow.proto

package workflow

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ActivateUpiLiteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivateUpiLiteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivateUpiLiteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivateUpiLiteRequestMultiError, or nil if none found.
func (m *ActivateUpiLiteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivateUpiLiteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivateUpiLiteRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivateUpiLiteRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivateUpiLiteRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActivateUpiLiteRequestMultiError(errors)
	}

	return nil
}

// ActivateUpiLiteRequestMultiError is an error wrapping multiple validation
// errors returned by ActivateUpiLiteRequest.ValidateAll() if the designated
// constraints aren't met.
type ActivateUpiLiteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivateUpiLiteRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivateUpiLiteRequestMultiError) AllErrors() []error { return m }

// ActivateUpiLiteRequestValidationError is the validation error returned by
// ActivateUpiLiteRequest.Validate if the designated constraints aren't met.
type ActivateUpiLiteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivateUpiLiteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivateUpiLiteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivateUpiLiteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivateUpiLiteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivateUpiLiteRequestValidationError) ErrorName() string {
	return "ActivateUpiLiteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ActivateUpiLiteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivateUpiLiteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivateUpiLiteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivateUpiLiteRequestValidationError{}

// Validate checks the field values on DeactivateUpiLiteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivateUpiLiteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateUpiLiteRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivateUpiLiteRequestMultiError, or nil if none found.
func (m *DeactivateUpiLiteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateUpiLiteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateUpiLiteRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateUpiLiteRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateUpiLiteRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeactivateUpiLiteRequestMultiError(errors)
	}

	return nil
}

// DeactivateUpiLiteRequestMultiError is an error wrapping multiple validation
// errors returned by DeactivateUpiLiteRequest.ValidateAll() if the designated
// constraints aren't met.
type DeactivateUpiLiteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateUpiLiteRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateUpiLiteRequestMultiError) AllErrors() []error { return m }

// DeactivateUpiLiteRequestValidationError is the validation error returned by
// DeactivateUpiLiteRequest.Validate if the designated constraints aren't met.
type DeactivateUpiLiteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateUpiLiteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateUpiLiteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateUpiLiteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateUpiLiteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateUpiLiteRequestValidationError) ErrorName() string {
	return "DeactivateUpiLiteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateUpiLiteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateUpiLiteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateUpiLiteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateUpiLiteRequestValidationError{}

// Validate checks the field values on DeactivateZeroBalanceUpiLiteRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeactivateZeroBalanceUpiLiteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateZeroBalanceUpiLiteRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeactivateZeroBalanceUpiLiteRequestMultiError, or nil if none found.
func (m *DeactivateZeroBalanceUpiLiteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateZeroBalanceUpiLiteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateZeroBalanceUpiLiteRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateZeroBalanceUpiLiteRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateZeroBalanceUpiLiteRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeactivateZeroBalanceUpiLiteRequestMultiError(errors)
	}

	return nil
}

// DeactivateZeroBalanceUpiLiteRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeactivateZeroBalanceUpiLiteRequest.ValidateAll() if the designated
// constraints aren't met.
type DeactivateZeroBalanceUpiLiteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateZeroBalanceUpiLiteRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateZeroBalanceUpiLiteRequestMultiError) AllErrors() []error { return m }

// DeactivateZeroBalanceUpiLiteRequestValidationError is the validation error
// returned by DeactivateZeroBalanceUpiLiteRequest.Validate if the designated
// constraints aren't met.
type DeactivateZeroBalanceUpiLiteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateZeroBalanceUpiLiteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateZeroBalanceUpiLiteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateZeroBalanceUpiLiteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateZeroBalanceUpiLiteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateZeroBalanceUpiLiteRequestValidationError) ErrorName() string {
	return "DeactivateZeroBalanceUpiLiteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateZeroBalanceUpiLiteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateZeroBalanceUpiLiteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateZeroBalanceUpiLiteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateZeroBalanceUpiLiteRequestValidationError{}

// Validate checks the field values on ActivateInternationalUpiPaymentsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ActivateInternationalUpiPaymentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ActivateInternationalUpiPaymentsRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ActivateInternationalUpiPaymentsRequestMultiError, or nil if none found.
func (m *ActivateInternationalUpiPaymentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivateInternationalUpiPaymentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivateInternationalUpiPaymentsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivateInternationalUpiPaymentsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivateInternationalUpiPaymentsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActivateInternationalUpiPaymentsRequestMultiError(errors)
	}

	return nil
}

// ActivateInternationalUpiPaymentsRequestMultiError is an error wrapping
// multiple validation errors returned by
// ActivateInternationalUpiPaymentsRequest.ValidateAll() if the designated
// constraints aren't met.
type ActivateInternationalUpiPaymentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivateInternationalUpiPaymentsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivateInternationalUpiPaymentsRequestMultiError) AllErrors() []error { return m }

// ActivateInternationalUpiPaymentsRequestValidationError is the validation
// error returned by ActivateInternationalUpiPaymentsRequest.Validate if the
// designated constraints aren't met.
type ActivateInternationalUpiPaymentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivateInternationalUpiPaymentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivateInternationalUpiPaymentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivateInternationalUpiPaymentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivateInternationalUpiPaymentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivateInternationalUpiPaymentsRequestValidationError) ErrorName() string {
	return "ActivateInternationalUpiPaymentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ActivateInternationalUpiPaymentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivateInternationalUpiPaymentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivateInternationalUpiPaymentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivateInternationalUpiPaymentsRequestValidationError{}

// Validate checks the field values on
// DeactivateInternationalUpiPaymentsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeactivateInternationalUpiPaymentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeactivateInternationalUpiPaymentsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DeactivateInternationalUpiPaymentsRequestMultiError, or nil if none found.
func (m *DeactivateInternationalUpiPaymentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateInternationalUpiPaymentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateInternationalUpiPaymentsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateInternationalUpiPaymentsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateInternationalUpiPaymentsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeactivateInternationalUpiPaymentsRequestMultiError(errors)
	}

	return nil
}

// DeactivateInternationalUpiPaymentsRequestMultiError is an error wrapping
// multiple validation errors returned by
// DeactivateInternationalUpiPaymentsRequest.ValidateAll() if the designated
// constraints aren't met.
type DeactivateInternationalUpiPaymentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateInternationalUpiPaymentsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateInternationalUpiPaymentsRequestMultiError) AllErrors() []error { return m }

// DeactivateInternationalUpiPaymentsRequestValidationError is the validation
// error returned by DeactivateInternationalUpiPaymentsRequest.Validate if the
// designated constraints aren't met.
type DeactivateInternationalUpiPaymentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateInternationalUpiPaymentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateInternationalUpiPaymentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateInternationalUpiPaymentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateInternationalUpiPaymentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateInternationalUpiPaymentsRequestValidationError) ErrorName() string {
	return "DeactivateInternationalUpiPaymentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateInternationalUpiPaymentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateInternationalUpiPaymentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateInternationalUpiPaymentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateInternationalUpiPaymentsRequestValidationError{}

// Validate checks the field values on LinkUpiAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkUpiAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkUpiAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkUpiAccountRequestMultiError, or nil if none found.
func (m *LinkUpiAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkUpiAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LinkUpiAccountRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LinkUpiAccountRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LinkUpiAccountRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LinkUpiAccountRequestMultiError(errors)
	}

	return nil
}

// LinkUpiAccountRequestMultiError is an error wrapping multiple validation
// errors returned by LinkUpiAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type LinkUpiAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkUpiAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkUpiAccountRequestMultiError) AllErrors() []error { return m }

// LinkUpiAccountRequestValidationError is the validation error returned by
// LinkUpiAccountRequest.Validate if the designated constraints aren't met.
type LinkUpiAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkUpiAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkUpiAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkUpiAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkUpiAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkUpiAccountRequestValidationError) ErrorName() string {
	return "LinkUpiAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LinkUpiAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkUpiAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkUpiAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkUpiAccountRequestValidationError{}

// Validate checks the field values on DelinkUpiAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DelinkUpiAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelinkUpiAccountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DelinkUpiAccountRequestMultiError, or nil if none found.
func (m *DelinkUpiAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DelinkUpiAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiAccountRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiAccountRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiAccountRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DelinkUpiAccountRequestMultiError(errors)
	}

	return nil
}

// DelinkUpiAccountRequestMultiError is an error wrapping multiple validation
// errors returned by DelinkUpiAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type DelinkUpiAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelinkUpiAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelinkUpiAccountRequestMultiError) AllErrors() []error { return m }

// DelinkUpiAccountRequestValidationError is the validation error returned by
// DelinkUpiAccountRequest.Validate if the designated constraints aren't met.
type DelinkUpiAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelinkUpiAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelinkUpiAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelinkUpiAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelinkUpiAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelinkUpiAccountRequestValidationError) ErrorName() string {
	return "DelinkUpiAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DelinkUpiAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelinkUpiAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelinkUpiAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelinkUpiAccountRequestValidationError{}

// Validate checks the field values on DelinkUpiAccountWithVendorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DelinkUpiAccountWithVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelinkUpiAccountWithVendorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DelinkUpiAccountWithVendorRequestMultiError, or nil if none found.
func (m *DelinkUpiAccountWithVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DelinkUpiAccountWithVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiAccountWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiAccountWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiAccountWithVendorRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiAccountWithVendorRequestValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiAccountWithVendorRequestValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiAccountWithVendorRequestValidationError{
				field:  "Payload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DelinkUpiAccountWithVendorRequestMultiError(errors)
	}

	return nil
}

// DelinkUpiAccountWithVendorRequestMultiError is an error wrapping multiple
// validation errors returned by
// DelinkUpiAccountWithVendorRequest.ValidateAll() if the designated
// constraints aren't met.
type DelinkUpiAccountWithVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelinkUpiAccountWithVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelinkUpiAccountWithVendorRequestMultiError) AllErrors() []error { return m }

// DelinkUpiAccountWithVendorRequestValidationError is the validation error
// returned by DelinkUpiAccountWithVendorRequest.Validate if the designated
// constraints aren't met.
type DelinkUpiAccountWithVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelinkUpiAccountWithVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelinkUpiAccountWithVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelinkUpiAccountWithVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelinkUpiAccountWithVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelinkUpiAccountWithVendorRequestValidationError) ErrorName() string {
	return "DelinkUpiAccountWithVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DelinkUpiAccountWithVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelinkUpiAccountWithVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelinkUpiAccountWithVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelinkUpiAccountWithVendorRequestValidationError{}

// Validate checks the field values on DelinkUpiAccountWithVendorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DelinkUpiAccountWithVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelinkUpiAccountWithVendorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DelinkUpiAccountWithVendorResponseMultiError, or nil if none found.
func (m *DelinkUpiAccountWithVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DelinkUpiAccountWithVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiAccountWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiAccountWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiAccountWithVendorResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiAccountWithVendorResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiAccountWithVendorResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiAccountWithVendorResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DelinkUpiAccountWithVendorResponseMultiError(errors)
	}

	return nil
}

// DelinkUpiAccountWithVendorResponseMultiError is an error wrapping multiple
// validation errors returned by
// DelinkUpiAccountWithVendorResponse.ValidateAll() if the designated
// constraints aren't met.
type DelinkUpiAccountWithVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelinkUpiAccountWithVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelinkUpiAccountWithVendorResponseMultiError) AllErrors() []error { return m }

// DelinkUpiAccountWithVendorResponseValidationError is the validation error
// returned by DelinkUpiAccountWithVendorResponse.Validate if the designated
// constraints aren't met.
type DelinkUpiAccountWithVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelinkUpiAccountWithVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelinkUpiAccountWithVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelinkUpiAccountWithVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelinkUpiAccountWithVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelinkUpiAccountWithVendorResponseValidationError) ErrorName() string {
	return "DelinkUpiAccountWithVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DelinkUpiAccountWithVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelinkUpiAccountWithVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelinkUpiAccountWithVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelinkUpiAccountWithVendorResponseValidationError{}

// Validate checks the field values on LinkUpiNumberRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkUpiNumberRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkUpiNumberRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinkUpiNumberRequestMultiError, or nil if none found.
func (m *LinkUpiNumberRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkUpiNumberRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LinkUpiNumberRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LinkUpiNumberRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LinkUpiNumberRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LinkUpiNumberRequestMultiError(errors)
	}

	return nil
}

// LinkUpiNumberRequestMultiError is an error wrapping multiple validation
// errors returned by LinkUpiNumberRequest.ValidateAll() if the designated
// constraints aren't met.
type LinkUpiNumberRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkUpiNumberRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkUpiNumberRequestMultiError) AllErrors() []error { return m }

// LinkUpiNumberRequestValidationError is the validation error returned by
// LinkUpiNumberRequest.Validate if the designated constraints aren't met.
type LinkUpiNumberRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkUpiNumberRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkUpiNumberRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkUpiNumberRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkUpiNumberRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkUpiNumberRequestValidationError) ErrorName() string {
	return "LinkUpiNumberRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LinkUpiNumberRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkUpiNumberRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkUpiNumberRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkUpiNumberRequestValidationError{}

// Validate checks the field values on DelinkUpiNumberRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DelinkUpiNumberRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelinkUpiNumberRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DelinkUpiNumberRequestMultiError, or nil if none found.
func (m *DelinkUpiNumberRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DelinkUpiNumberRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiNumberRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiNumberRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiNumberRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiNumberRequestValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiNumberRequestValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiNumberRequestValidationError{
				field:  "Payload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DelinkUpiNumberRequestMultiError(errors)
	}

	return nil
}

// DelinkUpiNumberRequestMultiError is an error wrapping multiple validation
// errors returned by DelinkUpiNumberRequest.ValidateAll() if the designated
// constraints aren't met.
type DelinkUpiNumberRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelinkUpiNumberRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelinkUpiNumberRequestMultiError) AllErrors() []error { return m }

// DelinkUpiNumberRequestValidationError is the validation error returned by
// DelinkUpiNumberRequest.Validate if the designated constraints aren't met.
type DelinkUpiNumberRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelinkUpiNumberRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelinkUpiNumberRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelinkUpiNumberRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelinkUpiNumberRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelinkUpiNumberRequestValidationError) ErrorName() string {
	return "DelinkUpiNumberRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DelinkUpiNumberRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelinkUpiNumberRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelinkUpiNumberRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelinkUpiNumberRequestValidationError{}

// Validate checks the field values on DelinkUpiNumberResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DelinkUpiNumberResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelinkUpiNumberResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DelinkUpiNumberResponseMultiError, or nil if none found.
func (m *DelinkUpiNumberResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DelinkUpiNumberResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiNumberResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiNumberResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiNumberResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiNumberResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiNumberResponseValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiNumberResponseValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DelinkUpiNumberResponseMultiError(errors)
	}

	return nil
}

// DelinkUpiNumberResponseMultiError is an error wrapping multiple validation
// errors returned by DelinkUpiNumberResponse.ValidateAll() if the designated
// constraints aren't met.
type DelinkUpiNumberResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelinkUpiNumberResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelinkUpiNumberResponseMultiError) AllErrors() []error { return m }

// DelinkUpiNumberResponseValidationError is the validation error returned by
// DelinkUpiNumberResponse.Validate if the designated constraints aren't met.
type DelinkUpiNumberResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelinkUpiNumberResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelinkUpiNumberResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelinkUpiNumberResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelinkUpiNumberResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelinkUpiNumberResponseValidationError) ErrorName() string {
	return "DelinkUpiNumberResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DelinkUpiNumberResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelinkUpiNumberResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelinkUpiNumberResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelinkUpiNumberResponseValidationError{}
