// protolint:disable MAX_LINE_LENGTH

// Upi RPC for operations to simulate payments
// in dev environment
// ** NOT TO BE USED IN PRODUCTION **

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/upi/simulation/service.proto

package simulation

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Simulation_SimulateReqPay_FullMethodName = "/upi.simulation.Simulation/SimulateReqPay"
	Simulation_CreateMandate_FullMethodName  = "/upi.simulation.Simulation/CreateMandate"
)

// SimulationClient is the client API for Simulation service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SimulationClient interface {
	// This RPC is meant to be used for testing Add Funds flow in staging and demo env
	// purely. Please refrain from using in production code
	SimulateReqPay(ctx context.Context, in *SimulateReqPayRequest, opts ...grpc.CallOption) (*SimulateReqPayResponse, error)
	// This RPC is meant to be used for testing Create Mandate flow in staging and demo env
	// purely. Please refrain from using in production code
	CreateMandate(ctx context.Context, in *SimulateMandateRequest, opts ...grpc.CallOption) (*SimulateMandateResponse, error)
}

type simulationClient struct {
	cc grpc.ClientConnInterface
}

func NewSimulationClient(cc grpc.ClientConnInterface) SimulationClient {
	return &simulationClient{cc}
}

func (c *simulationClient) SimulateReqPay(ctx context.Context, in *SimulateReqPayRequest, opts ...grpc.CallOption) (*SimulateReqPayResponse, error) {
	out := new(SimulateReqPayResponse)
	err := c.cc.Invoke(ctx, Simulation_SimulateReqPay_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *simulationClient) CreateMandate(ctx context.Context, in *SimulateMandateRequest, opts ...grpc.CallOption) (*SimulateMandateResponse, error) {
	out := new(SimulateMandateResponse)
	err := c.cc.Invoke(ctx, Simulation_CreateMandate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SimulationServer is the server API for Simulation service.
// All implementations should embed UnimplementedSimulationServer
// for forward compatibility
type SimulationServer interface {
	// This RPC is meant to be used for testing Add Funds flow in staging and demo env
	// purely. Please refrain from using in production code
	SimulateReqPay(context.Context, *SimulateReqPayRequest) (*SimulateReqPayResponse, error)
	// This RPC is meant to be used for testing Create Mandate flow in staging and demo env
	// purely. Please refrain from using in production code
	CreateMandate(context.Context, *SimulateMandateRequest) (*SimulateMandateResponse, error)
}

// UnimplementedSimulationServer should be embedded to have forward compatible implementations.
type UnimplementedSimulationServer struct {
}

func (UnimplementedSimulationServer) SimulateReqPay(context.Context, *SimulateReqPayRequest) (*SimulateReqPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SimulateReqPay not implemented")
}
func (UnimplementedSimulationServer) CreateMandate(context.Context, *SimulateMandateRequest) (*SimulateMandateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMandate not implemented")
}

// UnsafeSimulationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SimulationServer will
// result in compilation errors.
type UnsafeSimulationServer interface {
	mustEmbedUnimplementedSimulationServer()
}

func RegisterSimulationServer(s grpc.ServiceRegistrar, srv SimulationServer) {
	s.RegisterService(&Simulation_ServiceDesc, srv)
}

func _Simulation_SimulateReqPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateReqPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulationServer).SimulateReqPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Simulation_SimulateReqPay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulationServer).SimulateReqPay(ctx, req.(*SimulateReqPayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Simulation_CreateMandate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateMandateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SimulationServer).CreateMandate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Simulation_CreateMandate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SimulationServer).CreateMandate(ctx, req.(*SimulateMandateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Simulation_ServiceDesc is the grpc.ServiceDesc for Simulation service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Simulation_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "upi.simulation.Simulation",
	HandlerType: (*SimulationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SimulateReqPay",
			Handler:    _Simulation_SimulateReqPay_Handler,
		},
		{
			MethodName: "CreateMandate",
			Handler:    _Simulation_CreateMandate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/upi/simulation/service.proto",
}
