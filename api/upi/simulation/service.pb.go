// protolint:disable MAX_LINE_LENGTH

// Upi RPC for operations to simulate payments
// in dev environment
// ** NOT TO BE USED IN PRODUCTION **

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/simulation/service.proto

package simulation

import (
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SimulateMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawData []byte `protobuf:"bytes,1,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *SimulateMandateRequest) Reset() {
	*x = SimulateMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_simulation_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateMandateRequest) ProtoMessage() {}

func (x *SimulateMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_simulation_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateMandateRequest.ProtoReflect.Descriptor instead.
func (*SimulateMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_simulation_service_proto_rawDescGZIP(), []int{0}
}

func (x *SimulateMandateRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type SimulateMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SimulateMandateResponse) Reset() {
	*x = SimulateMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_simulation_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateMandateResponse) ProtoMessage() {}

func (x *SimulateMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_simulation_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateMandateResponse.ProtoReflect.Descriptor instead.
func (*SimulateMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_simulation_service_proto_rawDescGZIP(), []int{1}
}

func (x *SimulateMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type SimulateReqPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawData []byte `protobuf:"bytes,1,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *SimulateReqPayRequest) Reset() {
	*x = SimulateReqPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_simulation_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateReqPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateReqPayRequest) ProtoMessage() {}

func (x *SimulateReqPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_simulation_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateReqPayRequest.ProtoReflect.Descriptor instead.
func (*SimulateReqPayRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_simulation_service_proto_rawDescGZIP(), []int{2}
}

func (x *SimulateReqPayRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type SimulateReqPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Denotes the status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SimulateReqPayResponse) Reset() {
	*x = SimulateReqPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_simulation_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateReqPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateReqPayResponse) ProtoMessage() {}

func (x *SimulateReqPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_simulation_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateReqPayResponse.ProtoReflect.Descriptor instead.
func (*SimulateReqPayResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_simulation_service_proto_rawDescGZIP(), []int{3}
}

func (x *SimulateReqPayResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_upi_simulation_service_proto protoreflect.FileDescriptor

var file_api_upi_simulation_service_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x75, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x33, 0x0a, 0x16, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x22, 0x3e, 0x0a, 0x17, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x32, 0x0a, 0x15, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x3d, 0x0a, 0x16, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x50, 0x61,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xd8,
	0x01, 0x0a, 0x0a, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x66, 0x0a,
	0x0e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x50, 0x61, 0x79, 0x12,
	0x25, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x50, 0x61, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x05,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x12, 0x62, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x26, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_simulation_service_proto_rawDescOnce sync.Once
	file_api_upi_simulation_service_proto_rawDescData = file_api_upi_simulation_service_proto_rawDesc
)

func file_api_upi_simulation_service_proto_rawDescGZIP() []byte {
	file_api_upi_simulation_service_proto_rawDescOnce.Do(func() {
		file_api_upi_simulation_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_simulation_service_proto_rawDescData)
	})
	return file_api_upi_simulation_service_proto_rawDescData
}

var file_api_upi_simulation_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_upi_simulation_service_proto_goTypes = []interface{}{
	(*SimulateMandateRequest)(nil),  // 0: upi.simulation.SimulateMandateRequest
	(*SimulateMandateResponse)(nil), // 1: upi.simulation.SimulateMandateResponse
	(*SimulateReqPayRequest)(nil),   // 2: upi.simulation.SimulateReqPayRequest
	(*SimulateReqPayResponse)(nil),  // 3: upi.simulation.SimulateReqPayResponse
	(*rpc.Status)(nil),              // 4: rpc.Status
}
var file_api_upi_simulation_service_proto_depIdxs = []int32{
	4, // 0: upi.simulation.SimulateMandateResponse.status:type_name -> rpc.Status
	4, // 1: upi.simulation.SimulateReqPayResponse.status:type_name -> rpc.Status
	2, // 2: upi.simulation.Simulation.SimulateReqPay:input_type -> upi.simulation.SimulateReqPayRequest
	0, // 3: upi.simulation.Simulation.CreateMandate:input_type -> upi.simulation.SimulateMandateRequest
	3, // 4: upi.simulation.Simulation.SimulateReqPay:output_type -> upi.simulation.SimulateReqPayResponse
	1, // 5: upi.simulation.Simulation.CreateMandate:output_type -> upi.simulation.SimulateMandateResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_upi_simulation_service_proto_init() }
func file_api_upi_simulation_service_proto_init() {
	if File_api_upi_simulation_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_simulation_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimulateMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_simulation_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimulateMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_simulation_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimulateReqPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_simulation_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimulateReqPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_simulation_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_upi_simulation_service_proto_goTypes,
		DependencyIndexes: file_api_upi_simulation_service_proto_depIdxs,
		MessageInfos:      file_api_upi_simulation_service_proto_msgTypes,
	}.Build()
	File_api_upi_simulation_service_proto = out.File
	file_api_upi_simulation_service_proto_rawDesc = nil
	file_api_upi_simulation_service_proto_goTypes = nil
	file_api_upi_simulation_service_proto_depIdxs = nil
}
