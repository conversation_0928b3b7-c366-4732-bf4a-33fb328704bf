// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/simulation/service.proto

package simulation

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SimulateMandateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SimulateMandateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimulateMandateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SimulateMandateRequestMultiError, or nil if none found.
func (m *SimulateMandateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SimulateMandateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RawData

	if len(errors) > 0 {
		return SimulateMandateRequestMultiError(errors)
	}

	return nil
}

// SimulateMandateRequestMultiError is an error wrapping multiple validation
// errors returned by SimulateMandateRequest.ValidateAll() if the designated
// constraints aren't met.
type SimulateMandateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimulateMandateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimulateMandateRequestMultiError) AllErrors() []error { return m }

// SimulateMandateRequestValidationError is the validation error returned by
// SimulateMandateRequest.Validate if the designated constraints aren't met.
type SimulateMandateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimulateMandateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimulateMandateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimulateMandateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimulateMandateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimulateMandateRequestValidationError) ErrorName() string {
	return "SimulateMandateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SimulateMandateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimulateMandateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimulateMandateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimulateMandateRequestValidationError{}

// Validate checks the field values on SimulateMandateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SimulateMandateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimulateMandateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SimulateMandateResponseMultiError, or nil if none found.
func (m *SimulateMandateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SimulateMandateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SimulateMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SimulateMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SimulateMandateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SimulateMandateResponseMultiError(errors)
	}

	return nil
}

// SimulateMandateResponseMultiError is an error wrapping multiple validation
// errors returned by SimulateMandateResponse.ValidateAll() if the designated
// constraints aren't met.
type SimulateMandateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimulateMandateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimulateMandateResponseMultiError) AllErrors() []error { return m }

// SimulateMandateResponseValidationError is the validation error returned by
// SimulateMandateResponse.Validate if the designated constraints aren't met.
type SimulateMandateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimulateMandateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimulateMandateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimulateMandateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimulateMandateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimulateMandateResponseValidationError) ErrorName() string {
	return "SimulateMandateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SimulateMandateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimulateMandateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimulateMandateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimulateMandateResponseValidationError{}

// Validate checks the field values on SimulateReqPayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SimulateReqPayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimulateReqPayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SimulateReqPayRequestMultiError, or nil if none found.
func (m *SimulateReqPayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SimulateReqPayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RawData

	if len(errors) > 0 {
		return SimulateReqPayRequestMultiError(errors)
	}

	return nil
}

// SimulateReqPayRequestMultiError is an error wrapping multiple validation
// errors returned by SimulateReqPayRequest.ValidateAll() if the designated
// constraints aren't met.
type SimulateReqPayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimulateReqPayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimulateReqPayRequestMultiError) AllErrors() []error { return m }

// SimulateReqPayRequestValidationError is the validation error returned by
// SimulateReqPayRequest.Validate if the designated constraints aren't met.
type SimulateReqPayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimulateReqPayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimulateReqPayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimulateReqPayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimulateReqPayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimulateReqPayRequestValidationError) ErrorName() string {
	return "SimulateReqPayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SimulateReqPayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimulateReqPayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimulateReqPayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimulateReqPayRequestValidationError{}

// Validate checks the field values on SimulateReqPayResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SimulateReqPayResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimulateReqPayResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SimulateReqPayResponseMultiError, or nil if none found.
func (m *SimulateReqPayResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SimulateReqPayResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SimulateReqPayResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SimulateReqPayResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SimulateReqPayResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SimulateReqPayResponseMultiError(errors)
	}

	return nil
}

// SimulateReqPayResponseMultiError is an error wrapping multiple validation
// errors returned by SimulateReqPayResponse.ValidateAll() if the designated
// constraints aren't met.
type SimulateReqPayResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimulateReqPayResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimulateReqPayResponseMultiError) AllErrors() []error { return m }

// SimulateReqPayResponseValidationError is the validation error returned by
// SimulateReqPayResponse.Validate if the designated constraints aren't met.
type SimulateReqPayResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimulateReqPayResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimulateReqPayResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimulateReqPayResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimulateReqPayResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimulateReqPayResponseValidationError) ErrorName() string {
	return "SimulateReqPayResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SimulateReqPayResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimulateReqPayResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimulateReqPayResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimulateReqPayResponseValidationError{}
