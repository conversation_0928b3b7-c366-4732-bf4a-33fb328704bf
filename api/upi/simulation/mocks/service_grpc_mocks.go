// Code generated by MockGen. DO NOT EDIT.
// Source: api/upi/simulation/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	simulation "github.com/epifi/gamma/api/upi/simulation"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockSimulationClient is a mock of SimulationClient interface.
type MockSimulationClient struct {
	ctrl     *gomock.Controller
	recorder *MockSimulationClientMockRecorder
}

// MockSimulationClientMockRecorder is the mock recorder for MockSimulationClient.
type MockSimulationClientMockRecorder struct {
	mock *MockSimulationClient
}

// NewMockSimulationClient creates a new mock instance.
func NewMockSimulationClient(ctrl *gomock.Controller) *MockSimulationClient {
	mock := &MockSimulationClient{ctrl: ctrl}
	mock.recorder = &MockSimulationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSimulationClient) EXPECT() *MockSimulationClientMockRecorder {
	return m.recorder
}

// CreateMandate mocks base method.
func (m *MockSimulationClient) CreateMandate(ctx context.Context, in *simulation.SimulateMandateRequest, opts ...grpc.CallOption) (*simulation.SimulateMandateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateMandate", varargs...)
	ret0, _ := ret[0].(*simulation.SimulateMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateMandate indicates an expected call of CreateMandate.
func (mr *MockSimulationClientMockRecorder) CreateMandate(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMandate", reflect.TypeOf((*MockSimulationClient)(nil).CreateMandate), varargs...)
}

// SimulateReqPay mocks base method.
func (m *MockSimulationClient) SimulateReqPay(ctx context.Context, in *simulation.SimulateReqPayRequest, opts ...grpc.CallOption) (*simulation.SimulateReqPayResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SimulateReqPay", varargs...)
	ret0, _ := ret[0].(*simulation.SimulateReqPayResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimulateReqPay indicates an expected call of SimulateReqPay.
func (mr *MockSimulationClientMockRecorder) SimulateReqPay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimulateReqPay", reflect.TypeOf((*MockSimulationClient)(nil).SimulateReqPay), varargs...)
}

// MockSimulationServer is a mock of SimulationServer interface.
type MockSimulationServer struct {
	ctrl     *gomock.Controller
	recorder *MockSimulationServerMockRecorder
}

// MockSimulationServerMockRecorder is the mock recorder for MockSimulationServer.
type MockSimulationServerMockRecorder struct {
	mock *MockSimulationServer
}

// NewMockSimulationServer creates a new mock instance.
func NewMockSimulationServer(ctrl *gomock.Controller) *MockSimulationServer {
	mock := &MockSimulationServer{ctrl: ctrl}
	mock.recorder = &MockSimulationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSimulationServer) EXPECT() *MockSimulationServerMockRecorder {
	return m.recorder
}

// CreateMandate mocks base method.
func (m *MockSimulationServer) CreateMandate(arg0 context.Context, arg1 *simulation.SimulateMandateRequest) (*simulation.SimulateMandateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMandate", arg0, arg1)
	ret0, _ := ret[0].(*simulation.SimulateMandateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateMandate indicates an expected call of CreateMandate.
func (mr *MockSimulationServerMockRecorder) CreateMandate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMandate", reflect.TypeOf((*MockSimulationServer)(nil).CreateMandate), arg0, arg1)
}

// SimulateReqPay mocks base method.
func (m *MockSimulationServer) SimulateReqPay(arg0 context.Context, arg1 *simulation.SimulateReqPayRequest) (*simulation.SimulateReqPayResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimulateReqPay", arg0, arg1)
	ret0, _ := ret[0].(*simulation.SimulateReqPayResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimulateReqPay indicates an expected call of SimulateReqPay.
func (mr *MockSimulationServerMockRecorder) SimulateReqPay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimulateReqPay", reflect.TypeOf((*MockSimulationServer)(nil).SimulateReqPay), arg0, arg1)
}

// MockUnsafeSimulationServer is a mock of UnsafeSimulationServer interface.
type MockUnsafeSimulationServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSimulationServerMockRecorder
}

// MockUnsafeSimulationServerMockRecorder is the mock recorder for MockUnsafeSimulationServer.
type MockUnsafeSimulationServerMockRecorder struct {
	mock *MockUnsafeSimulationServer
}

// NewMockUnsafeSimulationServer creates a new mock instance.
func NewMockUnsafeSimulationServer(ctrl *gomock.Controller) *MockUnsafeSimulationServer {
	mock := &MockUnsafeSimulationServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSimulationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSimulationServer) EXPECT() *MockUnsafeSimulationServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSimulationServer mocks base method.
func (m *MockUnsafeSimulationServer) mustEmbedUnimplementedSimulationServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSimulationServer")
}

// mustEmbedUnimplementedSimulationServer indicates an expected call of mustEmbedUnimplementedSimulationServer.
func (mr *MockUnsafeSimulationServerMockRecorder) mustEmbedUnimplementedSimulationServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSimulationServer", reflect.TypeOf((*MockUnsafeSimulationServer)(nil).mustEmbedUnimplementedSimulationServer))
}
