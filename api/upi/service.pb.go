// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/service.proto

package upi

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	accounts "github.com/epifi/gamma/api/accounts"
	card "github.com/epifi/gamma/api/card"
	domain "github.com/epifi/gamma/api/order/domain"
	paymentinstrument "github.com/epifi/gamma/api/paymentinstrument"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	account "github.com/epifi/gamma/api/typesv2/account"
	complaint "github.com/epifi/gamma/api/upi/complaint"
	domain_model "github.com/epifi/gamma/api/upi/domain_model"
	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
	txnref "github.com/epifi/gamma/api/upi/txnref"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// enum for identifying the type of urn in VerifyUrnRequest
type URNType int32

const (
	URNType_URN_TYPE_UNSPECIFIED URNType = 0
	URNType_BHARAT_PAY_URN       URNType = 1
	URNType_QR_URN               URNType = 2
	URNType_INTENT_URN           URNType = 3
	// urn created for upi mandate txn
	URNType_MANDATE_URN URNType = 4
	// mandate collect urn type.
	// This type of urn will be used to one time execution of mandate.
	URNType_MANDATE_COLLECT URNType = 5
)

// Enum value maps for URNType.
var (
	URNType_name = map[int32]string{
		0: "URN_TYPE_UNSPECIFIED",
		1: "BHARAT_PAY_URN",
		2: "QR_URN",
		3: "INTENT_URN",
		4: "MANDATE_URN",
		5: "MANDATE_COLLECT",
	}
	URNType_value = map[string]int32{
		"URN_TYPE_UNSPECIFIED": 0,
		"BHARAT_PAY_URN":       1,
		"QR_URN":               2,
		"INTENT_URN":           3,
		"MANDATE_URN":          4,
		"MANDATE_COLLECT":      5,
	}
)

func (x URNType) Enum() *URNType {
	p := new(URNType)
	*p = x
	return p
}

func (x URNType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (URNType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[0].Descriptor()
}

func (URNType) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[0]
}

func (x URNType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use URNType.Descriptor instead.
func (URNType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{0}
}

type SortOrder int32

const (
	SortOrder_SORT_ORDER_UNSPECIFIED SortOrder = 0
	SortOrder_SORT_ORDER_ASCENDING   SortOrder = 1
	SortOrder_SORT_ORDER_DESCENDING  SortOrder = 2
)

// Enum value maps for SortOrder.
var (
	SortOrder_name = map[int32]string{
		0: "SORT_ORDER_UNSPECIFIED",
		1: "SORT_ORDER_ASCENDING",
		2: "SORT_ORDER_DESCENDING",
	}
	SortOrder_value = map[string]int32{
		"SORT_ORDER_UNSPECIFIED": 0,
		"SORT_ORDER_ASCENDING":   1,
		"SORT_ORDER_DESCENDING":  2,
	}
)

func (x SortOrder) Enum() *SortOrder {
	p := new(SortOrder)
	*p = x
	return p
}

func (x SortOrder) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortOrder) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[1].Descriptor()
}

func (SortOrder) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[1]
}

func (x SortOrder) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortOrder.Descriptor instead.
func (SortOrder) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{1}
}

type UpiUserActivity int32

const (
	UpiUserActivity_UPI_USER_ACTIVITY_UNSPECIFIED UpiUserActivity = 0
	// activity to trigger after user have seen
	// the UPI ETB(existing to bank) pin set message.
	// UPI service will change pin set state to PIN set
	// so that ETB message not appear again user screen.
	UpiUserActivity_UPI_ETB_MESSAGE_SEEN UpiUserActivity = 1
)

// Enum value maps for UpiUserActivity.
var (
	UpiUserActivity_name = map[int32]string{
		0: "UPI_USER_ACTIVITY_UNSPECIFIED",
		1: "UPI_ETB_MESSAGE_SEEN",
	}
	UpiUserActivity_value = map[string]int32{
		"UPI_USER_ACTIVITY_UNSPECIFIED": 0,
		"UPI_ETB_MESSAGE_SEEN":          1,
	}
)

func (x UpiUserActivity) Enum() *UpiUserActivity {
	p := new(UpiUserActivity)
	*p = x
	return p
}

func (x UpiUserActivity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiUserActivity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[2].Descriptor()
}

func (UpiUserActivity) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[2]
}

func (x UpiUserActivity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiUserActivity.Descriptor instead.
func (UpiUserActivity) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{2}
}

type CheckTxnStatusResponse_Status int32

const (
	// transaction was successful
	CheckTxnStatusResponse_OK CheckTxnStatusResponse_Status = 0
	// System faced internal errors while processing the request
	CheckTxnStatusResponse_INTERNAL CheckTxnStatusResponse_Status = 13
	// transaction still getting processed
	CheckTxnStatusResponse_IN_PROGRESS CheckTxnStatusResponse_Status = 51
	// payment failed due to business failure
	// which occur mostly due to wrong information or inputs provided by the user.
	// for eg. wrong MPIN entered by user
	CheckTxnStatusResponse_FAILURE CheckTxnStatusResponse_Status = 101
)

// Enum value maps for CheckTxnStatusResponse_Status.
var (
	CheckTxnStatusResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		51:  "IN_PROGRESS",
		101: "FAILURE",
	}
	CheckTxnStatusResponse_Status_value = map[string]int32{
		"OK":          0,
		"INTERNAL":    13,
		"IN_PROGRESS": 51,
		"FAILURE":     101,
	}
)

func (x CheckTxnStatusResponse_Status) Enum() *CheckTxnStatusResponse_Status {
	p := new(CheckTxnStatusResponse_Status)
	*p = x
	return p
}

func (x CheckTxnStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckTxnStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[3].Descriptor()
}

func (CheckTxnStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[3]
}

func (x CheckTxnStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckTxnStatusResponse_Status.Descriptor instead.
func (CheckTxnStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{1, 0}
}

type CheckTxnStatusResponse_Ref_RefType int32

const (
	// unspecified
	CheckTxnStatusResponse_Ref_REF_TYPE_UNSPECIFIED CheckTxnStatusResponse_Ref_RefType = 0
	// PAYER
	CheckTxnStatusResponse_Ref_PAYER CheckTxnStatusResponse_Ref_RefType = 1
	// PAYEE
	CheckTxnStatusResponse_Ref_PAYEE CheckTxnStatusResponse_Ref_RefType = 2
)

// Enum value maps for CheckTxnStatusResponse_Ref_RefType.
var (
	CheckTxnStatusResponse_Ref_RefType_name = map[int32]string{
		0: "REF_TYPE_UNSPECIFIED",
		1: "PAYER",
		2: "PAYEE",
	}
	CheckTxnStatusResponse_Ref_RefType_value = map[string]int32{
		"REF_TYPE_UNSPECIFIED": 0,
		"PAYER":                1,
		"PAYEE":                2,
	}
)

func (x CheckTxnStatusResponse_Ref_RefType) Enum() *CheckTxnStatusResponse_Ref_RefType {
	p := new(CheckTxnStatusResponse_Ref_RefType)
	*p = x
	return p
}

func (x CheckTxnStatusResponse_Ref_RefType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckTxnStatusResponse_Ref_RefType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[4].Descriptor()
}

func (CheckTxnStatusResponse_Ref_RefType) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[4]
}

func (x CheckTxnStatusResponse_Ref_RefType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckTxnStatusResponse_Ref_RefType.Descriptor instead.
func (CheckTxnStatusResponse_Ref_RefType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{1, 0, 0}
}

type VerifyPayeeVPAResponse_Status int32

const (
	// Returned an success
	VerifyPayeeVPAResponse_OK VerifyPayeeVPAResponse_Status = 0
	// System faced internal errors while processing the request
	VerifyPayeeVPAResponse_INTERNAL VerifyPayeeVPAResponse_Status = 13
	// the VPA Id is invalid
	VerifyPayeeVPAResponse_INVALID_VPA VerifyPayeeVPAResponse_Status = 100
	// invalid merchant psp
	VerifyPayeeVPAResponse_INVALID_MERCHANT VerifyPayeeVPAResponse_Status = 101
	// transaction not permitted to the vpa by psp
	VerifyPayeeVPAResponse_TRANSACTION_NOT_PERMITTED_BY_PSP VerifyPayeeVPAResponse_Status = 102
	// vpa restricted by the customer
	VerifyPayeeVPAResponse_VPA_RESTRICTED VerifyPayeeVPAResponse_Status = 103
	// PSP is not registered with NPCI
	VerifyPayeeVPAResponse_PSP_NOT_REGISTERED VerifyPayeeVPAResponse_Status = 104
	// PSP not available at the moment
	VerifyPayeeVPAResponse_PSP_NOT_AVAILABLE VerifyPayeeVPAResponse_Status = 105
	// active payer vpa not available at the moment
	VerifyPayeeVPAResponse_INACTIVE_PAYER_VPA VerifyPayeeVPAResponse_Status = 106
	// Merchant Error(Payee Psp)
	VerifyPayeeVPAResponse_PAYEE_PSP_MERCHANT_ERROR VerifyPayeeVPAResponse_Status = 107
	// Suspected Fraud, Decline / Transactions declined
	// based on risk score by beneficiary
	VerifyPayeeVPAResponse_SUSPECTED_FRAUD VerifyPayeeVPAResponse_Status = 108
	// upi number is in inactive
	// Note: validate address is used
	// to validate upi number in upi
	// number search
	VerifyPayeeVPAResponse_MAPPING_INACTIVE VerifyPayeeVPAResponse_Status = 109
	// no active internal UPI PI found
	// Returned when no active internal UPI Payment Instrument is found for the given VPA.
	// e.g. when all the upi pis have been disabled for the given VPA
	VerifyPayeeVPAResponse_NO_ACTIVE_INTERNAL_UPI_PI VerifyPayeeVPAResponse_Status = 110
)

// Enum value maps for VerifyPayeeVPAResponse_Status.
var (
	VerifyPayeeVPAResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "INVALID_VPA",
		101: "INVALID_MERCHANT",
		102: "TRANSACTION_NOT_PERMITTED_BY_PSP",
		103: "VPA_RESTRICTED",
		104: "PSP_NOT_REGISTERED",
		105: "PSP_NOT_AVAILABLE",
		106: "INACTIVE_PAYER_VPA",
		107: "PAYEE_PSP_MERCHANT_ERROR",
		108: "SUSPECTED_FRAUD",
		109: "MAPPING_INACTIVE",
		110: "NO_ACTIVE_INTERNAL_UPI_PI",
	}
	VerifyPayeeVPAResponse_Status_value = map[string]int32{
		"OK":                               0,
		"INTERNAL":                         13,
		"INVALID_VPA":                      100,
		"INVALID_MERCHANT":                 101,
		"TRANSACTION_NOT_PERMITTED_BY_PSP": 102,
		"VPA_RESTRICTED":                   103,
		"PSP_NOT_REGISTERED":               104,
		"PSP_NOT_AVAILABLE":                105,
		"INACTIVE_PAYER_VPA":               106,
		"PAYEE_PSP_MERCHANT_ERROR":         107,
		"SUSPECTED_FRAUD":                  108,
		"MAPPING_INACTIVE":                 109,
		"NO_ACTIVE_INTERNAL_UPI_PI":        110,
	}
)

func (x VerifyPayeeVPAResponse_Status) Enum() *VerifyPayeeVPAResponse_Status {
	p := new(VerifyPayeeVPAResponse_Status)
	*p = x
	return p
}

func (x VerifyPayeeVPAResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyPayeeVPAResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[5].Descriptor()
}

func (VerifyPayeeVPAResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[5]
}

func (x VerifyPayeeVPAResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyPayeeVPAResponse_Status.Descriptor instead.
func (VerifyPayeeVPAResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{3, 0}
}

// List of status codes returned
type GetTokenResponse_Status int32

const (
	// Returned an success
	GetTokenResponse_OK GetTokenResponse_Status = 0
	// Indicates that arguments are problematic
	GetTokenResponse_INVALID_ARGUMENT GetTokenResponse_Status = 3
	// System faced internal errors while processing the request
	GetTokenResponse_INTERNAL GetTokenResponse_Status = 13
	// Device registration failed. One of reason could be error at NPCI end.
	GetTokenResponse_DEVICE_REGISTRATION_FAILED GetTokenResponse_Status = 100
	// DEADLINE EXCEEDED at vendor side
	GetTokenResponse_DEADLINE_EXCEEDED_AT_VENDOR GetTokenResponse_Status = 101
)

// Enum value maps for GetTokenResponse_Status.
var (
	GetTokenResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		100: "DEVICE_REGISTRATION_FAILED",
		101: "DEADLINE_EXCEEDED_AT_VENDOR",
	}
	GetTokenResponse_Status_value = map[string]int32{
		"OK":                          0,
		"INVALID_ARGUMENT":            3,
		"INTERNAL":                    13,
		"DEVICE_REGISTRATION_FAILED":  100,
		"DEADLINE_EXCEEDED_AT_VENDOR": 101,
	}
)

func (x GetTokenResponse_Status) Enum() *GetTokenResponse_Status {
	p := new(GetTokenResponse_Status)
	*p = x
	return p
}

func (x GetTokenResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetTokenResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[6].Descriptor()
}

func (GetTokenResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[6]
}

func (x GetTokenResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetTokenResponse_Status.Descriptor instead.
func (GetTokenResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{5, 0}
}

// otp type to be generated
type GenerateUpiOtpRequest_ReqOtpType int32

const (
	GenerateUpiOtpRequest_REQ_OTP_TYPE_BANK       GenerateUpiOtpRequest_ReqOtpType = 0
	GenerateUpiOtpRequest_REQ_OTP_TYPE_UIDAI      GenerateUpiOtpRequest_ReqOtpType = 1
	GenerateUpiOtpRequest_REQ_OTP_TYPE_BANK_UIDAI GenerateUpiOtpRequest_ReqOtpType = 2
)

// Enum value maps for GenerateUpiOtpRequest_ReqOtpType.
var (
	GenerateUpiOtpRequest_ReqOtpType_name = map[int32]string{
		0: "REQ_OTP_TYPE_BANK",
		1: "REQ_OTP_TYPE_UIDAI",
		2: "REQ_OTP_TYPE_BANK_UIDAI",
	}
	GenerateUpiOtpRequest_ReqOtpType_value = map[string]int32{
		"REQ_OTP_TYPE_BANK":       0,
		"REQ_OTP_TYPE_UIDAI":      1,
		"REQ_OTP_TYPE_BANK_UIDAI": 2,
	}
)

func (x GenerateUpiOtpRequest_ReqOtpType) Enum() *GenerateUpiOtpRequest_ReqOtpType {
	p := new(GenerateUpiOtpRequest_ReqOtpType)
	*p = x
	return p
}

func (x GenerateUpiOtpRequest_ReqOtpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerateUpiOtpRequest_ReqOtpType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[7].Descriptor()
}

func (GenerateUpiOtpRequest_ReqOtpType) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[7]
}

func (x GenerateUpiOtpRequest_ReqOtpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerateUpiOtpRequest_ReqOtpType.Descriptor instead.
func (GenerateUpiOtpRequest_ReqOtpType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{6, 0}
}

// List of status codes returned
type GenerateUpiOtpResponse_Status int32

const (
	// Returned an success
	GenerateUpiOtpResponse_OK GenerateUpiOtpResponse_Status = 0
	// Indicates that arguments are problematic
	GenerateUpiOtpResponse_INVALID_ARGUMENT GenerateUpiOtpResponse_Status = 3
	// The deadline expired before the operation could complete. For operations
	// that change the state of the system, this error may be returned
	// even if the operation has completed successfully.
	GenerateUpiOtpResponse_DEADLINE_EXCEEDED GenerateUpiOtpResponse_Status = 4
	// System faced internal errors while processing the request
	GenerateUpiOtpResponse_INTERNAL GenerateUpiOtpResponse_Status = 13
	// The request does not have valid authentication credentials for the
	// operation.
	// Reason: Invalid cred block - Either card details or Otp or both
	// that are part of cred block are wrong
	GenerateUpiOtpResponse_UNAUTHENTICATED GenerateUpiOtpResponse_Status = 16
	// error occurred due timeout at vendor side.
	GenerateUpiOtpResponse_PSP_TIMEOUT GenerateUpiOtpResponse_Status = 100
	// account for which we hae requested otp doesn't exist
	// this happens in case there is a sync delay at vendor's end
	GenerateUpiOtpResponse_ACCOUNT_DOES_NOT_EXIST GenerateUpiOtpResponse_Status = 101
	// DEADLINE EXCEEDED at vendor side
	GenerateUpiOtpResponse_DEADLINE_EXCEEDED_AT_VENDOR GenerateUpiOtpResponse_Status = 102
	// external error at vendor
	GenerateUpiOtpResponse_VENDOR_EXTERNAL_ERROR GenerateUpiOtpResponse_Status = 103
)

// Enum value maps for GenerateUpiOtpResponse_Status.
var (
	GenerateUpiOtpResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		4:   "DEADLINE_EXCEEDED",
		13:  "INTERNAL",
		16:  "UNAUTHENTICATED",
		100: "PSP_TIMEOUT",
		101: "ACCOUNT_DOES_NOT_EXIST",
		102: "DEADLINE_EXCEEDED_AT_VENDOR",
		103: "VENDOR_EXTERNAL_ERROR",
	}
	GenerateUpiOtpResponse_Status_value = map[string]int32{
		"OK":                          0,
		"INVALID_ARGUMENT":            3,
		"DEADLINE_EXCEEDED":           4,
		"INTERNAL":                    13,
		"UNAUTHENTICATED":             16,
		"PSP_TIMEOUT":                 100,
		"ACCOUNT_DOES_NOT_EXIST":      101,
		"DEADLINE_EXCEEDED_AT_VENDOR": 102,
		"VENDOR_EXTERNAL_ERROR":       103,
	}
)

func (x GenerateUpiOtpResponse_Status) Enum() *GenerateUpiOtpResponse_Status {
	p := new(GenerateUpiOtpResponse_Status)
	*p = x
	return p
}

func (x GenerateUpiOtpResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerateUpiOtpResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[8].Descriptor()
}

func (GenerateUpiOtpResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[8]
}

func (x GenerateUpiOtpResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerateUpiOtpResponse_Status.Descriptor instead.
func (GenerateUpiOtpResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{7, 0}
}

// List of status codes returned
type ResolveAccountResponse_Status int32

const (
	// Returned an success
	ResolveAccountResponse_OK ResolveAccountResponse_Status = 0
	// Indicates that arguments are problematic
	ResolveAccountResponse_INVALID_ARGUMENT ResolveAccountResponse_Status = 3
	// Account details not found
	ResolveAccountResponse_NOT_FOUND ResolveAccountResponse_Status = 5
	// System faced internal errors while processing the request
	ResolveAccountResponse_INTERNAL ResolveAccountResponse_Status = 13
)

// Enum value maps for ResolveAccountResponse_Status.
var (
	ResolveAccountResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	ResolveAccountResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"NOT_FOUND":        5,
		"INTERNAL":         13,
	}
)

func (x ResolveAccountResponse_Status) Enum() *ResolveAccountResponse_Status {
	p := new(ResolveAccountResponse_Status)
	*p = x
	return p
}

func (x ResolveAccountResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResolveAccountResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[9].Descriptor()
}

func (ResolveAccountResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[9]
}

func (x ResolveAccountResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResolveAccountResponse_Status.Descriptor instead.
func (ResolveAccountResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{9, 0}
}

// List of status codes returned
type CreateVPAResponse_Status int32

const (
	// Returned an success
	CreateVPAResponse_OK CreateVPAResponse_Status = 0
	// Indicates that arguments are problematic
	CreateVPAResponse_INVALID_ARGUMENT CreateVPAResponse_Status = 3
	// System faced internal errors while processing the request
	CreateVPAResponse_INTERNAL CreateVPAResponse_Status = 13
	// some failure occured and message will be retried
	CreateVPAResponse_TRANSIENT_FAILURE CreateVPAResponse_Status = 100
	// For permanent failures which can't be retried. When this status code is returned the packet need to be
	// deleted from the queue
	// e.g. errors from NPCI for no account exist comes under this error
	CreateVPAResponse_PERMANENT_FAILURE CreateVPAResponse_Status = 101
)

// Enum value maps for CreateVPAResponse_Status.
var (
	CreateVPAResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		100: "TRANSIENT_FAILURE",
		101: "PERMANENT_FAILURE",
	}
	CreateVPAResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"INTERNAL":          13,
		"TRANSIENT_FAILURE": 100,
		"PERMANENT_FAILURE": 101,
	}
)

func (x CreateVPAResponse_Status) Enum() *CreateVPAResponse_Status {
	p := new(CreateVPAResponse_Status)
	*p = x
	return p
}

func (x CreateVPAResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateVPAResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[10].Descriptor()
}

func (CreateVPAResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[10]
}

func (x CreateVPAResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateVPAResponse_Status.Descriptor instead.
func (CreateVPAResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{11, 0}
}

// List of status codes returned
type ListKeysResponse_Status int32

const (
	// Returned an success
	ListKeysResponse_OK ListKeysResponse_Status = 0
	// System faced internal errors while processing the request
	ListKeysResponse_INTERNAL ListKeysResponse_Status = 13
)

// Enum value maps for ListKeysResponse_Status.
var (
	ListKeysResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	ListKeysResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x ListKeysResponse_Status) Enum() *ListKeysResponse_Status {
	p := new(ListKeysResponse_Status)
	*p = x
	return p
}

func (x ListKeysResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListKeysResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[11].Descriptor()
}

func (ListKeysResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[11]
}

func (x ListKeysResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListKeysResponse_Status.Descriptor instead.
func (ListKeysResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{12, 0}
}

type RegisterMobileResponse_Status int32

const (
	// Returned an success
	RegisterMobileResponse_OK RegisterMobileResponse_Status = 0
	// Indicates that arguments are problematic
	RegisterMobileResponse_INVALID_ARGUMENT RegisterMobileResponse_Status = 3
	// System faced internal errors while processing the request
	RegisterMobileResponse_INTERNAL RegisterMobileResponse_Status = 13
	// The request does not have valid authentication credentials for the
	// operation.
	// Reason: Invalid cred block - Card details
	// that is part of cred block are wrong
	RegisterMobileResponse_UNAUTHENTICATED RegisterMobileResponse_Status = 16
	// debit card is inactive
	RegisterMobileResponse_CARD_INACTIVE RegisterMobileResponse_Status = 101
	// bank server is down
	RegisterMobileResponse_BANK_SERVER_DOWN RegisterMobileResponse_Status = 102
	// registration blocked temporarily
	// due to max retries
	RegisterMobileResponse_MAX_REGISTRATION_RETRIES RegisterMobileResponse_Status = 103
	// incorrect atm pin
	RegisterMobileResponse_INCORRECT_ATM_PIN RegisterMobileResponse_Status = 104
	// debit card not found
	RegisterMobileResponse_DEBIT_CARD_NOT_FOUND RegisterMobileResponse_Status = 105
	// debit card is restricted
	RegisterMobileResponse_DEBIT_CARD_RESTRICTED RegisterMobileResponse_Status = 106
	// debit card has expired
	RegisterMobileResponse_DEBIT_CARD_EXPIRED RegisterMobileResponse_Status = 107
	// incorrect mpin
	RegisterMobileResponse_INCORRECT_MPIN RegisterMobileResponse_Status = 108
	// incorrect otp
	RegisterMobileResponse_INCORRECT_OTP RegisterMobileResponse_Status = 109
	// otp has expired
	RegisterMobileResponse_OTP_EXPIRED RegisterMobileResponse_Status = 110
	// max otp retries
	RegisterMobileResponse_MAX_OTP_RETRIES RegisterMobileResponse_Status = 111
	// error occurred due to psp timeout at vendor side.
	RegisterMobileResponse_PSP_TIMEOUT RegisterMobileResponse_Status = 112
	// Remitter's account is inactive or dormant
	RegisterMobileResponse_REMITTER_INACTIVE_OR_DORMANT_ACCOUNT RegisterMobileResponse_Status = 113
	// EXTERNAL_ERROR_AT_VENDOR
	RegisterMobileResponse_VENDOR_EXTERNAL_ERROR RegisterMobileResponse_Status = 114
	// Encryption Error at Vendor
	RegisterMobileResponse_VENDOR_ENCRYPTION_ERROR RegisterMobileResponse_Status = 115
)

// Enum value maps for RegisterMobileResponse_Status.
var (
	RegisterMobileResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		16:  "UNAUTHENTICATED",
		101: "CARD_INACTIVE",
		102: "BANK_SERVER_DOWN",
		103: "MAX_REGISTRATION_RETRIES",
		104: "INCORRECT_ATM_PIN",
		105: "DEBIT_CARD_NOT_FOUND",
		106: "DEBIT_CARD_RESTRICTED",
		107: "DEBIT_CARD_EXPIRED",
		108: "INCORRECT_MPIN",
		109: "INCORRECT_OTP",
		110: "OTP_EXPIRED",
		111: "MAX_OTP_RETRIES",
		112: "PSP_TIMEOUT",
		113: "REMITTER_INACTIVE_OR_DORMANT_ACCOUNT",
		114: "VENDOR_EXTERNAL_ERROR",
		115: "VENDOR_ENCRYPTION_ERROR",
	}
	RegisterMobileResponse_Status_value = map[string]int32{
		"OK":                                   0,
		"INVALID_ARGUMENT":                     3,
		"INTERNAL":                             13,
		"UNAUTHENTICATED":                      16,
		"CARD_INACTIVE":                        101,
		"BANK_SERVER_DOWN":                     102,
		"MAX_REGISTRATION_RETRIES":             103,
		"INCORRECT_ATM_PIN":                    104,
		"DEBIT_CARD_NOT_FOUND":                 105,
		"DEBIT_CARD_RESTRICTED":                106,
		"DEBIT_CARD_EXPIRED":                   107,
		"INCORRECT_MPIN":                       108,
		"INCORRECT_OTP":                        109,
		"OTP_EXPIRED":                          110,
		"MAX_OTP_RETRIES":                      111,
		"PSP_TIMEOUT":                          112,
		"REMITTER_INACTIVE_OR_DORMANT_ACCOUNT": 113,
		"VENDOR_EXTERNAL_ERROR":                114,
		"VENDOR_ENCRYPTION_ERROR":              115,
	}
)

func (x RegisterMobileResponse_Status) Enum() *RegisterMobileResponse_Status {
	p := new(RegisterMobileResponse_Status)
	*p = x
	return p
}

func (x RegisterMobileResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RegisterMobileResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[12].Descriptor()
}

func (RegisterMobileResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[12]
}

func (x RegisterMobileResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RegisterMobileResponse_Status.Descriptor instead.
func (RegisterMobileResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{14, 0}
}

// PinFlowType enum to get params as per pin flow type.
type GetPinFlowParametersRequest_PinFlowType int32

const (
	GetPinFlowParametersRequest_PIN_FLOW_TYPE_UNSPECIFIED GetPinFlowParametersRequest_PinFlowType = 0
	// It will be used to get params to set a PIN for the first time (or) Reset PIN if forgotten
	// User is allowed to set/reset the PIN with card PIN, Otp and Last 6 digits & Expiry of card.
	GetPinFlowParametersRequest_SET_PIN GetPinFlowParametersRequest_PinFlowType = 1
	// It will be used to get params to change PIN. To be used if user is aware of old PIN
	GetPinFlowParametersRequest_CHANGE_PIN GetPinFlowParametersRequest_PinFlowType = 2
)

// Enum value maps for GetPinFlowParametersRequest_PinFlowType.
var (
	GetPinFlowParametersRequest_PinFlowType_name = map[int32]string{
		0: "PIN_FLOW_TYPE_UNSPECIFIED",
		1: "SET_PIN",
		2: "CHANGE_PIN",
	}
	GetPinFlowParametersRequest_PinFlowType_value = map[string]int32{
		"PIN_FLOW_TYPE_UNSPECIFIED": 0,
		"SET_PIN":                   1,
		"CHANGE_PIN":                2,
	}
)

func (x GetPinFlowParametersRequest_PinFlowType) Enum() *GetPinFlowParametersRequest_PinFlowType {
	p := new(GetPinFlowParametersRequest_PinFlowType)
	*p = x
	return p
}

func (x GetPinFlowParametersRequest_PinFlowType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPinFlowParametersRequest_PinFlowType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[13].Descriptor()
}

func (GetPinFlowParametersRequest_PinFlowType) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[13]
}

func (x GetPinFlowParametersRequest_PinFlowType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPinFlowParametersRequest_PinFlowType.Descriptor instead.
func (GetPinFlowParametersRequest_PinFlowType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{15, 0}
}

// List of status codes returned
type GetPinFlowParametersResponse_Status int32

const (
	// Returned an success
	GetPinFlowParametersResponse_OK GetPinFlowParametersResponse_Status = 0
	// Requested entity i.e., upiAccountInfo is not found against account_id
	// One of the reasons could be due to the failure in epiFi's provisioning
	GetPinFlowParametersResponse_NOT_FOUND GetPinFlowParametersResponse_Status = 5
	// The actor does not have permission to execute the specified operation.
	// One of the reasons could be that Actor is not owner of the account
	GetPinFlowParametersResponse_PERMISSION_DENIED GetPinFlowParametersResponse_Status = 7
	// System faced internal errors while processing the request
	GetPinFlowParametersResponse_INTERNAL GetPinFlowParametersResponse_Status = 13
	// at least one debit card need to be activated before upi pin set attempt
	GetPinFlowParametersResponse_CARD_INACTIVED GetPinFlowParametersResponse_Status = 100
	// actor do not have any active card but one/more than one debit card are in suspended state.
	// Need to unsuspend card before pin set.
	GetPinFlowParametersResponse_CARD_SUSPENDED GetPinFlowParametersResponse_Status = 101
	// actor do not have any activated card, he need to first create and activate card before pin set.
	GetPinFlowParametersResponse_CARD_BLOCKED GetPinFlowParametersResponse_Status = 102
)

// Enum value maps for GetPinFlowParametersResponse_Status.
var (
	GetPinFlowParametersResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "NOT_FOUND",
		7:   "PERMISSION_DENIED",
		13:  "INTERNAL",
		100: "CARD_INACTIVED",
		101: "CARD_SUSPENDED",
		102: "CARD_BLOCKED",
	}
	GetPinFlowParametersResponse_Status_value = map[string]int32{
		"OK":                0,
		"NOT_FOUND":         5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
		"CARD_INACTIVED":    100,
		"CARD_SUSPENDED":    101,
		"CARD_BLOCKED":      102,
	}
)

func (x GetPinFlowParametersResponse_Status) Enum() *GetPinFlowParametersResponse_Status {
	p := new(GetPinFlowParametersResponse_Status)
	*p = x
	return p
}

func (x GetPinFlowParametersResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPinFlowParametersResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[14].Descriptor()
}

func (GetPinFlowParametersResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[14]
}

func (x GetPinFlowParametersResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPinFlowParametersResponse_Status.Descriptor instead.
func (GetPinFlowParametersResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{16, 0}
}

// List of status codes returned
type GetTransactionParametersResponse_Status int32

const (
	// Returned an success
	GetTransactionParametersResponse_OK GetTransactionParametersResponse_Status = 0
	// Requested entity i.e., upiAccountInfo is not found against account_id
	// One of the reasons could be due to the failure in epiFi's provisioning
	GetTransactionParametersResponse_NOT_FOUND GetTransactionParametersResponse_Status = 5
	// The actor does not have permission to execute the specified operation.
	// One of the reasons could be that Actor is not owner of the account
	GetTransactionParametersResponse_PERMISSION_DENIED GetTransactionParametersResponse_Status = 7
	// System faced internal errors while processing the request
	GetTransactionParametersResponse_INTERNAL GetTransactionParametersResponse_Status = 13
)

// Enum value maps for GetTransactionParametersResponse_Status.
var (
	GetTransactionParametersResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
	}
	GetTransactionParametersResponse_Status_value = map[string]int32{
		"OK":                0,
		"NOT_FOUND":         5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
	}
)

func (x GetTransactionParametersResponse_Status) Enum() *GetTransactionParametersResponse_Status {
	p := new(GetTransactionParametersResponse_Status)
	*p = x
	return p
}

func (x GetTransactionParametersResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetTransactionParametersResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[15].Descriptor()
}

func (GetTransactionParametersResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[15]
}

func (x GetTransactionParametersResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetTransactionParametersResponse_Status.Descriptor instead.
func (GetTransactionParametersResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{18, 0}
}

// List of status codes returned
type VerifyURNResponse_Status int32

const (
	// Returned an success
	VerifyURNResponse_OK VerifyURNResponse_Status = 0
	// unr passed in invalid, eg. missing mandatory fields
	VerifyURNResponse_INVALID_ARGUMENT VerifyURNResponse_Status = 3
	// System faced internal errors while processing the request
	VerifyURNResponse_INTERNAL VerifyURNResponse_Status = 13
	// urn's signature is invalid
	VerifyURNResponse_INVALID_SIGNATURE VerifyURNResponse_Status = 100
)

// Enum value maps for VerifyURNResponse_Status.
var (
	VerifyURNResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		100: "INVALID_SIGNATURE",
	}
	VerifyURNResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"INTERNAL":          13,
		"INVALID_SIGNATURE": 100,
	}
)

func (x VerifyURNResponse_Status) Enum() *VerifyURNResponse_Status {
	p := new(VerifyURNResponse_Status)
	*p = x
	return p
}

func (x VerifyURNResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyURNResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[16].Descriptor()
}

func (VerifyURNResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[16]
}

func (x VerifyURNResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyURNResponse_Status.Descriptor instead.
func (VerifyURNResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{20, 0}
}

// type of customer
type ChangePinRequest_CustomerType int32

const (
	// unspecified type
	ChangePinRequest_CUSTOMER_TYPE_UNSPECIFIED ChangePinRequest_CustomerType = 0
	// person type
	ChangePinRequest_PERSON ChangePinRequest_CustomerType = 1
	// entity type
	ChangePinRequest_ENTITY ChangePinRequest_CustomerType = 2
)

// Enum value maps for ChangePinRequest_CustomerType.
var (
	ChangePinRequest_CustomerType_name = map[int32]string{
		0: "CUSTOMER_TYPE_UNSPECIFIED",
		1: "PERSON",
		2: "ENTITY",
	}
	ChangePinRequest_CustomerType_value = map[string]int32{
		"CUSTOMER_TYPE_UNSPECIFIED": 0,
		"PERSON":                    1,
		"ENTITY":                    2,
	}
)

func (x ChangePinRequest_CustomerType) Enum() *ChangePinRequest_CustomerType {
	p := new(ChangePinRequest_CustomerType)
	*p = x
	return p
}

func (x ChangePinRequest_CustomerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChangePinRequest_CustomerType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[17].Descriptor()
}

func (ChangePinRequest_CustomerType) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[17]
}

func (x ChangePinRequest_CustomerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChangePinRequest_CustomerType.Descriptor instead.
func (ChangePinRequest_CustomerType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{21, 0}
}

type ChangePinResponse_Status int32

const (
	// Returned an success
	ChangePinResponse_OK ChangePinResponse_Status = 0
	// Indicates that arguments are problematic
	ChangePinResponse_INVALID_ARGUMENT ChangePinResponse_Status = 3
	// System faced internal errors while processing the request
	ChangePinResponse_INTERNAL ChangePinResponse_Status = 13
	// The request does not have valid authentication credentials for the
	// operation.
	// Reason: Invalid cred block - Either card details or Otp or both
	// that are part of cred block are wrong
	ChangePinResponse_UNAUTHENTICATED ChangePinResponse_Status = 16
	// bank server down
	ChangePinResponse_BANK_SERVER_DOWN ChangePinResponse_Status = 100
	// invalid upi pin
	ChangePinResponse_INVALID_UPI_PIN ChangePinResponse_Status = 101
	// MPIN NOT set/created by user
	ChangePinResponse_MPIN_NOT_SET ChangePinResponse_Status = 102
	// debit card not found
	ChangePinResponse_DEBIT_CARD_NOT_FOUND ChangePinResponse_Status = 103
	// debit card details used to
	// set pin is restricted
	ChangePinResponse_CARD_RESTRICTED ChangePinResponse_Status = 104
	// user's account is inactive/dormant
	ChangePinResponse_INACTIVE_ACCOUNT ChangePinResponse_Status = 105
	// error occurred due to psp timeout at vendor side.
	ChangePinResponse_PSP_TIMEOUT ChangePinResponse_Status = 106
	// Remitter's account is inactive/dormant
	ChangePinResponse_REMITTER_INACTIVE_OR_DORMANT_ACCOUNT ChangePinResponse_Status = 107
	// EXTERNAL_ERROR_AT_VENDOR
	ChangePinResponse_VENDOR_EXTERNAL_ERROR ChangePinResponse_Status = 108
	// Encryption Error at vendor
	ChangePinResponse_VENDOR_ENCRYPTION_ERROR ChangePinResponse_Status = 109
	// No of pin tries exceeded by the user
	ChangePinResponse_NUMBER_OF_PIN_TRIES_EXCEEDED ChangePinResponse_Status = 110
)

// Enum value maps for ChangePinResponse_Status.
var (
	ChangePinResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		16:  "UNAUTHENTICATED",
		100: "BANK_SERVER_DOWN",
		101: "INVALID_UPI_PIN",
		102: "MPIN_NOT_SET",
		103: "DEBIT_CARD_NOT_FOUND",
		104: "CARD_RESTRICTED",
		105: "INACTIVE_ACCOUNT",
		106: "PSP_TIMEOUT",
		107: "REMITTER_INACTIVE_OR_DORMANT_ACCOUNT",
		108: "VENDOR_EXTERNAL_ERROR",
		109: "VENDOR_ENCRYPTION_ERROR",
		110: "NUMBER_OF_PIN_TRIES_EXCEEDED",
	}
	ChangePinResponse_Status_value = map[string]int32{
		"OK":                                   0,
		"INVALID_ARGUMENT":                     3,
		"INTERNAL":                             13,
		"UNAUTHENTICATED":                      16,
		"BANK_SERVER_DOWN":                     100,
		"INVALID_UPI_PIN":                      101,
		"MPIN_NOT_SET":                         102,
		"DEBIT_CARD_NOT_FOUND":                 103,
		"CARD_RESTRICTED":                      104,
		"INACTIVE_ACCOUNT":                     105,
		"PSP_TIMEOUT":                          106,
		"REMITTER_INACTIVE_OR_DORMANT_ACCOUNT": 107,
		"VENDOR_EXTERNAL_ERROR":                108,
		"VENDOR_ENCRYPTION_ERROR":              109,
		"NUMBER_OF_PIN_TRIES_EXCEEDED":         110,
	}
)

func (x ChangePinResponse_Status) Enum() *ChangePinResponse_Status {
	p := new(ChangePinResponse_Status)
	*p = x
	return p
}

func (x ChangePinResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChangePinResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[18].Descriptor()
}

func (ChangePinResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[18]
}

func (x ChangePinResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChangePinResponse_Status.Descriptor instead.
func (ChangePinResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{22, 0}
}

// indicates if the payment is intent based or not
// for intent based payments the initiation mode will be "04"
// for dynamic QR the initiation mode will be "02"
type GenerateURNRequest_InitiationMode int32

const (
	// unspecified
	GenerateURNRequest_INITIATION_MODE_UNSPECIFIED GenerateURNRequest_InitiationMode = 0
	// QR code based payment
	GenerateURNRequest_QR_CODE GenerateURNRequest_InitiationMode = 1
	// intent based payment
	GenerateURNRequest_INTENT GenerateURNRequest_InitiationMode = 2
)

// Enum value maps for GenerateURNRequest_InitiationMode.
var (
	GenerateURNRequest_InitiationMode_name = map[int32]string{
		0: "INITIATION_MODE_UNSPECIFIED",
		1: "QR_CODE",
		2: "INTENT",
	}
	GenerateURNRequest_InitiationMode_value = map[string]int32{
		"INITIATION_MODE_UNSPECIFIED": 0,
		"QR_CODE":                     1,
		"INTENT":                      2,
	}
)

func (x GenerateURNRequest_InitiationMode) Enum() *GenerateURNRequest_InitiationMode {
	p := new(GenerateURNRequest_InitiationMode)
	*p = x
	return p
}

func (x GenerateURNRequest_InitiationMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerateURNRequest_InitiationMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[19].Descriptor()
}

func (GenerateURNRequest_InitiationMode) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[19]
}

func (x GenerateURNRequest_InitiationMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerateURNRequest_InitiationMode.Descriptor instead.
func (GenerateURNRequest_InitiationMode) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{23, 0}
}

// source of order creation
type GenerateURNRequest_Source int32

const (
	// unspecified
	GenerateURNRequest_SOURCE_UNSPECIFIED GenerateURNRequest_Source = 0
	// denotes the order creation was initiated by add funds flow during onboarding
	GenerateURNRequest_ONBOARD_ADD_FUNDS GenerateURNRequest_Source = 1
	// denotes the order creation was initiated by transfer in flow
	GenerateURNRequest_TRANSFER_IN GenerateURNRequest_Source = 2
	// Signifies add funds order was created from home screen
	GenerateURNRequest_HOME GenerateURNRequest_Source = 3
	// Signifies order was created from account details screen
	GenerateURNRequest_ACCOUNT_DETAILS GenerateURNRequest_Source = 4
	// Signifies order was created from account summary screen
	GenerateURNRequest_ACCOUNT_SUMMARY GenerateURNRequest_Source = 5
	// Signifies order was created from referrals screen
	GenerateURNRequest_REFERRALS GenerateURNRequest_Source = 6
	// Signifies order created from Home screen's persistent Add funds button
	GenerateURNRequest_HOME_PERSISTENT_CTA GenerateURNRequest_Source = 7
	// Signifies order was created from Deposit creation flow, if funds were insufficient
	// for deposit creation
	GenerateURNRequest_DEPOSIT_CREATION GenerateURNRequest_Source = 8
	// Signifies order was created from Bonus jar creation flow, if funds were insufficient
	// for bonus jar creation
	GenerateURNRequest_BONUS_JAR_CREATION GenerateURNRequest_Source = 9
	// Signifies order creation was initiated by pre-funding add funds flow during onboarding
	GenerateURNRequest_ONBOARD_ADD_FUNDS_PRE_FUNDING GenerateURNRequest_Source = 10
)

// Enum value maps for GenerateURNRequest_Source.
var (
	GenerateURNRequest_Source_name = map[int32]string{
		0:  "SOURCE_UNSPECIFIED",
		1:  "ONBOARD_ADD_FUNDS",
		2:  "TRANSFER_IN",
		3:  "HOME",
		4:  "ACCOUNT_DETAILS",
		5:  "ACCOUNT_SUMMARY",
		6:  "REFERRALS",
		7:  "HOME_PERSISTENT_CTA",
		8:  "DEPOSIT_CREATION",
		9:  "BONUS_JAR_CREATION",
		10: "ONBOARD_ADD_FUNDS_PRE_FUNDING",
	}
	GenerateURNRequest_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED":            0,
		"ONBOARD_ADD_FUNDS":             1,
		"TRANSFER_IN":                   2,
		"HOME":                          3,
		"ACCOUNT_DETAILS":               4,
		"ACCOUNT_SUMMARY":               5,
		"REFERRALS":                     6,
		"HOME_PERSISTENT_CTA":           7,
		"DEPOSIT_CREATION":              8,
		"BONUS_JAR_CREATION":            9,
		"ONBOARD_ADD_FUNDS_PRE_FUNDING": 10,
	}
)

func (x GenerateURNRequest_Source) Enum() *GenerateURNRequest_Source {
	p := new(GenerateURNRequest_Source)
	*p = x
	return p
}

func (x GenerateURNRequest_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerateURNRequest_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[20].Descriptor()
}

func (GenerateURNRequest_Source) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[20]
}

func (x GenerateURNRequest_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerateURNRequest_Source.Descriptor instead.
func (GenerateURNRequest_Source) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{23, 1}
}

type GenerateURNResponse_Status int32

const (
	// Returned an success
	GenerateURNResponse_OK GenerateURNResponse_Status = 0
	// invalid argument passed in the request
	GenerateURNResponse_INVALID_ARGUMENT GenerateURNResponse_Status = 3
	// System faced internal errors while processing the request
	GenerateURNResponse_INTERNAL GenerateURNResponse_Status = 13
	// add funds failure due to account duration check failed for a min kyc kyc user
	GenerateURNResponse_ADD_FUNDS_MIN_KYC_ACCOUNT_DURATION_CHECK_FAILED GenerateURNResponse_Status = 111
	// add funds failure due to maximum credit limit check failed for a min kyc kyc user
	GenerateURNResponse_ADD_FUNDS_MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILED GenerateURNResponse_Status = 112
	// add funds failure due to maximum balance check failed for a min kyc kyc user
	GenerateURNResponse_ADD_FUNDS_MIN_KYC_MAX_BALANCE_CHECK_FAILED GenerateURNResponse_Status = 113
)

// Enum value maps for GenerateURNResponse_Status.
var (
	GenerateURNResponse_Status_name = map[int32]string{
		0:   "OK",
		3:   "INVALID_ARGUMENT",
		13:  "INTERNAL",
		111: "ADD_FUNDS_MIN_KYC_ACCOUNT_DURATION_CHECK_FAILED",
		112: "ADD_FUNDS_MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILED",
		113: "ADD_FUNDS_MIN_KYC_MAX_BALANCE_CHECK_FAILED",
	}
	GenerateURNResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
		"ADD_FUNDS_MIN_KYC_ACCOUNT_DURATION_CHECK_FAILED": 111,
		"ADD_FUNDS_MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILED": 112,
		"ADD_FUNDS_MIN_KYC_MAX_BALANCE_CHECK_FAILED":      113,
	}
)

func (x GenerateURNResponse_Status) Enum() *GenerateURNResponse_Status {
	p := new(GenerateURNResponse_Status)
	*p = x
	return p
}

func (x GenerateURNResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerateURNResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[21].Descriptor()
}

func (GenerateURNResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[21]
}

func (x GenerateURNResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerateURNResponse_Status.Descriptor instead.
func (GenerateURNResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{24, 0}
}

type GetUpiSetupStatusResponse_Status int32

const (
	// Success
	GetUpiSetupStatusResponse_OK GetUpiSetupStatusResponse_Status = 0
	// Record not found
	GetUpiSetupStatusResponse_NOT_FOUND GetUpiSetupStatusResponse_Status = 5
	// Internal error code
	GetUpiSetupStatusResponse_INTERNAL GetUpiSetupStatusResponse_Status = 13
)

// Enum value maps for GetUpiSetupStatusResponse_Status.
var (
	GetUpiSetupStatusResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetUpiSetupStatusResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetUpiSetupStatusResponse_Status) Enum() *GetUpiSetupStatusResponse_Status {
	p := new(GetUpiSetupStatusResponse_Status)
	*p = x
	return p
}

func (x GetUpiSetupStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetUpiSetupStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[22].Descriptor()
}

func (GetUpiSetupStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[22]
}

func (x GetUpiSetupStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetUpiSetupStatusResponse_Status.Descriptor instead.
func (GetUpiSetupStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{26, 0}
}

type DisableOrEnableVPARequest_RequestType int32

const (
	DisableOrEnableVPARequest_REQUEST_TYPE_UNSPECIFIED DisableOrEnableVPARequest_RequestType = 0
	// to enable the vpa
	DisableOrEnableVPARequest_ENABLE DisableOrEnableVPARequest_RequestType = 1
	// to disable the vpa
	DisableOrEnableVPARequest_DISABLE DisableOrEnableVPARequest_RequestType = 2
)

// Enum value maps for DisableOrEnableVPARequest_RequestType.
var (
	DisableOrEnableVPARequest_RequestType_name = map[int32]string{
		0: "REQUEST_TYPE_UNSPECIFIED",
		1: "ENABLE",
		2: "DISABLE",
	}
	DisableOrEnableVPARequest_RequestType_value = map[string]int32{
		"REQUEST_TYPE_UNSPECIFIED": 0,
		"ENABLE":                   1,
		"DISABLE":                  2,
	}
)

func (x DisableOrEnableVPARequest_RequestType) Enum() *DisableOrEnableVPARequest_RequestType {
	p := new(DisableOrEnableVPARequest_RequestType)
	*p = x
	return p
}

func (x DisableOrEnableVPARequest_RequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisableOrEnableVPARequest_RequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[23].Descriptor()
}

func (DisableOrEnableVPARequest_RequestType) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[23]
}

func (x DisableOrEnableVPARequest_RequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisableOrEnableVPARequest_RequestType.Descriptor instead.
func (DisableOrEnableVPARequest_RequestType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{27, 0}
}

type DisableOrEnableVPAResponse_Status int32

const (
	// Returned an success
	DisableOrEnableVPAResponse_OK DisableOrEnableVPAResponse_Status = 0
	// invalid argument passed by the client
	DisableOrEnableVPAResponse_INVALID_ARGUMENT DisableOrEnableVPAResponse_Status = 3
	// pi corresponding to the vpa is not present
	DisableOrEnableVPAResponse_RECORD_NOT_FOUND DisableOrEnableVPAResponse_Status = 5
	// vpa dose not belong to the actor
	DisableOrEnableVPAResponse_PERMISSION_DENIED DisableOrEnableVPAResponse_Status = 7
	// System faced internal errors while processing the request
	DisableOrEnableVPAResponse_INTERNAL DisableOrEnableVPAResponse_Status = 13
	// request has been already processed. i.e the vpa is already enabled/disabled
	DisableOrEnableVPAResponse_ALREADY_PROCESSED DisableOrEnableVPAResponse_Status = 50
)

// Enum value maps for DisableOrEnableVPAResponse_Status.
var (
	DisableOrEnableVPAResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
		50: "ALREADY_PROCESSED",
	}
	DisableOrEnableVPAResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"RECORD_NOT_FOUND":  5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
		"ALREADY_PROCESSED": 50,
	}
)

func (x DisableOrEnableVPAResponse_Status) Enum() *DisableOrEnableVPAResponse_Status {
	p := new(DisableOrEnableVPAResponse_Status)
	*p = x
	return p
}

func (x DisableOrEnableVPAResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DisableOrEnableVPAResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[24].Descriptor()
}

func (DisableOrEnableVPAResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[24]
}

func (x DisableOrEnableVPAResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DisableOrEnableVPAResponse_Status.Descriptor instead.
func (DisableOrEnableVPAResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{28, 0}
}

type PinStatusResponse_Status int32

const (
	// Returned an success
	PinStatusResponse_OK PinStatusResponse_Status = 0
	// System faced internal errors while processing the request
	PinStatusResponse_INTERNAL PinStatusResponse_Status = 13
)

// Enum value maps for PinStatusResponse_Status.
var (
	PinStatusResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	PinStatusResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x PinStatusResponse_Status) Enum() *PinStatusResponse_Status {
	p := new(PinStatusResponse_Status)
	*p = x
	return p
}

func (x PinStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PinStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[25].Descriptor()
}

func (PinStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[25]
}

func (x PinStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PinStatusResponse_Status.Descriptor instead.
func (PinStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{30, 0}
}

type GetAccountPinInfosResponse_Status int32

const (
	// Returned an success
	GetAccountPinInfosResponse_OK GetAccountPinInfosResponse_Status = 0
	// record not found
	GetAccountPinInfosResponse_RECORD_NOT_FOUND GetAccountPinInfosResponse_Status = 5
	// System faced internal errors while processing the request
	GetAccountPinInfosResponse_INTERNAL GetAccountPinInfosResponse_Status = 13
)

// Enum value maps for GetAccountPinInfosResponse_Status.
var (
	GetAccountPinInfosResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetAccountPinInfosResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetAccountPinInfosResponse_Status) Enum() *GetAccountPinInfosResponse_Status {
	p := new(GetAccountPinInfosResponse_Status)
	*p = x
	return p
}

func (x GetAccountPinInfosResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAccountPinInfosResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[26].Descriptor()
}

func (GetAccountPinInfosResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[26]
}

func (x GetAccountPinInfosResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAccountPinInfosResponse_Status.Descriptor instead.
func (GetAccountPinInfosResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{32, 0}
}

type GetLatestAccountPinInfosResponse_Status int32

const (
	// Returned an success
	GetLatestAccountPinInfosResponse_OK GetLatestAccountPinInfosResponse_Status = 0
	// record not found
	GetLatestAccountPinInfosResponse_RECORD_NOT_FOUND GetLatestAccountPinInfosResponse_Status = 5
	// System faced internal errors while processing the request
	GetLatestAccountPinInfosResponse_INTERNAL GetLatestAccountPinInfosResponse_Status = 13
)

// Enum value maps for GetLatestAccountPinInfosResponse_Status.
var (
	GetLatestAccountPinInfosResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetLatestAccountPinInfosResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetLatestAccountPinInfosResponse_Status) Enum() *GetLatestAccountPinInfosResponse_Status {
	p := new(GetLatestAccountPinInfosResponse_Status)
	*p = x
	return p
}

func (x GetLatestAccountPinInfosResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetLatestAccountPinInfosResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[27].Descriptor()
}

func (GetLatestAccountPinInfosResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[27]
}

func (x GetLatestAccountPinInfosResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetLatestAccountPinInfosResponse_Status.Descriptor instead.
func (GetLatestAccountPinInfosResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{34, 0}
}

type CheckVerifiedMerchantResponse_Status int32

const (
	// Returned success if merchant is in verified merchant list
	CheckVerifiedMerchantResponse_OK CheckVerifiedMerchantResponse_Status = 0
	// record not found if merchant is not in verified merchant list
	CheckVerifiedMerchantResponse_RECORD_NOT_FOUND CheckVerifiedMerchantResponse_Status = 5
	// System faced internal errors while processing the request
	CheckVerifiedMerchantResponse_INTERNAL CheckVerifiedMerchantResponse_Status = 13
)

// Enum value maps for CheckVerifiedMerchantResponse_Status.
var (
	CheckVerifiedMerchantResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	CheckVerifiedMerchantResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x CheckVerifiedMerchantResponse_Status) Enum() *CheckVerifiedMerchantResponse_Status {
	p := new(CheckVerifiedMerchantResponse_Status)
	*p = x
	return p
}

func (x CheckVerifiedMerchantResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckVerifiedMerchantResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[28].Descriptor()
}

func (CheckVerifiedMerchantResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[28]
}

func (x CheckVerifiedMerchantResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckVerifiedMerchantResponse_Status.Descriptor instead.
func (CheckVerifiedMerchantResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{36, 0}
}

type GetVpaMerchantInfoResponse_Status int32

const (
	// Returned success if merchant is found
	GetVpaMerchantInfoResponse_OK GetVpaMerchantInfoResponse_Status = 0
	// record not found if merchant is not found
	GetVpaMerchantInfoResponse_RECORD_NOT_FOUND GetVpaMerchantInfoResponse_Status = 5
	// System faced internal errors while processing the request
	GetVpaMerchantInfoResponse_INTERNAL GetVpaMerchantInfoResponse_Status = 13
)

// Enum value maps for GetVpaMerchantInfoResponse_Status.
var (
	GetVpaMerchantInfoResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetVpaMerchantInfoResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetVpaMerchantInfoResponse_Status) Enum() *GetVpaMerchantInfoResponse_Status {
	p := new(GetVpaMerchantInfoResponse_Status)
	*p = x
	return p
}

func (x GetVpaMerchantInfoResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetVpaMerchantInfoResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[29].Descriptor()
}

func (GetVpaMerchantInfoResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[29]
}

func (x GetVpaMerchantInfoResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetVpaMerchantInfoResponse_Status.Descriptor instead.
func (GetVpaMerchantInfoResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{38, 0}
}

type GetPinInfosByAccountIdRequest_Action int32

const (
	// unspecified
	GetPinInfosByAccountIdRequest_ACTION_UNSPECIFIED GetPinInfosByAccountIdRequest_Action = 0
	// pin set
	// pin was set for the first time by the user
	GetPinInfosByAccountIdRequest_PIN_SET GetPinInfosByAccountIdRequest_Action = 1
	// pin was changed by the user
	GetPinInfosByAccountIdRequest_PIN_CHANGE GetPinInfosByAccountIdRequest_Action = 2
	// pin was reset by the user
	// eg. forget pin
	GetPinInfosByAccountIdRequest_PIN_RESET GetPinInfosByAccountIdRequest_Action = 3
)

// Enum value maps for GetPinInfosByAccountIdRequest_Action.
var (
	GetPinInfosByAccountIdRequest_Action_name = map[int32]string{
		0: "ACTION_UNSPECIFIED",
		1: "PIN_SET",
		2: "PIN_CHANGE",
		3: "PIN_RESET",
	}
	GetPinInfosByAccountIdRequest_Action_value = map[string]int32{
		"ACTION_UNSPECIFIED": 0,
		"PIN_SET":            1,
		"PIN_CHANGE":         2,
		"PIN_RESET":          3,
	}
)

func (x GetPinInfosByAccountIdRequest_Action) Enum() *GetPinInfosByAccountIdRequest_Action {
	p := new(GetPinInfosByAccountIdRequest_Action)
	*p = x
	return p
}

func (x GetPinInfosByAccountIdRequest_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPinInfosByAccountIdRequest_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[30].Descriptor()
}

func (GetPinInfosByAccountIdRequest_Action) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[30]
}

func (x GetPinInfosByAccountIdRequest_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPinInfosByAccountIdRequest_Action.Descriptor instead.
func (GetPinInfosByAccountIdRequest_Action) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{39, 0}
}

type GetPinInfosByAccountIdRequest_Status int32

const (
	GetPinInfosByAccountIdRequest_STATUS_UNSPECIFIED GetPinInfosByAccountIdRequest_Status = 0
	// pin set/reset is initiated
	GetPinInfosByAccountIdRequest_INITIATED GetPinInfosByAccountIdRequest_Status = 1
	// pin set/reset is successful
	GetPinInfosByAccountIdRequest_SUCCESS GetPinInfosByAccountIdRequest_Status = 2
	// pin set/reset is fail
	GetPinInfosByAccountIdRequest_FAILURE GetPinInfosByAccountIdRequest_Status = 3
)

// Enum value maps for GetPinInfosByAccountIdRequest_Status.
var (
	GetPinInfosByAccountIdRequest_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "INITIATED",
		2: "SUCCESS",
		3: "FAILURE",
	}
	GetPinInfosByAccountIdRequest_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"INITIATED":          1,
		"SUCCESS":            2,
		"FAILURE":            3,
	}
)

func (x GetPinInfosByAccountIdRequest_Status) Enum() *GetPinInfosByAccountIdRequest_Status {
	p := new(GetPinInfosByAccountIdRequest_Status)
	*p = x
	return p
}

func (x GetPinInfosByAccountIdRequest_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPinInfosByAccountIdRequest_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[31].Descriptor()
}

func (GetPinInfosByAccountIdRequest_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[31]
}

func (x GetPinInfosByAccountIdRequest_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPinInfosByAccountIdRequest_Status.Descriptor instead.
func (GetPinInfosByAccountIdRequest_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{39, 1}
}

type GetPinInfosByAccountIdResponse_Status int32

const (
	GetPinInfosByAccountIdResponse_OK GetPinInfosByAccountIdResponse_Status = 0
	// request parameters invalid.
	GetPinInfosByAccountIdResponse_INVALID_ARGUMENT GetPinInfosByAccountIdResponse_Status = 3
	// record not found if no pin infos found for account id
	GetPinInfosByAccountIdResponse_RECORD_NOT_FOUND GetPinInfosByAccountIdResponse_Status = 5
	// internal error while processing the request
	GetPinInfosByAccountIdResponse_INTERNAL GetPinInfosByAccountIdResponse_Status = 13
)

// Enum value maps for GetPinInfosByAccountIdResponse_Status.
var (
	GetPinInfosByAccountIdResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetPinInfosByAccountIdResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetPinInfosByAccountIdResponse_Status) Enum() *GetPinInfosByAccountIdResponse_Status {
	p := new(GetPinInfosByAccountIdResponse_Status)
	*p = x
	return p
}

func (x GetPinInfosByAccountIdResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPinInfosByAccountIdResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[32].Descriptor()
}

func (GetPinInfosByAccountIdResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[32]
}

func (x GetPinInfosByAccountIdResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPinInfosByAccountIdResponse_Status.Descriptor instead.
func (GetPinInfosByAccountIdResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{40, 0}
}

type PostUserActivityResponse_Status int32

const (
	// Returned success if activity processed successfully
	PostUserActivityResponse_OK PostUserActivityResponse_Status = 0
	// record not found if account not found
	PostUserActivityResponse_RECORD_NOT_FOUND PostUserActivityResponse_Status = 5
	// System faced internal errors while processing the request
	PostUserActivityResponse_INTERNAL PostUserActivityResponse_Status = 13
)

// Enum value maps for PostUserActivityResponse_Status.
var (
	PostUserActivityResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	PostUserActivityResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x PostUserActivityResponse_Status) Enum() *PostUserActivityResponse_Status {
	p := new(PostUserActivityResponse_Status)
	*p = x
	return p
}

func (x PostUserActivityResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PostUserActivityResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[33].Descriptor()
}

func (PostUserActivityResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[33]
}

func (x PostUserActivityResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PostUserActivityResponse_Status.Descriptor instead.
func (PostUserActivityResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{42, 0}
}

type ValidateSecurePinResponse_Status int32

const (
	ValidateSecurePinResponse_OK ValidateSecurePinResponse_Status = 0
	// record not found if account not found
	ValidateSecurePinResponse_RECORD_NOT_FOUND ValidateSecurePinResponse_Status = 5
	// System faced internal errors while processing the request
	ValidateSecurePinResponse_INTERNAL ValidateSecurePinResponse_Status = 13
	// Incorrect UPI pin
	ValidateSecurePinResponse_INVALID_SECURE_PIN ValidateSecurePinResponse_Status = 100
	// Secure pin validation retries exhausted
	ValidateSecurePinResponse_PIN_RETRIES_EXCEEDED ValidateSecurePinResponse_Status = 101
	// business failures
	ValidateSecurePinResponse_BUSINESS_FAILURE ValidateSecurePinResponse_Status = 102
	// EXTERNAL_ERROR_AT_VENDOR
	ValidateSecurePinResponse_VENDOR_EXTERNAL_ERROR ValidateSecurePinResponse_Status = 103
)

// Enum value maps for ValidateSecurePinResponse_Status.
var (
	ValidateSecurePinResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "RECORD_NOT_FOUND",
		13:  "INTERNAL",
		100: "INVALID_SECURE_PIN",
		101: "PIN_RETRIES_EXCEEDED",
		102: "BUSINESS_FAILURE",
		103: "VENDOR_EXTERNAL_ERROR",
	}
	ValidateSecurePinResponse_Status_value = map[string]int32{
		"OK":                    0,
		"RECORD_NOT_FOUND":      5,
		"INTERNAL":              13,
		"INVALID_SECURE_PIN":    100,
		"PIN_RETRIES_EXCEEDED":  101,
		"BUSINESS_FAILURE":      102,
		"VENDOR_EXTERNAL_ERROR": 103,
	}
)

func (x ValidateSecurePinResponse_Status) Enum() *ValidateSecurePinResponse_Status {
	p := new(ValidateSecurePinResponse_Status)
	*p = x
	return p
}

func (x ValidateSecurePinResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValidateSecurePinResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[34].Descriptor()
}

func (ValidateSecurePinResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[34]
}

func (x ValidateSecurePinResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValidateSecurePinResponse_Status.Descriptor instead.
func (ValidateSecurePinResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{44, 0}
}

type RaiseComplaintRequest_RaiseComplaintType int32

const (
	RaiseComplaintRequest_RAISE_COMPLAINT_TYPE_UNSPECIFIED RaiseComplaintRequest_RaiseComplaintType = 0
	// request of type COMPLAINT is only applicable to be raised by payer
	RaiseComplaintRequest_COMPLAINT RaiseComplaintRequest_RaiseComplaintType = 1
)

// Enum value maps for RaiseComplaintRequest_RaiseComplaintType.
var (
	RaiseComplaintRequest_RaiseComplaintType_name = map[int32]string{
		0: "RAISE_COMPLAINT_TYPE_UNSPECIFIED",
		1: "COMPLAINT",
	}
	RaiseComplaintRequest_RaiseComplaintType_value = map[string]int32{
		"RAISE_COMPLAINT_TYPE_UNSPECIFIED": 0,
		"COMPLAINT":                        1,
	}
)

func (x RaiseComplaintRequest_RaiseComplaintType) Enum() *RaiseComplaintRequest_RaiseComplaintType {
	p := new(RaiseComplaintRequest_RaiseComplaintType)
	*p = x
	return p
}

func (x RaiseComplaintRequest_RaiseComplaintType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RaiseComplaintRequest_RaiseComplaintType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[35].Descriptor()
}

func (RaiseComplaintRequest_RaiseComplaintType) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[35]
}

func (x RaiseComplaintRequest_RaiseComplaintType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RaiseComplaintRequest_RaiseComplaintType.Descriptor instead.
func (RaiseComplaintRequest_RaiseComplaintType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{45, 0}
}

type RaiseComplaintResponse_Status int32

const (
	RaiseComplaintResponse_OK RaiseComplaintResponse_Status = 0
	// System faced internal errors while processing the request
	RaiseComplaintResponse_INTERNAL RaiseComplaintResponse_Status = 13
	RaiseComplaintResponse_FAILED   RaiseComplaintResponse_Status = 100
	// complaint is getting processed
	RaiseComplaintResponse_IN_PROGRESS RaiseComplaintResponse_Status = 51
)

// Enum value maps for RaiseComplaintResponse_Status.
var (
	RaiseComplaintResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "FAILED",
		51:  "IN_PROGRESS",
	}
	RaiseComplaintResponse_Status_value = map[string]int32{
		"OK":          0,
		"INTERNAL":    13,
		"FAILED":      100,
		"IN_PROGRESS": 51,
	}
)

func (x RaiseComplaintResponse_Status) Enum() *RaiseComplaintResponse_Status {
	p := new(RaiseComplaintResponse_Status)
	*p = x
	return p
}

func (x RaiseComplaintResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RaiseComplaintResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[36].Descriptor()
}

func (RaiseComplaintResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[36]
}

func (x RaiseComplaintResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RaiseComplaintResponse_Status.Descriptor instead.
func (RaiseComplaintResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{46, 0}
}

type CheckComplaintStatusResponse_Status int32

const (
	CheckComplaintStatusResponse_OK CheckComplaintStatusResponse_Status = 0
	// if no complaint registered against txn-id
	CheckComplaintStatusResponse_RECORD_NOT_FOUND CheckComplaintStatusResponse_Status = 5
	// System faced internal errors while processing the request
	CheckComplaintStatusResponse_INTERNAL CheckComplaintStatusResponse_Status = 13
	CheckComplaintStatusResponse_FAILED   CheckComplaintStatusResponse_Status = 100
	// complaint is getting processed
	CheckComplaintStatusResponse_IN_PROGRESS CheckComplaintStatusResponse_Status = 51
)

// Enum value maps for CheckComplaintStatusResponse_Status.
var (
	CheckComplaintStatusResponse_Status_name = map[int32]string{
		0:   "OK",
		5:   "RECORD_NOT_FOUND",
		13:  "INTERNAL",
		100: "FAILED",
		51:  "IN_PROGRESS",
	}
	CheckComplaintStatusResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
		"FAILED":           100,
		"IN_PROGRESS":      51,
	}
)

func (x CheckComplaintStatusResponse_Status) Enum() *CheckComplaintStatusResponse_Status {
	p := new(CheckComplaintStatusResponse_Status)
	*p = x
	return p
}

func (x CheckComplaintStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckComplaintStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[37].Descriptor()
}

func (CheckComplaintStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[37]
}

func (x CheckComplaintStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckComplaintStatusResponse_Status.Descriptor instead.
func (CheckComplaintStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{48, 0}
}

type ChangeUpiPinSetStateResponse_Status int32

const (
	ChangeUpiPinSetStateResponse_OK ChangeUpiPinSetStateResponse_Status = 0
	// record not found if account not found
	ChangeUpiPinSetStateResponse_RECORD_NOT_FOUND ChangeUpiPinSetStateResponse_Status = 5
	// System faced internal errors while processing the request
	ChangeUpiPinSetStateResponse_INTERNAL ChangeUpiPinSetStateResponse_Status = 13
)

// Enum value maps for ChangeUpiPinSetStateResponse_Status.
var (
	ChangeUpiPinSetStateResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	ChangeUpiPinSetStateResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x ChangeUpiPinSetStateResponse_Status) Enum() *ChangeUpiPinSetStateResponse_Status {
	p := new(ChangeUpiPinSetStateResponse_Status)
	*p = x
	return p
}

func (x ChangeUpiPinSetStateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChangeUpiPinSetStateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[38].Descriptor()
}

func (ChangeUpiPinSetStateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[38]
}

func (x ChangeUpiPinSetStateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChangeUpiPinSetStateResponse_Status.Descriptor instead.
func (ChangeUpiPinSetStateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{50, 0}
}

type GetVerifiedVpasByPhoneNumberResponse_Status int32

const (
	GetVerifiedVpasByPhoneNumberResponse_OK GetVerifiedVpasByPhoneNumberResponse_Status = 0
	// record not found
	GetVerifiedVpasByPhoneNumberResponse_RECORD_NOT_FOUND GetVerifiedVpasByPhoneNumberResponse_Status = 5
	// System faced internal errors while processing the request
	GetVerifiedVpasByPhoneNumberResponse_INTERNAL GetVerifiedVpasByPhoneNumberResponse_Status = 13
)

// Enum value maps for GetVerifiedVpasByPhoneNumberResponse_Status.
var (
	GetVerifiedVpasByPhoneNumberResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetVerifiedVpasByPhoneNumberResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetVerifiedVpasByPhoneNumberResponse_Status) Enum() *GetVerifiedVpasByPhoneNumberResponse_Status {
	p := new(GetVerifiedVpasByPhoneNumberResponse_Status)
	*p = x
	return p
}

func (x GetVerifiedVpasByPhoneNumberResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetVerifiedVpasByPhoneNumberResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[39].Descriptor()
}

func (GetVerifiedVpasByPhoneNumberResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[39]
}

func (x GetVerifiedVpasByPhoneNumberResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetVerifiedVpasByPhoneNumberResponse_Status.Descriptor instead.
func (GetVerifiedVpasByPhoneNumberResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{52, 0}
}

type ValidateAddressAndCreatePiRequest_VpaValidationMode int32

const (
	// Default mode - uses strict validation for VPA verification
	// Strict validation mode:
	// - Returns errors for any validation failures including:
	//   - Invalid VPAs
	//   - No active internal UPI PI found ( due to disabled UPIs or device AFU )
	//   - Used in scenarios where VPA needs to be fully validated ( by calling
	//     ValidateAddress RPC ) before proceeding with a transaction
	//   - Validation is done through vendor (bank) that returns VPA details and
	//     performs validation checks
	ValidateAddressAndCreatePiRequest_VPA_VALIDATION_MODE_UNSPECIFIED ValidateAddressAndCreatePiRequest_VpaValidationMode = 0
	// Lenient validation mode - allows PI creation without strict validation
	// requirements
	// Use cases:
	// 1. Inbound transaction notifications and remitter info backfill when no active internal UPI PI exists:
	//   - Skips vendor validation entirely when no active UPI PI is found
	//   - Ensures transaction processing can continue with more PIs in create stated and less TRANSIENT_FAILURE
	//
	// Validation behavior:
	// - If active UPI PI exists: Validates through vendor
	// - If no active UPI PI exists:
	//   - Skips vendor validation completely
	//   - Creates PI with just the VPA information
	ValidateAddressAndCreatePiRequest_VPA_VALIDATION_MODE_LENIENT ValidateAddressAndCreatePiRequest_VpaValidationMode = 1
)

// Enum value maps for ValidateAddressAndCreatePiRequest_VpaValidationMode.
var (
	ValidateAddressAndCreatePiRequest_VpaValidationMode_name = map[int32]string{
		0: "VPA_VALIDATION_MODE_UNSPECIFIED",
		1: "VPA_VALIDATION_MODE_LENIENT",
	}
	ValidateAddressAndCreatePiRequest_VpaValidationMode_value = map[string]int32{
		"VPA_VALIDATION_MODE_UNSPECIFIED": 0,
		"VPA_VALIDATION_MODE_LENIENT":     1,
	}
)

func (x ValidateAddressAndCreatePiRequest_VpaValidationMode) Enum() *ValidateAddressAndCreatePiRequest_VpaValidationMode {
	p := new(ValidateAddressAndCreatePiRequest_VpaValidationMode)
	*p = x
	return p
}

func (x ValidateAddressAndCreatePiRequest_VpaValidationMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValidateAddressAndCreatePiRequest_VpaValidationMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[40].Descriptor()
}

func (ValidateAddressAndCreatePiRequest_VpaValidationMode) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[40]
}

func (x ValidateAddressAndCreatePiRequest_VpaValidationMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValidateAddressAndCreatePiRequest_VpaValidationMode.Descriptor instead.
func (ValidateAddressAndCreatePiRequest_VpaValidationMode) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{53, 0}
}

type ValidateAddressAndCreatePiResponse_Status int32

const (
	// Returned an success
	ValidateAddressAndCreatePiResponse_OK ValidateAddressAndCreatePiResponse_Status = 0
	// System faced internal errors while processing the request
	ValidateAddressAndCreatePiResponse_INTERNAL ValidateAddressAndCreatePiResponse_Status = 13
	// the VPA Id is invalid
	ValidateAddressAndCreatePiResponse_INVALID_VPA ValidateAddressAndCreatePiResponse_Status = 100
	// vpa restricted by the customer
	ValidateAddressAndCreatePiResponse_VPA_RESTRICTED ValidateAddressAndCreatePiResponse_Status = 101
	// PSP not available at the moment
	ValidateAddressAndCreatePiResponse_PSP_NOT_AVAILABLE ValidateAddressAndCreatePiResponse_Status = 102
	// Suspected Fraud, Decline / Transactions declined
	// based on risk score by beneficiary
	ValidateAddressAndCreatePiResponse_SUSPECTED_FRAUD ValidateAddressAndCreatePiResponse_Status = 103
	// PSP not registered
	ValidateAddressAndCreatePiResponse_PSP_NOT_REGISTERED ValidateAddressAndCreatePiResponse_Status = 104
)

// Enum value maps for ValidateAddressAndCreatePiResponse_Status.
var (
	ValidateAddressAndCreatePiResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "INVALID_VPA",
		101: "VPA_RESTRICTED",
		102: "PSP_NOT_AVAILABLE",
		103: "SUSPECTED_FRAUD",
		104: "PSP_NOT_REGISTERED",
	}
	ValidateAddressAndCreatePiResponse_Status_value = map[string]int32{
		"OK":                 0,
		"INTERNAL":           13,
		"INVALID_VPA":        100,
		"VPA_RESTRICTED":     101,
		"PSP_NOT_AVAILABLE":  102,
		"SUSPECTED_FRAUD":    103,
		"PSP_NOT_REGISTERED": 104,
	}
)

func (x ValidateAddressAndCreatePiResponse_Status) Enum() *ValidateAddressAndCreatePiResponse_Status {
	p := new(ValidateAddressAndCreatePiResponse_Status)
	*p = x
	return p
}

func (x ValidateAddressAndCreatePiResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValidateAddressAndCreatePiResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[41].Descriptor()
}

func (ValidateAddressAndCreatePiResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[41]
}

func (x ValidateAddressAndCreatePiResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValidateAddressAndCreatePiResponse_Status.Descriptor instead.
func (ValidateAddressAndCreatePiResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{54, 0}
}

type ValidateUpiNumberAndCreatePiResponse_Status int32

const (
	// Returned an success
	ValidateUpiNumberAndCreatePiResponse_OK ValidateUpiNumberAndCreatePiResponse_Status = 0
	// System faced internal errors while processing the request
	ValidateUpiNumberAndCreatePiResponse_INTERNAL ValidateUpiNumberAndCreatePiResponse_Status = 13
	// the VPA Id is invalid
	ValidateUpiNumberAndCreatePiResponse_INVALID_VPA ValidateUpiNumberAndCreatePiResponse_Status = 100
	// vpa restricted by the customer
	ValidateUpiNumberAndCreatePiResponse_VPA_RESTRICTED ValidateUpiNumberAndCreatePiResponse_Status = 101
	// PSP not available at the moment
	ValidateUpiNumberAndCreatePiResponse_PSP_NOT_AVAILABLE ValidateUpiNumberAndCreatePiResponse_Status = 102
	// upi number is inactive
	ValidateUpiNumberAndCreatePiResponse_MAPPING_INACTIVE ValidateUpiNumberAndCreatePiResponse_Status = 103
)

// Enum value maps for ValidateUpiNumberAndCreatePiResponse_Status.
var (
	ValidateUpiNumberAndCreatePiResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "INVALID_VPA",
		101: "VPA_RESTRICTED",
		102: "PSP_NOT_AVAILABLE",
		103: "MAPPING_INACTIVE",
	}
	ValidateUpiNumberAndCreatePiResponse_Status_value = map[string]int32{
		"OK":                0,
		"INTERNAL":          13,
		"INVALID_VPA":       100,
		"VPA_RESTRICTED":    101,
		"PSP_NOT_AVAILABLE": 102,
		"MAPPING_INACTIVE":  103,
	}
)

func (x ValidateUpiNumberAndCreatePiResponse_Status) Enum() *ValidateUpiNumberAndCreatePiResponse_Status {
	p := new(ValidateUpiNumberAndCreatePiResponse_Status)
	*p = x
	return p
}

func (x ValidateUpiNumberAndCreatePiResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValidateUpiNumberAndCreatePiResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[42].Descriptor()
}

func (ValidateUpiNumberAndCreatePiResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[42]
}

func (x ValidateUpiNumberAndCreatePiResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValidateUpiNumberAndCreatePiResponse_Status.Descriptor instead.
func (ValidateUpiNumberAndCreatePiResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{56, 0}
}

type ValidateInternationalPaymentResponse_Status int32

const (
	// Returned an success
	ValidateInternationalPaymentResponse_OK ValidateInternationalPaymentResponse_Status = 0
	// Account not found
	ValidateInternationalPaymentResponse_RECORD_NOT_FOUND ValidateInternationalPaymentResponse_Status = 5
	// Invalid argument passed in the request
	ValidateInternationalPaymentResponse_INVALID_ARGUMENT ValidateInternationalPaymentResponse_Status = 3
	// System faced internal errors while processing the request
	ValidateInternationalPaymentResponse_INTERNAL ValidateInternationalPaymentResponse_Status = 13
	// Requested action already exists
	ValidateInternationalPaymentResponse_ALREADY_EXISTS ValidateInternationalPaymentResponse_Status = 6
)

// Enum value maps for ValidateInternationalPaymentResponse_Status.
var (
	ValidateInternationalPaymentResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
		6:  "ALREADY_EXISTS",
	}
	ValidateInternationalPaymentResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
		"ALREADY_EXISTS":   6,
	}
)

func (x ValidateInternationalPaymentResponse_Status) Enum() *ValidateInternationalPaymentResponse_Status {
	p := new(ValidateInternationalPaymentResponse_Status)
	*p = x
	return p
}

func (x ValidateInternationalPaymentResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValidateInternationalPaymentResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[43].Descriptor()
}

func (ValidateInternationalPaymentResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[43]
}

func (x ValidateInternationalPaymentResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValidateInternationalPaymentResponse_Status.Descriptor instead.
func (ValidateInternationalPaymentResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{58, 0}
}

type GetUpiInternationalQrInfoFromCacheResponse_Status int32

const (
	// Returned an success
	GetUpiInternationalQrInfoFromCacheResponse_OK GetUpiInternationalQrInfoFromCacheResponse_Status = 0
	// Account not found
	GetUpiInternationalQrInfoFromCacheResponse_RECORD_NOT_FOUND GetUpiInternationalQrInfoFromCacheResponse_Status = 5
	// Invalid argument passed in the request
	GetUpiInternationalQrInfoFromCacheResponse_INVALID_ARGUMENT GetUpiInternationalQrInfoFromCacheResponse_Status = 3
	// System faced internal errors while processing the request
	GetUpiInternationalQrInfoFromCacheResponse_INTERNAL GetUpiInternationalQrInfoFromCacheResponse_Status = 13
)

// Enum value maps for GetUpiInternationalQrInfoFromCacheResponse_Status.
var (
	GetUpiInternationalQrInfoFromCacheResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	GetUpiInternationalQrInfoFromCacheResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x GetUpiInternationalQrInfoFromCacheResponse_Status) Enum() *GetUpiInternationalQrInfoFromCacheResponse_Status {
	p := new(GetUpiInternationalQrInfoFromCacheResponse_Status)
	*p = x
	return p
}

func (x GetUpiInternationalQrInfoFromCacheResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetUpiInternationalQrInfoFromCacheResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[44].Descriptor()
}

func (GetUpiInternationalQrInfoFromCacheResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[44]
}

func (x GetUpiInternationalQrInfoFromCacheResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetUpiInternationalQrInfoFromCacheResponse_Status.Descriptor instead.
func (GetUpiInternationalQrInfoFromCacheResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{60, 0}
}

type GetExternalVpasRequest_IdentifierFieldMask int32

const (
	GetExternalVpasRequest_IDENTIFIER_MASK_UNDEFINED    GetExternalVpasRequest_IdentifierFieldMask = 0
	GetExternalVpasRequest_IDENTIFIER_MASK_EMAIL        GetExternalVpasRequest_IdentifierFieldMask = 1
	GetExternalVpasRequest_IDENTIFIER_MASK_PHONE_NUMBER GetExternalVpasRequest_IdentifierFieldMask = 2
)

// Enum value maps for GetExternalVpasRequest_IdentifierFieldMask.
var (
	GetExternalVpasRequest_IdentifierFieldMask_name = map[int32]string{
		0: "IDENTIFIER_MASK_UNDEFINED",
		1: "IDENTIFIER_MASK_EMAIL",
		2: "IDENTIFIER_MASK_PHONE_NUMBER",
	}
	GetExternalVpasRequest_IdentifierFieldMask_value = map[string]int32{
		"IDENTIFIER_MASK_UNDEFINED":    0,
		"IDENTIFIER_MASK_EMAIL":        1,
		"IDENTIFIER_MASK_PHONE_NUMBER": 2,
	}
)

func (x GetExternalVpasRequest_IdentifierFieldMask) Enum() *GetExternalVpasRequest_IdentifierFieldMask {
	p := new(GetExternalVpasRequest_IdentifierFieldMask)
	*p = x
	return p
}

func (x GetExternalVpasRequest_IdentifierFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetExternalVpasRequest_IdentifierFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[45].Descriptor()
}

func (GetExternalVpasRequest_IdentifierFieldMask) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[45]
}

func (x GetExternalVpasRequest_IdentifierFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetExternalVpasRequest_IdentifierFieldMask.Descriptor instead.
func (GetExternalVpasRequest_IdentifierFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{61, 0}
}

type GetExternalVpasResponse_Status int32

const (
	GetExternalVpasResponse_OK GetExternalVpasResponse_Status = 0
	// if request validation fails,
	// wrong field mask passed for given identifier
	GetExternalVpasResponse_INVALID_ARGUMENT GetExternalVpasResponse_Status = 3
	// record not found
	GetExternalVpasResponse_RECORD_NOT_FOUND GetExternalVpasResponse_Status = 5
	// System faced internal errors while processing the request
	GetExternalVpasResponse_INTERNAL GetExternalVpasResponse_Status = 13
)

// Enum value maps for GetExternalVpasResponse_Status.
var (
	GetExternalVpasResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetExternalVpasResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetExternalVpasResponse_Status) Enum() *GetExternalVpasResponse_Status {
	p := new(GetExternalVpasResponse_Status)
	*p = x
	return p
}

func (x GetExternalVpasResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetExternalVpasResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[46].Descriptor()
}

func (GetExternalVpasResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[46]
}

func (x GetExternalVpasResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetExternalVpasResponse_Status.Descriptor instead.
func (GetExternalVpasResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{62, 0}
}

// List of status codes returned
type GetNPCICLParametersV1Response_Status int32

const (
	// Returned an success
	GetNPCICLParametersV1Response_OK GetNPCICLParametersV1Response_Status = 0
	// Requested entity i.e., upiAccountInfo is not found against account_id
	// One of the reasons could be due to the failure in epiFi's provisioning
	GetNPCICLParametersV1Response_NOT_FOUND GetNPCICLParametersV1Response_Status = 5
	// The actor does not have permission to execute the specified operation.
	// One of the reasons could be that Actor is not owner of the account
	GetNPCICLParametersV1Response_PERMISSION_DENIED GetNPCICLParametersV1Response_Status = 7
	// System faced internal errors while processing the request
	GetNPCICLParametersV1Response_INTERNAL GetNPCICLParametersV1Response_Status = 13
)

// Enum value maps for GetNPCICLParametersV1Response_Status.
var (
	GetNPCICLParametersV1Response_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
	}
	GetNPCICLParametersV1Response_Status_value = map[string]int32{
		"OK":                0,
		"NOT_FOUND":         5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
	}
)

func (x GetNPCICLParametersV1Response_Status) Enum() *GetNPCICLParametersV1Response_Status {
	p := new(GetNPCICLParametersV1Response_Status)
	*p = x
	return p
}

func (x GetNPCICLParametersV1Response_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetNPCICLParametersV1Response_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[47].Descriptor()
}

func (GetNPCICLParametersV1Response_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[47]
}

func (x GetNPCICLParametersV1Response_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetNPCICLParametersV1Response_Status.Descriptor instead.
func (GetNPCICLParametersV1Response_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{64, 0}
}

type IsNewAddFundsVpaEnabledForActorResponse_Status int32

const (
	// Returned an success
	IsNewAddFundsVpaEnabledForActorResponse_OK IsNewAddFundsVpaEnabledForActorResponse_Status = 0
	// request parameters invalid
	IsNewAddFundsVpaEnabledForActorResponse_INVALID_ARGUMENT IsNewAddFundsVpaEnabledForActorResponse_Status = 3
	// no request found for given client req id
	IsNewAddFundsVpaEnabledForActorResponse_RECORD_NOT_FOUND IsNewAddFundsVpaEnabledForActorResponse_Status = 5
	// System faced internal errors while processing the request
	IsNewAddFundsVpaEnabledForActorResponse_INTERNAL IsNewAddFundsVpaEnabledForActorResponse_Status = 13
)

// Enum value maps for IsNewAddFundsVpaEnabledForActorResponse_Status.
var (
	IsNewAddFundsVpaEnabledForActorResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	IsNewAddFundsVpaEnabledForActorResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x IsNewAddFundsVpaEnabledForActorResponse_Status) Enum() *IsNewAddFundsVpaEnabledForActorResponse_Status {
	p := new(IsNewAddFundsVpaEnabledForActorResponse_Status)
	*p = x
	return p
}

func (x IsNewAddFundsVpaEnabledForActorResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IsNewAddFundsVpaEnabledForActorResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_service_proto_enumTypes[48].Descriptor()
}

func (IsNewAddFundsVpaEnabledForActorResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_service_proto_enumTypes[48]
}

func (x IsNewAddFundsVpaEnabledForActorResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IsNewAddFundsVpaEnabledForActorResponse_Status.Descriptor instead.
func (IsNewAddFundsVpaEnabledForActorResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{66, 0}
}

type CheckTxnStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id for transaction generated from epifi
	TxnId string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *CheckTxnStatusRequest) Reset() {
	*x = CheckTxnStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTxnStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTxnStatusRequest) ProtoMessage() {}

func (x *CheckTxnStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTxnStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckTxnStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{0}
}

func (x *CheckTxnStatusRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type CheckTxnStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Request acknowledgement status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of reference received in checkTxn
	Ref []*CheckTxnStatusResponse_Ref `protobuf:"bytes,2,rep,name=ref,proto3" json:"ref,omitempty"`
	// Transaction's customer reference number.
	// In case of UPI it is a 12-digit unique ID generated by UPI switch.
	// A customer gets an custRefId on initiating a transaction and use in case of any query regarding his txn.
	// custRefId will be same across all the parties of a transaction similar to transaction id.
	// It's just a more readable proxy to identify a transaction for a customer.
	// Only difference being CustRef is unique w.r.t a customer while TxnID is unique across whole NPCI system.
	CustRefId string `protobuf:"bytes,3,opt,name=cust_ref_id,json=custRefId,proto3" json:"cust_ref_id,omitempty"`
	// raw status code as sent by the
	RawStatusCode string `protobuf:"bytes,4,opt,name=raw_status_code,json=rawStatusCode,proto3" json:"raw_status_code,omitempty"`
	// description of the raw status code
	RawStatusDescription string `protobuf:"bytes,5,opt,name=raw_status_description,json=rawStatusDescription,proto3" json:"raw_status_description,omitempty"`
	// epifi status code for the payer
	StatusCodePayer string `protobuf:"bytes,6,opt,name=status_code_payer,json=statusCodePayer,proto3" json:"status_code_payer,omitempty"`
	// description of the status code for payer
	StatusDescriptionPayer string `protobuf:"bytes,7,opt,name=status_description_payer,json=statusDescriptionPayer,proto3" json:"status_description_payer,omitempty"`
	// status code for the payee
	StatusCodePayee string `protobuf:"bytes,8,opt,name=status_code_payee,json=statusCodePayee,proto3" json:"status_code_payee,omitempty"`
	// description of the status code for payee
	StatusDescriptionPayee string `protobuf:"bytes,9,opt,name=status_description_payee,json=statusDescriptionPayee,proto3" json:"status_description_payee,omitempty"`
	// original transaction execution time
	OriginalTransactionDate *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=original_transaction_date,json=originalTransactionDate,proto3" json:"original_transaction_date,omitempty"`
	// transaction remarks
	Remarks string `protobuf:"bytes,11,opt,name=remarks,proto3" json:"remarks,omitempty"`
}

func (x *CheckTxnStatusResponse) Reset() {
	*x = CheckTxnStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTxnStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTxnStatusResponse) ProtoMessage() {}

func (x *CheckTxnStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTxnStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckTxnStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{1}
}

func (x *CheckTxnStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckTxnStatusResponse) GetRef() []*CheckTxnStatusResponse_Ref {
	if x != nil {
		return x.Ref
	}
	return nil
}

func (x *CheckTxnStatusResponse) GetCustRefId() string {
	if x != nil {
		return x.CustRefId
	}
	return ""
}

func (x *CheckTxnStatusResponse) GetRawStatusCode() string {
	if x != nil {
		return x.RawStatusCode
	}
	return ""
}

func (x *CheckTxnStatusResponse) GetRawStatusDescription() string {
	if x != nil {
		return x.RawStatusDescription
	}
	return ""
}

func (x *CheckTxnStatusResponse) GetStatusCodePayer() string {
	if x != nil {
		return x.StatusCodePayer
	}
	return ""
}

func (x *CheckTxnStatusResponse) GetStatusDescriptionPayer() string {
	if x != nil {
		return x.StatusDescriptionPayer
	}
	return ""
}

func (x *CheckTxnStatusResponse) GetStatusCodePayee() string {
	if x != nil {
		return x.StatusCodePayee
	}
	return ""
}

func (x *CheckTxnStatusResponse) GetStatusDescriptionPayee() string {
	if x != nil {
		return x.StatusDescriptionPayee
	}
	return ""
}

func (x *CheckTxnStatusResponse) GetOriginalTransactionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.OriginalTransactionDate
	}
	return nil
}

func (x *CheckTxnStatusResponse) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

// Request message for verifying VPA
type VerifyPayeeVPARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Device ID or fingerprint of the device that is registered with the partner bank
	Device *Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// VPA that need to be verified
	UpiVpa string `protobuf:"bytes,2,opt,name=upi_vpa,json=upiVpa,proto3" json:"upi_vpa,omitempty"`
	// entity id of the actor payer
	PayerActorId string `protobuf:"bytes,3,opt,name=payer_actor_id,json=payerActorId,proto3" json:"payer_actor_id,omitempty"`
}

func (x *VerifyPayeeVPARequest) Reset() {
	*x = VerifyPayeeVPARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPayeeVPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPayeeVPARequest) ProtoMessage() {}

func (x *VerifyPayeeVPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPayeeVPARequest.ProtoReflect.Descriptor instead.
func (*VerifyPayeeVPARequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{2}
}

func (x *VerifyPayeeVPARequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *VerifyPayeeVPARequest) GetUpiVpa() string {
	if x != nil {
		return x.UpiVpa
	}
	return ""
}

func (x *VerifyPayeeVPARequest) GetPayerActorId() string {
	if x != nil {
		return x.PayerActorId
	}
	return ""
}

// Response for verifying VPA
type VerifyPayeeVPAResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// name of the user to which the id belongs to
	CustomerName string `protobuf:"bytes,2,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// Merchant Classification Code
	Mcc string `protobuf:"bytes,3,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// merchant details will be populated if the customer is merchant
	Merchant *MerchantDetails `protobuf:"bytes,4,opt,name=merchant,proto3" json:"merchant,omitempty"`
	// ifsc code of the account linked with the vpa
	// bank account liked to a vpa can change and hence
	// the ifsc code can also change
	Ifsc string `protobuf:"bytes,5,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	// type of the account linked with the vpa
	AccountType accounts.Type `protobuf:"varint,6,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// account product offering associated with the account type
	Apo account.AccountProductOffering `protobuf:"varint,7,opt,name=apo,proto3,enum=api.typesv2.account.AccountProductOffering" json:"apo,omitempty"`
}

func (x *VerifyPayeeVPAResponse) Reset() {
	*x = VerifyPayeeVPAResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyPayeeVPAResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyPayeeVPAResponse) ProtoMessage() {}

func (x *VerifyPayeeVPAResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyPayeeVPAResponse.ProtoReflect.Descriptor instead.
func (*VerifyPayeeVPAResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{3}
}

func (x *VerifyPayeeVPAResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *VerifyPayeeVPAResponse) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *VerifyPayeeVPAResponse) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *VerifyPayeeVPAResponse) GetMerchant() *MerchantDetails {
	if x != nil {
		return x.Merchant
	}
	return nil
}

func (x *VerifyPayeeVPAResponse) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *VerifyPayeeVPAResponse) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *VerifyPayeeVPAResponse) GetApo() account.AccountProductOffering {
	if x != nil {
		return x.Apo
	}
	return account.AccountProductOffering(0)
}

// request object to get token for registering the app with NPCI common library
type GetTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// cred block to get the token to register the device with NPCI common library
	// Data inside cred block should be in the format - device id|app id|mobile number|challenge
	// device id, app id, mobile number and challenge are concatenated including pipe characters in between.
	CredBlockChallenge *CredBlock `protobuf:"bytes,1,opt,name=cred_block_challenge,json=credBlockChallenge,proto3" json:"cred_block_challenge,omitempty"`
}

func (x *GetTokenRequest) Reset() {
	*x = GetTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenRequest) ProtoMessage() {}

func (x *GetTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenRequest.ProtoReflect.Descriptor instead.
func (*GetTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetTokenRequest) GetCredBlockChallenge() *CredBlock {
	if x != nil {
		return x.CredBlockChallenge
	}
	return nil
}

// response object to get token for registering the app with NPCI common library
type GetTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// token to be used for registering the app with NPCI common library
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *GetTokenResponse) Reset() {
	*x = GetTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenResponse) ProtoMessage() {}

func (x *GetTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenResponse.ProtoReflect.Descriptor instead.
func (*GetTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetTokenResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// Request for UPI otp
type GenerateUpiOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Device details
	Device *Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// Identifier of the account for which the request is initiated
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// actor id of the user
	ActorId    string                           `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ReqOtpType GenerateUpiOtpRequest_ReqOtpType `protobuf:"varint,4,opt,name=req_otp_type,json=reqOtpType,proto3,enum=upi.GenerateUpiOtpRequest_ReqOtpType" json:"req_otp_type,omitempty"`
}

func (x *GenerateUpiOtpRequest) Reset() {
	*x = GenerateUpiOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateUpiOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateUpiOtpRequest) ProtoMessage() {}

func (x *GenerateUpiOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateUpiOtpRequest.ProtoReflect.Descriptor instead.
func (*GenerateUpiOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{6}
}

func (x *GenerateUpiOtpRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *GenerateUpiOtpRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GenerateUpiOtpRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GenerateUpiOtpRequest) GetReqOtpType() GenerateUpiOtpRequest_ReqOtpType {
	if x != nil {
		return x.ReqOtpType
	}
	return GenerateUpiOtpRequest_REQ_OTP_TYPE_BANK
}

// Response for get UPI otp from issuer PSP
type GenerateUpiOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// App will call the Common Library(CL) with this URL in the
	// specified format. CL will auto-populate the OTP.
	// CL will then call this url to re-direct to issuer page for ATM PIN capture.
	// Ref: Bank_URL in UPI docs
	SecureUrl string `protobuf:"bytes,2,opt,name=secure_url,json=secureUrl,proto3" json:"secure_url,omitempty"`
}

func (x *GenerateUpiOtpResponse) Reset() {
	*x = GenerateUpiOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateUpiOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateUpiOtpResponse) ProtoMessage() {}

func (x *GenerateUpiOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateUpiOtpResponse.ProtoReflect.Descriptor instead.
func (*GenerateUpiOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{7}
}

func (x *GenerateUpiOtpResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateUpiOtpResponse) GetSecureUrl() string {
	if x != nil {
		return x.SecureUrl
	}
	return ""
}

type ResolveAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// VPA for which account resolution needs to be done.
	Vpa string `protobuf:"bytes,1,opt,name=vpa,proto3" json:"vpa,omitempty"`
}

func (x *ResolveAccountRequest) Reset() {
	*x = ResolveAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveAccountRequest) ProtoMessage() {}

func (x *ResolveAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveAccountRequest.ProtoReflect.Descriptor instead.
func (*ResolveAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{8}
}

func (x *ResolveAccountRequest) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

type ResolveAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Obfuscated value referencing the account number to which VPA belongs to
	// This reference number would be returned as part of `List Accounts` API
	AccountReferenceNumber string `protobuf:"bytes,2,opt,name=account_reference_number,json=accountReferenceNumber,proto3" json:"account_reference_number,omitempty"`
	// IFSC code of the account to which VPA belongs to
	Ifsc string `protobuf:"bytes,3,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	// type of the account to which VPA belongs to
	AccType accounts.Type `protobuf:"varint,4,opt,name=acc_type,json=accType,proto3,enum=accounts.Type" json:"acc_type,omitempty"`
}

func (x *ResolveAccountResponse) Reset() {
	*x = ResolveAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResolveAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResolveAccountResponse) ProtoMessage() {}

func (x *ResolveAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResolveAccountResponse.ProtoReflect.Descriptor instead.
func (*ResolveAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{9}
}

func (x *ResolveAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ResolveAccountResponse) GetAccountReferenceNumber() string {
	if x != nil {
		return x.AccountReferenceNumber
	}
	return ""
}

func (x *ResolveAccountResponse) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *ResolveAccountResponse) GetAccType() accounts.Type {
	if x != nil {
		return x.AccType
	}
	return accounts.Type(0)
}

// Request to create VPA for account
type CreateVPARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id to which the vpa needs to be provisioned
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// phone number corresponding to the account
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Partner bank to which the account belongs to
	PartnerBank vendorgateway.Vendor `protobuf:"varint,4,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// Type of the account eg. savings
	AccountType accounts.Type `protobuf:"varint,5,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// Device details of user
	Device *Device `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`
	// customer name
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	// account no
	AccountNo string `protobuf:"bytes,8,opt,name=account_no,json=accountNo,proto3" json:"account_no,omitempty"`
	// IFSC code
	IfscCode string `protobuf:"bytes,9,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	// actor id of user
	ActorId string `protobuf:"bytes,10,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// user if of the user for which the vpa is to be provisioned
	UserId string `protobuf:"bytes,11,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// name to be used for vpa creation
	// we will be using legal name of the users for vpa creation
	VpaName *common.Name `protobuf:"bytes,12,opt,name=vpa_name,json=vpaName,proto3" json:"vpa_name,omitempty"`
}

func (x *CreateVPARequest) Reset() {
	*x = CreateVPARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateVPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVPARequest) ProtoMessage() {}

func (x *CreateVPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVPARequest.ProtoReflect.Descriptor instead.
func (*CreateVPARequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateVPARequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CreateVPARequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CreateVPARequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *CreateVPARequest) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *CreateVPARequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *CreateVPARequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateVPARequest) GetAccountNo() string {
	if x != nil {
		return x.AccountNo
	}
	return ""
}

func (x *CreateVPARequest) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *CreateVPARequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateVPARequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateVPARequest) GetVpaName() *common.Name {
	if x != nil {
		return x.VpaName
	}
	return nil
}

// response for create VPA
type CreateVPAResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateVPAResponse) Reset() {
	*x = CreateVPAResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateVPAResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVPAResponse) ProtoMessage() {}

func (x *CreateVPAResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVPAResponse.ProtoReflect.Descriptor instead.
func (*CreateVPAResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreateVPAResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ListKeysResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// xml key payload returned from NPCI/UPI API
	KeyXmlPayload string `protobuf:"bytes,2,opt,name=key_xml_payload,json=keyXmlPayload,proto3" json:"key_xml_payload,omitempty"`
}

func (x *ListKeysResponse) Reset() {
	*x = ListKeysResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListKeysResponse) ProtoMessage() {}

func (x *ListKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListKeysResponse.ProtoReflect.Descriptor instead.
func (*ListKeysResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{12}
}

func (x *ListKeysResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListKeysResponse) GetKeyXmlPayload() string {
	if x != nil {
		return x.KeyXmlPayload
	}
	return ""
}

type RegisterMobileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User device deatils
	Device *Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// Identifier of the account for which the request is initiated
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Cred blocks to capture sensitive data by CL.
	// Sample format look like below :
	// <Cred type="PIN" subType=" MPIN">
	// <Datacode="" ki=""> base-64 encoded/encrypted authentication data</Data> </Cred>
	CredBlock []*CredBlock `protobuf:"bytes,3,rep,name=cred_block,json=credBlock,proto3" json:"cred_block,omitempty"`
	// Unique value generated from UPI service to identify a request and passed in GetPinFlowParameters RPC response
	// This transaction_id is used in salt and trust generation.
	TransactionId string `protobuf:"bytes,4,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// card info has information like card number, expiry etc. that is use to set upi pin for the account
	// we need this information for tpap accounts which are not internal accounts
	CardInfo *card.BasicCardInfo `protobuf:"bytes,5,opt,name=card_info,json=cardInfo,proto3" json:"card_info,omitempty"`
	// user can initiated set pin / reset flow using two options
	// Debit card and Aadhaar number
	// Based on the type of flow, different parameters need to be
	// passed to vendor to initiate set pin request
	UpiPinSetOptionType enums.UpiPinSetOptionType `protobuf:"varint,6,opt,name=upi_pin_set_option_type,json=upiPinSetOptionType,proto3,enum=upi.onboarding.enums.UpiPinSetOptionType" json:"upi_pin_set_option_type,omitempty"`
}

func (x *RegisterMobileRequest) Reset() {
	*x = RegisterMobileRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterMobileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterMobileRequest) ProtoMessage() {}

func (x *RegisterMobileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterMobileRequest.ProtoReflect.Descriptor instead.
func (*RegisterMobileRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{13}
}

func (x *RegisterMobileRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *RegisterMobileRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *RegisterMobileRequest) GetCredBlock() []*CredBlock {
	if x != nil {
		return x.CredBlock
	}
	return nil
}

func (x *RegisterMobileRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *RegisterMobileRequest) GetCardInfo() *card.BasicCardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

func (x *RegisterMobileRequest) GetUpiPinSetOptionType() enums.UpiPinSetOptionType {
	if x != nil {
		return x.UpiPinSetOptionType
	}
	return enums.UpiPinSetOptionType(0)
}

type RegisterMobileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// raw response code send by the vendor
	RawResponseCode string `protobuf:"bytes,2,opt,name=raw_response_code,json=rawResponseCode,proto3" json:"raw_response_code,omitempty"`
}

func (x *RegisterMobileResponse) Reset() {
	*x = RegisterMobileResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterMobileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterMobileResponse) ProtoMessage() {}

func (x *RegisterMobileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterMobileResponse.ProtoReflect.Descriptor instead.
func (*RegisterMobileResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{14}
}

func (x *RegisterMobileResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RegisterMobileResponse) GetRawResponseCode() string {
	if x != nil {
		return x.RawResponseCode
	}
	return ""
}

type GetPinFlowParametersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier of the account for which the request is initiated
	AccountId   string                                  `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	PinFlowType GetPinFlowParametersRequest_PinFlowType `protobuf:"varint,2,opt,name=pin_flow_type,json=pinFlowType,proto3,enum=upi.GetPinFlowParametersRequest_PinFlowType" json:"pin_flow_type,omitempty"`
	// actor id of the current user
	CurrentActorId string `protobuf:"bytes,3,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	// backend will send creds based on upi pin set flow type (DEBIT CARD / AADHAAR NUMBER)
	UpiPinSetOptionType enums.UpiPinSetOptionType `protobuf:"varint,4,opt,name=upi_pin_set_option_type,json=upiPinSetOptionType,proto3,enum=upi.onboarding.enums.UpiPinSetOptionType" json:"upi_pin_set_option_type,omitempty"`
}

func (x *GetPinFlowParametersRequest) Reset() {
	*x = GetPinFlowParametersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinFlowParametersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinFlowParametersRequest) ProtoMessage() {}

func (x *GetPinFlowParametersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinFlowParametersRequest.ProtoReflect.Descriptor instead.
func (*GetPinFlowParametersRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetPinFlowParametersRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetPinFlowParametersRequest) GetPinFlowType() GetPinFlowParametersRequest_PinFlowType {
	if x != nil {
		return x.PinFlowType
	}
	return GetPinFlowParametersRequest_PIN_FLOW_TYPE_UNSPECIFIED
}

func (x *GetPinFlowParametersRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *GetPinFlowParametersRequest) GetUpiPinSetOptionType() enums.UpiPinSetOptionType {
	if x != nil {
		return x.UpiPinSetOptionType
	}
	return enums.UpiPinSetOptionType(0)
}

type GetPinFlowParametersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// An enum that represents the type of public key i.e., NPCI or UIDAI
	// Currently, RPC sends a hardcoded NPCI response
	KeyCode typesv2.KeyCode `protobuf:"varint,2,opt,name=key_code,json=keyCode,proto3,enum=api.typesv2.KeyCode" json:"key_code,omitempty"`
	// This is digitally signed XML payload received from list-Keys API of UPI.
	// This is mandatory field  required by mobile app to execute the “Get Credential”
	// service of common Library
	KeyXmlPayload string `protobuf:"bytes,3,opt,name=key_xml_payload,json=keyXmlPayload,proto3" json:"key_xml_payload,omitempty"`
	// Based on the number of blocks in the JSON, one or
	// more credential input control will be rendered by the
	// common library. Required by app to execute the “Get Credential”
	ControlJson *ControlJson `protobuf:"bytes,4,opt,name=control_json,json=controlJson,proto3" json:"control_json,omitempty"`
	// This enables to customize UI displayed by common library.
	// Client is not expected to change this and pass as it is to CL
	// Right now we are returning hardcoded json
	//
	//	ex json: {
	//	 {"payerBankName": "Indian Bank Ltd.",
	//	 "backgroundColor": "#FF9933",
	//	 "color": "#FF9933"}
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	BankConfigJson string `protobuf:"bytes,5,opt,name=bank_config_json,json=bankConfigJson,proto3" json:"bank_config_json,omitempty"`
	// masked account number to show to show at CL
	AccountNumber string `protobuf:"bytes,6,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// Unique value generated from UPI service to identify a transaction and passed in GetUpiInfo RPC response
	// This transaction_id is used in salt and trust generation.
	TransactionId string `protobuf:"bytes,7,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// boolean flag to inform if user have more than one account in bank.
	// if flag is true then user have more than one account in vendor bank
	// else only one account (i.e. current saving account opened by fi).
	IsExistingBankUser bool `protobuf:"varint,8,opt,name=is_existing_bank_user,json=isExistingBankUser,proto3" json:"is_existing_bank_user,omitempty"`
	// masked account number to show to show at CL
	MaskedAccountNumber string `protobuf:"bytes,9,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	// List of VPAs provisioned for the account
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	Vpas []string `protobuf:"bytes,10,rep,name=vpas,proto3" json:"vpas,omitempty"`
	// This enables to customize UI displayed by common library.
	BankConfig *GetPinFlowParametersResponse_BankConfig `protobuf:"bytes,11,opt,name=bank_config,json=bankConfig,proto3" json:"bank_config,omitempty"`
}

func (x *GetPinFlowParametersResponse) Reset() {
	*x = GetPinFlowParametersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinFlowParametersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinFlowParametersResponse) ProtoMessage() {}

func (x *GetPinFlowParametersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinFlowParametersResponse.ProtoReflect.Descriptor instead.
func (*GetPinFlowParametersResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetPinFlowParametersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPinFlowParametersResponse) GetKeyCode() typesv2.KeyCode {
	if x != nil {
		return x.KeyCode
	}
	return typesv2.KeyCode(0)
}

func (x *GetPinFlowParametersResponse) GetKeyXmlPayload() string {
	if x != nil {
		return x.KeyXmlPayload
	}
	return ""
}

func (x *GetPinFlowParametersResponse) GetControlJson() *ControlJson {
	if x != nil {
		return x.ControlJson
	}
	return nil
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *GetPinFlowParametersResponse) GetBankConfigJson() string {
	if x != nil {
		return x.BankConfigJson
	}
	return ""
}

func (x *GetPinFlowParametersResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetPinFlowParametersResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *GetPinFlowParametersResponse) GetIsExistingBankUser() bool {
	if x != nil {
		return x.IsExistingBankUser
	}
	return false
}

func (x *GetPinFlowParametersResponse) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *GetPinFlowParametersResponse) GetVpas() []string {
	if x != nil {
		return x.Vpas
	}
	return nil
}

func (x *GetPinFlowParametersResponse) GetBankConfig() *GetPinFlowParametersResponse_BankConfig {
	if x != nil {
		return x.BankConfig
	}
	return nil
}

type GetTransactionParametersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier of the account for which the request is initiated
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *GetTransactionParametersRequest) Reset() {
	*x = GetTransactionParametersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionParametersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionParametersRequest) ProtoMessage() {}

func (x *GetTransactionParametersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionParametersRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionParametersRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetTransactionParametersRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetTransactionParametersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// An enum that represents the type of public key i.e., NPCI or UIDAI
	// Currently, RPC sends a hardcoded NPCI response
	KeyCode typesv2.KeyCode `protobuf:"varint,2,opt,name=key_code,json=keyCode,proto3,enum=api.typesv2.KeyCode" json:"key_code,omitempty"`
	// This is digitally signed XML payload received from list-Keys API of UPI.
	// This is mandatory field  required by mobile app to execute the “Get Credential”
	// service of common Library
	KeyXmlPayload string `protobuf:"bytes,3,opt,name=key_xml_payload,json=keyXmlPayload,proto3" json:"key_xml_payload,omitempty"`
	// Based on the number of blocks in the JSON, one or
	// more credential input control will be rendered by the
	// common library. Required by app to execute the “Get Credential”
	ControlJson *ControlJson `protobuf:"bytes,4,opt,name=control_json,json=controlJson,proto3" json:"control_json,omitempty"`
	// This enables to customize UI displayed by common library.
	// Client is not expected to change this and pass as it is to CL
	// Right now we are returning hardcoded json
	//
	//	ex json: {
	//	 {"payerBankName": "Indian Bank Ltd.",
	//	 "backgroundColor": "#FF9933",
	//	 "color": "#FF9933"}
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	BankConfigJson string `protobuf:"bytes,5,opt,name=bank_config_json,json=bankConfigJson,proto3" json:"bank_config_json,omitempty"`
	// This enables to customize UI displayed by common library.
	BankConfig *GetTransactionParametersResponse_BankConfig `protobuf:"bytes,6,opt,name=bank_config,json=bankConfig,proto3" json:"bank_config,omitempty"`
}

func (x *GetTransactionParametersResponse) Reset() {
	*x = GetTransactionParametersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionParametersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionParametersResponse) ProtoMessage() {}

func (x *GetTransactionParametersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionParametersResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionParametersResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetTransactionParametersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTransactionParametersResponse) GetKeyCode() typesv2.KeyCode {
	if x != nil {
		return x.KeyCode
	}
	return typesv2.KeyCode(0)
}

func (x *GetTransactionParametersResponse) GetKeyXmlPayload() string {
	if x != nil {
		return x.KeyXmlPayload
	}
	return ""
}

func (x *GetTransactionParametersResponse) GetControlJson() *ControlJson {
	if x != nil {
		return x.ControlJson
	}
	return nil
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *GetTransactionParametersResponse) GetBankConfigJson() string {
	if x != nil {
		return x.BankConfigJson
	}
	return ""
}

func (x *GetTransactionParametersResponse) GetBankConfig() *GetTransactionParametersResponse_BankConfig {
	if x != nil {
		return x.BankConfig
	}
	return nil
}

type VerifyURNRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// urn that needs to be verified
	Urn string `protobuf:"bytes,1,opt,name=urn,proto3" json:"urn,omitempty"`
	// merchant classification code for the vpa associated the urn
	Mcc string `protobuf:"bytes,2,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// type of the urn passed in the request
	UrnType URNType `protobuf:"varint,3,opt,name=urn_type,json=urnType,proto3,enum=upi.URNType" json:"urn_type,omitempty"`
}

func (x *VerifyURNRequest) Reset() {
	*x = VerifyURNRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyURNRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyURNRequest) ProtoMessage() {}

func (x *VerifyURNRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyURNRequest.ProtoReflect.Descriptor instead.
func (*VerifyURNRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{19}
}

func (x *VerifyURNRequest) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *VerifyURNRequest) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *VerifyURNRequest) GetUrnType() URNType {
	if x != nil {
		return x.UrnType
	}
	return URNType_URN_TYPE_UNSPECIFIED
}

type VerifyURNResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// an optional field to be used in certain workflow where transaction id needs to be generated by external merchant
	// system e.g. dynamic QR scan and intent based payment
	//
	// This txn id is passed to all the external systems to uniquely identify an system transaction.
	//
	// Optional field, if present this id will be used to initiate payment with NPCI
	// for QR and intent based payments.. merchant system can send this information
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	TxnId string `protobuf:"bytes,5,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	// an optional field to be used in certain workflow where merchant ref id needs to be generated by external merchant
	// merchant ref id passed to associate a transaction with merchant's order system.
	//
	// In certain flows, e.g UPI QR scans or UPI intent based payments,
	// a merchant_ref_id can be present in the QR or the intent and it is mandatory
	// for epiFi to consume this information while initiating the transaction as per NPCI guidelines to PSP.
	//
	// Typically, this could be order number, subscription number, Bill ID,
	// booking ID, insurance, renewal reference, etc. from the merchant's system.
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	MerchantRefId string `protobuf:"bytes,7,opt,name=merchant_ref_id,json=merchantRefId,proto3" json:"merchant_ref_id,omitempty"`
	// transaction reference url should be a URL when clicked provides customer with further transaction details
	// like complete bill details, bill copy, order copy, ticket details, etc.
	//
	// for dynamic QR and intent based payments.. merchant system can send this information
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	ReferenceUrl string `protobuf:"bytes,8,opt,name=reference_url,json=referenceUrl,proto3" json:"reference_url,omitempty"`
	// initiation mode for the payment
	// 00=Default txn
	// 01=QR Code
	// 02=Secure QR Code
	// 04=Intent
	// 05=Secure Intent
	// 06=NFC
	// 07=BLE (Bluetooth)
	// 08=UHF(Ultra High
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	InitiationMode string `protobuf:"bytes,9,opt,name=initiation_mode,json=initiationMode,proto3" json:"initiation_mode,omitempty"`
	// purpose of the transaction
	// 00 - Default
	// 01 - SEBI
	// 02 - AMC
	// 03 - Travel
	// 04 - Hospitality
	// 05 – Hospital
	// 06 – Telecom
	// 07 – Insurance
	// 08 – Education
	// 09- Gifting
	// 10-Others
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	Purpose string `protobuf:"bytes,10,opt,name=purpose,proto3" json:"purpose,omitempty"`
	// unique identifier for classification of a merchant - Merchant Classification Code
	// for QR and intent based payments.. merchant system can send this information
	// in other cases we will be using the default code -  "0000"
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	Mcc string `protobuf:"bytes,11,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// unique identifier of the merchant registered with NPCI
	// for QR and intent based payments.. merchant system can send this information
	// for all other cases this field will be empty
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	MerchantId string `protobuf:"bytes,12,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	// unique identifier of a registered merchant store with NPCI
	// for QR and intent based payments.. merchant system can send this information
	// for all other cases this field will be empty
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	MerchantStoreId string `protobuf:"bytes,13,opt,name=merchant_store_id,json=merchantStoreId,proto3" json:"merchant_store_id,omitempty"`
	// terminal id of the merchant
	// for QR and intent based payments.. merchant system can send this information
	// for all other cases this field will be empty
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	MerchantTerminalId string `protobuf:"bytes,14,opt,name=merchant_terminal_id,json=merchantTerminalId,proto3" json:"merchant_terminal_id,omitempty"`
	// If the transaction is initiated by any PSP app then the respective orgID passed by the PSP app
	// needs to be passed.
	// for merchant initiated intent ‘000000’ will be used.
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	OrgId string `protobuf:"bytes,15,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	// Boolean flag to show if signature is verified for URN
	//  1. If signature is true then the application should bypass
	//     passcode page.
	//  2. If signature is false and status is invalid argument then request must be declined stating ‘intent is
	//     tampered or corrupt’.
	//  3. If signature is false and status is ok then the application should show
	//     warning message to user that the ‘source of intent could not be
	//     verified’ and shall request for passcode to proceed with the payment.
	IsSignatureVerified bool `protobuf:"varint,16,opt,name=isSignatureVerified,proto3" json:"isSignatureVerified,omitempty"`
	// denotes if the urn payment belongs to a dynamic qr/intent
	// if the amount and txnId is pre populated in the urn it is considered as dynamic
	IsDynamicQrInitialised bool `protobuf:"varint,17,opt,name=is_dynamic_qr_initialised,json=isDynamicQrInitialised,proto3" json:"is_dynamic_qr_initialised,omitempty"`
	// denotes the payee vpa address in the URN
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	UrnPayeeAddress string `protobuf:"bytes,18,opt,name=urn_payee_address,json=urnPayeeAddress,proto3" json:"urn_payee_address,omitempty"`
	// payee name present in urn(if any).
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	PayeeName string `protobuf:"bytes,19,opt,name=payee_name,json=payeeName,proto3" json:"payee_name,omitempty"`
	// Types that are assignable to UrnParams:
	//
	//	*VerifyURNResponse_MandateUrnInfo
	UrnParams isVerifyURNResponse_UrnParams `protobuf_oneof:"urn_params"`
	// urn expiry time in case of dynamic qr
	ExpiryTime *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=expiry_time,json=expiryTime,proto3" json:"expiry_time,omitempty"`
	// Amount of monies involved in the transaction
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	Amount *money.Money `protobuf:"bytes,22,opt,name=amount,proto3" json:"amount,omitempty"`
	// minimum amount for the transaction
	// Moved to parsed_urn_info, use that instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	MinAmount *money.Money `protobuf:"bytes,23,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	// information parsed from the urn like refId, refUrl , amount etc.
	ParsedUrnInfo *domain_model.UrnInfo `protobuf:"bytes,24,opt,name=parsed_urn_info,json=parsedUrnInfo,proto3" json:"parsed_urn_info,omitempty"`
}

func (x *VerifyURNResponse) Reset() {
	*x = VerifyURNResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyURNResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyURNResponse) ProtoMessage() {}

func (x *VerifyURNResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyURNResponse.ProtoReflect.Descriptor instead.
func (*VerifyURNResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{20}
}

func (x *VerifyURNResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetMerchantRefId() string {
	if x != nil {
		return x.MerchantRefId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetReferenceUrl() string {
	if x != nil {
		return x.ReferenceUrl
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetInitiationMode() string {
	if x != nil {
		return x.InitiationMode
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetMerchantStoreId() string {
	if x != nil {
		return x.MerchantStoreId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetMerchantTerminalId() string {
	if x != nil {
		return x.MerchantTerminalId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetOrgId() string {
	if x != nil {
		return x.OrgId
	}
	return ""
}

func (x *VerifyURNResponse) GetIsSignatureVerified() bool {
	if x != nil {
		return x.IsSignatureVerified
	}
	return false
}

func (x *VerifyURNResponse) GetIsDynamicQrInitialised() bool {
	if x != nil {
		return x.IsDynamicQrInitialised
	}
	return false
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetUrnPayeeAddress() string {
	if x != nil {
		return x.UrnPayeeAddress
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetPayeeName() string {
	if x != nil {
		return x.PayeeName
	}
	return ""
}

func (m *VerifyURNResponse) GetUrnParams() isVerifyURNResponse_UrnParams {
	if m != nil {
		return m.UrnParams
	}
	return nil
}

func (x *VerifyURNResponse) GetMandateUrnInfo() *domain_model.MandateUrnInfo {
	if x, ok := x.GetUrnParams().(*VerifyURNResponse_MandateUrnInfo); ok {
		return x.MandateUrnInfo
	}
	return nil
}

func (x *VerifyURNResponse) GetExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTime
	}
	return nil
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *VerifyURNResponse) GetMinAmount() *money.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *VerifyURNResponse) GetParsedUrnInfo() *domain_model.UrnInfo {
	if x != nil {
		return x.ParsedUrnInfo
	}
	return nil
}

type isVerifyURNResponse_UrnParams interface {
	isVerifyURNResponse_UrnParams()
}

type VerifyURNResponse_MandateUrnInfo struct {
	MandateUrnInfo *domain_model.MandateUrnInfo `protobuf:"bytes,20,opt,name=mandate_urn_info,json=mandateUrnInfo,proto3,oneof"`
}

func (*VerifyURNResponse_MandateUrnInfo) isVerifyURNResponse_UrnParams() {}

// Request message for setting PIN
type ChangePinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// User device deatils
	Device *Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// Cred block is an encrypted payload that is created by the NPCI common library
	// Cred block acts as partner bank's authentication attributes for the request
	// and is typically forwarded without modification to the partner bank.
	//
	// It will contain the base-64 encoded/encrypted authentication data
	// for cred block type PIN and Subtype MPIN of old pin
	OldPinCredBlock *CredBlock `protobuf:"bytes,2,opt,name=old_pin_cred_block,json=oldPinCredBlock,proto3" json:"old_pin_cred_block,omitempty"`
	// It will contain the base-64 encoded/encrypted authentication data
	// for cred block type PIN and Subtype MPIN of new pin
	NewPinCredBlock *CredBlock `protobuf:"bytes,3,opt,name=new_pin_cred_block,json=newPinCredBlock,proto3" json:"new_pin_cred_block,omitempty"`
	// This reference number is resolved from PI
	// and forwarded without modifications.
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	AccountReferenceNumber string `protobuf:"bytes,4,opt,name=account_reference_number,json=accountReferenceNumber,proto3" json:"account_reference_number,omitempty"`
	// IFSC code of the account to which VPA belongs to
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	Ifsc string `protobuf:"bytes,5,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	// type of the account to which VPA belongs to
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	AccType accounts.Type `protobuf:"varint,6,opt,name=acc_type,json=accType,proto3,enum=accounts.Type" json:"acc_type,omitempty"`
	// vpa of associated account
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	Vpa string `protobuf:"bytes,7,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// name of account holding person
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	AccountHolderName string `protobuf:"bytes,8,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	// merchant code
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	MccCode string `protobuf:"bytes,9,opt,name=mcc_code,json=mccCode,proto3" json:"mcc_code,omitempty"`
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	CustomerType ChangePinRequest_CustomerType `protobuf:"varint,10,opt,name=customer_type,json=customerType,proto3,enum=upi.ChangePinRequest_CustomerType" json:"customer_type,omitempty"`
	// account id of the user for whom, the pin needs to be set
	AccountId string `protobuf:"bytes,11,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Unique value generated from UPI service to identify a request and passed in GetPinFlowParameters RPC response
	// This transaction_id is used in salt and trust generation.
	TransactionId string `protobuf:"bytes,12,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
}

func (x *ChangePinRequest) Reset() {
	*x = ChangePinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangePinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePinRequest) ProtoMessage() {}

func (x *ChangePinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePinRequest.ProtoReflect.Descriptor instead.
func (*ChangePinRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{21}
}

func (x *ChangePinRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *ChangePinRequest) GetOldPinCredBlock() *CredBlock {
	if x != nil {
		return x.OldPinCredBlock
	}
	return nil
}

func (x *ChangePinRequest) GetNewPinCredBlock() *CredBlock {
	if x != nil {
		return x.NewPinCredBlock
	}
	return nil
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *ChangePinRequest) GetAccountReferenceNumber() string {
	if x != nil {
		return x.AccountReferenceNumber
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *ChangePinRequest) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *ChangePinRequest) GetAccType() accounts.Type {
	if x != nil {
		return x.AccType
	}
	return accounts.Type(0)
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *ChangePinRequest) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *ChangePinRequest) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *ChangePinRequest) GetMccCode() string {
	if x != nil {
		return x.MccCode
	}
	return ""
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *ChangePinRequest) GetCustomerType() ChangePinRequest_CustomerType {
	if x != nil {
		return x.CustomerType
	}
	return ChangePinRequest_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *ChangePinRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ChangePinRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

// Response messsage for setting PIN
type ChangePinResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// raw response code send by the vendor
	RawResponseCode string `protobuf:"bytes,2,opt,name=raw_response_code,json=rawResponseCode,proto3" json:"raw_response_code,omitempty"`
}

func (x *ChangePinResponse) Reset() {
	*x = ChangePinResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangePinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePinResponse) ProtoMessage() {}

func (x *ChangePinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePinResponse.ProtoReflect.Descriptor instead.
func (*ChangePinResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{22}
}

func (x *ChangePinResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ChangePinResponse) GetRawResponseCode() string {
	if x != nil {
		return x.RawResponseCode
	}
	return ""
}

type GenerateURNRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id of the payee
	PayeeAccountId string `protobuf:"bytes,1,opt,name=payee_account_id,json=payeeAccountId,proto3" json:"payee_account_id,omitempty"`
	// Amount of monies involved in the transaction
	Amount *money.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// initiation  mode of the payment
	// eg. QR code, Intent etc.
	InitiationMode GenerateURNRequest_InitiationMode `protobuf:"varint,3,opt,name=initiation_mode,json=initiationMode,proto3,enum=upi.GenerateURNRequest_InitiationMode" json:"initiation_mode,omitempty"`
	// identifier belonging to the current logged in actor
	// TODO(nitesh): get this from context
	CurrentActorId string `protobuf:"bytes,4,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	// Order details for the next order to be created on successful processing of order with URN_TRANSFER workflow.
	// The below field will be added to the payload of the order (URN_TRANSFER workflow) as it is and will be used to create
	// the next order after successfully processing of this order.
	NextOrderInfo *NextOrderInfo `protobuf:"bytes,5,opt,name=next_order_info,json=nextOrderInfo,proto3" json:"next_order_info,omitempty"`
	// denotes the order creation source eg. OUTGOING_INTENT, ONBORAD_ADD_FUNDS
	OrderSource GenerateURNRequest_Source `protobuf:"varint,6,opt,name=order_source,json=orderSource,proto3,enum=upi.GenerateURNRequest_Source" json:"order_source,omitempty"`
	// Obfuscated GPS coordinates location identifier for the payee.
	// Since, location identifier is a sensitive user information, it's not
	// recommended to store this data directly in the order domain object.
	// A location token is a place holder for the exact user co-ordinates.
	// Co-ordinates for the specified token can be fetched using location service's
	// GetCoordinates RPC
	// Note - Optional field, will be populated only if the payee's location is known
	// This rpc will always be initiated via the payee
	ToActorLocationToken string `protobuf:"bytes,17,opt,name=to_actor_location_token,json=toActorLocationToken,proto3" json:"to_actor_location_token,omitempty"`
}

func (x *GenerateURNRequest) Reset() {
	*x = GenerateURNRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateURNRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateURNRequest) ProtoMessage() {}

func (x *GenerateURNRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateURNRequest.ProtoReflect.Descriptor instead.
func (*GenerateURNRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{23}
}

func (x *GenerateURNRequest) GetPayeeAccountId() string {
	if x != nil {
		return x.PayeeAccountId
	}
	return ""
}

func (x *GenerateURNRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *GenerateURNRequest) GetInitiationMode() GenerateURNRequest_InitiationMode {
	if x != nil {
		return x.InitiationMode
	}
	return GenerateURNRequest_INITIATION_MODE_UNSPECIFIED
}

func (x *GenerateURNRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *GenerateURNRequest) GetNextOrderInfo() *NextOrderInfo {
	if x != nil {
		return x.NextOrderInfo
	}
	return nil
}

func (x *GenerateURNRequest) GetOrderSource() GenerateURNRequest_Source {
	if x != nil {
		return x.OrderSource
	}
	return GenerateURNRequest_SOURCE_UNSPECIFIED
}

func (x *GenerateURNRequest) GetToActorLocationToken() string {
	if x != nil {
		return x.ToActorLocationToken
	}
	return ""
}

type GenerateURNResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// urn that needs to be passed to the 3rd party app for dynamic QR/outgoing intent payments
	Urn     string `protobuf:"bytes,2,opt,name=urn,proto3" json:"urn,omitempty"`
	OrderId string `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *GenerateURNResponse) Reset() {
	*x = GenerateURNResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateURNResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateURNResponse) ProtoMessage() {}

func (x *GenerateURNResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateURNResponse.ProtoReflect.Descriptor instead.
func (*GenerateURNResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{24}
}

func (x *GenerateURNResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateURNResponse) GetUrn() string {
	if x != nil {
		return x.Urn
	}
	return ""
}

func (x *GenerateURNResponse) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type GetUpiSetupStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id of a user bank account
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *GetUpiSetupStatusRequest) Reset() {
	*x = GetUpiSetupStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUpiSetupStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUpiSetupStatusRequest) ProtoMessage() {}

func (x *GetUpiSetupStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUpiSetupStatusRequest.ProtoReflect.Descriptor instead.
func (*GetUpiSetupStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetUpiSetupStatusRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetUpiSetupStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// upi setup status
	UpiStatus UpiSetUpState `protobuf:"varint,2,opt,name=upi_status,json=upiStatus,proto3,enum=upi.UpiSetUpState" json:"upi_status,omitempty"`
}

func (x *GetUpiSetupStatusResponse) Reset() {
	*x = GetUpiSetupStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUpiSetupStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUpiSetupStatusResponse) ProtoMessage() {}

func (x *GetUpiSetupStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUpiSetupStatusResponse.ProtoReflect.Descriptor instead.
func (*GetUpiSetupStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetUpiSetupStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUpiSetupStatusResponse) GetUpiStatus() UpiSetUpState {
	if x != nil {
		return x.UpiStatus
	}
	return UpiSetUpState_UPI_SET_UP_STATE_UNSPECIFIED
}

type DisableOrEnableVPARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vpa that needs to be enabled/disabled
	// deprecated in favour of Identifier
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	Vpa string `protobuf:"bytes,1,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// actor id of the actor to whom the vpa belongs to
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// request type denoting whether to disable or enable the VPA
	RequestType DisableOrEnableVPARequest_RequestType `protobuf:"varint,3,opt,name=request_type,json=requestType,proto3,enum=upi.DisableOrEnableVPARequest_RequestType" json:"request_type,omitempty"`
	// reason for disabling/enabling the cpa
	Reason string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	// source which initiated this request.
	// eg. Epifi user, sherlock
	Source paymentinstrument.Source `protobuf:"varint,5,opt,name=source,proto3,enum=paymentinstrument.Source" json:"source,omitempty"`
	// Types that are assignable to Identifier:
	//
	//	*DisableOrEnableVPARequest_UserVpa
	//	*DisableOrEnableVPARequest_PiId
	Identifier isDisableOrEnableVPARequest_Identifier `protobuf_oneof:"Identifier"`
}

func (x *DisableOrEnableVPARequest) Reset() {
	*x = DisableOrEnableVPARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisableOrEnableVPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisableOrEnableVPARequest) ProtoMessage() {}

func (x *DisableOrEnableVPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisableOrEnableVPARequest.ProtoReflect.Descriptor instead.
func (*DisableOrEnableVPARequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{27}
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *DisableOrEnableVPARequest) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *DisableOrEnableVPARequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *DisableOrEnableVPARequest) GetRequestType() DisableOrEnableVPARequest_RequestType {
	if x != nil {
		return x.RequestType
	}
	return DisableOrEnableVPARequest_REQUEST_TYPE_UNSPECIFIED
}

func (x *DisableOrEnableVPARequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *DisableOrEnableVPARequest) GetSource() paymentinstrument.Source {
	if x != nil {
		return x.Source
	}
	return paymentinstrument.Source(0)
}

func (m *DisableOrEnableVPARequest) GetIdentifier() isDisableOrEnableVPARequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *DisableOrEnableVPARequest) GetUserVpa() string {
	if x, ok := x.GetIdentifier().(*DisableOrEnableVPARequest_UserVpa); ok {
		return x.UserVpa
	}
	return ""
}

func (x *DisableOrEnableVPARequest) GetPiId() string {
	if x, ok := x.GetIdentifier().(*DisableOrEnableVPARequest_PiId); ok {
		return x.PiId
	}
	return ""
}

type isDisableOrEnableVPARequest_Identifier interface {
	isDisableOrEnableVPARequest_Identifier()
}

type DisableOrEnableVPARequest_UserVpa struct {
	// vpa that needs to be enabled/disabled
	// NOTE - client will send vpa in req in order to disable / enable vpa
	UserVpa string `protobuf:"bytes,6,opt,name=user_vpa,json=userVpa,proto3,oneof"`
}

type DisableOrEnableVPARequest_PiId struct {
	// pi-id corresponding to the vpa that needs to be enabled/disabled
	// NOTE - sherlock client doesn't have the unmasked vpa, so it will
	// use pi-id to enable/disable vpa
	PiId string `protobuf:"bytes,7,opt,name=pi_id,json=piId,proto3,oneof"`
}

func (*DisableOrEnableVPARequest_UserVpa) isDisableOrEnableVPARequest_Identifier() {}

func (*DisableOrEnableVPARequest_PiId) isDisableOrEnableVPARequest_Identifier() {}

type DisableOrEnableVPAResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DisableOrEnableVPAResponse) Reset() {
	*x = DisableOrEnableVPAResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DisableOrEnableVPAResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisableOrEnableVPAResponse) ProtoMessage() {}

func (x *DisableOrEnableVPAResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisableOrEnableVPAResponse.ProtoReflect.Descriptor instead.
func (*DisableOrEnableVPAResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{28}
}

func (x *DisableOrEnableVPAResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type PinStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of account ids for which the pin status is required
	AccountIds []string `protobuf:"bytes,1,rep,name=account_ids,json=accountIds,proto3" json:"account_ids,omitempty"`
	// actor id of the current user
	CurrentActorId string `protobuf:"bytes,2,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
}

func (x *PinStatusRequest) Reset() {
	*x = PinStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinStatusRequest) ProtoMessage() {}

func (x *PinStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinStatusRequest.ProtoReflect.Descriptor instead.
func (*PinStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{29}
}

func (x *PinStatusRequest) GetAccountIds() []string {
	if x != nil {
		return x.AccountIds
	}
	return nil
}

func (x *PinStatusRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

type PinStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// map between account id to boolean, boolean denotes if the pin is set or not
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	AccountPinStatusMap map[string]bool `protobuf:"bytes,2,rep,name=account_pin_status_map,json=accountPinStatusMap,proto3" json:"account_pin_status_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// map between account id to pin set state
	AccountPinStateMap map[string]PinSetState `protobuf:"bytes,3,rep,name=account_pin_state_map,json=accountPinStateMap,proto3" json:"account_pin_state_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=upi.PinSetState"`
}

func (x *PinStatusResponse) Reset() {
	*x = PinStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinStatusResponse) ProtoMessage() {}

func (x *PinStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinStatusResponse.ProtoReflect.Descriptor instead.
func (*PinStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{30}
}

func (x *PinStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *PinStatusResponse) GetAccountPinStatusMap() map[string]bool {
	if x != nil {
		return x.AccountPinStatusMap
	}
	return nil
}

func (x *PinStatusResponse) GetAccountPinStateMap() map[string]PinSetState {
	if x != nil {
		return x.AccountPinStateMap
	}
	return nil
}

type GetAccountPinInfosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accountId for which we need the account pin info
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *GetAccountPinInfosRequest) Reset() {
	*x = GetAccountPinInfosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountPinInfosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountPinInfosRequest) ProtoMessage() {}

func (x *GetAccountPinInfosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountPinInfosRequest.ProtoReflect.Descriptor instead.
func (*GetAccountPinInfosRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetAccountPinInfosRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetAccountPinInfosResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of account upi pin info for the account id
	AccountUpiPinInfos []*AccountUpiPinInfo `protobuf:"bytes,2,rep,name=account_upi_pin_infos,json=accountUpiPinInfos,proto3" json:"account_upi_pin_infos,omitempty"`
}

func (x *GetAccountPinInfosResponse) Reset() {
	*x = GetAccountPinInfosResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountPinInfosResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountPinInfosResponse) ProtoMessage() {}

func (x *GetAccountPinInfosResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountPinInfosResponse.ProtoReflect.Descriptor instead.
func (*GetAccountPinInfosResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{32}
}

func (x *GetAccountPinInfosResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAccountPinInfosResponse) GetAccountUpiPinInfos() []*AccountUpiPinInfo {
	if x != nil {
		return x.AccountUpiPinInfos
	}
	return nil
}

type GetLatestAccountPinInfosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accountIds for which we need the account pin info
	AccountIds []string `protobuf:"bytes,1,rep,name=account_ids,json=accountIds,proto3" json:"account_ids,omitempty"`
}

func (x *GetLatestAccountPinInfosRequest) Reset() {
	*x = GetLatestAccountPinInfosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestAccountPinInfosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestAccountPinInfosRequest) ProtoMessage() {}

func (x *GetLatestAccountPinInfosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestAccountPinInfosRequest.ProtoReflect.Descriptor instead.
func (*GetLatestAccountPinInfosRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetLatestAccountPinInfosRequest) GetAccountIds() []string {
	if x != nil {
		return x.AccountIds
	}
	return nil
}

type GetLatestAccountPinInfosResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of account upi pin info for the account id
	AccountUpiPinInfos []*AccountUpiPinInfo `protobuf:"bytes,2,rep,name=account_upi_pin_infos,json=accountUpiPinInfos,proto3" json:"account_upi_pin_infos,omitempty"`
}

func (x *GetLatestAccountPinInfosResponse) Reset() {
	*x = GetLatestAccountPinInfosResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestAccountPinInfosResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestAccountPinInfosResponse) ProtoMessage() {}

func (x *GetLatestAccountPinInfosResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestAccountPinInfosResponse.ProtoReflect.Descriptor instead.
func (*GetLatestAccountPinInfosResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetLatestAccountPinInfosResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLatestAccountPinInfosResponse) GetAccountUpiPinInfos() []*AccountUpiPinInfo {
	if x != nil {
		return x.AccountUpiPinInfos
	}
	return nil
}

type CheckVerifiedMerchantRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vpa of merchant
	Vpa string `protobuf:"bytes,1,opt,name=vpa,proto3" json:"vpa,omitempty"`
}

func (x *CheckVerifiedMerchantRequest) Reset() {
	*x = CheckVerifiedMerchantRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckVerifiedMerchantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVerifiedMerchantRequest) ProtoMessage() {}

func (x *CheckVerifiedMerchantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVerifiedMerchantRequest.ProtoReflect.Descriptor instead.
func (*CheckVerifiedMerchantRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{35}
}

func (x *CheckVerifiedMerchantRequest) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

type CheckVerifiedMerchantResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CheckVerifiedMerchantResponse) Reset() {
	*x = CheckVerifiedMerchantResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckVerifiedMerchantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVerifiedMerchantResponse) ProtoMessage() {}

func (x *CheckVerifiedMerchantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVerifiedMerchantResponse.ProtoReflect.Descriptor instead.
func (*CheckVerifiedMerchantResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{36}
}

func (x *CheckVerifiedMerchantResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetVpaMerchantInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vpa of merchant
	Vpa string `protobuf:"bytes,1,opt,name=vpa,proto3" json:"vpa,omitempty"`
}

func (x *GetVpaMerchantInfoRequest) Reset() {
	*x = GetVpaMerchantInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVpaMerchantInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVpaMerchantInfoRequest) ProtoMessage() {}

func (x *GetVpaMerchantInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVpaMerchantInfoRequest.ProtoReflect.Descriptor instead.
func (*GetVpaMerchantInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetVpaMerchantInfoRequest) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

type GetVpaMerchantInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	VpaMerchantInfo *VpaMerchantInfo `protobuf:"bytes,2,opt,name=vpa_merchant_info,json=vpaMerchantInfo,proto3" json:"vpa_merchant_info,omitempty"`
}

func (x *GetVpaMerchantInfoResponse) Reset() {
	*x = GetVpaMerchantInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVpaMerchantInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVpaMerchantInfoResponse) ProtoMessage() {}

func (x *GetVpaMerchantInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVpaMerchantInfoResponse.ProtoReflect.Descriptor instead.
func (*GetVpaMerchantInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{38}
}

func (x *GetVpaMerchantInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetVpaMerchantInfoResponse) GetVpaMerchantInfo() *VpaMerchantInfo {
	if x != nil {
		return x.VpaMerchantInfo
	}
	return nil
}

type GetPinInfosByAccountIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accountId for which pin reset attempts have to be fetched
	AccountId   string                                 `protobuf:"bytes,1,opt,name=accountId,proto3" json:"accountId,omitempty"`
	UserActions []GetPinInfosByAccountIdRequest_Action `protobuf:"varint,2,rep,packed,name=user_actions,json=userActions,proto3,enum=upi.GetPinInfosByAccountIdRequest_Action" json:"user_actions,omitempty"`
	// status filter
	Status GetPinInfosByAccountIdRequest_Status `protobuf:"varint,3,opt,name=status,proto3,enum=upi.GetPinInfosByAccountIdRequest_Status" json:"status,omitempty"`
	// fields for the response payload
	SelectMask []AccountUpiPinInfoFieldMask `protobuf:"varint,4,rep,packed,name=select_mask,json=selectMask,proto3,enum=upi.AccountUpiPinInfoFieldMask" json:"select_mask,omitempty"`
	// starting time from which AccountPinInfo records have to be fetched
	FromTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	// ending time before which AccountPinInfo records have to be fetched
	ToTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=to_time,json=toTime,proto3" json:"to_time,omitempty"`
	// limit the number of fetched AccountPinInfo records
	Limit int32 `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	// offset the number of fetched PinResetAttempts
	Offset int32 `protobuf:"varint,8,opt,name=offset,proto3" json:"offset,omitempty"`
	// field to be used to sort the fetched records
	SortBy AccountUpiPinInfoFieldMask `protobuf:"varint,9,opt,name=sort_by,json=sortBy,proto3,enum=upi.AccountUpiPinInfoFieldMask" json:"sort_by,omitempty"`
	// sort_order for the results. can be ascending or descending. if nothing is specified and sort_by is given, ascending order is used
	SortOrder SortOrder `protobuf:"varint,10,opt,name=sort_order,json=sortOrder,proto3,enum=upi.SortOrder" json:"sort_order,omitempty"`
}

func (x *GetPinInfosByAccountIdRequest) Reset() {
	*x = GetPinInfosByAccountIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinInfosByAccountIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinInfosByAccountIdRequest) ProtoMessage() {}

func (x *GetPinInfosByAccountIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinInfosByAccountIdRequest.ProtoReflect.Descriptor instead.
func (*GetPinInfosByAccountIdRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetPinInfosByAccountIdRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetPinInfosByAccountIdRequest) GetUserActions() []GetPinInfosByAccountIdRequest_Action {
	if x != nil {
		return x.UserActions
	}
	return nil
}

func (x *GetPinInfosByAccountIdRequest) GetStatus() GetPinInfosByAccountIdRequest_Status {
	if x != nil {
		return x.Status
	}
	return GetPinInfosByAccountIdRequest_STATUS_UNSPECIFIED
}

func (x *GetPinInfosByAccountIdRequest) GetSelectMask() []AccountUpiPinInfoFieldMask {
	if x != nil {
		return x.SelectMask
	}
	return nil
}

func (x *GetPinInfosByAccountIdRequest) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *GetPinInfosByAccountIdRequest) GetToTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTime
	}
	return nil
}

func (x *GetPinInfosByAccountIdRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetPinInfosByAccountIdRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *GetPinInfosByAccountIdRequest) GetSortBy() AccountUpiPinInfoFieldMask {
	if x != nil {
		return x.SortBy
	}
	return AccountUpiPinInfoFieldMask_ACCOUNT_UPI_PIN_INFO_FIELD_MASK_UNSPECIFIED
}

func (x *GetPinInfosByAccountIdRequest) GetSortOrder() SortOrder {
	if x != nil {
		return x.SortOrder
	}
	return SortOrder_SORT_ORDER_UNSPECIFIED
}

type GetPinInfosByAccountIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of account upi pin infos
	AccountUpiPinInfos []*AccountUpiPinInfo `protobuf:"bytes,2,rep,name=account_upi_pin_infos,json=accountUpiPinInfos,proto3" json:"account_upi_pin_infos,omitempty"`
}

func (x *GetPinInfosByAccountIdResponse) Reset() {
	*x = GetPinInfosByAccountIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinInfosByAccountIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinInfosByAccountIdResponse) ProtoMessage() {}

func (x *GetPinInfosByAccountIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinInfosByAccountIdResponse.ProtoReflect.Descriptor instead.
func (*GetPinInfosByAccountIdResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetPinInfosByAccountIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPinInfosByAccountIdResponse) GetAccountUpiPinInfos() []*AccountUpiPinInfo {
	if x != nil {
		return x.AccountUpiPinInfos
	}
	return nil
}

type PostUserActivityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// use derived_account_id instead
	//
	// Deprecated: Marked as deprecated in api/upi/service.proto.
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// user activity posted on frontend that need to be tracked in UPI
	UserActivity     UpiUserActivity `protobuf:"varint,2,opt,name=user_activity,json=userActivity,proto3,enum=upi.UpiUserActivity" json:"user_activity,omitempty"`
	DerivedAccountId string          `protobuf:"bytes,3,opt,name=derived_account_id,json=derivedAccountId,proto3" json:"derived_account_id,omitempty"`
}

func (x *PostUserActivityRequest) Reset() {
	*x = PostUserActivityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostUserActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostUserActivityRequest) ProtoMessage() {}

func (x *PostUserActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostUserActivityRequest.ProtoReflect.Descriptor instead.
func (*PostUserActivityRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{41}
}

// Deprecated: Marked as deprecated in api/upi/service.proto.
func (x *PostUserActivityRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *PostUserActivityRequest) GetUserActivity() UpiUserActivity {
	if x != nil {
		return x.UserActivity
	}
	return UpiUserActivity_UPI_USER_ACTIVITY_UNSPECIFIED
}

func (x *PostUserActivityRequest) GetDerivedAccountId() string {
	if x != nil {
		return x.DerivedAccountId
	}
	return ""
}

type PostUserActivityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *PostUserActivityResponse) Reset() {
	*x = PostUserActivityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostUserActivityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostUserActivityResponse) ProtoMessage() {}

func (x *PostUserActivityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostUserActivityResponse.ProtoReflect.Descriptor instead.
func (*PostUserActivityResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{42}
}

func (x *PostUserActivityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ValidateSecurePinRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Device details
	Device *Device `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`
	// cred block
	NpciCredBlock *CredBlock `protobuf:"bytes,3,opt,name=npci_cred_block,json=npciCredBlock,proto3" json:"npci_cred_block,omitempty"`
	// unique identifier for a request.
	TxnId string `protobuf:"bytes,4,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *ValidateSecurePinRequest) Reset() {
	*x = ValidateSecurePinRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateSecurePinRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSecurePinRequest) ProtoMessage() {}

func (x *ValidateSecurePinRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSecurePinRequest.ProtoReflect.Descriptor instead.
func (*ValidateSecurePinRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{43}
}

func (x *ValidateSecurePinRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ValidateSecurePinRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *ValidateSecurePinRequest) GetNpciCredBlock() *CredBlock {
	if x != nil {
		return x.NpciCredBlock
	}
	return nil
}

func (x *ValidateSecurePinRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type ValidateSecurePinResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ValidateSecurePinResponse) Reset() {
	*x = ValidateSecurePinResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateSecurePinResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSecurePinResponse) ProtoMessage() {}

func (x *ValidateSecurePinResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSecurePinResponse.ProtoReflect.Descriptor instead.
func (*ValidateSecurePinResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{44}
}

func (x *ValidateSecurePinResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type RaiseComplaintRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// transaction id for which the complaint is being raised
	TxnId string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	// current actor id
	CurrentActorId string `protobuf:"bytes,2,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	// type of complaint
	Type RaiseComplaintRequest_RaiseComplaintType `protobuf:"varint,3,opt,name=type,proto3,enum=upi.RaiseComplaintRequest_RaiseComplaintType" json:"type,omitempty"`
	// contains information such as complaint action, complaint reason, amount
	Complaint *complaint.Complaint `protobuf:"bytes,4,opt,name=complaint,proto3" json:"complaint,omitempty"`
	// intiation mode for Raise complaint
	// U0- Auto Conversion
	// U1-Customer App
	// U2-PSP
	// U3-Bank
	// U4-CRM
	IntiationMode string `protobuf:"bytes,5,opt,name=intiation_mode,json=intiationMode,proto3" json:"intiation_mode,omitempty"`
}

func (x *RaiseComplaintRequest) Reset() {
	*x = RaiseComplaintRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RaiseComplaintRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RaiseComplaintRequest) ProtoMessage() {}

func (x *RaiseComplaintRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RaiseComplaintRequest.ProtoReflect.Descriptor instead.
func (*RaiseComplaintRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{45}
}

func (x *RaiseComplaintRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *RaiseComplaintRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *RaiseComplaintRequest) GetType() RaiseComplaintRequest_RaiseComplaintType {
	if x != nil {
		return x.Type
	}
	return RaiseComplaintRequest_RAISE_COMPLAINT_TYPE_UNSPECIFIED
}

func (x *RaiseComplaintRequest) GetComplaint() *complaint.Complaint {
	if x != nil {
		return x.Complaint
	}
	return nil
}

func (x *RaiseComplaintRequest) GetIntiationMode() string {
	if x != nil {
		return x.IntiationMode
	}
	return ""
}

type RaiseComplaintResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// unique reference number for the complaint/dispute issued from vendor side
	CustomerRefNumber string `protobuf:"bytes,2,opt,name=customer_ref_number,json=customerRefNumber,proto3" json:"customer_ref_number,omitempty"`
	// complaint reference, action and reason from vendor
	Refs []*txnref.TransactionReference `protobuf:"bytes,3,rep,name=refs,proto3" json:"refs,omitempty"`
	// Info regarding the state of the raised dispute
	ComplaintDisputeState complaint.ComplaintDisputeState `protobuf:"varint,4,opt,name=complaint_dispute_state,json=complaintDisputeState,proto3,enum=upi.complaint.ComplaintDisputeState" json:"complaint_dispute_state,omitempty"`
}

func (x *RaiseComplaintResponse) Reset() {
	*x = RaiseComplaintResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RaiseComplaintResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RaiseComplaintResponse) ProtoMessage() {}

func (x *RaiseComplaintResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RaiseComplaintResponse.ProtoReflect.Descriptor instead.
func (*RaiseComplaintResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{46}
}

func (x *RaiseComplaintResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RaiseComplaintResponse) GetCustomerRefNumber() string {
	if x != nil {
		return x.CustomerRefNumber
	}
	return ""
}

func (x *RaiseComplaintResponse) GetRefs() []*txnref.TransactionReference {
	if x != nil {
		return x.Refs
	}
	return nil
}

func (x *RaiseComplaintResponse) GetComplaintDisputeState() complaint.ComplaintDisputeState {
	if x != nil {
		return x.ComplaintDisputeState
	}
	return complaint.ComplaintDisputeState(0)
}

type CheckComplaintStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// transaction_id of epifi generated system
	TxnId string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *CheckComplaintStatusRequest) Reset() {
	*x = CheckComplaintStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckComplaintStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckComplaintStatusRequest) ProtoMessage() {}

func (x *CheckComplaintStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckComplaintStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckComplaintStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{47}
}

func (x *CheckComplaintStatusRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type CheckComplaintStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of reference received in checkTxn for payer and payee with complaint response
	Ref []*txnref.TransactionReference `protobuf:"bytes,2,rep,name=ref,proto3" json:"ref,omitempty"`
	// Info regarding the state of the raised dispute
	ComplaintDisputeState complaint.ComplaintDisputeState `protobuf:"varint,3,opt,name=complaint_dispute_state,json=complaintDisputeState,proto3,enum=upi.complaint.ComplaintDisputeState" json:"complaint_dispute_state,omitempty"`
}

func (x *CheckComplaintStatusResponse) Reset() {
	*x = CheckComplaintStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckComplaintStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckComplaintStatusResponse) ProtoMessage() {}

func (x *CheckComplaintStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckComplaintStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckComplaintStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{48}
}

func (x *CheckComplaintStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckComplaintStatusResponse) GetRef() []*txnref.TransactionReference {
	if x != nil {
		return x.Ref
	}
	return nil
}

func (x *CheckComplaintStatusResponse) GetComplaintDisputeState() complaint.ComplaintDisputeState {
	if x != nil {
		return x.ComplaintDisputeState
	}
	return complaint.ComplaintDisputeState(0)
}

type ChangeUpiPinSetStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accountId for which we need the upi pin state to change
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// pin set state to set
	PinSetState PinSetState `protobuf:"varint,2,opt,name=pin_set_state,json=pinSetState,proto3,enum=upi.PinSetState" json:"pin_set_state,omitempty"`
}

func (x *ChangeUpiPinSetStateRequest) Reset() {
	*x = ChangeUpiPinSetStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeUpiPinSetStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUpiPinSetStateRequest) ProtoMessage() {}

func (x *ChangeUpiPinSetStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUpiPinSetStateRequest.ProtoReflect.Descriptor instead.
func (*ChangeUpiPinSetStateRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{49}
}

func (x *ChangeUpiPinSetStateRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ChangeUpiPinSetStateRequest) GetPinSetState() PinSetState {
	if x != nil {
		return x.PinSetState
	}
	return PinSetState_PIN_SET_STATE_UNSPECIFIED
}

type ChangeUpiPinSetStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ChangeUpiPinSetStateResponse) Reset() {
	*x = ChangeUpiPinSetStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeUpiPinSetStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUpiPinSetStateResponse) ProtoMessage() {}

func (x *ChangeUpiPinSetStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUpiPinSetStateResponse.ProtoReflect.Descriptor instead.
func (*ChangeUpiPinSetStateResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{50}
}

func (x *ChangeUpiPinSetStateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetVerifiedVpasByPhoneNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Device ID or fingerprint of the device that is registered with the partner bank
	Device *Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// phone number whose VPAs is to be verified
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// actor id of the payer
	PayerActorId string `protobuf:"bytes,3,opt,name=payer_actor_id,json=payerActorId,proto3" json:"payer_actor_id,omitempty"`
}

func (x *GetVerifiedVpasByPhoneNumberRequest) Reset() {
	*x = GetVerifiedVpasByPhoneNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVerifiedVpasByPhoneNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVerifiedVpasByPhoneNumberRequest) ProtoMessage() {}

func (x *GetVerifiedVpasByPhoneNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVerifiedVpasByPhoneNumberRequest.ProtoReflect.Descriptor instead.
func (*GetVerifiedVpasByPhoneNumberRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{51}
}

func (x *GetVerifiedVpasByPhoneNumberRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *GetVerifiedVpasByPhoneNumberRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *GetVerifiedVpasByPhoneNumberRequest) GetPayerActorId() string {
	if x != nil {
		return x.PayerActorId
	}
	return ""
}

type GetVerifiedVpasByPhoneNumberResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	VpaInfo []*VpaInfo  `protobuf:"bytes,2,rep,name=vpa_info,json=vpaInfo,proto3" json:"vpa_info,omitempty"`
}

func (x *GetVerifiedVpasByPhoneNumberResponse) Reset() {
	*x = GetVerifiedVpasByPhoneNumberResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVerifiedVpasByPhoneNumberResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVerifiedVpasByPhoneNumberResponse) ProtoMessage() {}

func (x *GetVerifiedVpasByPhoneNumberResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVerifiedVpasByPhoneNumberResponse.ProtoReflect.Descriptor instead.
func (*GetVerifiedVpasByPhoneNumberResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{52}
}

func (x *GetVerifiedVpasByPhoneNumberResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetVerifiedVpasByPhoneNumberResponse) GetVpaInfo() []*VpaInfo {
	if x != nil {
		return x.VpaInfo
	}
	return nil
}

type ValidateAddressAndCreatePiRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Device ID or fingerprint of the device that is registered with the partner bank
	Device *Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// VPA that need to be verified
	UpiVpa string `protobuf:"bytes,2,opt,name=upi_vpa,json=upiVpa,proto3" json:"upi_vpa,omitempty"`
	// entity id of the actor payer
	PayerActorId string `protobuf:"bytes,3,opt,name=payer_actor_id,json=payerActorId,proto3" json:"payer_actor_id,omitempty"`
	// ownership of the pi to be created
	Ownership paymentinstrument.Ownership `protobuf:"varint,4,opt,name=ownership,proto3,enum=paymentinstrument.Ownership" json:"ownership,omitempty"`
	// enum to specify the handling of errors in the rpc
	VpaValidationMode ValidateAddressAndCreatePiRequest_VpaValidationMode `protobuf:"varint,5,opt,name=vpa_validation_mode,json=vpaValidationMode,proto3,enum=upi.ValidateAddressAndCreatePiRequest_VpaValidationMode" json:"vpa_validation_mode,omitempty"`
}

func (x *ValidateAddressAndCreatePiRequest) Reset() {
	*x = ValidateAddressAndCreatePiRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateAddressAndCreatePiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateAddressAndCreatePiRequest) ProtoMessage() {}

func (x *ValidateAddressAndCreatePiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateAddressAndCreatePiRequest.ProtoReflect.Descriptor instead.
func (*ValidateAddressAndCreatePiRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{53}
}

func (x *ValidateAddressAndCreatePiRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *ValidateAddressAndCreatePiRequest) GetUpiVpa() string {
	if x != nil {
		return x.UpiVpa
	}
	return ""
}

func (x *ValidateAddressAndCreatePiRequest) GetPayerActorId() string {
	if x != nil {
		return x.PayerActorId
	}
	return ""
}

func (x *ValidateAddressAndCreatePiRequest) GetOwnership() paymentinstrument.Ownership {
	if x != nil {
		return x.Ownership
	}
	return paymentinstrument.Ownership(0)
}

func (x *ValidateAddressAndCreatePiRequest) GetVpaValidationMode() ValidateAddressAndCreatePiRequest_VpaValidationMode {
	if x != nil {
		return x.VpaValidationMode
	}
	return ValidateAddressAndCreatePiRequest_VPA_VALIDATION_MODE_UNSPECIFIED
}

type ValidateAddressAndCreatePiResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Name of the user to which the VPA belongs
	// This field can be either:
	//  1. The validated customer name returned from vendor validation (default behavior)
	//  2. "" if validation fails
	//  3. Customer name extracted directly from the VPA when using VPA_VALIDATION_MODE_LENIENT
	//     and no active internal UPI PI exists (bypassing vendor validation when vendor call
	//     cannot be made)
	CustomerName string `protobuf:"bytes,2,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// Merchant Classification Code
	Mcc string `protobuf:"bytes,3,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// merchant details will be populated if the customer is merchant
	Merchant *MerchantDetails `protobuf:"bytes,4,opt,name=merchant,proto3" json:"merchant,omitempty"`
	// pi id for the payment instrument for the vpa
	PiId string `protobuf:"bytes,5,opt,name=pi_id,json=piId,proto3" json:"pi_id,omitempty"`
}

func (x *ValidateAddressAndCreatePiResponse) Reset() {
	*x = ValidateAddressAndCreatePiResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateAddressAndCreatePiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateAddressAndCreatePiResponse) ProtoMessage() {}

func (x *ValidateAddressAndCreatePiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateAddressAndCreatePiResponse.ProtoReflect.Descriptor instead.
func (*ValidateAddressAndCreatePiResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{54}
}

func (x *ValidateAddressAndCreatePiResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ValidateAddressAndCreatePiResponse) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *ValidateAddressAndCreatePiResponse) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *ValidateAddressAndCreatePiResponse) GetMerchant() *MerchantDetails {
	if x != nil {
		return x.Merchant
	}
	return nil
}

func (x *ValidateAddressAndCreatePiResponse) GetPiId() string {
	if x != nil {
		return x.PiId
	}
	return ""
}

type ValidateUpiNumberAndCreatePiRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Device ID or fingerprint of the device that is registered with the partner bank
	Device *Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// upi number that need to be verified
	UpiNumber string `protobuf:"bytes,2,opt,name=upi_number,json=upiNumber,proto3" json:"upi_number,omitempty"`
	// entity id of the actor payer
	PayerActorId string `protobuf:"bytes,3,opt,name=payer_actor_id,json=payerActorId,proto3" json:"payer_actor_id,omitempty"`
	// ownership of the pi to be created
	Ownership paymentinstrument.Ownership `protobuf:"varint,4,opt,name=ownership,proto3,enum=paymentinstrument.Ownership" json:"ownership,omitempty"`
}

func (x *ValidateUpiNumberAndCreatePiRequest) Reset() {
	*x = ValidateUpiNumberAndCreatePiRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateUpiNumberAndCreatePiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateUpiNumberAndCreatePiRequest) ProtoMessage() {}

func (x *ValidateUpiNumberAndCreatePiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateUpiNumberAndCreatePiRequest.ProtoReflect.Descriptor instead.
func (*ValidateUpiNumberAndCreatePiRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{55}
}

func (x *ValidateUpiNumberAndCreatePiRequest) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *ValidateUpiNumberAndCreatePiRequest) GetUpiNumber() string {
	if x != nil {
		return x.UpiNumber
	}
	return ""
}

func (x *ValidateUpiNumberAndCreatePiRequest) GetPayerActorId() string {
	if x != nil {
		return x.PayerActorId
	}
	return ""
}

func (x *ValidateUpiNumberAndCreatePiRequest) GetOwnership() paymentinstrument.Ownership {
	if x != nil {
		return x.Ownership
	}
	return paymentinstrument.Ownership(0)
}

type ValidateUpiNumberAndCreatePiResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// name of the user to which the id belongs to
	CustomerName string `protobuf:"bytes,2,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// Merchant Classification Code
	Mcc string `protobuf:"bytes,3,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// merchant details will be populated if the customer is merchant
	Merchant *MerchantDetails `protobuf:"bytes,4,opt,name=merchant,proto3" json:"merchant,omitempty"`
	// pi id for the payment instrument for the vpa linked to upi number
	PiId string `protobuf:"bytes,5,opt,name=pi_id,json=piId,proto3" json:"pi_id,omitempty"`
	// vpa linked to the upi number
	Vpa string `protobuf:"bytes,6,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// psp badge icon, will be populated for external psps
	PspBadgeIcon *common.Image `protobuf:"bytes,7,opt,name=psp_badge_icon,json=pspBadgeIcon,proto3" json:"psp_badge_icon,omitempty"`
}

func (x *ValidateUpiNumberAndCreatePiResponse) Reset() {
	*x = ValidateUpiNumberAndCreatePiResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateUpiNumberAndCreatePiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateUpiNumberAndCreatePiResponse) ProtoMessage() {}

func (x *ValidateUpiNumberAndCreatePiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateUpiNumberAndCreatePiResponse.ProtoReflect.Descriptor instead.
func (*ValidateUpiNumberAndCreatePiResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{56}
}

func (x *ValidateUpiNumberAndCreatePiResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ValidateUpiNumberAndCreatePiResponse) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *ValidateUpiNumberAndCreatePiResponse) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *ValidateUpiNumberAndCreatePiResponse) GetMerchant() *MerchantDetails {
	if x != nil {
		return x.Merchant
	}
	return nil
}

func (x *ValidateUpiNumberAndCreatePiResponse) GetPiId() string {
	if x != nil {
		return x.PiId
	}
	return ""
}

func (x *ValidateUpiNumberAndCreatePiResponse) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *ValidateUpiNumberAndCreatePiResponse) GetPspBadgeIcon() *common.Image {
	if x != nil {
		return x.PspBadgeIcon
	}
	return nil
}

type ValidateInternationalPaymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id corresponding to the international payment
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// quote currency base amount
	BaseAmountQuoteCurrency *money.Money `protobuf:"bytes,2,opt,name=base_amount_quote_currency,json=baseAmountQuoteCurrency,proto3" json:"base_amount_quote_currency,omitempty"`
	// total INR amount including all additional charges and taxes (will be used for validation of conversion of amount
	// from quote currency to INR)
	TotalAmountInr *money.Money `protobuf:"bytes,3,opt,name=total_amount_inr,json=totalAmountInr,proto3" json:"total_amount_inr,omitempty"`
}

func (x *ValidateInternationalPaymentRequest) Reset() {
	*x = ValidateInternationalPaymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateInternationalPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateInternationalPaymentRequest) ProtoMessage() {}

func (x *ValidateInternationalPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateInternationalPaymentRequest.ProtoReflect.Descriptor instead.
func (*ValidateInternationalPaymentRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{57}
}

func (x *ValidateInternationalPaymentRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ValidateInternationalPaymentRequest) GetBaseAmountQuoteCurrency() *money.Money {
	if x != nil {
		return x.BaseAmountQuoteCurrency
	}
	return nil
}

func (x *ValidateInternationalPaymentRequest) GetTotalAmountInr() *money.Money {
	if x != nil {
		return x.TotalAmountInr
	}
	return nil
}

type ValidateInternationalPaymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// UpiInternationalPaymentCharge - details regarding each type of charge being
	// applied on the international upi transaction
	UpiInternationalPaymentCharges []*UpiInternationalPaymentCharge `protobuf:"bytes,2,rep,name=upi_international_payment_charges,json=upiInternationalPaymentCharges,proto3" json:"upi_international_payment_charges,omitempty"`
}

func (x *ValidateInternationalPaymentResponse) Reset() {
	*x = ValidateInternationalPaymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateInternationalPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateInternationalPaymentResponse) ProtoMessage() {}

func (x *ValidateInternationalPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateInternationalPaymentResponse.ProtoReflect.Descriptor instead.
func (*ValidateInternationalPaymentResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{58}
}

func (x *ValidateInternationalPaymentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ValidateInternationalPaymentResponse) GetUpiInternationalPaymentCharges() []*UpiInternationalPaymentCharge {
	if x != nil {
		return x.UpiInternationalPaymentCharges
	}
	return nil
}

type GetUpiInternationalQrInfoFromCacheRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of the user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetUpiInternationalQrInfoFromCacheRequest) Reset() {
	*x = GetUpiInternationalQrInfoFromCacheRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUpiInternationalQrInfoFromCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUpiInternationalQrInfoFromCacheRequest) ProtoMessage() {}

func (x *GetUpiInternationalQrInfoFromCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUpiInternationalQrInfoFromCacheRequest.ProtoReflect.Descriptor instead.
func (*GetUpiInternationalQrInfoFromCacheRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{59}
}

func (x *GetUpiInternationalQrInfoFromCacheRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetUpiInternationalQrInfoFromCacheResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// upi_international_qr_info - stored qr info for
	// the user
	UpiInternationalQrInfo *UpiInternationalQrInfo `protobuf:"bytes,2,opt,name=upi_international_qr_info,json=upiInternationalQrInfo,proto3" json:"upi_international_qr_info,omitempty"`
}

func (x *GetUpiInternationalQrInfoFromCacheResponse) Reset() {
	*x = GetUpiInternationalQrInfoFromCacheResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUpiInternationalQrInfoFromCacheResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUpiInternationalQrInfoFromCacheResponse) ProtoMessage() {}

func (x *GetUpiInternationalQrInfoFromCacheResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUpiInternationalQrInfoFromCacheResponse.ProtoReflect.Descriptor instead.
func (*GetUpiInternationalQrInfoFromCacheResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{60}
}

func (x *GetUpiInternationalQrInfoFromCacheResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUpiInternationalQrInfoFromCacheResponse) GetUpiInternationalQrInfo() *UpiInternationalQrInfo {
	if x != nil {
		return x.UpiInternationalQrInfo
	}
	return nil
}

type GetExternalVpasRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the primary/ requester actor id initiating request
	PrimaryActorId string `protobuf:"bytes,1,opt,name=primary_actor_id,json=primaryActorId,proto3" json:"primary_actor_id,omitempty"`
	// field mask for identifier, used to filter out the final result
	// if the requirement is to fetch based on both email and phone, pass the actor_id in target_identifier
	// with email and phone as field masks
	IdentifierFieldMask []GetExternalVpasRequest_IdentifierFieldMask `protobuf:"varint,2,rep,packed,name=identifier_field_mask,json=identifierFieldMask,proto3,enum=upi.GetExternalVpasRequest_IdentifierFieldMask" json:"identifier_field_mask,omitempty"`
	// accepting one-of actor_id, email, phone-number
	//
	// Types that are assignable to Identifier:
	//
	//	*GetExternalVpasRequest_ActorId
	//	*GetExternalVpasRequest_Email
	//	*GetExternalVpasRequest_PhoneNumber
	Identifier isGetExternalVpasRequest_Identifier `protobuf_oneof:"Identifier"`
}

func (x *GetExternalVpasRequest) Reset() {
	*x = GetExternalVpasRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExternalVpasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExternalVpasRequest) ProtoMessage() {}

func (x *GetExternalVpasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExternalVpasRequest.ProtoReflect.Descriptor instead.
func (*GetExternalVpasRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{61}
}

func (x *GetExternalVpasRequest) GetPrimaryActorId() string {
	if x != nil {
		return x.PrimaryActorId
	}
	return ""
}

func (x *GetExternalVpasRequest) GetIdentifierFieldMask() []GetExternalVpasRequest_IdentifierFieldMask {
	if x != nil {
		return x.IdentifierFieldMask
	}
	return nil
}

func (m *GetExternalVpasRequest) GetIdentifier() isGetExternalVpasRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetExternalVpasRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetExternalVpasRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetExternalVpasRequest) GetEmail() string {
	if x, ok := x.GetIdentifier().(*GetExternalVpasRequest_Email); ok {
		return x.Email
	}
	return ""
}

func (x *GetExternalVpasRequest) GetPhoneNumber() *common.PhoneNumber {
	if x, ok := x.GetIdentifier().(*GetExternalVpasRequest_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return nil
}

type isGetExternalVpasRequest_Identifier interface {
	isGetExternalVpasRequest_Identifier()
}

type GetExternalVpasRequest_ActorId struct {
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3,oneof"`
}

type GetExternalVpasRequest_Email struct {
	Email string `protobuf:"bytes,4,opt,name=email,proto3,oneof"`
}

type GetExternalVpasRequest_PhoneNumber struct {
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

func (*GetExternalVpasRequest_ActorId) isGetExternalVpasRequest_Identifier() {}

func (*GetExternalVpasRequest_Email) isGetExternalVpasRequest_Identifier() {}

func (*GetExternalVpasRequest_PhoneNumber) isGetExternalVpasRequest_Identifier() {}

type GetExternalVpasResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// repeated vpa infos
	VpaInfos []*VpaInfo `protobuf:"bytes,2,rep,name=vpa_infos,json=vpaInfos,proto3" json:"vpa_infos,omitempty"`
}

func (x *GetExternalVpasResponse) Reset() {
	*x = GetExternalVpasResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExternalVpasResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExternalVpasResponse) ProtoMessage() {}

func (x *GetExternalVpasResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExternalVpasResponse.ProtoReflect.Descriptor instead.
func (*GetExternalVpasResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{62}
}

func (x *GetExternalVpasResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExternalVpasResponse) GetVpaInfos() []*VpaInfo {
	if x != nil {
		return x.VpaInfos
	}
	return nil
}

type GetNPCICLParametersV1Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of the user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Identifier of the account for which the request is initiated
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// upi flow type specifies the type of flow for which we need the CL Parameters
	UpiFlowType UpiFlowType `protobuf:"varint,3,opt,name=upi_flow_type,json=upiFlowType,proto3,enum=upi.UpiFlowType" json:"upi_flow_type,omitempty"`
	// backend will send creds based on upi pin set flow type (DEBIT CARD / AADHAAR NUMBER)
	// [optional]: required in flows like set pin when creds depends on the
	// chosen method for pin set (Debit Card / Aadhaar Number)
	UpiPinSetOptionType enums.UpiPinSetOptionType `protobuf:"varint,4,opt,name=upi_pin_set_option_type,json=upiPinSetOptionType,proto3,enum=upi.onboarding.enums.UpiPinSetOptionType" json:"upi_pin_set_option_type,omitempty"`
}

func (x *GetNPCICLParametersV1Request) Reset() {
	*x = GetNPCICLParametersV1Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNPCICLParametersV1Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNPCICLParametersV1Request) ProtoMessage() {}

func (x *GetNPCICLParametersV1Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNPCICLParametersV1Request.ProtoReflect.Descriptor instead.
func (*GetNPCICLParametersV1Request) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{63}
}

func (x *GetNPCICLParametersV1Request) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetNPCICLParametersV1Request) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetNPCICLParametersV1Request) GetUpiFlowType() UpiFlowType {
	if x != nil {
		return x.UpiFlowType
	}
	return UpiFlowType_UPI_FLOW_TYPE_UNSPECIFIED
}

func (x *GetNPCICLParametersV1Request) GetUpiPinSetOptionType() enums.UpiPinSetOptionType {
	if x != nil {
		return x.UpiPinSetOptionType
	}
	return enums.UpiPinSetOptionType(0)
}

type GetNPCICLParametersV1Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// NPCICLParams - represents the params based on the flow type
	//
	// Types that are assignable to NPCICLParams:
	//
	//	*GetNPCICLParametersV1Response_UpiLiteTransactionParams
	//	*GetNPCICLParametersV1Response_UpiLiteBalanceParams
	NPCICLParams isGetNPCICLParametersV1Response_NPCICLParams `protobuf_oneof:"NPCICLParams"`
}

func (x *GetNPCICLParametersV1Response) Reset() {
	*x = GetNPCICLParametersV1Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNPCICLParametersV1Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNPCICLParametersV1Response) ProtoMessage() {}

func (x *GetNPCICLParametersV1Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNPCICLParametersV1Response.ProtoReflect.Descriptor instead.
func (*GetNPCICLParametersV1Response) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{64}
}

func (x *GetNPCICLParametersV1Response) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (m *GetNPCICLParametersV1Response) GetNPCICLParams() isGetNPCICLParametersV1Response_NPCICLParams {
	if m != nil {
		return m.NPCICLParams
	}
	return nil
}

func (x *GetNPCICLParametersV1Response) GetUpiLiteTransactionParams() *UpiLiteTransactionParams {
	if x, ok := x.GetNPCICLParams().(*GetNPCICLParametersV1Response_UpiLiteTransactionParams); ok {
		return x.UpiLiteTransactionParams
	}
	return nil
}

func (x *GetNPCICLParametersV1Response) GetUpiLiteBalanceParams() *UpiLiteBalanceParams {
	if x, ok := x.GetNPCICLParams().(*GetNPCICLParametersV1Response_UpiLiteBalanceParams); ok {
		return x.UpiLiteBalanceParams
	}
	return nil
}

type isGetNPCICLParametersV1Response_NPCICLParams interface {
	isGetNPCICLParametersV1Response_NPCICLParams()
}

type GetNPCICLParametersV1Response_UpiLiteTransactionParams struct {
	// UPI_FLOW_TYPE_UPI_LITE_TOP_UP (MPIN and ARQC required)
	// UPI_FLOW_TYPE_UPI_LITE_PAYMENTS (ARQC required)
	// UPI_FLOW_TYPE_DEACTIVATE_UPI_LITE (ARQC required)
	UpiLiteTransactionParams *UpiLiteTransactionParams `protobuf:"bytes,2,opt,name=upi_lite_transaction_params,json=upiLiteTransactionParams,proto3,oneof"`
}

type GetNPCICLParametersV1Response_UpiLiteBalanceParams struct {
	// UPI_FLOW_TYPE_UPI_LITE_BALANCE
	UpiLiteBalanceParams *UpiLiteBalanceParams `protobuf:"bytes,3,opt,name=upi_lite_balance_params,json=upiLiteBalanceParams,proto3,oneof"`
}

func (*GetNPCICLParametersV1Response_UpiLiteTransactionParams) isGetNPCICLParametersV1Response_NPCICLParams() {
}

func (*GetNPCICLParametersV1Response_UpiLiteBalanceParams) isGetNPCICLParametersV1Response_NPCICLParams() {
}

type IsNewAddFundsVpaEnabledForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor id of user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *IsNewAddFundsVpaEnabledForActorRequest) Reset() {
	*x = IsNewAddFundsVpaEnabledForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsNewAddFundsVpaEnabledForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsNewAddFundsVpaEnabledForActorRequest) ProtoMessage() {}

func (x *IsNewAddFundsVpaEnabledForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsNewAddFundsVpaEnabledForActorRequest.ProtoReflect.Descriptor instead.
func (*IsNewAddFundsVpaEnabledForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{65}
}

func (x *IsNewAddFundsVpaEnabledForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type IsNewAddFundsVpaEnabledForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of the request
	Status    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsEnabled bool        `protobuf:"varint,2,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
}

func (x *IsNewAddFundsVpaEnabledForActorResponse) Reset() {
	*x = IsNewAddFundsVpaEnabledForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsNewAddFundsVpaEnabledForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsNewAddFundsVpaEnabledForActorResponse) ProtoMessage() {}

func (x *IsNewAddFundsVpaEnabledForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsNewAddFundsVpaEnabledForActorResponse.ProtoReflect.Descriptor instead.
func (*IsNewAddFundsVpaEnabledForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{66}
}

func (x *IsNewAddFundsVpaEnabledForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsNewAddFundsVpaEnabledForActorResponse) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

// reference received in checkTxn
// contains the payer/payee details with the status code for transaction
type CheckTxnStatusResponse_Ref struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type of ref eg, Payer, Payee
	Type CheckTxnStatusResponse_Ref_RefType `protobuf:"varint,1,opt,name=type,proto3,enum=upi.CheckTxnStatusResponse_Ref_RefType" json:"type,omitempty"`
	// sequence number
	SeqNum string `protobuf:"bytes,2,opt,name=seq_num,json=seqNum,proto3" json:"seq_num,omitempty"`
	// VPA of the Payer/Payee
	Vpa string `protobuf:"bytes,3,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// settlement amount
	SettAmount string `protobuf:"bytes,4,opt,name=sett_amount,json=settAmount,proto3" json:"sett_amount,omitempty"`
	// original amount
	OrgAmount string `protobuf:"bytes,5,opt,name=org_amount,json=orgAmount,proto3" json:"org_amount,omitempty"`
	// settlement currency
	SettCurrency string `protobuf:"bytes,6,opt,name=sett_currency,json=settCurrency,proto3" json:"sett_currency,omitempty"`
	ApprovalNum  string `protobuf:"bytes,7,opt,name=approval_num,json=approvalNum,proto3" json:"approval_num,omitempty"`
	// account details of payer/payee
	AccountDetails *CustomerAccountDetails `protobuf:"bytes,8,opt,name=account_details,json=accountDetails,proto3" json:"account_details,omitempty"`
	// response code
	RespCode string `protobuf:"bytes,9,opt,name=respCode,proto3" json:"respCode,omitempty"`
	// reversal response code
	ReversalRespCode string `protobuf:"bytes,10,opt,name=reversal_resp_code,json=reversalRespCode,proto3" json:"reversal_resp_code,omitempty"`
	// registered name of the customer
	Name string `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CheckTxnStatusResponse_Ref) Reset() {
	*x = CheckTxnStatusResponse_Ref{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTxnStatusResponse_Ref) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTxnStatusResponse_Ref) ProtoMessage() {}

func (x *CheckTxnStatusResponse_Ref) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTxnStatusResponse_Ref.ProtoReflect.Descriptor instead.
func (*CheckTxnStatusResponse_Ref) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CheckTxnStatusResponse_Ref) GetType() CheckTxnStatusResponse_Ref_RefType {
	if x != nil {
		return x.Type
	}
	return CheckTxnStatusResponse_Ref_REF_TYPE_UNSPECIFIED
}

func (x *CheckTxnStatusResponse_Ref) GetSeqNum() string {
	if x != nil {
		return x.SeqNum
	}
	return ""
}

func (x *CheckTxnStatusResponse_Ref) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *CheckTxnStatusResponse_Ref) GetSettAmount() string {
	if x != nil {
		return x.SettAmount
	}
	return ""
}

func (x *CheckTxnStatusResponse_Ref) GetOrgAmount() string {
	if x != nil {
		return x.OrgAmount
	}
	return ""
}

func (x *CheckTxnStatusResponse_Ref) GetSettCurrency() string {
	if x != nil {
		return x.SettCurrency
	}
	return ""
}

func (x *CheckTxnStatusResponse_Ref) GetApprovalNum() string {
	if x != nil {
		return x.ApprovalNum
	}
	return ""
}

func (x *CheckTxnStatusResponse_Ref) GetAccountDetails() *CustomerAccountDetails {
	if x != nil {
		return x.AccountDetails
	}
	return nil
}

func (x *CheckTxnStatusResponse_Ref) GetRespCode() string {
	if x != nil {
		return x.RespCode
	}
	return ""
}

func (x *CheckTxnStatusResponse_Ref) GetReversalRespCode() string {
	if x != nil {
		return x.ReversalRespCode
	}
	return ""
}

func (x *CheckTxnStatusResponse_Ref) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetPinFlowParametersResponse_BankConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PayerBankName    string `protobuf:"bytes,1,opt,name=payer_bank_name,json=payerBankName,proto3" json:"payer_bank_name,omitempty"`
	BackgroundColor  string `protobuf:"bytes,2,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	Color            string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
	ResendOtpFeature bool   `protobuf:"varint,4,opt,name=resend_otp_feature,json=resendOtpFeature,proto3" json:"resend_otp_feature,omitempty"`
}

func (x *GetPinFlowParametersResponse_BankConfig) Reset() {
	*x = GetPinFlowParametersResponse_BankConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinFlowParametersResponse_BankConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinFlowParametersResponse_BankConfig) ProtoMessage() {}

func (x *GetPinFlowParametersResponse_BankConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinFlowParametersResponse_BankConfig.ProtoReflect.Descriptor instead.
func (*GetPinFlowParametersResponse_BankConfig) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *GetPinFlowParametersResponse_BankConfig) GetPayerBankName() string {
	if x != nil {
		return x.PayerBankName
	}
	return ""
}

func (x *GetPinFlowParametersResponse_BankConfig) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *GetPinFlowParametersResponse_BankConfig) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *GetPinFlowParametersResponse_BankConfig) GetResendOtpFeature() bool {
	if x != nil {
		return x.ResendOtpFeature
	}
	return false
}

type GetTransactionParametersResponse_BankConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PayerBankName    string `protobuf:"bytes,1,opt,name=payer_bank_name,json=payerBankName,proto3" json:"payer_bank_name,omitempty"`
	BackgroundColor  string `protobuf:"bytes,2,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	Color            string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
	ResendOtpFeature bool   `protobuf:"varint,4,opt,name=resend_otp_feature,json=resendOtpFeature,proto3" json:"resend_otp_feature,omitempty"`
}

func (x *GetTransactionParametersResponse_BankConfig) Reset() {
	*x = GetTransactionParametersResponse_BankConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionParametersResponse_BankConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionParametersResponse_BankConfig) ProtoMessage() {}

func (x *GetTransactionParametersResponse_BankConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionParametersResponse_BankConfig.ProtoReflect.Descriptor instead.
func (*GetTransactionParametersResponse_BankConfig) Descriptor() ([]byte, []int) {
	return file_api_upi_service_proto_rawDescGZIP(), []int{18, 0}
}

func (x *GetTransactionParametersResponse_BankConfig) GetPayerBankName() string {
	if x != nil {
		return x.PayerBankName
	}
	return ""
}

func (x *GetTransactionParametersResponse_BankConfig) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *GetTransactionParametersResponse_BankConfig) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *GetTransactionParametersResponse_BankConfig) GetResendOtpFeature() bool {
	if x != nil {
		return x.ResendOtpFeature
	}
	return false
}

var File_api_upi_service_proto protoreflect.FileDescriptor

var file_api_upi_service_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x75, 0x70, 0x69, 0x1a, 0x1f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x21, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x72, 0x65, 0x64,
	0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70,
	0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f,
	0x75, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x72, 0x6e, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69,
	0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x75, 0x72,
	0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70,
	0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75,
	0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70,
	0x69, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x71, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x70, 0x69, 0x2f, 0x74, 0x78, 0x6e, 0x72, 0x65, 0x66, 0x2f, 0x74, 0x78, 0x6e, 0x5f, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x2e, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64,
	0x22, 0xc1, 0x08, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x31, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x66, 0x52, 0x03,
	0x72, 0x65, 0x66, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x75, 0x73, 0x74, 0x52, 0x65,
	0x66, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x61,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x72,
	0x61, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x61, 0x77,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x79, 0x65, 0x72, 0x12, 0x38, 0x0a,
	0x18, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x79, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61,
	0x79, 0x65, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x65, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x79, 0x65, 0x65, 0x12, 0x56, 0x0a,
	0x19, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x17, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x1a,
	0xd4, 0x03, 0x0a, 0x03, 0x52, 0x65, 0x66, 0x12, 0x3b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x52, 0x65, 0x66, 0x2e, 0x52, 0x65, 0x66, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x70, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x74, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x74, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x72, 0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x72, 0x67, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x65, 0x74, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x44, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x73, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x73, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x39, 0x0a, 0x07, 0x52,
	0x65, 0x66, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x46, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x09, 0x0a, 0x05, 0x50, 0x41, 0x59, 0x45, 0x52, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x50,
	0x41, 0x59, 0x45, 0x45, 0x10, 0x02, 0x22, 0x3c, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x33, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x10, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x15, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50,
	0x61, 0x79, 0x65, 0x65, 0x56, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d,
	0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x70, 0x69, 0x5f, 0x76, 0x70, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x70, 0x69, 0x56, 0x70, 0x61, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x70, 0x61, 0x79, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xdd, 0x04, 0x0a,
	0x16, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x65, 0x65, 0x56, 0x50, 0x41, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x63, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x63, 0x63, 0x12, 0x30, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x08, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x66, 0x73, 0x63, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x03,
	0x61, 0x70, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x03, 0x61, 0x70, 0x6f, 0x22, 0xae, 0x02, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0f, 0x0a, 0x0b,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x56, 0x50, 0x41, 0x10, 0x64, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e,
	0x54, 0x10, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44,
	0x5f, 0x42, 0x59, 0x5f, 0x50, 0x53, 0x50, 0x10, 0x66, 0x12, 0x12, 0x0a, 0x0e, 0x56, 0x50, 0x41,
	0x5f, 0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x45, 0x44, 0x10, 0x67, 0x12, 0x16, 0x0a,
	0x12, 0x50, 0x53, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45,
	0x52, 0x45, 0x44, 0x10, 0x68, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x53, 0x50, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x69, 0x12, 0x16, 0x0a, 0x12,
	0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x56,
	0x50, 0x41, 0x10, 0x6a, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x41, 0x59, 0x45, 0x45, 0x5f, 0x50, 0x53,
	0x50, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x6b, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x55, 0x53, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f,
	0x46, 0x52, 0x41, 0x55, 0x44, 0x10, 0x6c, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x41, 0x50, 0x50, 0x49,
	0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x6d, 0x12, 0x1d, 0x0a,
	0x19, 0x4e, 0x4f, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x10, 0x6e, 0x22, 0x53, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x40, 0x0a, 0x14, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x63, 0x68,
	0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x12, 0x63,
	0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67,
	0x65, 0x22, 0xc4, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x75, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41,
	0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x64, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x41, 0x44, 0x4c,
	0x49, 0x4e, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x5f,
	0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x65, 0x22, 0x99, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x47, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x4f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x72, 0x65, 0x71, 0x4f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65, 0x22, 0x58, 0x0a, 0x0a, 0x52, 0x65,
	0x71, 0x4f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x51, 0x5f,
	0x4f, 0x54, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x00, 0x12,
	0x16, 0x0a, 0x12, 0x52, 0x45, 0x51, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x49, 0x44, 0x41, 0x49, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x51, 0x5f, 0x4f,
	0x54, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x55, 0x49, 0x44,
	0x41, 0x49, 0x10, 0x02, 0x22, 0xa8, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x55, 0x70, 0x69, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65,
	0x55, 0x72, 0x6c, 0x22, 0xc9, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11,
	0x44, 0x45, 0x41, 0x44, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45,
	0x44, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x10, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x53, 0x50, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x4f, 0x55, 0x54, 0x10, 0x64, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x10, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x45, 0x41, 0x44, 0x4c, 0x49, 0x4e, 0x45, 0x5f,
	0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44,
	0x4f, 0x52, 0x10, 0x66, 0x12, 0x19, 0x0a, 0x15, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x45,
	0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x67, 0x22,
	0x29, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x22, 0xfb, 0x01, 0x0a, 0x16, 0x52,
	0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x69, 0x66, 0x73, 0x63, 0x12, 0x29, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x61, 0x63, 0x63, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x43, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xfa, 0x03, 0x0a, 0x10, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x56, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x42, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x42, 0x61, 0x6e, 0x6b, 0x12, 0x3b, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x26, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04,
	0x18, 0x32, 0x52, 0x08, 0x69, 0x66, 0x73, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x3d, 0x0a, 0x08, 0x76, 0x70, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x76, 0x70, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x4a,
	0x04, 0x08, 0x02, 0x10, 0x03, 0x22, 0x9c, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x56, 0x50, 0x41, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x62, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52,
	0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x64, 0x12, 0x15, 0x0a,
	0x11, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x10, 0x65, 0x22, 0x7f, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x6b, 0x65, 0x79, 0x5f, 0x78, 0x6d, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x65, 0x79, 0x58, 0x6d, 0x6c, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xc4, 0x02, 0x0a, 0x15, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x23, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x72,
	0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x09, 0x63, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x09, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63,
	0x61, 0x72, 0x64, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5f, 0x0a, 0x17, 0x75,
	0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x75, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53,
	0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa0, 0x04, 0x0a,
	0x16, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11,
	0x72, 0x61, 0x77, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xb4, 0x03, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12,
	0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x10, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x4e, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x41, 0x4e, 0x4b, 0x5f,
	0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x66, 0x12, 0x1c, 0x0a,
	0x18, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x10, 0x67, 0x12, 0x15, 0x0a, 0x11, 0x49,
	0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x50, 0x49, 0x4e,
	0x10, 0x68, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x69, 0x12, 0x19, 0x0a, 0x15,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x52,
	0x49, 0x43, 0x54, 0x45, 0x44, 0x10, 0x6a, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x45, 0x42, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x6b, 0x12,
	0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x5f, 0x4d, 0x50, 0x49,
	0x4e, 0x10, 0x6c, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54,
	0x5f, 0x4f, 0x54, 0x50, 0x10, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x54, 0x50, 0x5f, 0x45, 0x58,
	0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x6e, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x41, 0x58, 0x5f, 0x4f,
	0x54, 0x50, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x10, 0x6f, 0x12, 0x0f, 0x0a, 0x0b,
	0x50, 0x53, 0x50, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x10, 0x70, 0x12, 0x28, 0x0a,
	0x24, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x44, 0x4f, 0x52, 0x4d, 0x41, 0x4e, 0x54, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x71, 0x12, 0x19, 0x0a, 0x15, 0x56, 0x45, 0x4e, 0x44, 0x4f,
	0x52, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x72, 0x12, 0x1b, 0x0a, 0x17, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x45, 0x4e, 0x43,
	0x52, 0x59, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x73, 0x22,
	0xf9, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x0d, 0x70, 0x69, 0x6e,
	0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x46, 0x6c, 0x6f,
	0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x50, 0x69, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x69, 0x6e, 0x46, 0x6c, 0x6f,
	0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x5f, 0x0a, 0x17, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x75, 0x70, 0x69,
	0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x49, 0x0a, 0x0b, 0x50, 0x69, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1d, 0x0a, 0x19, 0x50, 0x49, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x43,
	0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x02, 0x22, 0xc1, 0x06, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2f, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x4b, 0x65, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6b, 0x65, 0x79, 0x5f, 0x78, 0x6d, 0x6c, 0x5f, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x65, 0x79,
	0x58, 0x6d, 0x6c, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x33, 0x0a, 0x0c, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4a, 0x73,
	0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4a, 0x73, 0x6f, 0x6e, 0x12,
	0x2c, 0x0a, 0x10, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6a,
	0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x62,
	0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x15, 0x69,
	0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x61, 0x6e, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x12, 0x32,
	0x0a, 0x15, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d,
	0x61, 0x73, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x04, 0x76, 0x70, 0x61, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x76, 0x70, 0x61, 0x73, 0x12, 0x4d, 0x0a, 0x0b, 0x62, 0x61,
	0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x46, 0x6c, 0x6f, 0x77,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x62,
	0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xa3, 0x01, 0x0a, 0x0a, 0x42, 0x61,
	0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x65,
	0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x74, 0x70, 0x5f,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x64, 0x4f, 0x74, 0x70, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22,
	0x7e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05,
	0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44,
	0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x4e,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x44, 0x10, 0x64, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x10, 0x65, 0x12, 0x10, 0x0a,
	0x0c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x66, 0x22,
	0x4b, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18,
	0x64, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xc2, 0x04, 0x0a,
	0x20, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4b, 0x65, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x07,
	0x6b, 0x65, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6b, 0x65, 0x79, 0x5f, 0x78,
	0x6d, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6b, 0x65, 0x79, 0x58, 0x6d, 0x6c, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x33, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x10, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4a, 0x73,
	0x6f, 0x6e, 0x12, 0x51, 0x0a, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x42,
	0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x62, 0x61, 0x6e, 0x6b, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xa3, 0x01, 0x0a, 0x0a, 0x42, 0x61, 0x6e, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70,
	0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2c, 0x0a,
	0x12, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x72, 0x65, 0x73, 0x65, 0x6e,
	0x64, 0x4f, 0x74, 0x70, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0x44, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11,
	0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45,
	0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x22, 0x5f, 0x0a, 0x10, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x55, 0x52, 0x4e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x63, 0x63, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x63, 0x63, 0x12, 0x27, 0x0a, 0x08, 0x75, 0x72, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x55, 0x52, 0x4e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x75, 0x72, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xff, 0x07, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x55, 0x52, 0x4e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a,
	0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0f, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52,
	0x65, 0x66, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x2b, 0x0a,
	0x0f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x07, 0x70, 0x75,
	0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x03, 0x6d, 0x63, 0x63, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x6d, 0x63, 0x63, 0x12, 0x23,
	0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x11, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x14, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x06, 0x6f, 0x72, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x05, 0x6f,
	0x72, 0x67, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x69, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x69, 0x73, 0x5f, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x5f, 0x71, 0x72, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69,
	0x73, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x73, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x51, 0x72, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x73, 0x65,
	0x64, 0x12, 0x2e, 0x0a, 0x11, 0x75, 0x72, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x65, 0x65, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0f, 0x75, 0x72, 0x6e, 0x50, 0x61, 0x79, 0x65, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x21, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x70, 0x61, 0x79, 0x65, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x10, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x75, 0x72, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x55, 0x72, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x0e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x55, 0x72, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x2e, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x35, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x6d, 0x69, 0x6e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x73, 0x65, 0x64,
	0x5f, 0x75, 0x72, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x55, 0x72, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x73,
	0x65, 0x64, 0x55, 0x72, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4b, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12,
	0x15, 0x0a, 0x11, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x10, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x75, 0x72, 0x6e, 0x5f, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x22, 0x84, 0x05, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3b,
	0x0a, 0x12, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x0f, 0x6f, 0x6c, 0x64, 0x50,
	0x69, 0x6e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x3b, 0x0a, 0x12, 0x6e,
	0x65, 0x77, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x72,
	0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x0f, 0x6e, 0x65, 0x77, 0x50, 0x69, 0x6e, 0x43,
	0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x3c, 0x0a, 0x18, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x16,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x69, 0x66, 0x73, 0x63, 0x12, 0x2d,
	0x0a, 0x08, 0x61, 0x63, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x61, 0x63, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x03, 0x76, 0x70, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03,
	0x76, 0x70, 0x61, 0x12, 0x32, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x08, 0x6d, 0x63, 0x63, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x6d,
	0x63, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4b, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x30, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x10, 0x02, 0x22, 0xc7, 0x03, 0x0a, 0x11,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0xe0, 0x02, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x4e, 0x41,
	0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x10, 0x10, 0x12, 0x14,
	0x0a, 0x10, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x44, 0x4f,
	0x57, 0x4e, 0x10, 0x64, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x50, 0x49,
	0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x66, 0x12, 0x18, 0x0a, 0x14, 0x44,
	0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x67, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45,
	0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x45, 0x44, 0x10, 0x68, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x69,
	0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x53, 0x50, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x10,
	0x6a, 0x12, 0x28, 0x0a, 0x24, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x52, 0x5f, 0x49, 0x4e,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x44, 0x4f, 0x52, 0x4d, 0x41, 0x4e,
	0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x6b, 0x12, 0x19, 0x0a, 0x15, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x6c, 0x12, 0x1b, 0x0a, 0x17, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x6d, 0x12, 0x20, 0x0a, 0x1c, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4f, 0x46,
	0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45,
	0x44, 0x45, 0x44, 0x10, 0x6e, 0x22, 0xdf, 0x05, 0x0a, 0x12, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x55, 0x52, 0x4e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10,
	0x70, 0x61, 0x79, 0x65, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x65, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x4f, 0x0a, 0x0f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x52, 0x4e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3a, 0x0a,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4e, 0x65, 0x78,
	0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x0c, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x52,
	0x4e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x17,
	0x74, 0x6f, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74,
	0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x22, 0x4a, 0x0a, 0x0e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x51, 0x52, 0x5f, 0x43, 0x4f, 0x44,
	0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x22,
	0xf5, 0x01, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f,
	0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x44,
	0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x4f,
	0x4d, 0x45, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x05, 0x12, 0x0d,
	0x0a, 0x09, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x10, 0x06, 0x12, 0x17, 0x0a,
	0x13, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x49, 0x53, 0x54, 0x45, 0x4e, 0x54,
	0x5f, 0x43, 0x54, 0x41, 0x10, 0x07, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12,
	0x42, 0x4f, 0x4e, 0x55, 0x53, 0x5f, 0x4a, 0x41, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x09, 0x12, 0x21, 0x0a, 0x1d, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f,
	0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0a, 0x22, 0xb8, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x55, 0x52, 0x4e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x64, 0x22, 0xce, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x33, 0x0a, 0x2f, 0x41, 0x44, 0x44, 0x5f,
	0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43,
	0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x6f, 0x12, 0x33, 0x0a,
	0x2f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x4b,
	0x59, 0x43, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x70, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f,
	0x4d, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x42, 0x41, 0x4c, 0x41,
	0x4e, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x71, 0x22, 0x39, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x55, 0x70, 0x69, 0x53, 0x65, 0x74, 0x75,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xa2, 0x01,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x70, 0x69, 0x53, 0x65, 0x74, 0x75, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x31, 0x0a, 0x0a, 0x75, 0x70, 0x69, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x69, 0x53, 0x65,
	0x74, 0x55, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x75, 0x70, 0x69, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x22, 0xee, 0x02, 0x0a, 0x19, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x72,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x4d, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x50, 0x41,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x76, 0x70, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x56, 0x70, 0x61, 0x12, 0x15, 0x0a, 0x05, 0x70, 0x69, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x70, 0x69, 0x49, 0x64, 0x22,
	0x44, 0x0a, 0x0b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c,
	0x0a, 0x18, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x53, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x02, 0x42, 0x0c, 0x0a, 0x0a, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x22, 0xbb, 0x01, 0x0a, 0x1a, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x4f,
	0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x50, 0x41, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x78, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12,
	0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x4c,
	0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10,
	0x32, 0x22, 0x5d, 0x0a, 0x10, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x22, 0xc6, 0x03, 0x0a, 0x11, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x68, 0x0a, 0x16, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x4d, 0x61, 0x70, 0x12, 0x61, 0x0a, 0x15, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x69, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x1a, 0x46, 0x0a, 0x18, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x57, 0x0a, 0x17, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x26, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x3a, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xc2, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x15, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x42, 0x0a, 0x1f, 0x47, 0x65,
	0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xc8,
	0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x15, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x30, 0x0a, 0x1c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x22, 0x7a, 0x0a, 0x1d, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x2d, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x56, 0x70,
	0x61, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x22, 0xb9, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x56, 0x70,
	0x61, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x11, 0x76, 0x70,
	0x61, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x70, 0x61, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x76, 0x70, 0x61,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x34, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x22, 0xb7, 0x05, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0c, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x75, 0x73,
	0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x41, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0b,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55,
	0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x37,
	0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66,
	0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x74, 0x6f, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x38, 0x0a, 0x07, 0x73, 0x6f,
	0x72, 0x74, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x06, 0x73, 0x6f,
	0x72, 0x74, 0x42, 0x79, 0x12, 0x2d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x53,
	0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x22, 0x4c, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x12, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54,
	0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x10,
	0x03, 0x22, 0x49, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12,
	0x0b, 0x0a, 0x07, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x03, 0x22, 0xdc, 0x01, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x42, 0x79, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x15, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x75, 0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22,
	0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xa5, 0x01, 0x0a, 0x17,
	0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0d, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x69, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x75, 0x0a, 0x18, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xc1, 0x01, 0x0a, 0x18, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x50, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x0f, 0x6e, 0x70, 0x63, 0x69, 0x5f, 0x63,
	0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52,
	0x0d, 0x6e, 0x70, 0x63, 0x69, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1e,
	0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x22, 0xda,
	0x01, 0x0a, 0x19, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x65, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x97, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x64,
	0x12, 0x18, 0x0a, 0x14, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f,
	0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x55,
	0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x66,
	0x12, 0x19, 0x0a, 0x15, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x67, 0x22, 0xc5, 0x02, 0x0a, 0x15,
	0x52, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x52, 0x61, 0x69, 0x73, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x52, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x49, 0x0a, 0x12, 0x52, 0x61, 0x69, 0x73,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24,
	0x0a, 0x20, 0x52, 0x41, 0x49, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x41, 0x49, 0x4e,
	0x54, 0x10, 0x01, 0x22, 0xbe, 0x02, 0x0a, 0x16, 0x52, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x04, 0x72, 0x65, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x74, 0x78, 0x6e, 0x72, 0x65, 0x66, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x04, 0x72, 0x65, 0x66, 0x73, 0x12, 0x5c, 0x0a, 0x17, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x3b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x64, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x10, 0x33, 0x22, 0x34, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x22, 0xa8, 0x02, 0x0a, 0x1c, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x32, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x74, 0x78, 0x6e, 0x72, 0x65, 0x66, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x03, 0x72, 0x65, 0x66, 0x12, 0x5c, 0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e,
	0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x15, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x22, 0x51, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x64, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x33, 0x22, 0x72, 0x0a, 0x1b, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x55,
	0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0d, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x70, 0x69,
	0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x79, 0x0a, 0x1c, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x34,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x22, 0xbe, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x56, 0x70, 0x61, 0x73, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x24, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x65, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x56, 0x70, 0x61, 0x73, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x08, 0x76, 0x70, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x70, 0x61, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x76, 0x70, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x34, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0x88, 0x03, 0x0a, 0x21, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x41, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x75, 0x70, 0x69, 0x5f, 0x76, 0x70, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x70, 0x69, 0x56, 0x70, 0x61, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x65, 0x72,
	0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x61, 0x79, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3a, 0x0a,
	0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x09,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x68, 0x0a, 0x13, 0x76, 0x70, 0x61,
	0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x41, 0x6e, 0x64, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x56,
	0x70, 0x61, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x52, 0x11, 0x76, 0x70, 0x61, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x22, 0x59, 0x0a, 0x11, 0x56, 0x70, 0x61, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x56, 0x50, 0x41, 0x5f,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a,
	0x1b, 0x56, 0x50, 0x41, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4c, 0x45, 0x4e, 0x49, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x22, 0xd1,
	0x02, 0x0a, 0x22, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x41, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x63, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x63,
	0x63, 0x12, 0x30, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x12, 0x13, 0x0a, 0x05, 0x70, 0x69, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x69, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x56, 0x50, 0x41, 0x10, 0x64, 0x12, 0x12, 0x0a, 0x0e, 0x56, 0x50,
	0x41, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x45, 0x44, 0x10, 0x65, 0x12, 0x15,
	0x0a, 0x11, 0x50, 0x53, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x66, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x55, 0x53, 0x50, 0x45, 0x43, 0x54,
	0x45, 0x44, 0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x10, 0x67, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x53,
	0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44,
	0x10, 0x68, 0x22, 0xd5, 0x01, 0x0a, 0x23, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x69,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75,
	0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x65,
	0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x70, 0x61, 0x79, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3a,
	0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x69, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52,
	0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x22, 0x8e, 0x03, 0x0a, 0x24, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x41, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x63, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x63, 0x63, 0x12,
	0x30, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x12, 0x13, 0x0a, 0x05, 0x70, 0x69, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x70, 0x69, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x3f, 0x0a, 0x0e, 0x70, 0x73, 0x70, 0x5f,
	0x62, 0x61, 0x64, 0x67, 0x65, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x70, 0x73, 0x70,
	0x42, 0x61, 0x64, 0x67, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0x70, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x56, 0x50, 0x41, 0x10, 0x64, 0x12, 0x12, 0x0a, 0x0e, 0x56, 0x50,
	0x41, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x45, 0x44, 0x10, 0x65, 0x12, 0x15,
	0x0a, 0x11, 0x50, 0x53, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x66, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47,
	0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x67, 0x22, 0xcf, 0x01, 0x0a, 0x23,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4f,
	0x0a, 0x1a, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x71, 0x75,
	0x6f, 0x74, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x17, 0x62, 0x61, 0x73, 0x65, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x3c, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x6e, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x72, 0x22, 0x9a, 0x02,
	0x0a, 0x24, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6d, 0x0a, 0x21, 0x75,
	0x70, 0x69, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x69,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x1e, 0x75, 0x70, 0x69, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x22, 0x5e, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52,
	0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44,
	0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x06, 0x22, 0x4f, 0x0a, 0x29, 0x47, 0x65,
	0x74, 0x55, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x51, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xf5, 0x01, 0x0a, 0x2a,
	0x47, 0x65, 0x74, 0x55, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x51, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x56, 0x0a, 0x19, 0x75, 0x70, 0x69, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x71, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x16, 0x75, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x51, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43,
	0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12,
	0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0xac, 0x03, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x56, 0x70, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31,
	0x0a, 0x10, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x63, 0x0a, 0x15, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x2f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x56, 0x70, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x52, 0x13, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x44, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x22, 0x71, 0x0a, 0x13, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x44, 0x45, 0x4e,
	0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x44, 0x45,
	0x46, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x44, 0x45, 0x4e, 0x54,
	0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42,
	0x45, 0x52, 0x10, 0x02, 0x42, 0x0c, 0x0a, 0x0a, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x22, 0xb6, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x56, 0x70, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x09, 0x76, 0x70, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x70, 0x61,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x76, 0x70, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22,
	0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x8b, 0x02, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x4e, 0x50, 0x43, 0x49, 0x43, 0x4c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x56, 0x31, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0d, 0x75, 0x70, 0x69, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x10, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x69, 0x46, 0x6c, 0x6f, 0x77, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x75, 0x70, 0x69,
	0x46, 0x6c, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5f, 0x0a, 0x17, 0x75, 0x70, 0x69, 0x5f,
	0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x75, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0xce, 0x02, 0x0a, 0x1d, 0x47, 0x65,
	0x74, 0x4e, 0x50, 0x43, 0x49, 0x43, 0x4c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x56, 0x31, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x5e, 0x0a, 0x1b, 0x75, 0x70, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x69, 0x4c,
	0x69, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x18, 0x75, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x52, 0x0a, 0x17, 0x75, 0x70, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f, 0x62, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x14,
	0x75, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x22, 0x44, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x42, 0x0e, 0x0a, 0x0c, 0x4e, 0x50,
	0x43, 0x49, 0x43, 0x4c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x43, 0x0a, 0x26, 0x49, 0x73,
	0x4e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x56, 0x70, 0x61, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22,
	0xb9, 0x01, 0x0a, 0x27, 0x49, 0x73, 0x4e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64,
	0x73, 0x56, 0x70, 0x61, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22,
	0x4a, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47,
	0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x2a, 0x79, 0x0a, 0x07, 0x55,
	0x52, 0x4e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x52, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x12, 0x0a, 0x0e, 0x42, 0x48, 0x41, 0x52, 0x41, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x55,
	0x52, 0x4e, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x51, 0x52, 0x5f, 0x55, 0x52, 0x4e, 0x10, 0x02,
	0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x52, 0x4e, 0x10, 0x03,
	0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x52, 0x4e, 0x10,
	0x04, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4c,
	0x4c, 0x45, 0x43, 0x54, 0x10, 0x05, 0x2a, 0x5c, 0x0a, 0x09, 0x53, 0x6f, 0x72, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x18, 0x0a, 0x14, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x53,
	0x43, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x4f, 0x52,
	0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x02, 0x2a, 0x4e, 0x0a, 0x0f, 0x55, 0x70, 0x69, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x50, 0x49, 0x5f, 0x55,
	0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x50,
	0x49, 0x5f, 0x45, 0x54, 0x42, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45,
	0x45, 0x4e, 0x10, 0x01, 0x32, 0xb4, 0x1a, 0x0a, 0x03, 0x55, 0x50, 0x49, 0x12, 0x4b, 0x0a, 0x0e,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x65, 0x65, 0x56, 0x50, 0x41, 0x12, 0x1a,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x65, 0x65,
	0x56, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x50, 0x61, 0x79, 0x65, 0x65, 0x56, 0x50, 0x41, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x39, 0x0a, 0x08, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x55, 0x70, 0x69, 0x4f, 0x74, 0x70, 0x12, 0x1a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x55, 0x70, 0x69, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x4b, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3c,
	0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x50, 0x41, 0x12, 0x15, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56,
	0x50, 0x41, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3b, 0x0a, 0x08,
	0x4c, 0x69, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x15, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4b, 0x65, 0x79, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0e, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e,
	0x46, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x20,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x46, 0x6c, 0x6f,
	0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x24, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x3c, 0x0a, 0x09, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x55, 0x52, 0x4e, 0x12, 0x15, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x55, 0x52, 0x4e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x55, 0x52, 0x4e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3c,
	0x0a, 0x09, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x69, 0x6e, 0x12, 0x15, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50,
	0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x42, 0x0a, 0x0b,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x52, 0x4e, 0x12, 0x17, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x55, 0x52, 0x4e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x55, 0x52, 0x4e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x54, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x55, 0x70, 0x69, 0x53, 0x65, 0x74, 0x75, 0x70, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x70, 0x69, 0x53, 0x65, 0x74, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x70,
	0x69, 0x53, 0x65, 0x74, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x68, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x52, 0x4e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x23, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x57, 0x0a, 0x12, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x4f, 0x72, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x56, 0x50, 0x41, 0x12, 0x1e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x4f, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x50, 0x41, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x4f, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x50, 0x41, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73,
	0x12, 0x1e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12,
	0x24, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60,
	0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x12, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x6d, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x23, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x57, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x56, 0x70, 0x61, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x70, 0x61, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x70, 0x61, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50,
	0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x22, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a,
	0x10, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x12, 0x1c, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x54, 0x0a, 0x11, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x65, 0x50, 0x69, 0x6e, 0x12, 0x1d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x2a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x6e, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x4b, 0x0a, 0x0e, 0x52, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x12, 0x1a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x52, 0x61, 0x69, 0x73, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x52, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b,
	0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x78, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x14, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x20, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x14, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x20, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x55,
	0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x1c, 0x47, 0x65, 0x74,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x56, 0x70, 0x61, 0x73, 0x42, 0x79, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x56, 0x70, 0x61, 0x73, 0x42,
	0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x56, 0x70, 0x61, 0x73, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x6f, 0x0a, 0x1a, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x41, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x12, 0x26,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x41, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x41, 0x6e, 0x64, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x75, 0x0a, 0x1c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x69, 0x12, 0x28, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x41, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x69, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x1c, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x87, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x55, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f,
	0x6d, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x2e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x51, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x51, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x56, 0x70, 0x61, 0x73, 0x12, 0x1b, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x56, 0x70,
	0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x56, 0x70, 0x61, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x4e, 0x50, 0x43, 0x49, 0x43, 0x4c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x56, 0x31, 0x12, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x50, 0x43, 0x49,
	0x43, 0x4c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x56, 0x31, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x50, 0x43, 0x49, 0x43, 0x4c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x56,
	0x31, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x1f, 0x49,
	0x73, 0x4e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x56, 0x70, 0x61, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2b,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x49, 0x73, 0x4e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e,
	0x64, 0x73, 0x56, 0x70, 0x61, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x49, 0x73, 0x4e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x56,
	0x70, 0x61, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x40, 0x0a, 0x1e, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x5a, 0x1e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_service_proto_rawDescOnce sync.Once
	file_api_upi_service_proto_rawDescData = file_api_upi_service_proto_rawDesc
)

func file_api_upi_service_proto_rawDescGZIP() []byte {
	file_api_upi_service_proto_rawDescOnce.Do(func() {
		file_api_upi_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_service_proto_rawDescData)
	})
	return file_api_upi_service_proto_rawDescData
}

var file_api_upi_service_proto_enumTypes = make([]protoimpl.EnumInfo, 49)
var file_api_upi_service_proto_msgTypes = make([]protoimpl.MessageInfo, 72)
var file_api_upi_service_proto_goTypes = []interface{}{
	(URNType)(0),                                             // 0: upi.URNType
	(SortOrder)(0),                                           // 1: upi.SortOrder
	(UpiUserActivity)(0),                                     // 2: upi.UpiUserActivity
	(CheckTxnStatusResponse_Status)(0),                       // 3: upi.CheckTxnStatusResponse.Status
	(CheckTxnStatusResponse_Ref_RefType)(0),                  // 4: upi.CheckTxnStatusResponse.Ref.RefType
	(VerifyPayeeVPAResponse_Status)(0),                       // 5: upi.VerifyPayeeVPAResponse.Status
	(GetTokenResponse_Status)(0),                             // 6: upi.GetTokenResponse.Status
	(GenerateUpiOtpRequest_ReqOtpType)(0),                    // 7: upi.GenerateUpiOtpRequest.ReqOtpType
	(GenerateUpiOtpResponse_Status)(0),                       // 8: upi.GenerateUpiOtpResponse.Status
	(ResolveAccountResponse_Status)(0),                       // 9: upi.ResolveAccountResponse.Status
	(CreateVPAResponse_Status)(0),                            // 10: upi.CreateVPAResponse.Status
	(ListKeysResponse_Status)(0),                             // 11: upi.ListKeysResponse.Status
	(RegisterMobileResponse_Status)(0),                       // 12: upi.RegisterMobileResponse.Status
	(GetPinFlowParametersRequest_PinFlowType)(0),             // 13: upi.GetPinFlowParametersRequest.PinFlowType
	(GetPinFlowParametersResponse_Status)(0),                 // 14: upi.GetPinFlowParametersResponse.Status
	(GetTransactionParametersResponse_Status)(0),             // 15: upi.GetTransactionParametersResponse.Status
	(VerifyURNResponse_Status)(0),                            // 16: upi.VerifyURNResponse.Status
	(ChangePinRequest_CustomerType)(0),                       // 17: upi.ChangePinRequest.CustomerType
	(ChangePinResponse_Status)(0),                            // 18: upi.ChangePinResponse.Status
	(GenerateURNRequest_InitiationMode)(0),                   // 19: upi.GenerateURNRequest.InitiationMode
	(GenerateURNRequest_Source)(0),                           // 20: upi.GenerateURNRequest.Source
	(GenerateURNResponse_Status)(0),                          // 21: upi.GenerateURNResponse.Status
	(GetUpiSetupStatusResponse_Status)(0),                    // 22: upi.GetUpiSetupStatusResponse.Status
	(DisableOrEnableVPARequest_RequestType)(0),               // 23: upi.DisableOrEnableVPARequest.RequestType
	(DisableOrEnableVPAResponse_Status)(0),                   // 24: upi.DisableOrEnableVPAResponse.Status
	(PinStatusResponse_Status)(0),                            // 25: upi.PinStatusResponse.Status
	(GetAccountPinInfosResponse_Status)(0),                   // 26: upi.GetAccountPinInfosResponse.Status
	(GetLatestAccountPinInfosResponse_Status)(0),             // 27: upi.GetLatestAccountPinInfosResponse.Status
	(CheckVerifiedMerchantResponse_Status)(0),                // 28: upi.CheckVerifiedMerchantResponse.Status
	(GetVpaMerchantInfoResponse_Status)(0),                   // 29: upi.GetVpaMerchantInfoResponse.Status
	(GetPinInfosByAccountIdRequest_Action)(0),                // 30: upi.GetPinInfosByAccountIdRequest.Action
	(GetPinInfosByAccountIdRequest_Status)(0),                // 31: upi.GetPinInfosByAccountIdRequest.Status
	(GetPinInfosByAccountIdResponse_Status)(0),               // 32: upi.GetPinInfosByAccountIdResponse.Status
	(PostUserActivityResponse_Status)(0),                     // 33: upi.PostUserActivityResponse.Status
	(ValidateSecurePinResponse_Status)(0),                    // 34: upi.ValidateSecurePinResponse.Status
	(RaiseComplaintRequest_RaiseComplaintType)(0),            // 35: upi.RaiseComplaintRequest.RaiseComplaintType
	(RaiseComplaintResponse_Status)(0),                       // 36: upi.RaiseComplaintResponse.Status
	(CheckComplaintStatusResponse_Status)(0),                 // 37: upi.CheckComplaintStatusResponse.Status
	(ChangeUpiPinSetStateResponse_Status)(0),                 // 38: upi.ChangeUpiPinSetStateResponse.Status
	(GetVerifiedVpasByPhoneNumberResponse_Status)(0),         // 39: upi.GetVerifiedVpasByPhoneNumberResponse.Status
	(ValidateAddressAndCreatePiRequest_VpaValidationMode)(0), // 40: upi.ValidateAddressAndCreatePiRequest.VpaValidationMode
	(ValidateAddressAndCreatePiResponse_Status)(0),           // 41: upi.ValidateAddressAndCreatePiResponse.Status
	(ValidateUpiNumberAndCreatePiResponse_Status)(0),         // 42: upi.ValidateUpiNumberAndCreatePiResponse.Status
	(ValidateInternationalPaymentResponse_Status)(0),         // 43: upi.ValidateInternationalPaymentResponse.Status
	(GetUpiInternationalQrInfoFromCacheResponse_Status)(0),   // 44: upi.GetUpiInternationalQrInfoFromCacheResponse.Status
	(GetExternalVpasRequest_IdentifierFieldMask)(0),          // 45: upi.GetExternalVpasRequest.IdentifierFieldMask
	(GetExternalVpasResponse_Status)(0),                      // 46: upi.GetExternalVpasResponse.Status
	(GetNPCICLParametersV1Response_Status)(0),                // 47: upi.GetNPCICLParametersV1Response.Status
	(IsNewAddFundsVpaEnabledForActorResponse_Status)(0),      // 48: upi.IsNewAddFundsVpaEnabledForActorResponse.Status
	(*CheckTxnStatusRequest)(nil),                            // 49: upi.CheckTxnStatusRequest
	(*CheckTxnStatusResponse)(nil),                           // 50: upi.CheckTxnStatusResponse
	(*VerifyPayeeVPARequest)(nil),                            // 51: upi.VerifyPayeeVPARequest
	(*VerifyPayeeVPAResponse)(nil),                           // 52: upi.VerifyPayeeVPAResponse
	(*GetTokenRequest)(nil),                                  // 53: upi.GetTokenRequest
	(*GetTokenResponse)(nil),                                 // 54: upi.GetTokenResponse
	(*GenerateUpiOtpRequest)(nil),                            // 55: upi.GenerateUpiOtpRequest
	(*GenerateUpiOtpResponse)(nil),                           // 56: upi.GenerateUpiOtpResponse
	(*ResolveAccountRequest)(nil),                            // 57: upi.ResolveAccountRequest
	(*ResolveAccountResponse)(nil),                           // 58: upi.ResolveAccountResponse
	(*CreateVPARequest)(nil),                                 // 59: upi.CreateVPARequest
	(*CreateVPAResponse)(nil),                                // 60: upi.CreateVPAResponse
	(*ListKeysResponse)(nil),                                 // 61: upi.ListKeysResponse
	(*RegisterMobileRequest)(nil),                            // 62: upi.RegisterMobileRequest
	(*RegisterMobileResponse)(nil),                           // 63: upi.RegisterMobileResponse
	(*GetPinFlowParametersRequest)(nil),                      // 64: upi.GetPinFlowParametersRequest
	(*GetPinFlowParametersResponse)(nil),                     // 65: upi.GetPinFlowParametersResponse
	(*GetTransactionParametersRequest)(nil),                  // 66: upi.GetTransactionParametersRequest
	(*GetTransactionParametersResponse)(nil),                 // 67: upi.GetTransactionParametersResponse
	(*VerifyURNRequest)(nil),                                 // 68: upi.VerifyURNRequest
	(*VerifyURNResponse)(nil),                                // 69: upi.VerifyURNResponse
	(*ChangePinRequest)(nil),                                 // 70: upi.ChangePinRequest
	(*ChangePinResponse)(nil),                                // 71: upi.ChangePinResponse
	(*GenerateURNRequest)(nil),                               // 72: upi.GenerateURNRequest
	(*GenerateURNResponse)(nil),                              // 73: upi.GenerateURNResponse
	(*GetUpiSetupStatusRequest)(nil),                         // 74: upi.GetUpiSetupStatusRequest
	(*GetUpiSetupStatusResponse)(nil),                        // 75: upi.GetUpiSetupStatusResponse
	(*DisableOrEnableVPARequest)(nil),                        // 76: upi.DisableOrEnableVPARequest
	(*DisableOrEnableVPAResponse)(nil),                       // 77: upi.DisableOrEnableVPAResponse
	(*PinStatusRequest)(nil),                                 // 78: upi.PinStatusRequest
	(*PinStatusResponse)(nil),                                // 79: upi.PinStatusResponse
	(*GetAccountPinInfosRequest)(nil),                        // 80: upi.GetAccountPinInfosRequest
	(*GetAccountPinInfosResponse)(nil),                       // 81: upi.GetAccountPinInfosResponse
	(*GetLatestAccountPinInfosRequest)(nil),                  // 82: upi.GetLatestAccountPinInfosRequest
	(*GetLatestAccountPinInfosResponse)(nil),                 // 83: upi.GetLatestAccountPinInfosResponse
	(*CheckVerifiedMerchantRequest)(nil),                     // 84: upi.CheckVerifiedMerchantRequest
	(*CheckVerifiedMerchantResponse)(nil),                    // 85: upi.CheckVerifiedMerchantResponse
	(*GetVpaMerchantInfoRequest)(nil),                        // 86: upi.GetVpaMerchantInfoRequest
	(*GetVpaMerchantInfoResponse)(nil),                       // 87: upi.GetVpaMerchantInfoResponse
	(*GetPinInfosByAccountIdRequest)(nil),                    // 88: upi.GetPinInfosByAccountIdRequest
	(*GetPinInfosByAccountIdResponse)(nil),                   // 89: upi.GetPinInfosByAccountIdResponse
	(*PostUserActivityRequest)(nil),                          // 90: upi.PostUserActivityRequest
	(*PostUserActivityResponse)(nil),                         // 91: upi.PostUserActivityResponse
	(*ValidateSecurePinRequest)(nil),                         // 92: upi.ValidateSecurePinRequest
	(*ValidateSecurePinResponse)(nil),                        // 93: upi.ValidateSecurePinResponse
	(*RaiseComplaintRequest)(nil),                            // 94: upi.RaiseComplaintRequest
	(*RaiseComplaintResponse)(nil),                           // 95: upi.RaiseComplaintResponse
	(*CheckComplaintStatusRequest)(nil),                      // 96: upi.CheckComplaintStatusRequest
	(*CheckComplaintStatusResponse)(nil),                     // 97: upi.CheckComplaintStatusResponse
	(*ChangeUpiPinSetStateRequest)(nil),                      // 98: upi.ChangeUpiPinSetStateRequest
	(*ChangeUpiPinSetStateResponse)(nil),                     // 99: upi.ChangeUpiPinSetStateResponse
	(*GetVerifiedVpasByPhoneNumberRequest)(nil),              // 100: upi.GetVerifiedVpasByPhoneNumberRequest
	(*GetVerifiedVpasByPhoneNumberResponse)(nil),             // 101: upi.GetVerifiedVpasByPhoneNumberResponse
	(*ValidateAddressAndCreatePiRequest)(nil),                // 102: upi.ValidateAddressAndCreatePiRequest
	(*ValidateAddressAndCreatePiResponse)(nil),               // 103: upi.ValidateAddressAndCreatePiResponse
	(*ValidateUpiNumberAndCreatePiRequest)(nil),              // 104: upi.ValidateUpiNumberAndCreatePiRequest
	(*ValidateUpiNumberAndCreatePiResponse)(nil),             // 105: upi.ValidateUpiNumberAndCreatePiResponse
	(*ValidateInternationalPaymentRequest)(nil),              // 106: upi.ValidateInternationalPaymentRequest
	(*ValidateInternationalPaymentResponse)(nil),             // 107: upi.ValidateInternationalPaymentResponse
	(*GetUpiInternationalQrInfoFromCacheRequest)(nil),        // 108: upi.GetUpiInternationalQrInfoFromCacheRequest
	(*GetUpiInternationalQrInfoFromCacheResponse)(nil),       // 109: upi.GetUpiInternationalQrInfoFromCacheResponse
	(*GetExternalVpasRequest)(nil),                           // 110: upi.GetExternalVpasRequest
	(*GetExternalVpasResponse)(nil),                          // 111: upi.GetExternalVpasResponse
	(*GetNPCICLParametersV1Request)(nil),                     // 112: upi.GetNPCICLParametersV1Request
	(*GetNPCICLParametersV1Response)(nil),                    // 113: upi.GetNPCICLParametersV1Response
	(*IsNewAddFundsVpaEnabledForActorRequest)(nil),           // 114: upi.IsNewAddFundsVpaEnabledForActorRequest
	(*IsNewAddFundsVpaEnabledForActorResponse)(nil),          // 115: upi.IsNewAddFundsVpaEnabledForActorResponse
	(*CheckTxnStatusResponse_Ref)(nil),                       // 116: upi.CheckTxnStatusResponse.Ref
	(*GetPinFlowParametersResponse_BankConfig)(nil),          // 117: upi.GetPinFlowParametersResponse.BankConfig
	(*GetTransactionParametersResponse_BankConfig)(nil),      // 118: upi.GetTransactionParametersResponse.BankConfig
	nil,                                   // 119: upi.PinStatusResponse.AccountPinStatusMapEntry
	nil,                                   // 120: upi.PinStatusResponse.AccountPinStateMapEntry
	(*rpc.Status)(nil),                    // 121: rpc.Status
	(*timestamppb.Timestamp)(nil),         // 122: google.protobuf.Timestamp
	(*Device)(nil),                        // 123: upi.Device
	(*MerchantDetails)(nil),               // 124: upi.MerchantDetails
	(accounts.Type)(0),                    // 125: accounts.Type
	(account.AccountProductOffering)(0),   // 126: api.typesv2.account.AccountProductOffering
	(*CredBlock)(nil),                     // 127: upi.CredBlock
	(*common.PhoneNumber)(nil),            // 128: api.typesv2.common.PhoneNumber
	(vendorgateway.Vendor)(0),             // 129: vendorgateway.Vendor
	(*common.Name)(nil),                   // 130: api.typesv2.common.Name
	(*card.BasicCardInfo)(nil),            // 131: card.BasicCardInfo
	(enums.UpiPinSetOptionType)(0),        // 132: upi.onboarding.enums.UpiPinSetOptionType
	(typesv2.KeyCode)(0),                  // 133: api.typesv2.KeyCode
	(*ControlJson)(nil),                   // 134: upi.ControlJson
	(*domain_model.MandateUrnInfo)(nil),   // 135: upi.domain_model.MandateUrnInfo
	(*money.Money)(nil),                   // 136: google.type.Money
	(*domain_model.UrnInfo)(nil),          // 137: upi.domain_model.UrnInfo
	(*NextOrderInfo)(nil),                 // 138: upi.NextOrderInfo
	(UpiSetUpState)(0),                    // 139: upi.UpiSetUpState
	(paymentinstrument.Source)(0),         // 140: paymentinstrument.Source
	(*AccountUpiPinInfo)(nil),             // 141: upi.AccountUpiPinInfo
	(*VpaMerchantInfo)(nil),               // 142: upi.VpaMerchantInfo
	(AccountUpiPinInfoFieldMask)(0),       // 143: upi.AccountUpiPinInfoFieldMask
	(*complaint.Complaint)(nil),           // 144: upi.complaint.Complaint
	(*txnref.TransactionReference)(nil),   // 145: upi.txnref.TransactionReference
	(complaint.ComplaintDisputeState)(0),  // 146: upi.complaint.ComplaintDisputeState
	(PinSetState)(0),                      // 147: upi.PinSetState
	(*VpaInfo)(nil),                       // 148: upi.Vpa_info
	(paymentinstrument.Ownership)(0),      // 149: paymentinstrument.Ownership
	(*common.Image)(nil),                  // 150: api.typesv2.common.Image
	(*UpiInternationalPaymentCharge)(nil), // 151: upi.UpiInternationalPaymentCharge
	(*UpiInternationalQrInfo)(nil),        // 152: upi.UpiInternationalQrInfo
	(UpiFlowType)(0),                      // 153: upi.UpiFlowType
	(*UpiLiteTransactionParams)(nil),      // 154: upi.UpiLiteTransactionParams
	(*UpiLiteBalanceParams)(nil),          // 155: upi.UpiLiteBalanceParams
	(*CustomerAccountDetails)(nil),        // 156: upi.CustomerAccountDetails
	(*emptypb.Empty)(nil),                 // 157: google.protobuf.Empty
	(*domain.ProcessPaymentRequest)(nil),  // 158: order.domain.ProcessPaymentRequest
	(*domain.ProcessPaymentResponse)(nil), // 159: order.domain.ProcessPaymentResponse
}
var file_api_upi_service_proto_depIdxs = []int32{
	121, // 0: upi.CheckTxnStatusResponse.status:type_name -> rpc.Status
	116, // 1: upi.CheckTxnStatusResponse.ref:type_name -> upi.CheckTxnStatusResponse.Ref
	122, // 2: upi.CheckTxnStatusResponse.original_transaction_date:type_name -> google.protobuf.Timestamp
	123, // 3: upi.VerifyPayeeVPARequest.device:type_name -> upi.Device
	121, // 4: upi.VerifyPayeeVPAResponse.status:type_name -> rpc.Status
	124, // 5: upi.VerifyPayeeVPAResponse.merchant:type_name -> upi.MerchantDetails
	125, // 6: upi.VerifyPayeeVPAResponse.account_type:type_name -> accounts.Type
	126, // 7: upi.VerifyPayeeVPAResponse.apo:type_name -> api.typesv2.account.AccountProductOffering
	127, // 8: upi.GetTokenRequest.cred_block_challenge:type_name -> upi.CredBlock
	121, // 9: upi.GetTokenResponse.status:type_name -> rpc.Status
	123, // 10: upi.GenerateUpiOtpRequest.device:type_name -> upi.Device
	7,   // 11: upi.GenerateUpiOtpRequest.req_otp_type:type_name -> upi.GenerateUpiOtpRequest.ReqOtpType
	121, // 12: upi.GenerateUpiOtpResponse.status:type_name -> rpc.Status
	121, // 13: upi.ResolveAccountResponse.status:type_name -> rpc.Status
	125, // 14: upi.ResolveAccountResponse.acc_type:type_name -> accounts.Type
	128, // 15: upi.CreateVPARequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	129, // 16: upi.CreateVPARequest.partner_bank:type_name -> vendorgateway.Vendor
	125, // 17: upi.CreateVPARequest.account_type:type_name -> accounts.Type
	123, // 18: upi.CreateVPARequest.device:type_name -> upi.Device
	130, // 19: upi.CreateVPARequest.vpa_name:type_name -> api.typesv2.common.Name
	121, // 20: upi.CreateVPAResponse.status:type_name -> rpc.Status
	121, // 21: upi.ListKeysResponse.status:type_name -> rpc.Status
	123, // 22: upi.RegisterMobileRequest.device:type_name -> upi.Device
	127, // 23: upi.RegisterMobileRequest.cred_block:type_name -> upi.CredBlock
	131, // 24: upi.RegisterMobileRequest.card_info:type_name -> card.BasicCardInfo
	132, // 25: upi.RegisterMobileRequest.upi_pin_set_option_type:type_name -> upi.onboarding.enums.UpiPinSetOptionType
	121, // 26: upi.RegisterMobileResponse.status:type_name -> rpc.Status
	13,  // 27: upi.GetPinFlowParametersRequest.pin_flow_type:type_name -> upi.GetPinFlowParametersRequest.PinFlowType
	132, // 28: upi.GetPinFlowParametersRequest.upi_pin_set_option_type:type_name -> upi.onboarding.enums.UpiPinSetOptionType
	121, // 29: upi.GetPinFlowParametersResponse.status:type_name -> rpc.Status
	133, // 30: upi.GetPinFlowParametersResponse.key_code:type_name -> api.typesv2.KeyCode
	134, // 31: upi.GetPinFlowParametersResponse.control_json:type_name -> upi.ControlJson
	117, // 32: upi.GetPinFlowParametersResponse.bank_config:type_name -> upi.GetPinFlowParametersResponse.BankConfig
	121, // 33: upi.GetTransactionParametersResponse.status:type_name -> rpc.Status
	133, // 34: upi.GetTransactionParametersResponse.key_code:type_name -> api.typesv2.KeyCode
	134, // 35: upi.GetTransactionParametersResponse.control_json:type_name -> upi.ControlJson
	118, // 36: upi.GetTransactionParametersResponse.bank_config:type_name -> upi.GetTransactionParametersResponse.BankConfig
	0,   // 37: upi.VerifyURNRequest.urn_type:type_name -> upi.URNType
	121, // 38: upi.VerifyURNResponse.status:type_name -> rpc.Status
	135, // 39: upi.VerifyURNResponse.mandate_urn_info:type_name -> upi.domain_model.MandateUrnInfo
	122, // 40: upi.VerifyURNResponse.expiry_time:type_name -> google.protobuf.Timestamp
	136, // 41: upi.VerifyURNResponse.amount:type_name -> google.type.Money
	136, // 42: upi.VerifyURNResponse.min_amount:type_name -> google.type.Money
	137, // 43: upi.VerifyURNResponse.parsed_urn_info:type_name -> upi.domain_model.UrnInfo
	123, // 44: upi.ChangePinRequest.device:type_name -> upi.Device
	127, // 45: upi.ChangePinRequest.old_pin_cred_block:type_name -> upi.CredBlock
	127, // 46: upi.ChangePinRequest.new_pin_cred_block:type_name -> upi.CredBlock
	125, // 47: upi.ChangePinRequest.acc_type:type_name -> accounts.Type
	17,  // 48: upi.ChangePinRequest.customer_type:type_name -> upi.ChangePinRequest.CustomerType
	121, // 49: upi.ChangePinResponse.status:type_name -> rpc.Status
	136, // 50: upi.GenerateURNRequest.amount:type_name -> google.type.Money
	19,  // 51: upi.GenerateURNRequest.initiation_mode:type_name -> upi.GenerateURNRequest.InitiationMode
	138, // 52: upi.GenerateURNRequest.next_order_info:type_name -> upi.NextOrderInfo
	20,  // 53: upi.GenerateURNRequest.order_source:type_name -> upi.GenerateURNRequest.Source
	121, // 54: upi.GenerateURNResponse.status:type_name -> rpc.Status
	121, // 55: upi.GetUpiSetupStatusResponse.status:type_name -> rpc.Status
	139, // 56: upi.GetUpiSetupStatusResponse.upi_status:type_name -> upi.UpiSetUpState
	23,  // 57: upi.DisableOrEnableVPARequest.request_type:type_name -> upi.DisableOrEnableVPARequest.RequestType
	140, // 58: upi.DisableOrEnableVPARequest.source:type_name -> paymentinstrument.Source
	121, // 59: upi.DisableOrEnableVPAResponse.status:type_name -> rpc.Status
	121, // 60: upi.PinStatusResponse.status:type_name -> rpc.Status
	119, // 61: upi.PinStatusResponse.account_pin_status_map:type_name -> upi.PinStatusResponse.AccountPinStatusMapEntry
	120, // 62: upi.PinStatusResponse.account_pin_state_map:type_name -> upi.PinStatusResponse.AccountPinStateMapEntry
	121, // 63: upi.GetAccountPinInfosResponse.status:type_name -> rpc.Status
	141, // 64: upi.GetAccountPinInfosResponse.account_upi_pin_infos:type_name -> upi.AccountUpiPinInfo
	121, // 65: upi.GetLatestAccountPinInfosResponse.status:type_name -> rpc.Status
	141, // 66: upi.GetLatestAccountPinInfosResponse.account_upi_pin_infos:type_name -> upi.AccountUpiPinInfo
	121, // 67: upi.CheckVerifiedMerchantResponse.status:type_name -> rpc.Status
	121, // 68: upi.GetVpaMerchantInfoResponse.status:type_name -> rpc.Status
	142, // 69: upi.GetVpaMerchantInfoResponse.vpa_merchant_info:type_name -> upi.VpaMerchantInfo
	30,  // 70: upi.GetPinInfosByAccountIdRequest.user_actions:type_name -> upi.GetPinInfosByAccountIdRequest.Action
	31,  // 71: upi.GetPinInfosByAccountIdRequest.status:type_name -> upi.GetPinInfosByAccountIdRequest.Status
	143, // 72: upi.GetPinInfosByAccountIdRequest.select_mask:type_name -> upi.AccountUpiPinInfoFieldMask
	122, // 73: upi.GetPinInfosByAccountIdRequest.from_time:type_name -> google.protobuf.Timestamp
	122, // 74: upi.GetPinInfosByAccountIdRequest.to_time:type_name -> google.protobuf.Timestamp
	143, // 75: upi.GetPinInfosByAccountIdRequest.sort_by:type_name -> upi.AccountUpiPinInfoFieldMask
	1,   // 76: upi.GetPinInfosByAccountIdRequest.sort_order:type_name -> upi.SortOrder
	121, // 77: upi.GetPinInfosByAccountIdResponse.status:type_name -> rpc.Status
	141, // 78: upi.GetPinInfosByAccountIdResponse.account_upi_pin_infos:type_name -> upi.AccountUpiPinInfo
	2,   // 79: upi.PostUserActivityRequest.user_activity:type_name -> upi.UpiUserActivity
	121, // 80: upi.PostUserActivityResponse.status:type_name -> rpc.Status
	123, // 81: upi.ValidateSecurePinRequest.device:type_name -> upi.Device
	127, // 82: upi.ValidateSecurePinRequest.npci_cred_block:type_name -> upi.CredBlock
	121, // 83: upi.ValidateSecurePinResponse.status:type_name -> rpc.Status
	35,  // 84: upi.RaiseComplaintRequest.type:type_name -> upi.RaiseComplaintRequest.RaiseComplaintType
	144, // 85: upi.RaiseComplaintRequest.complaint:type_name -> upi.complaint.Complaint
	121, // 86: upi.RaiseComplaintResponse.status:type_name -> rpc.Status
	145, // 87: upi.RaiseComplaintResponse.refs:type_name -> upi.txnref.TransactionReference
	146, // 88: upi.RaiseComplaintResponse.complaint_dispute_state:type_name -> upi.complaint.ComplaintDisputeState
	121, // 89: upi.CheckComplaintStatusResponse.status:type_name -> rpc.Status
	145, // 90: upi.CheckComplaintStatusResponse.ref:type_name -> upi.txnref.TransactionReference
	146, // 91: upi.CheckComplaintStatusResponse.complaint_dispute_state:type_name -> upi.complaint.ComplaintDisputeState
	147, // 92: upi.ChangeUpiPinSetStateRequest.pin_set_state:type_name -> upi.PinSetState
	121, // 93: upi.ChangeUpiPinSetStateResponse.status:type_name -> rpc.Status
	123, // 94: upi.GetVerifiedVpasByPhoneNumberRequest.device:type_name -> upi.Device
	128, // 95: upi.GetVerifiedVpasByPhoneNumberRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	121, // 96: upi.GetVerifiedVpasByPhoneNumberResponse.status:type_name -> rpc.Status
	148, // 97: upi.GetVerifiedVpasByPhoneNumberResponse.vpa_info:type_name -> upi.Vpa_info
	123, // 98: upi.ValidateAddressAndCreatePiRequest.device:type_name -> upi.Device
	149, // 99: upi.ValidateAddressAndCreatePiRequest.ownership:type_name -> paymentinstrument.Ownership
	40,  // 100: upi.ValidateAddressAndCreatePiRequest.vpa_validation_mode:type_name -> upi.ValidateAddressAndCreatePiRequest.VpaValidationMode
	121, // 101: upi.ValidateAddressAndCreatePiResponse.status:type_name -> rpc.Status
	124, // 102: upi.ValidateAddressAndCreatePiResponse.merchant:type_name -> upi.MerchantDetails
	123, // 103: upi.ValidateUpiNumberAndCreatePiRequest.device:type_name -> upi.Device
	149, // 104: upi.ValidateUpiNumberAndCreatePiRequest.ownership:type_name -> paymentinstrument.Ownership
	121, // 105: upi.ValidateUpiNumberAndCreatePiResponse.status:type_name -> rpc.Status
	124, // 106: upi.ValidateUpiNumberAndCreatePiResponse.merchant:type_name -> upi.MerchantDetails
	150, // 107: upi.ValidateUpiNumberAndCreatePiResponse.psp_badge_icon:type_name -> api.typesv2.common.Image
	136, // 108: upi.ValidateInternationalPaymentRequest.base_amount_quote_currency:type_name -> google.type.Money
	136, // 109: upi.ValidateInternationalPaymentRequest.total_amount_inr:type_name -> google.type.Money
	121, // 110: upi.ValidateInternationalPaymentResponse.status:type_name -> rpc.Status
	151, // 111: upi.ValidateInternationalPaymentResponse.upi_international_payment_charges:type_name -> upi.UpiInternationalPaymentCharge
	121, // 112: upi.GetUpiInternationalQrInfoFromCacheResponse.status:type_name -> rpc.Status
	152, // 113: upi.GetUpiInternationalQrInfoFromCacheResponse.upi_international_qr_info:type_name -> upi.UpiInternationalQrInfo
	45,  // 114: upi.GetExternalVpasRequest.identifier_field_mask:type_name -> upi.GetExternalVpasRequest.IdentifierFieldMask
	128, // 115: upi.GetExternalVpasRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	121, // 116: upi.GetExternalVpasResponse.status:type_name -> rpc.Status
	148, // 117: upi.GetExternalVpasResponse.vpa_infos:type_name -> upi.Vpa_info
	153, // 118: upi.GetNPCICLParametersV1Request.upi_flow_type:type_name -> upi.UpiFlowType
	132, // 119: upi.GetNPCICLParametersV1Request.upi_pin_set_option_type:type_name -> upi.onboarding.enums.UpiPinSetOptionType
	121, // 120: upi.GetNPCICLParametersV1Response.status:type_name -> rpc.Status
	154, // 121: upi.GetNPCICLParametersV1Response.upi_lite_transaction_params:type_name -> upi.UpiLiteTransactionParams
	155, // 122: upi.GetNPCICLParametersV1Response.upi_lite_balance_params:type_name -> upi.UpiLiteBalanceParams
	121, // 123: upi.IsNewAddFundsVpaEnabledForActorResponse.status:type_name -> rpc.Status
	4,   // 124: upi.CheckTxnStatusResponse.Ref.type:type_name -> upi.CheckTxnStatusResponse.Ref.RefType
	156, // 125: upi.CheckTxnStatusResponse.Ref.account_details:type_name -> upi.CustomerAccountDetails
	147, // 126: upi.PinStatusResponse.AccountPinStateMapEntry.value:type_name -> upi.PinSetState
	51,  // 127: upi.UPI.VerifyPayeeVPA:input_type -> upi.VerifyPayeeVPARequest
	53,  // 128: upi.UPI.GetToken:input_type -> upi.GetTokenRequest
	55,  // 129: upi.UPI.GenerateUpiOtp:input_type -> upi.GenerateUpiOtpRequest
	57,  // 130: upi.UPI.ResolveAccount:input_type -> upi.ResolveAccountRequest
	59,  // 131: upi.UPI.CreateVPA:input_type -> upi.CreateVPARequest
	157, // 132: upi.UPI.ListKeys:input_type -> google.protobuf.Empty
	62,  // 133: upi.UPI.RegisterMobile:input_type -> upi.RegisterMobileRequest
	64,  // 134: upi.UPI.GetPinFlowParameters:input_type -> upi.GetPinFlowParametersRequest
	66,  // 135: upi.UPI.GetTransactionParameters:input_type -> upi.GetTransactionParametersRequest
	68,  // 136: upi.UPI.VerifyURN:input_type -> upi.VerifyURNRequest
	70,  // 137: upi.UPI.ChangePin:input_type -> upi.ChangePinRequest
	72,  // 138: upi.UPI.GenerateURN:input_type -> upi.GenerateURNRequest
	74,  // 139: upi.UPI.GetUpiSetupStatus:input_type -> upi.GetUpiSetupStatusRequest
	158, // 140: upi.UPI.GetOrCreateURNTransaction:input_type -> order.domain.ProcessPaymentRequest
	76,  // 141: upi.UPI.DisableOrEnableVPA:input_type -> upi.DisableOrEnableVPARequest
	78,  // 142: upi.UPI.GetPinStatus:input_type -> upi.PinStatusRequest
	80,  // 143: upi.UPI.GetAccountPinInfos:input_type -> upi.GetAccountPinInfosRequest
	82,  // 144: upi.UPI.GetLatestAccountPinInfos:input_type -> upi.GetLatestAccountPinInfosRequest
	84,  // 145: upi.UPI.CheckVerifiedMerchant:input_type -> upi.CheckVerifiedMerchantRequest
	158, // 146: upi.UPI.GetOrCreateAddFundsTransaction:input_type -> order.domain.ProcessPaymentRequest
	86,  // 147: upi.UPI.GetVpaMerchantInfo:input_type -> upi.GetVpaMerchantInfoRequest
	88,  // 148: upi.UPI.GetPinInfosByAccountId:input_type -> upi.GetPinInfosByAccountIdRequest
	90,  // 149: upi.UPI.PostUserActivity:input_type -> upi.PostUserActivityRequest
	92,  // 150: upi.UPI.ValidateSecurePin:input_type -> upi.ValidateSecurePinRequest
	158, // 151: upi.UPI.CheckAndUpdateAddFundsCollectPaymentStatus:input_type -> order.domain.ProcessPaymentRequest
	94,  // 152: upi.UPI.RaiseComplaint:input_type -> upi.RaiseComplaintRequest
	49,  // 153: upi.UPI.CheckTxnStatus:input_type -> upi.CheckTxnStatusRequest
	96,  // 154: upi.UPI.CheckComplaintStatus:input_type -> upi.CheckComplaintStatusRequest
	98,  // 155: upi.UPI.ChangeUpiPinSetState:input_type -> upi.ChangeUpiPinSetStateRequest
	100, // 156: upi.UPI.GetVerifiedVpasByPhoneNumber:input_type -> upi.GetVerifiedVpasByPhoneNumberRequest
	102, // 157: upi.UPI.ValidateAddressAndCreatePi:input_type -> upi.ValidateAddressAndCreatePiRequest
	104, // 158: upi.UPI.ValidateUpiNumberAndCreatePi:input_type -> upi.ValidateUpiNumberAndCreatePiRequest
	106, // 159: upi.UPI.ValidateInternationalPayment:input_type -> upi.ValidateInternationalPaymentRequest
	108, // 160: upi.UPI.GetUpiInternationalQrInfoFromCache:input_type -> upi.GetUpiInternationalQrInfoFromCacheRequest
	110, // 161: upi.UPI.GetExternalVpas:input_type -> upi.GetExternalVpasRequest
	112, // 162: upi.UPI.GetNPCICLParametersV1:input_type -> upi.GetNPCICLParametersV1Request
	114, // 163: upi.UPI.IsNewAddFundsVpaEnabledForActor:input_type -> upi.IsNewAddFundsVpaEnabledForActorRequest
	52,  // 164: upi.UPI.VerifyPayeeVPA:output_type -> upi.VerifyPayeeVPAResponse
	54,  // 165: upi.UPI.GetToken:output_type -> upi.GetTokenResponse
	56,  // 166: upi.UPI.GenerateUpiOtp:output_type -> upi.GenerateUpiOtpResponse
	58,  // 167: upi.UPI.ResolveAccount:output_type -> upi.ResolveAccountResponse
	60,  // 168: upi.UPI.CreateVPA:output_type -> upi.CreateVPAResponse
	61,  // 169: upi.UPI.ListKeys:output_type -> upi.ListKeysResponse
	63,  // 170: upi.UPI.RegisterMobile:output_type -> upi.RegisterMobileResponse
	65,  // 171: upi.UPI.GetPinFlowParameters:output_type -> upi.GetPinFlowParametersResponse
	67,  // 172: upi.UPI.GetTransactionParameters:output_type -> upi.GetTransactionParametersResponse
	69,  // 173: upi.UPI.VerifyURN:output_type -> upi.VerifyURNResponse
	71,  // 174: upi.UPI.ChangePin:output_type -> upi.ChangePinResponse
	73,  // 175: upi.UPI.GenerateURN:output_type -> upi.GenerateURNResponse
	75,  // 176: upi.UPI.GetUpiSetupStatus:output_type -> upi.GetUpiSetupStatusResponse
	159, // 177: upi.UPI.GetOrCreateURNTransaction:output_type -> order.domain.ProcessPaymentResponse
	77,  // 178: upi.UPI.DisableOrEnableVPA:output_type -> upi.DisableOrEnableVPAResponse
	79,  // 179: upi.UPI.GetPinStatus:output_type -> upi.PinStatusResponse
	81,  // 180: upi.UPI.GetAccountPinInfos:output_type -> upi.GetAccountPinInfosResponse
	83,  // 181: upi.UPI.GetLatestAccountPinInfos:output_type -> upi.GetLatestAccountPinInfosResponse
	85,  // 182: upi.UPI.CheckVerifiedMerchant:output_type -> upi.CheckVerifiedMerchantResponse
	159, // 183: upi.UPI.GetOrCreateAddFundsTransaction:output_type -> order.domain.ProcessPaymentResponse
	87,  // 184: upi.UPI.GetVpaMerchantInfo:output_type -> upi.GetVpaMerchantInfoResponse
	89,  // 185: upi.UPI.GetPinInfosByAccountId:output_type -> upi.GetPinInfosByAccountIdResponse
	91,  // 186: upi.UPI.PostUserActivity:output_type -> upi.PostUserActivityResponse
	93,  // 187: upi.UPI.ValidateSecurePin:output_type -> upi.ValidateSecurePinResponse
	159, // 188: upi.UPI.CheckAndUpdateAddFundsCollectPaymentStatus:output_type -> order.domain.ProcessPaymentResponse
	95,  // 189: upi.UPI.RaiseComplaint:output_type -> upi.RaiseComplaintResponse
	50,  // 190: upi.UPI.CheckTxnStatus:output_type -> upi.CheckTxnStatusResponse
	97,  // 191: upi.UPI.CheckComplaintStatus:output_type -> upi.CheckComplaintStatusResponse
	99,  // 192: upi.UPI.ChangeUpiPinSetState:output_type -> upi.ChangeUpiPinSetStateResponse
	101, // 193: upi.UPI.GetVerifiedVpasByPhoneNumber:output_type -> upi.GetVerifiedVpasByPhoneNumberResponse
	103, // 194: upi.UPI.ValidateAddressAndCreatePi:output_type -> upi.ValidateAddressAndCreatePiResponse
	105, // 195: upi.UPI.ValidateUpiNumberAndCreatePi:output_type -> upi.ValidateUpiNumberAndCreatePiResponse
	107, // 196: upi.UPI.ValidateInternationalPayment:output_type -> upi.ValidateInternationalPaymentResponse
	109, // 197: upi.UPI.GetUpiInternationalQrInfoFromCache:output_type -> upi.GetUpiInternationalQrInfoFromCacheResponse
	111, // 198: upi.UPI.GetExternalVpas:output_type -> upi.GetExternalVpasResponse
	113, // 199: upi.UPI.GetNPCICLParametersV1:output_type -> upi.GetNPCICLParametersV1Response
	115, // 200: upi.UPI.IsNewAddFundsVpaEnabledForActor:output_type -> upi.IsNewAddFundsVpaEnabledForActorResponse
	164, // [164:201] is the sub-list for method output_type
	127, // [127:164] is the sub-list for method input_type
	127, // [127:127] is the sub-list for extension type_name
	127, // [127:127] is the sub-list for extension extendee
	0,   // [0:127] is the sub-list for field type_name
}

func init() { file_api_upi_service_proto_init() }
func file_api_upi_service_proto_init() {
	if File_api_upi_service_proto != nil {
		return
	}
	file_api_upi_cred_block_proto_init()
	file_api_upi_customer_proto_init()
	file_api_upi_device_proto_init()
	file_api_upi_merchant_proto_init()
	file_api_upi_payload_proto_init()
	file_api_upi_qr_details_proto_init()
	file_api_upi_upi_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_upi_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckTxnStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckTxnStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPayeeVPARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyPayeeVPAResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateUpiOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateUpiOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResolveAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateVPARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateVPAResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListKeysResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterMobileRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterMobileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinFlowParametersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinFlowParametersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionParametersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionParametersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyURNRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyURNResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangePinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangePinResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateURNRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateURNResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUpiSetupStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUpiSetupStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisableOrEnableVPARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DisableOrEnableVPAResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountPinInfosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountPinInfosResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLatestAccountPinInfosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLatestAccountPinInfosResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckVerifiedMerchantRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckVerifiedMerchantResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVpaMerchantInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVpaMerchantInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinInfosByAccountIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinInfosByAccountIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostUserActivityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostUserActivityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateSecurePinRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateSecurePinResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RaiseComplaintRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RaiseComplaintResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckComplaintStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckComplaintStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeUpiPinSetStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeUpiPinSetStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVerifiedVpasByPhoneNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVerifiedVpasByPhoneNumberResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateAddressAndCreatePiRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateAddressAndCreatePiResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateUpiNumberAndCreatePiRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateUpiNumberAndCreatePiResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateInternationalPaymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateInternationalPaymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUpiInternationalQrInfoFromCacheRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUpiInternationalQrInfoFromCacheResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExternalVpasRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExternalVpasResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNPCICLParametersV1Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNPCICLParametersV1Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsNewAddFundsVpaEnabledForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsNewAddFundsVpaEnabledForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckTxnStatusResponse_Ref); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinFlowParametersResponse_BankConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionParametersResponse_BankConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_upi_service_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*VerifyURNResponse_MandateUrnInfo)(nil),
	}
	file_api_upi_service_proto_msgTypes[27].OneofWrappers = []interface{}{
		(*DisableOrEnableVPARequest_UserVpa)(nil),
		(*DisableOrEnableVPARequest_PiId)(nil),
	}
	file_api_upi_service_proto_msgTypes[61].OneofWrappers = []interface{}{
		(*GetExternalVpasRequest_ActorId)(nil),
		(*GetExternalVpasRequest_Email)(nil),
		(*GetExternalVpasRequest_PhoneNumber)(nil),
	}
	file_api_upi_service_proto_msgTypes[64].OneofWrappers = []interface{}{
		(*GetNPCICLParametersV1Response_UpiLiteTransactionParams)(nil),
		(*GetNPCICLParametersV1Response_UpiLiteBalanceParams)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_service_proto_rawDesc,
			NumEnums:      49,
			NumMessages:   72,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_upi_service_proto_goTypes,
		DependencyIndexes: file_api_upi_service_proto_depIdxs,
		EnumInfos:         file_api_upi_service_proto_enumTypes,
		MessageInfos:      file_api_upi_service_proto_msgTypes,
	}.Build()
	File_api_upi_service_proto = out.File
	file_api_upi_service_proto_rawDesc = nil
	file_api_upi_service_proto_goTypes = nil
	file_api_upi_service_proto_depIdxs = nil
}
