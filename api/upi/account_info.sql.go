package upi

import (
	"bytes"
	"database/sql/driver"
	"fmt"

	"github.com/golang/protobuf/jsonpb"
)

// Valuer interface implementation for storing the data in string format in DB
func (cj *ControlJson) Value() (driver.Value, error) {
	if cj == nil {
		return nil, nil
	}
	var controlJsonByte bytes.Buffer
	marshaller := jsonpb.Marshaler{}
	err := marshaller.Marshal(&controlJsonByte, cj)
	if err != nil {
		return nil, err
	}
	return controlJsonByte.Bytes(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (cj *ControlJson) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte, got %T", input)
		return err
	}
	var controlJsonByte = bytes.NewBuffer(val)
	unMarshaller := jsonpb.Unmarshaler{}
	if err := unMarshaller.Unmarshal(controlJsonByte, cj); err != nil {
		return err
	}
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x UpiSetUpState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *UpiSetUpState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := UpiSetUpState_value[val]
	*x = UpiSetUpState(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x PinSetState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *PinSetState) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := PinSetState_value[val]
	*x = PinSetState(valInt)
	return nil
}
