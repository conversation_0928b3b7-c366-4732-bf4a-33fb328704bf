// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/qr_details.proto

package upi

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on QRDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QRDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QRDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QRDetailsMultiError, or nil
// if none found.
func (m *QRDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *QRDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QrVersion

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QRDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QRDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QRDetailsValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QrMedium

	if all {
		switch v := interface{}(m.GetExpireAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QRDetailsValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QRDetailsValidationError{
					field:  "ExpireAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpireAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QRDetailsValidationError{
				field:  "ExpireAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Query

	// no validation rules for VersionToken

	// no validation rules for Stan

	if len(errors) > 0 {
		return QRDetailsMultiError(errors)
	}

	return nil
}

// QRDetailsMultiError is an error wrapping multiple validation errors returned
// by QRDetails.ValidateAll() if the designated constraints aren't met.
type QRDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QRDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QRDetailsMultiError) AllErrors() []error { return m }

// QRDetailsValidationError is the validation error returned by
// QRDetails.Validate if the designated constraints aren't met.
type QRDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QRDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QRDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QRDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QRDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QRDetailsValidationError) ErrorName() string { return "QRDetailsValidationError" }

// Error satisfies the builtin error interface
func (e QRDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQRDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QRDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QRDetailsValidationError{}

// Validate checks the field values on UpiInternationalQrInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiInternationalQrInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiInternationalQrInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiInternationalQrInfoMultiError, or nil if none found.
func (m *UpiInternationalQrInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiInternationalQrInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetQrDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiInternationalQrInfoValidationError{
					field:  "QrDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiInternationalQrInfoValidationError{
					field:  "QrDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQrDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiInternationalQrInfoValidationError{
				field:  "QrDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetForexDetailList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpiInternationalQrInfoValidationError{
						field:  fmt.Sprintf("ForexDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpiInternationalQrInfoValidationError{
						field:  fmt.Sprintf("ForexDetailList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpiInternationalQrInfoValidationError{
					field:  fmt.Sprintf("ForexDetailList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInstitution()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiInternationalQrInfoValidationError{
					field:  "Institution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiInternationalQrInfoValidationError{
					field:  "Institution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstitution()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiInternationalQrInfoValidationError{
				field:  "Institution",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiInternationalQrInfoMultiError(errors)
	}

	return nil
}

// UpiInternationalQrInfoMultiError is an error wrapping multiple validation
// errors returned by UpiInternationalQrInfo.ValidateAll() if the designated
// constraints aren't met.
type UpiInternationalQrInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiInternationalQrInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiInternationalQrInfoMultiError) AllErrors() []error { return m }

// UpiInternationalQrInfoValidationError is the validation error returned by
// UpiInternationalQrInfo.Validate if the designated constraints aren't met.
type UpiInternationalQrInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiInternationalQrInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiInternationalQrInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiInternationalQrInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiInternationalQrInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiInternationalQrInfoValidationError) ErrorName() string {
	return "UpiInternationalQrInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpiInternationalQrInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiInternationalQrInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiInternationalQrInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiInternationalQrInfoValidationError{}
