package upi

// AddDetailedStatus append detailed status to detailedStatus list.
// If detailed status do not exist then it will create one and append.
func (a *AccountUpiPinInfo) AddDetailedStatus(detailedStatus *DetailedStatus) {
	if detailedStatus == nil {
		return
	}

	if a.GetDetailedStatusList() == nil {
		a.DetailedStatusList = &AccountUpiPinInfo_DetailedStatusList{}
	}
	a.DetailedStatusList.DetailedStatus = append(a.DetailedStatusList.DetailedStatus, detailedStatus)
}
