// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/upi
package upi

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessReqAuthMethod                     = "ProcessReqAuth"
	ProcessResPayMethod                      = "ProcessResPay"
	ProcessReqTxnConfirmationMethod          = "ProcessReqTxnConfirmation"
	ProcessReqValAddressMethod               = "ProcessReqValAddress"
	ProcessListPspKeysMethod                 = "ProcessListPspKeys"
	ProcessListVaeMethod                     = "ProcessListVae"
	ProcessOnboardingEventMethod             = "ProcessOnboardingEvent"
	ProcessAuthFactorUpdateEventMethod       = "ProcessAuthFactorUpdateEvent"
	ProcessReqTxnConfirmationComplaintMethod = "ProcessReqTxnConfirmationComplaint"
	ProcessRespComplaintMethod               = "ProcessRespComplaint"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &UpiEvent{}
var _ queue.ConsumerRequest = &ProcessReqAuthRequest{}
var _ queue.ConsumerRequest = &ProcessResPayRequest{}
var _ queue.ConsumerRequest = &ProcessReqTxnConfirmationRequest{}
var _ queue.ConsumerRequest = &ProcessReqValAddressRequest{}
var _ queue.ConsumerRequest = &ProcessListPspKeysRequest{}
var _ queue.ConsumerRequest = &ProcessListVaeRequest{}
var _ queue.ConsumerRequest = &ProcessReqTxnConfirmationComplaintRequest{}
var _ queue.ConsumerRequest = &ProcessRespComplaintRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *UpiEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessReqAuthRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessResPayRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessReqTxnConfirmationRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessReqValAddressRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessListPspKeysRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessListVaeRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessReqTxnConfirmationComplaintRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessRespComplaintRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessReqAuthMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessReqAuthMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessReqAuthMethod)
}

// RegisterProcessResPayMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessResPayMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessResPayMethod)
}

// RegisterProcessReqTxnConfirmationMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessReqTxnConfirmationMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessReqTxnConfirmationMethod)
}

// RegisterProcessReqValAddressMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessReqValAddressMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessReqValAddressMethod)
}

// RegisterProcessListPspKeysMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessListPspKeysMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessListPspKeysMethod)
}

// RegisterProcessListVaeMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessListVaeMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessListVaeMethod)
}

// RegisterProcessOnboardingEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessOnboardingEventMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessOnboardingEventMethod)
}

// RegisterProcessAuthFactorUpdateEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessAuthFactorUpdateEventMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessAuthFactorUpdateEventMethod)
}

// RegisterProcessReqTxnConfirmationComplaintMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessReqTxnConfirmationComplaintMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessReqTxnConfirmationComplaintMethod)
}

// RegisterProcessRespComplaintMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessRespComplaintMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessRespComplaintMethod)
}
