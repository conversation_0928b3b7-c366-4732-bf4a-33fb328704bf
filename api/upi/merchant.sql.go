package upi

import (
	"database/sql/driver"
	"fmt"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x MerchantOwnershipType) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *MerchantOwnershipType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := MerchantType_value[val]
	*x = MerchantOwnershipType(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x MerchantType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *MerchantType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := MerchantType_value[val]
	*x = MerchantType(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x MerchantGenre) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *MerchantGenre) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := MerchantGenre_value[val]
	*x = MerchantGenre(valInt)
	return nil
}

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x MerchantOnboardingType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *MerchantOnboardingType) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := MerchantOnboardingType_value[val]
	*x = MerchantOnboardingType(valInt)
	return nil
}
