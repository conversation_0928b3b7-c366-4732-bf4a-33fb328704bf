// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/service.proto

package upi

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	account "github.com/epifi/gamma/api/typesv2/account"

	accounts "github.com/epifi/gamma/api/accounts"

	complaint "github.com/epifi/gamma/api/upi/complaint"

	enums "github.com/epifi/gamma/api/upi/onboarding/enums"

	paymentinstrument "github.com/epifi/gamma/api/paymentinstrument"

	typesv2 "github.com/epifi/gamma/api/typesv2"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = account.AccountProductOffering(0)

	_ = accounts.Type(0)

	_ = complaint.ComplaintDisputeState(0)

	_ = enums.UpiPinSetOptionType(0)

	_ = paymentinstrument.Source(0)

	_ = typesv2.KeyCode(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on CheckTxnStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckTxnStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckTxnStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckTxnStatusRequestMultiError, or nil if none found.
func (m *CheckTxnStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckTxnStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	if len(errors) > 0 {
		return CheckTxnStatusRequestMultiError(errors)
	}

	return nil
}

// CheckTxnStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CheckTxnStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckTxnStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckTxnStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckTxnStatusRequestMultiError) AllErrors() []error { return m }

// CheckTxnStatusRequestValidationError is the validation error returned by
// CheckTxnStatusRequest.Validate if the designated constraints aren't met.
type CheckTxnStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckTxnStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckTxnStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckTxnStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckTxnStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckTxnStatusRequestValidationError) ErrorName() string {
	return "CheckTxnStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckTxnStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckTxnStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckTxnStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckTxnStatusRequestValidationError{}

// Validate checks the field values on CheckTxnStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckTxnStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckTxnStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckTxnStatusResponseMultiError, or nil if none found.
func (m *CheckTxnStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckTxnStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckTxnStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckTxnStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckTxnStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRef() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckTxnStatusResponseValidationError{
						field:  fmt.Sprintf("Ref[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckTxnStatusResponseValidationError{
						field:  fmt.Sprintf("Ref[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckTxnStatusResponseValidationError{
					field:  fmt.Sprintf("Ref[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CustRefId

	// no validation rules for RawStatusCode

	// no validation rules for RawStatusDescription

	// no validation rules for StatusCodePayer

	// no validation rules for StatusDescriptionPayer

	// no validation rules for StatusCodePayee

	// no validation rules for StatusDescriptionPayee

	if all {
		switch v := interface{}(m.GetOriginalTransactionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckTxnStatusResponseValidationError{
					field:  "OriginalTransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckTxnStatusResponseValidationError{
					field:  "OriginalTransactionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOriginalTransactionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckTxnStatusResponseValidationError{
				field:  "OriginalTransactionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	if len(errors) > 0 {
		return CheckTxnStatusResponseMultiError(errors)
	}

	return nil
}

// CheckTxnStatusResponseMultiError is an error wrapping multiple validation
// errors returned by CheckTxnStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckTxnStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckTxnStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckTxnStatusResponseMultiError) AllErrors() []error { return m }

// CheckTxnStatusResponseValidationError is the validation error returned by
// CheckTxnStatusResponse.Validate if the designated constraints aren't met.
type CheckTxnStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckTxnStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckTxnStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckTxnStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckTxnStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckTxnStatusResponseValidationError) ErrorName() string {
	return "CheckTxnStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckTxnStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckTxnStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckTxnStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckTxnStatusResponseValidationError{}

// Validate checks the field values on VerifyPayeeVPARequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyPayeeVPARequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyPayeeVPARequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyPayeeVPARequestMultiError, or nil if none found.
func (m *VerifyPayeeVPARequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyPayeeVPARequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDevice() == nil {
		err := VerifyPayeeVPARequestValidationError{
			field:  "Device",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPayeeVPARequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPayeeVPARequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPayeeVPARequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpiVpa

	// no validation rules for PayerActorId

	if len(errors) > 0 {
		return VerifyPayeeVPARequestMultiError(errors)
	}

	return nil
}

// VerifyPayeeVPARequestMultiError is an error wrapping multiple validation
// errors returned by VerifyPayeeVPARequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyPayeeVPARequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyPayeeVPARequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyPayeeVPARequestMultiError) AllErrors() []error { return m }

// VerifyPayeeVPARequestValidationError is the validation error returned by
// VerifyPayeeVPARequest.Validate if the designated constraints aren't met.
type VerifyPayeeVPARequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyPayeeVPARequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyPayeeVPARequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyPayeeVPARequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyPayeeVPARequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyPayeeVPARequestValidationError) ErrorName() string {
	return "VerifyPayeeVPARequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyPayeeVPARequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyPayeeVPARequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyPayeeVPARequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyPayeeVPARequestValidationError{}

// Validate checks the field values on VerifyPayeeVPAResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyPayeeVPAResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyPayeeVPAResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyPayeeVPAResponseMultiError, or nil if none found.
func (m *VerifyPayeeVPAResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyPayeeVPAResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPayeeVPAResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPayeeVPAResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPayeeVPAResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerName

	// no validation rules for Mcc

	if all {
		switch v := interface{}(m.GetMerchant()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPayeeVPAResponseValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPayeeVPAResponseValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchant()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPayeeVPAResponseValidationError{
				field:  "Merchant",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Ifsc

	// no validation rules for AccountType

	// no validation rules for Apo

	if len(errors) > 0 {
		return VerifyPayeeVPAResponseMultiError(errors)
	}

	return nil
}

// VerifyPayeeVPAResponseMultiError is an error wrapping multiple validation
// errors returned by VerifyPayeeVPAResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyPayeeVPAResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyPayeeVPAResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyPayeeVPAResponseMultiError) AllErrors() []error { return m }

// VerifyPayeeVPAResponseValidationError is the validation error returned by
// VerifyPayeeVPAResponse.Validate if the designated constraints aren't met.
type VerifyPayeeVPAResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyPayeeVPAResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyPayeeVPAResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyPayeeVPAResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyPayeeVPAResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyPayeeVPAResponseValidationError) ErrorName() string {
	return "VerifyPayeeVPAResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyPayeeVPAResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyPayeeVPAResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyPayeeVPAResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyPayeeVPAResponseValidationError{}

// Validate checks the field values on GetTokenRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTokenRequestMultiError, or nil if none found.
func (m *GetTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCredBlockChallenge()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTokenRequestValidationError{
					field:  "CredBlockChallenge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTokenRequestValidationError{
					field:  "CredBlockChallenge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCredBlockChallenge()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTokenRequestValidationError{
				field:  "CredBlockChallenge",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTokenRequestMultiError(errors)
	}

	return nil
}

// GetTokenRequestMultiError is an error wrapping multiple validation errors
// returned by GetTokenRequest.ValidateAll() if the designated constraints
// aren't met.
type GetTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTokenRequestMultiError) AllErrors() []error { return m }

// GetTokenRequestValidationError is the validation error returned by
// GetTokenRequest.Validate if the designated constraints aren't met.
type GetTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTokenRequestValidationError) ErrorName() string { return "GetTokenRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTokenRequestValidationError{}

// Validate checks the field values on GetTokenResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTokenResponseMultiError, or nil if none found.
func (m *GetTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTokenResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTokenResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	if len(errors) > 0 {
		return GetTokenResponseMultiError(errors)
	}

	return nil
}

// GetTokenResponseMultiError is an error wrapping multiple validation errors
// returned by GetTokenResponse.ValidateAll() if the designated constraints
// aren't met.
type GetTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTokenResponseMultiError) AllErrors() []error { return m }

// GetTokenResponseValidationError is the validation error returned by
// GetTokenResponse.Validate if the designated constraints aren't met.
type GetTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTokenResponseValidationError) ErrorName() string { return "GetTokenResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTokenResponseValidationError{}

// Validate checks the field values on GenerateUpiOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateUpiOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateUpiOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateUpiOtpRequestMultiError, or nil if none found.
func (m *GenerateUpiOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateUpiOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateUpiOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateUpiOtpRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateUpiOtpRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	// no validation rules for ActorId

	// no validation rules for ReqOtpType

	if len(errors) > 0 {
		return GenerateUpiOtpRequestMultiError(errors)
	}

	return nil
}

// GenerateUpiOtpRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateUpiOtpRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateUpiOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateUpiOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateUpiOtpRequestMultiError) AllErrors() []error { return m }

// GenerateUpiOtpRequestValidationError is the validation error returned by
// GenerateUpiOtpRequest.Validate if the designated constraints aren't met.
type GenerateUpiOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateUpiOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateUpiOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateUpiOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateUpiOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateUpiOtpRequestValidationError) ErrorName() string {
	return "GenerateUpiOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateUpiOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateUpiOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateUpiOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateUpiOtpRequestValidationError{}

// Validate checks the field values on GenerateUpiOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateUpiOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateUpiOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateUpiOtpResponseMultiError, or nil if none found.
func (m *GenerateUpiOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateUpiOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateUpiOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateUpiOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateUpiOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SecureUrl

	if len(errors) > 0 {
		return GenerateUpiOtpResponseMultiError(errors)
	}

	return nil
}

// GenerateUpiOtpResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateUpiOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateUpiOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateUpiOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateUpiOtpResponseMultiError) AllErrors() []error { return m }

// GenerateUpiOtpResponseValidationError is the validation error returned by
// GenerateUpiOtpResponse.Validate if the designated constraints aren't met.
type GenerateUpiOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateUpiOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateUpiOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateUpiOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateUpiOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateUpiOtpResponseValidationError) ErrorName() string {
	return "GenerateUpiOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateUpiOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateUpiOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateUpiOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateUpiOtpResponseValidationError{}

// Validate checks the field values on ResolveAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveAccountRequestMultiError, or nil if none found.
func (m *ResolveAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vpa

	if len(errors) > 0 {
		return ResolveAccountRequestMultiError(errors)
	}

	return nil
}

// ResolveAccountRequestMultiError is an error wrapping multiple validation
// errors returned by ResolveAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type ResolveAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveAccountRequestMultiError) AllErrors() []error { return m }

// ResolveAccountRequestValidationError is the validation error returned by
// ResolveAccountRequest.Validate if the designated constraints aren't met.
type ResolveAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveAccountRequestValidationError) ErrorName() string {
	return "ResolveAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveAccountRequestValidationError{}

// Validate checks the field values on ResolveAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResolveAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResolveAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResolveAccountResponseMultiError, or nil if none found.
func (m *ResolveAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResolveAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResolveAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResolveAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResolveAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountReferenceNumber

	// no validation rules for Ifsc

	// no validation rules for AccType

	if len(errors) > 0 {
		return ResolveAccountResponseMultiError(errors)
	}

	return nil
}

// ResolveAccountResponseMultiError is an error wrapping multiple validation
// errors returned by ResolveAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type ResolveAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResolveAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResolveAccountResponseMultiError) AllErrors() []error { return m }

// ResolveAccountResponseValidationError is the validation error returned by
// ResolveAccountResponse.Validate if the designated constraints aren't met.
type ResolveAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResolveAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResolveAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResolveAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResolveAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResolveAccountResponseValidationError) ErrorName() string {
	return "ResolveAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResolveAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResolveAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResolveAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResolveAccountResponseValidationError{}

// Validate checks the field values on CreateVPARequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateVPARequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVPARequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVPARequestMultiError, or nil if none found.
func (m *CreateVPARequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVPARequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVPARequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVPARequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVPARequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _CreateVPARequest_PartnerBank_NotInLookup[m.GetPartnerBank()]; ok {
		err := CreateVPARequestValidationError{
			field:  "PartnerBank",
			reason: "value must not be in list [VENDOR_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateVPARequest_AccountType_NotInLookup[m.GetAccountType()]; ok {
		err := CreateVPARequestValidationError{
			field:  "AccountType",
			reason: "value must not be in list [TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVPARequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVPARequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVPARequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	if l := utf8.RuneCountInString(m.GetAccountNo()); l < 4 || l > 100 {
		err := CreateVPARequestValidationError{
			field:  "AccountNo",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetIfscCode()); l < 4 || l > 50 {
		err := CreateVPARequestValidationError{
			field:  "IfscCode",
			reason: "value length must be between 4 and 50 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ActorId

	// no validation rules for UserId

	if m.GetVpaName() == nil {
		err := CreateVPARequestValidationError{
			field:  "VpaName",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVpaName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVPARequestValidationError{
					field:  "VpaName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVPARequestValidationError{
					field:  "VpaName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpaName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVPARequestValidationError{
				field:  "VpaName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVPARequestMultiError(errors)
	}

	return nil
}

// CreateVPARequestMultiError is an error wrapping multiple validation errors
// returned by CreateVPARequest.ValidateAll() if the designated constraints
// aren't met.
type CreateVPARequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVPARequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVPARequestMultiError) AllErrors() []error { return m }

// CreateVPARequestValidationError is the validation error returned by
// CreateVPARequest.Validate if the designated constraints aren't met.
type CreateVPARequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVPARequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVPARequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVPARequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVPARequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVPARequestValidationError) ErrorName() string { return "CreateVPARequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateVPARequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVPARequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVPARequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVPARequestValidationError{}

var _CreateVPARequest_PartnerBank_NotInLookup = map[vendorgateway.Vendor]struct{}{
	0: {},
}

var _CreateVPARequest_AccountType_NotInLookup = map[accounts.Type]struct{}{
	0: {},
}

// Validate checks the field values on CreateVPAResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateVPAResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVPAResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVPAResponseMultiError, or nil if none found.
func (m *CreateVPAResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVPAResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVPAResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVPAResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVPAResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVPAResponseMultiError(errors)
	}

	return nil
}

// CreateVPAResponseMultiError is an error wrapping multiple validation errors
// returned by CreateVPAResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateVPAResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVPAResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVPAResponseMultiError) AllErrors() []error { return m }

// CreateVPAResponseValidationError is the validation error returned by
// CreateVPAResponse.Validate if the designated constraints aren't met.
type CreateVPAResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVPAResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVPAResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVPAResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVPAResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVPAResponseValidationError) ErrorName() string {
	return "CreateVPAResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVPAResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVPAResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVPAResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVPAResponseValidationError{}

// Validate checks the field values on ListKeysResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListKeysResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListKeysResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListKeysResponseMultiError, or nil if none found.
func (m *ListKeysResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListKeysResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListKeysResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListKeysResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListKeysResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KeyXmlPayload

	if len(errors) > 0 {
		return ListKeysResponseMultiError(errors)
	}

	return nil
}

// ListKeysResponseMultiError is an error wrapping multiple validation errors
// returned by ListKeysResponse.ValidateAll() if the designated constraints
// aren't met.
type ListKeysResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListKeysResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListKeysResponseMultiError) AllErrors() []error { return m }

// ListKeysResponseValidationError is the validation error returned by
// ListKeysResponse.Validate if the designated constraints aren't met.
type ListKeysResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListKeysResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListKeysResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListKeysResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListKeysResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListKeysResponseValidationError) ErrorName() string { return "ListKeysResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListKeysResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListKeysResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListKeysResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListKeysResponseValidationError{}

// Validate checks the field values on RegisterMobileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterMobileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterMobileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterMobileRequestMultiError, or nil if none found.
func (m *RegisterMobileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterMobileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterMobileRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterMobileRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterMobileRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	for idx, item := range m.GetCredBlock() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RegisterMobileRequestValidationError{
						field:  fmt.Sprintf("CredBlock[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RegisterMobileRequestValidationError{
						field:  fmt.Sprintf("CredBlock[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RegisterMobileRequestValidationError{
					field:  fmt.Sprintf("CredBlock[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TransactionId

	if all {
		switch v := interface{}(m.GetCardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterMobileRequestValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterMobileRequestValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterMobileRequestValidationError{
				field:  "CardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpiPinSetOptionType

	if len(errors) > 0 {
		return RegisterMobileRequestMultiError(errors)
	}

	return nil
}

// RegisterMobileRequestMultiError is an error wrapping multiple validation
// errors returned by RegisterMobileRequest.ValidateAll() if the designated
// constraints aren't met.
type RegisterMobileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterMobileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterMobileRequestMultiError) AllErrors() []error { return m }

// RegisterMobileRequestValidationError is the validation error returned by
// RegisterMobileRequest.Validate if the designated constraints aren't met.
type RegisterMobileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterMobileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterMobileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterMobileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterMobileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterMobileRequestValidationError) ErrorName() string {
	return "RegisterMobileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterMobileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterMobileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterMobileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterMobileRequestValidationError{}

// Validate checks the field values on RegisterMobileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterMobileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterMobileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterMobileResponseMultiError, or nil if none found.
func (m *RegisterMobileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterMobileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterMobileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterMobileResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterMobileResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawResponseCode

	if len(errors) > 0 {
		return RegisterMobileResponseMultiError(errors)
	}

	return nil
}

// RegisterMobileResponseMultiError is an error wrapping multiple validation
// errors returned by RegisterMobileResponse.ValidateAll() if the designated
// constraints aren't met.
type RegisterMobileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterMobileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterMobileResponseMultiError) AllErrors() []error { return m }

// RegisterMobileResponseValidationError is the validation error returned by
// RegisterMobileResponse.Validate if the designated constraints aren't met.
type RegisterMobileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterMobileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterMobileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterMobileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterMobileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterMobileResponseValidationError) ErrorName() string {
	return "RegisterMobileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterMobileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterMobileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterMobileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterMobileResponseValidationError{}

// Validate checks the field values on GetPinFlowParametersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPinFlowParametersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPinFlowParametersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPinFlowParametersRequestMultiError, or nil if none found.
func (m *GetPinFlowParametersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPinFlowParametersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetAccountId()); l < 4 || l > 100 {
		err := GetPinFlowParametersRequestValidationError{
			field:  "AccountId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetPinFlowParametersRequest_PinFlowType_NotInLookup[m.GetPinFlowType()]; ok {
		err := GetPinFlowParametersRequestValidationError{
			field:  "PinFlowType",
			reason: "value must not be in list [PIN_FLOW_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CurrentActorId

	// no validation rules for UpiPinSetOptionType

	if len(errors) > 0 {
		return GetPinFlowParametersRequestMultiError(errors)
	}

	return nil
}

// GetPinFlowParametersRequestMultiError is an error wrapping multiple
// validation errors returned by GetPinFlowParametersRequest.ValidateAll() if
// the designated constraints aren't met.
type GetPinFlowParametersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPinFlowParametersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPinFlowParametersRequestMultiError) AllErrors() []error { return m }

// GetPinFlowParametersRequestValidationError is the validation error returned
// by GetPinFlowParametersRequest.Validate if the designated constraints
// aren't met.
type GetPinFlowParametersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPinFlowParametersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPinFlowParametersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPinFlowParametersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPinFlowParametersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPinFlowParametersRequestValidationError) ErrorName() string {
	return "GetPinFlowParametersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPinFlowParametersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPinFlowParametersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPinFlowParametersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPinFlowParametersRequestValidationError{}

var _GetPinFlowParametersRequest_PinFlowType_NotInLookup = map[GetPinFlowParametersRequest_PinFlowType]struct{}{
	0: {},
}

// Validate checks the field values on GetPinFlowParametersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPinFlowParametersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPinFlowParametersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPinFlowParametersResponseMultiError, or nil if none found.
func (m *GetPinFlowParametersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPinFlowParametersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinFlowParametersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinFlowParametersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinFlowParametersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KeyCode

	// no validation rules for KeyXmlPayload

	if all {
		switch v := interface{}(m.GetControlJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinFlowParametersResponseValidationError{
					field:  "ControlJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinFlowParametersResponseValidationError{
					field:  "ControlJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetControlJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinFlowParametersResponseValidationError{
				field:  "ControlJson",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BankConfigJson

	// no validation rules for AccountNumber

	// no validation rules for TransactionId

	// no validation rules for IsExistingBankUser

	// no validation rules for MaskedAccountNumber

	if all {
		switch v := interface{}(m.GetBankConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinFlowParametersResponseValidationError{
					field:  "BankConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinFlowParametersResponseValidationError{
					field:  "BankConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinFlowParametersResponseValidationError{
				field:  "BankConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPinFlowParametersResponseMultiError(errors)
	}

	return nil
}

// GetPinFlowParametersResponseMultiError is an error wrapping multiple
// validation errors returned by GetPinFlowParametersResponse.ValidateAll() if
// the designated constraints aren't met.
type GetPinFlowParametersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPinFlowParametersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPinFlowParametersResponseMultiError) AllErrors() []error { return m }

// GetPinFlowParametersResponseValidationError is the validation error returned
// by GetPinFlowParametersResponse.Validate if the designated constraints
// aren't met.
type GetPinFlowParametersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPinFlowParametersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPinFlowParametersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPinFlowParametersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPinFlowParametersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPinFlowParametersResponseValidationError) ErrorName() string {
	return "GetPinFlowParametersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPinFlowParametersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPinFlowParametersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPinFlowParametersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPinFlowParametersResponseValidationError{}

// Validate checks the field values on GetTransactionParametersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionParametersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionParametersRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTransactionParametersRequestMultiError, or nil if none found.
func (m *GetTransactionParametersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionParametersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetAccountId()); l < 4 || l > 100 {
		err := GetTransactionParametersRequestValidationError{
			field:  "AccountId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTransactionParametersRequestMultiError(errors)
	}

	return nil
}

// GetTransactionParametersRequestMultiError is an error wrapping multiple
// validation errors returned by GetTransactionParametersRequest.ValidateAll()
// if the designated constraints aren't met.
type GetTransactionParametersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionParametersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionParametersRequestMultiError) AllErrors() []error { return m }

// GetTransactionParametersRequestValidationError is the validation error
// returned by GetTransactionParametersRequest.Validate if the designated
// constraints aren't met.
type GetTransactionParametersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionParametersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionParametersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionParametersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionParametersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionParametersRequestValidationError) ErrorName() string {
	return "GetTransactionParametersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionParametersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionParametersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionParametersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionParametersRequestValidationError{}

// Validate checks the field values on GetTransactionParametersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTransactionParametersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionParametersResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTransactionParametersResponseMultiError, or nil if none found.
func (m *GetTransactionParametersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionParametersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionParametersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionParametersResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionParametersResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KeyCode

	// no validation rules for KeyXmlPayload

	if all {
		switch v := interface{}(m.GetControlJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionParametersResponseValidationError{
					field:  "ControlJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionParametersResponseValidationError{
					field:  "ControlJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetControlJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionParametersResponseValidationError{
				field:  "ControlJson",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BankConfigJson

	if all {
		switch v := interface{}(m.GetBankConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionParametersResponseValidationError{
					field:  "BankConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionParametersResponseValidationError{
					field:  "BankConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionParametersResponseValidationError{
				field:  "BankConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTransactionParametersResponseMultiError(errors)
	}

	return nil
}

// GetTransactionParametersResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTransactionParametersResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionParametersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionParametersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionParametersResponseMultiError) AllErrors() []error { return m }

// GetTransactionParametersResponseValidationError is the validation error
// returned by GetTransactionParametersResponse.Validate if the designated
// constraints aren't met.
type GetTransactionParametersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionParametersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionParametersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionParametersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionParametersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionParametersResponseValidationError) ErrorName() string {
	return "GetTransactionParametersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionParametersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionParametersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionParametersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionParametersResponseValidationError{}

// Validate checks the field values on VerifyURNRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyURNRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyURNRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyURNRequestMultiError, or nil if none found.
func (m *VerifyURNRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyURNRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Urn

	// no validation rules for Mcc

	// no validation rules for UrnType

	if len(errors) > 0 {
		return VerifyURNRequestMultiError(errors)
	}

	return nil
}

// VerifyURNRequestMultiError is an error wrapping multiple validation errors
// returned by VerifyURNRequest.ValidateAll() if the designated constraints
// aren't met.
type VerifyURNRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyURNRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyURNRequestMultiError) AllErrors() []error { return m }

// VerifyURNRequestValidationError is the validation error returned by
// VerifyURNRequest.Validate if the designated constraints aren't met.
type VerifyURNRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyURNRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyURNRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyURNRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyURNRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyURNRequestValidationError) ErrorName() string { return "VerifyURNRequestValidationError" }

// Error satisfies the builtin error interface
func (e VerifyURNRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyURNRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyURNRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyURNRequestValidationError{}

// Validate checks the field values on VerifyURNResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyURNResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyURNResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyURNResponseMultiError, or nil if none found.
func (m *VerifyURNResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyURNResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyURNResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TxnId

	// no validation rules for MerchantRefId

	// no validation rules for ReferenceUrl

	// no validation rules for InitiationMode

	// no validation rules for Purpose

	// no validation rules for Mcc

	// no validation rules for MerchantId

	// no validation rules for MerchantStoreId

	// no validation rules for MerchantTerminalId

	// no validation rules for OrgId

	// no validation rules for IsSignatureVerified

	// no validation rules for IsDynamicQrInitialised

	// no validation rules for UrnPayeeAddress

	// no validation rules for PayeeName

	if all {
		switch v := interface{}(m.GetExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyURNResponseValidationError{
				field:  "ExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyURNResponseValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyURNResponseValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetParsedUrnInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "ParsedUrnInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyURNResponseValidationError{
					field:  "ParsedUrnInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParsedUrnInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyURNResponseValidationError{
				field:  "ParsedUrnInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.UrnParams.(type) {
	case *VerifyURNResponse_MandateUrnInfo:
		if v == nil {
			err := VerifyURNResponseValidationError{
				field:  "UrnParams",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMandateUrnInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VerifyURNResponseValidationError{
						field:  "MandateUrnInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VerifyURNResponseValidationError{
						field:  "MandateUrnInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMandateUrnInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VerifyURNResponseValidationError{
					field:  "MandateUrnInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return VerifyURNResponseMultiError(errors)
	}

	return nil
}

// VerifyURNResponseMultiError is an error wrapping multiple validation errors
// returned by VerifyURNResponse.ValidateAll() if the designated constraints
// aren't met.
type VerifyURNResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyURNResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyURNResponseMultiError) AllErrors() []error { return m }

// VerifyURNResponseValidationError is the validation error returned by
// VerifyURNResponse.Validate if the designated constraints aren't met.
type VerifyURNResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyURNResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyURNResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyURNResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyURNResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyURNResponseValidationError) ErrorName() string {
	return "VerifyURNResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyURNResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyURNResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyURNResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyURNResponseValidationError{}

// Validate checks the field values on ChangePinRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChangePinRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangePinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangePinRequestMultiError, or nil if none found.
func (m *ChangePinRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangePinRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChangePinRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChangePinRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChangePinRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOldPinCredBlock()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChangePinRequestValidationError{
					field:  "OldPinCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChangePinRequestValidationError{
					field:  "OldPinCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOldPinCredBlock()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChangePinRequestValidationError{
				field:  "OldPinCredBlock",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNewPinCredBlock()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChangePinRequestValidationError{
					field:  "NewPinCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChangePinRequestValidationError{
					field:  "NewPinCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNewPinCredBlock()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChangePinRequestValidationError{
				field:  "NewPinCredBlock",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountReferenceNumber

	// no validation rules for Ifsc

	// no validation rules for AccType

	// no validation rules for Vpa

	// no validation rules for AccountHolderName

	// no validation rules for MccCode

	// no validation rules for CustomerType

	// no validation rules for AccountId

	if l := utf8.RuneCountInString(m.GetTransactionId()); l < 1 || l > 100 {
		err := ChangePinRequestValidationError{
			field:  "TransactionId",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ChangePinRequestMultiError(errors)
	}

	return nil
}

// ChangePinRequestMultiError is an error wrapping multiple validation errors
// returned by ChangePinRequest.ValidateAll() if the designated constraints
// aren't met.
type ChangePinRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangePinRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangePinRequestMultiError) AllErrors() []error { return m }

// ChangePinRequestValidationError is the validation error returned by
// ChangePinRequest.Validate if the designated constraints aren't met.
type ChangePinRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangePinRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangePinRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangePinRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangePinRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangePinRequestValidationError) ErrorName() string { return "ChangePinRequestValidationError" }

// Error satisfies the builtin error interface
func (e ChangePinRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangePinRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangePinRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangePinRequestValidationError{}

// Validate checks the field values on ChangePinResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChangePinResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangePinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangePinResponseMultiError, or nil if none found.
func (m *ChangePinResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangePinResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChangePinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChangePinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChangePinResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawResponseCode

	if len(errors) > 0 {
		return ChangePinResponseMultiError(errors)
	}

	return nil
}

// ChangePinResponseMultiError is an error wrapping multiple validation errors
// returned by ChangePinResponse.ValidateAll() if the designated constraints
// aren't met.
type ChangePinResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangePinResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangePinResponseMultiError) AllErrors() []error { return m }

// ChangePinResponseValidationError is the validation error returned by
// ChangePinResponse.Validate if the designated constraints aren't met.
type ChangePinResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangePinResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangePinResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangePinResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangePinResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangePinResponseValidationError) ErrorName() string {
	return "ChangePinResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ChangePinResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangePinResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangePinResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangePinResponseValidationError{}

// Validate checks the field values on GenerateURNRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateURNRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateURNRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateURNRequestMultiError, or nil if none found.
func (m *GenerateURNRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateURNRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayeeAccountId

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateURNRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateURNRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateURNRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InitiationMode

	// no validation rules for CurrentActorId

	if all {
		switch v := interface{}(m.GetNextOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateURNRequestValidationError{
					field:  "NextOrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateURNRequestValidationError{
					field:  "NextOrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateURNRequestValidationError{
				field:  "NextOrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderSource

	// no validation rules for ToActorLocationToken

	if len(errors) > 0 {
		return GenerateURNRequestMultiError(errors)
	}

	return nil
}

// GenerateURNRequestMultiError is an error wrapping multiple validation errors
// returned by GenerateURNRequest.ValidateAll() if the designated constraints
// aren't met.
type GenerateURNRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateURNRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateURNRequestMultiError) AllErrors() []error { return m }

// GenerateURNRequestValidationError is the validation error returned by
// GenerateURNRequest.Validate if the designated constraints aren't met.
type GenerateURNRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateURNRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateURNRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateURNRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateURNRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateURNRequestValidationError) ErrorName() string {
	return "GenerateURNRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateURNRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateURNRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateURNRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateURNRequestValidationError{}

// Validate checks the field values on GenerateURNResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateURNResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateURNResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateURNResponseMultiError, or nil if none found.
func (m *GenerateURNResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateURNResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateURNResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateURNResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateURNResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Urn

	// no validation rules for OrderId

	if len(errors) > 0 {
		return GenerateURNResponseMultiError(errors)
	}

	return nil
}

// GenerateURNResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateURNResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateURNResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateURNResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateURNResponseMultiError) AllErrors() []error { return m }

// GenerateURNResponseValidationError is the validation error returned by
// GenerateURNResponse.Validate if the designated constraints aren't met.
type GenerateURNResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateURNResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateURNResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateURNResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateURNResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateURNResponseValidationError) ErrorName() string {
	return "GenerateURNResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateURNResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateURNResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateURNResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateURNResponseValidationError{}

// Validate checks the field values on GetUpiSetupStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUpiSetupStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUpiSetupStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUpiSetupStatusRequestMultiError, or nil if none found.
func (m *GetUpiSetupStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUpiSetupStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if len(errors) > 0 {
		return GetUpiSetupStatusRequestMultiError(errors)
	}

	return nil
}

// GetUpiSetupStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetUpiSetupStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUpiSetupStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUpiSetupStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUpiSetupStatusRequestMultiError) AllErrors() []error { return m }

// GetUpiSetupStatusRequestValidationError is the validation error returned by
// GetUpiSetupStatusRequest.Validate if the designated constraints aren't met.
type GetUpiSetupStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUpiSetupStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUpiSetupStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUpiSetupStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUpiSetupStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUpiSetupStatusRequestValidationError) ErrorName() string {
	return "GetUpiSetupStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUpiSetupStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUpiSetupStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUpiSetupStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUpiSetupStatusRequestValidationError{}

// Validate checks the field values on GetUpiSetupStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUpiSetupStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUpiSetupStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUpiSetupStatusResponseMultiError, or nil if none found.
func (m *GetUpiSetupStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUpiSetupStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUpiSetupStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUpiSetupStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUpiSetupStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpiStatus

	if len(errors) > 0 {
		return GetUpiSetupStatusResponseMultiError(errors)
	}

	return nil
}

// GetUpiSetupStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetUpiSetupStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type GetUpiSetupStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUpiSetupStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUpiSetupStatusResponseMultiError) AllErrors() []error { return m }

// GetUpiSetupStatusResponseValidationError is the validation error returned by
// GetUpiSetupStatusResponse.Validate if the designated constraints aren't met.
type GetUpiSetupStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUpiSetupStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUpiSetupStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUpiSetupStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUpiSetupStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUpiSetupStatusResponseValidationError) ErrorName() string {
	return "GetUpiSetupStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUpiSetupStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUpiSetupStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUpiSetupStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUpiSetupStatusResponseValidationError{}

// Validate checks the field values on DisableOrEnableVPARequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DisableOrEnableVPARequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisableOrEnableVPARequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DisableOrEnableVPARequestMultiError, or nil if none found.
func (m *DisableOrEnableVPARequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DisableOrEnableVPARequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vpa

	// no validation rules for ActorId

	// no validation rules for RequestType

	// no validation rules for Reason

	// no validation rules for Source

	switch v := m.Identifier.(type) {
	case *DisableOrEnableVPARequest_UserVpa:
		if v == nil {
			err := DisableOrEnableVPARequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for UserVpa
	case *DisableOrEnableVPARequest_PiId:
		if v == nil {
			err := DisableOrEnableVPARequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PiId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return DisableOrEnableVPARequestMultiError(errors)
	}

	return nil
}

// DisableOrEnableVPARequestMultiError is an error wrapping multiple validation
// errors returned by DisableOrEnableVPARequest.ValidateAll() if the
// designated constraints aren't met.
type DisableOrEnableVPARequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisableOrEnableVPARequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisableOrEnableVPARequestMultiError) AllErrors() []error { return m }

// DisableOrEnableVPARequestValidationError is the validation error returned by
// DisableOrEnableVPARequest.Validate if the designated constraints aren't met.
type DisableOrEnableVPARequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisableOrEnableVPARequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisableOrEnableVPARequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisableOrEnableVPARequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisableOrEnableVPARequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisableOrEnableVPARequestValidationError) ErrorName() string {
	return "DisableOrEnableVPARequestValidationError"
}

// Error satisfies the builtin error interface
func (e DisableOrEnableVPARequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisableOrEnableVPARequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisableOrEnableVPARequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisableOrEnableVPARequestValidationError{}

// Validate checks the field values on DisableOrEnableVPAResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DisableOrEnableVPAResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisableOrEnableVPAResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DisableOrEnableVPAResponseMultiError, or nil if none found.
func (m *DisableOrEnableVPAResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DisableOrEnableVPAResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DisableOrEnableVPAResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DisableOrEnableVPAResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DisableOrEnableVPAResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DisableOrEnableVPAResponseMultiError(errors)
	}

	return nil
}

// DisableOrEnableVPAResponseMultiError is an error wrapping multiple
// validation errors returned by DisableOrEnableVPAResponse.ValidateAll() if
// the designated constraints aren't met.
type DisableOrEnableVPAResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisableOrEnableVPAResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisableOrEnableVPAResponseMultiError) AllErrors() []error { return m }

// DisableOrEnableVPAResponseValidationError is the validation error returned
// by DisableOrEnableVPAResponse.Validate if the designated constraints aren't met.
type DisableOrEnableVPAResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisableOrEnableVPAResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisableOrEnableVPAResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisableOrEnableVPAResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisableOrEnableVPAResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisableOrEnableVPAResponseValidationError) ErrorName() string {
	return "DisableOrEnableVPAResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DisableOrEnableVPAResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisableOrEnableVPAResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisableOrEnableVPAResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisableOrEnableVPAResponseValidationError{}

// Validate checks the field values on PinStatusRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PinStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PinStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PinStatusRequestMultiError, or nil if none found.
func (m *PinStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PinStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentActorId

	if len(errors) > 0 {
		return PinStatusRequestMultiError(errors)
	}

	return nil
}

// PinStatusRequestMultiError is an error wrapping multiple validation errors
// returned by PinStatusRequest.ValidateAll() if the designated constraints
// aren't met.
type PinStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PinStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PinStatusRequestMultiError) AllErrors() []error { return m }

// PinStatusRequestValidationError is the validation error returned by
// PinStatusRequest.Validate if the designated constraints aren't met.
type PinStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PinStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PinStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PinStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PinStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PinStatusRequestValidationError) ErrorName() string { return "PinStatusRequestValidationError" }

// Error satisfies the builtin error interface
func (e PinStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPinStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PinStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PinStatusRequestValidationError{}

// Validate checks the field values on PinStatusResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PinStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PinStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PinStatusResponseMultiError, or nil if none found.
func (m *PinStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PinStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PinStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PinStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PinStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountPinStatusMap

	// no validation rules for AccountPinStateMap

	if len(errors) > 0 {
		return PinStatusResponseMultiError(errors)
	}

	return nil
}

// PinStatusResponseMultiError is an error wrapping multiple validation errors
// returned by PinStatusResponse.ValidateAll() if the designated constraints
// aren't met.
type PinStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PinStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PinStatusResponseMultiError) AllErrors() []error { return m }

// PinStatusResponseValidationError is the validation error returned by
// PinStatusResponse.Validate if the designated constraints aren't met.
type PinStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PinStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PinStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PinStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PinStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PinStatusResponseValidationError) ErrorName() string {
	return "PinStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PinStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPinStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PinStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PinStatusResponseValidationError{}

// Validate checks the field values on GetAccountPinInfosRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountPinInfosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountPinInfosRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountPinInfosRequestMultiError, or nil if none found.
func (m *GetAccountPinInfosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountPinInfosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if len(errors) > 0 {
		return GetAccountPinInfosRequestMultiError(errors)
	}

	return nil
}

// GetAccountPinInfosRequestMultiError is an error wrapping multiple validation
// errors returned by GetAccountPinInfosRequest.ValidateAll() if the
// designated constraints aren't met.
type GetAccountPinInfosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountPinInfosRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountPinInfosRequestMultiError) AllErrors() []error { return m }

// GetAccountPinInfosRequestValidationError is the validation error returned by
// GetAccountPinInfosRequest.Validate if the designated constraints aren't met.
type GetAccountPinInfosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountPinInfosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountPinInfosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountPinInfosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountPinInfosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountPinInfosRequestValidationError) ErrorName() string {
	return "GetAccountPinInfosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountPinInfosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountPinInfosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountPinInfosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountPinInfosRequestValidationError{}

// Validate checks the field values on GetAccountPinInfosResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountPinInfosResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountPinInfosResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountPinInfosResponseMultiError, or nil if none found.
func (m *GetAccountPinInfosResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountPinInfosResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountPinInfosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountPinInfosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountPinInfosResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountUpiPinInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountPinInfosResponseValidationError{
						field:  fmt.Sprintf("AccountUpiPinInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountPinInfosResponseValidationError{
						field:  fmt.Sprintf("AccountUpiPinInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountPinInfosResponseValidationError{
					field:  fmt.Sprintf("AccountUpiPinInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAccountPinInfosResponseMultiError(errors)
	}

	return nil
}

// GetAccountPinInfosResponseMultiError is an error wrapping multiple
// validation errors returned by GetAccountPinInfosResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAccountPinInfosResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountPinInfosResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountPinInfosResponseMultiError) AllErrors() []error { return m }

// GetAccountPinInfosResponseValidationError is the validation error returned
// by GetAccountPinInfosResponse.Validate if the designated constraints aren't met.
type GetAccountPinInfosResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountPinInfosResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountPinInfosResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountPinInfosResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountPinInfosResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountPinInfosResponseValidationError) ErrorName() string {
	return "GetAccountPinInfosResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountPinInfosResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountPinInfosResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountPinInfosResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountPinInfosResponseValidationError{}

// Validate checks the field values on GetLatestAccountPinInfosRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLatestAccountPinInfosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLatestAccountPinInfosRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLatestAccountPinInfosRequestMultiError, or nil if none found.
func (m *GetLatestAccountPinInfosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestAccountPinInfosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetLatestAccountPinInfosRequestMultiError(errors)
	}

	return nil
}

// GetLatestAccountPinInfosRequestMultiError is an error wrapping multiple
// validation errors returned by GetLatestAccountPinInfosRequest.ValidateAll()
// if the designated constraints aren't met.
type GetLatestAccountPinInfosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestAccountPinInfosRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestAccountPinInfosRequestMultiError) AllErrors() []error { return m }

// GetLatestAccountPinInfosRequestValidationError is the validation error
// returned by GetLatestAccountPinInfosRequest.Validate if the designated
// constraints aren't met.
type GetLatestAccountPinInfosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestAccountPinInfosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestAccountPinInfosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestAccountPinInfosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestAccountPinInfosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestAccountPinInfosRequestValidationError) ErrorName() string {
	return "GetLatestAccountPinInfosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestAccountPinInfosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestAccountPinInfosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestAccountPinInfosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestAccountPinInfosRequestValidationError{}

// Validate checks the field values on GetLatestAccountPinInfosResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLatestAccountPinInfosResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLatestAccountPinInfosResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLatestAccountPinInfosResponseMultiError, or nil if none found.
func (m *GetLatestAccountPinInfosResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestAccountPinInfosResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLatestAccountPinInfosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLatestAccountPinInfosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLatestAccountPinInfosResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountUpiPinInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLatestAccountPinInfosResponseValidationError{
						field:  fmt.Sprintf("AccountUpiPinInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLatestAccountPinInfosResponseValidationError{
						field:  fmt.Sprintf("AccountUpiPinInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLatestAccountPinInfosResponseValidationError{
					field:  fmt.Sprintf("AccountUpiPinInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLatestAccountPinInfosResponseMultiError(errors)
	}

	return nil
}

// GetLatestAccountPinInfosResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetLatestAccountPinInfosResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLatestAccountPinInfosResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestAccountPinInfosResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestAccountPinInfosResponseMultiError) AllErrors() []error { return m }

// GetLatestAccountPinInfosResponseValidationError is the validation error
// returned by GetLatestAccountPinInfosResponse.Validate if the designated
// constraints aren't met.
type GetLatestAccountPinInfosResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestAccountPinInfosResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestAccountPinInfosResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestAccountPinInfosResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestAccountPinInfosResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestAccountPinInfosResponseValidationError) ErrorName() string {
	return "GetLatestAccountPinInfosResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestAccountPinInfosResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestAccountPinInfosResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestAccountPinInfosResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestAccountPinInfosResponseValidationError{}

// Validate checks the field values on CheckVerifiedMerchantRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckVerifiedMerchantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckVerifiedMerchantRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckVerifiedMerchantRequestMultiError, or nil if none found.
func (m *CheckVerifiedMerchantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckVerifiedMerchantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vpa

	if len(errors) > 0 {
		return CheckVerifiedMerchantRequestMultiError(errors)
	}

	return nil
}

// CheckVerifiedMerchantRequestMultiError is an error wrapping multiple
// validation errors returned by CheckVerifiedMerchantRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckVerifiedMerchantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckVerifiedMerchantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckVerifiedMerchantRequestMultiError) AllErrors() []error { return m }

// CheckVerifiedMerchantRequestValidationError is the validation error returned
// by CheckVerifiedMerchantRequest.Validate if the designated constraints
// aren't met.
type CheckVerifiedMerchantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckVerifiedMerchantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckVerifiedMerchantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckVerifiedMerchantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckVerifiedMerchantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckVerifiedMerchantRequestValidationError) ErrorName() string {
	return "CheckVerifiedMerchantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckVerifiedMerchantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckVerifiedMerchantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckVerifiedMerchantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckVerifiedMerchantRequestValidationError{}

// Validate checks the field values on CheckVerifiedMerchantResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckVerifiedMerchantResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckVerifiedMerchantResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckVerifiedMerchantResponseMultiError, or nil if none found.
func (m *CheckVerifiedMerchantResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckVerifiedMerchantResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckVerifiedMerchantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckVerifiedMerchantResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckVerifiedMerchantResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckVerifiedMerchantResponseMultiError(errors)
	}

	return nil
}

// CheckVerifiedMerchantResponseMultiError is an error wrapping multiple
// validation errors returned by CheckVerifiedMerchantResponse.ValidateAll()
// if the designated constraints aren't met.
type CheckVerifiedMerchantResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckVerifiedMerchantResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckVerifiedMerchantResponseMultiError) AllErrors() []error { return m }

// CheckVerifiedMerchantResponseValidationError is the validation error
// returned by CheckVerifiedMerchantResponse.Validate if the designated
// constraints aren't met.
type CheckVerifiedMerchantResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckVerifiedMerchantResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckVerifiedMerchantResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckVerifiedMerchantResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckVerifiedMerchantResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckVerifiedMerchantResponseValidationError) ErrorName() string {
	return "CheckVerifiedMerchantResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckVerifiedMerchantResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckVerifiedMerchantResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckVerifiedMerchantResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckVerifiedMerchantResponseValidationError{}

// Validate checks the field values on GetVpaMerchantInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVpaMerchantInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVpaMerchantInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVpaMerchantInfoRequestMultiError, or nil if none found.
func (m *GetVpaMerchantInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVpaMerchantInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vpa

	if len(errors) > 0 {
		return GetVpaMerchantInfoRequestMultiError(errors)
	}

	return nil
}

// GetVpaMerchantInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetVpaMerchantInfoRequest.ValidateAll() if the
// designated constraints aren't met.
type GetVpaMerchantInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVpaMerchantInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVpaMerchantInfoRequestMultiError) AllErrors() []error { return m }

// GetVpaMerchantInfoRequestValidationError is the validation error returned by
// GetVpaMerchantInfoRequest.Validate if the designated constraints aren't met.
type GetVpaMerchantInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVpaMerchantInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVpaMerchantInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVpaMerchantInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVpaMerchantInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVpaMerchantInfoRequestValidationError) ErrorName() string {
	return "GetVpaMerchantInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetVpaMerchantInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVpaMerchantInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVpaMerchantInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVpaMerchantInfoRequestValidationError{}

// Validate checks the field values on GetVpaMerchantInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVpaMerchantInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVpaMerchantInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVpaMerchantInfoResponseMultiError, or nil if none found.
func (m *GetVpaMerchantInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVpaMerchantInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVpaMerchantInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVpaMerchantInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVpaMerchantInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVpaMerchantInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVpaMerchantInfoResponseValidationError{
					field:  "VpaMerchantInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVpaMerchantInfoResponseValidationError{
					field:  "VpaMerchantInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpaMerchantInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVpaMerchantInfoResponseValidationError{
				field:  "VpaMerchantInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetVpaMerchantInfoResponseMultiError(errors)
	}

	return nil
}

// GetVpaMerchantInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetVpaMerchantInfoResponse.ValidateAll() if
// the designated constraints aren't met.
type GetVpaMerchantInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVpaMerchantInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVpaMerchantInfoResponseMultiError) AllErrors() []error { return m }

// GetVpaMerchantInfoResponseValidationError is the validation error returned
// by GetVpaMerchantInfoResponse.Validate if the designated constraints aren't met.
type GetVpaMerchantInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVpaMerchantInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVpaMerchantInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVpaMerchantInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVpaMerchantInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVpaMerchantInfoResponseValidationError) ErrorName() string {
	return "GetVpaMerchantInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetVpaMerchantInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVpaMerchantInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVpaMerchantInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVpaMerchantInfoResponseValidationError{}

// Validate checks the field values on GetPinInfosByAccountIdRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPinInfosByAccountIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPinInfosByAccountIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPinInfosByAccountIdRequestMultiError, or nil if none found.
func (m *GetPinInfosByAccountIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPinInfosByAccountIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetAccountId()) < 1 {
		err := GetPinInfosByAccountIdRequestValidationError{
			field:  "AccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinInfosByAccountIdRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinInfosByAccountIdRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinInfosByAccountIdRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinInfosByAccountIdRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinInfosByAccountIdRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinInfosByAccountIdRequestValidationError{
				field:  "ToTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Limit

	// no validation rules for Offset

	// no validation rules for SortBy

	// no validation rules for SortOrder

	if len(errors) > 0 {
		return GetPinInfosByAccountIdRequestMultiError(errors)
	}

	return nil
}

// GetPinInfosByAccountIdRequestMultiError is an error wrapping multiple
// validation errors returned by GetPinInfosByAccountIdRequest.ValidateAll()
// if the designated constraints aren't met.
type GetPinInfosByAccountIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPinInfosByAccountIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPinInfosByAccountIdRequestMultiError) AllErrors() []error { return m }

// GetPinInfosByAccountIdRequestValidationError is the validation error
// returned by GetPinInfosByAccountIdRequest.Validate if the designated
// constraints aren't met.
type GetPinInfosByAccountIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPinInfosByAccountIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPinInfosByAccountIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPinInfosByAccountIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPinInfosByAccountIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPinInfosByAccountIdRequestValidationError) ErrorName() string {
	return "GetPinInfosByAccountIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPinInfosByAccountIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPinInfosByAccountIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPinInfosByAccountIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPinInfosByAccountIdRequestValidationError{}

// Validate checks the field values on GetPinInfosByAccountIdResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPinInfosByAccountIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPinInfosByAccountIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPinInfosByAccountIdResponseMultiError, or nil if none found.
func (m *GetPinInfosByAccountIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPinInfosByAccountIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPinInfosByAccountIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPinInfosByAccountIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPinInfosByAccountIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountUpiPinInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPinInfosByAccountIdResponseValidationError{
						field:  fmt.Sprintf("AccountUpiPinInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPinInfosByAccountIdResponseValidationError{
						field:  fmt.Sprintf("AccountUpiPinInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPinInfosByAccountIdResponseValidationError{
					field:  fmt.Sprintf("AccountUpiPinInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetPinInfosByAccountIdResponseMultiError(errors)
	}

	return nil
}

// GetPinInfosByAccountIdResponseMultiError is an error wrapping multiple
// validation errors returned by GetPinInfosByAccountIdResponse.ValidateAll()
// if the designated constraints aren't met.
type GetPinInfosByAccountIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPinInfosByAccountIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPinInfosByAccountIdResponseMultiError) AllErrors() []error { return m }

// GetPinInfosByAccountIdResponseValidationError is the validation error
// returned by GetPinInfosByAccountIdResponse.Validate if the designated
// constraints aren't met.
type GetPinInfosByAccountIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPinInfosByAccountIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPinInfosByAccountIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPinInfosByAccountIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPinInfosByAccountIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPinInfosByAccountIdResponseValidationError) ErrorName() string {
	return "GetPinInfosByAccountIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPinInfosByAccountIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPinInfosByAccountIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPinInfosByAccountIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPinInfosByAccountIdResponseValidationError{}

// Validate checks the field values on PostUserActivityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostUserActivityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostUserActivityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostUserActivityRequestMultiError, or nil if none found.
func (m *PostUserActivityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PostUserActivityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for UserActivity

	// no validation rules for DerivedAccountId

	if len(errors) > 0 {
		return PostUserActivityRequestMultiError(errors)
	}

	return nil
}

// PostUserActivityRequestMultiError is an error wrapping multiple validation
// errors returned by PostUserActivityRequest.ValidateAll() if the designated
// constraints aren't met.
type PostUserActivityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostUserActivityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostUserActivityRequestMultiError) AllErrors() []error { return m }

// PostUserActivityRequestValidationError is the validation error returned by
// PostUserActivityRequest.Validate if the designated constraints aren't met.
type PostUserActivityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostUserActivityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostUserActivityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostUserActivityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostUserActivityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostUserActivityRequestValidationError) ErrorName() string {
	return "PostUserActivityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PostUserActivityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostUserActivityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostUserActivityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostUserActivityRequestValidationError{}

// Validate checks the field values on PostUserActivityResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostUserActivityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostUserActivityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostUserActivityResponseMultiError, or nil if none found.
func (m *PostUserActivityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PostUserActivityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostUserActivityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostUserActivityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostUserActivityResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostUserActivityResponseMultiError(errors)
	}

	return nil
}

// PostUserActivityResponseMultiError is an error wrapping multiple validation
// errors returned by PostUserActivityResponse.ValidateAll() if the designated
// constraints aren't met.
type PostUserActivityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostUserActivityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostUserActivityResponseMultiError) AllErrors() []error { return m }

// PostUserActivityResponseValidationError is the validation error returned by
// PostUserActivityResponse.Validate if the designated constraints aren't met.
type PostUserActivityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostUserActivityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostUserActivityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostUserActivityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostUserActivityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostUserActivityResponseValidationError) ErrorName() string {
	return "PostUserActivityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PostUserActivityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostUserActivityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostUserActivityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostUserActivityResponseValidationError{}

// Validate checks the field values on ValidateSecurePinRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateSecurePinRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateSecurePinRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateSecurePinRequestMultiError, or nil if none found.
func (m *ValidateSecurePinRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateSecurePinRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetAccountId()); l < 4 || l > 100 {
		err := ValidateSecurePinRequestValidationError{
			field:  "AccountId",
			reason: "value length must be between 4 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateSecurePinRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateSecurePinRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateSecurePinRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNpciCredBlock()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateSecurePinRequestValidationError{
					field:  "NpciCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateSecurePinRequestValidationError{
					field:  "NpciCredBlock",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNpciCredBlock()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateSecurePinRequestValidationError{
				field:  "NpciCredBlock",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetTxnId()) < 1 {
		err := ValidateSecurePinRequestValidationError{
			field:  "TxnId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ValidateSecurePinRequestMultiError(errors)
	}

	return nil
}

// ValidateSecurePinRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateSecurePinRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateSecurePinRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateSecurePinRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateSecurePinRequestMultiError) AllErrors() []error { return m }

// ValidateSecurePinRequestValidationError is the validation error returned by
// ValidateSecurePinRequest.Validate if the designated constraints aren't met.
type ValidateSecurePinRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateSecurePinRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateSecurePinRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateSecurePinRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateSecurePinRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateSecurePinRequestValidationError) ErrorName() string {
	return "ValidateSecurePinRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateSecurePinRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateSecurePinRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateSecurePinRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateSecurePinRequestValidationError{}

// Validate checks the field values on ValidateSecurePinResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateSecurePinResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateSecurePinResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateSecurePinResponseMultiError, or nil if none found.
func (m *ValidateSecurePinResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateSecurePinResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateSecurePinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateSecurePinResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateSecurePinResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateSecurePinResponseMultiError(errors)
	}

	return nil
}

// ValidateSecurePinResponseMultiError is an error wrapping multiple validation
// errors returned by ValidateSecurePinResponse.ValidateAll() if the
// designated constraints aren't met.
type ValidateSecurePinResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateSecurePinResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateSecurePinResponseMultiError) AllErrors() []error { return m }

// ValidateSecurePinResponseValidationError is the validation error returned by
// ValidateSecurePinResponse.Validate if the designated constraints aren't met.
type ValidateSecurePinResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateSecurePinResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateSecurePinResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateSecurePinResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateSecurePinResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateSecurePinResponseValidationError) ErrorName() string {
	return "ValidateSecurePinResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateSecurePinResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateSecurePinResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateSecurePinResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateSecurePinResponseValidationError{}

// Validate checks the field values on RaiseComplaintRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RaiseComplaintRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaiseComplaintRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RaiseComplaintRequestMultiError, or nil if none found.
func (m *RaiseComplaintRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RaiseComplaintRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	// no validation rules for CurrentActorId

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetComplaint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseComplaintRequestValidationError{
					field:  "Complaint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseComplaintRequestValidationError{
					field:  "Complaint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComplaint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseComplaintRequestValidationError{
				field:  "Complaint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IntiationMode

	if len(errors) > 0 {
		return RaiseComplaintRequestMultiError(errors)
	}

	return nil
}

// RaiseComplaintRequestMultiError is an error wrapping multiple validation
// errors returned by RaiseComplaintRequest.ValidateAll() if the designated
// constraints aren't met.
type RaiseComplaintRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaiseComplaintRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaiseComplaintRequestMultiError) AllErrors() []error { return m }

// RaiseComplaintRequestValidationError is the validation error returned by
// RaiseComplaintRequest.Validate if the designated constraints aren't met.
type RaiseComplaintRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaiseComplaintRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaiseComplaintRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaiseComplaintRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaiseComplaintRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaiseComplaintRequestValidationError) ErrorName() string {
	return "RaiseComplaintRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RaiseComplaintRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaiseComplaintRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaiseComplaintRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaiseComplaintRequestValidationError{}

// Validate checks the field values on RaiseComplaintResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RaiseComplaintResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaiseComplaintResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RaiseComplaintResponseMultiError, or nil if none found.
func (m *RaiseComplaintResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RaiseComplaintResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RaiseComplaintResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RaiseComplaintResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RaiseComplaintResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerRefNumber

	for idx, item := range m.GetRefs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RaiseComplaintResponseValidationError{
						field:  fmt.Sprintf("Refs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RaiseComplaintResponseValidationError{
						field:  fmt.Sprintf("Refs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RaiseComplaintResponseValidationError{
					field:  fmt.Sprintf("Refs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ComplaintDisputeState

	if len(errors) > 0 {
		return RaiseComplaintResponseMultiError(errors)
	}

	return nil
}

// RaiseComplaintResponseMultiError is an error wrapping multiple validation
// errors returned by RaiseComplaintResponse.ValidateAll() if the designated
// constraints aren't met.
type RaiseComplaintResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaiseComplaintResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaiseComplaintResponseMultiError) AllErrors() []error { return m }

// RaiseComplaintResponseValidationError is the validation error returned by
// RaiseComplaintResponse.Validate if the designated constraints aren't met.
type RaiseComplaintResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaiseComplaintResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaiseComplaintResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaiseComplaintResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaiseComplaintResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaiseComplaintResponseValidationError) ErrorName() string {
	return "RaiseComplaintResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RaiseComplaintResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaiseComplaintResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaiseComplaintResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaiseComplaintResponseValidationError{}

// Validate checks the field values on CheckComplaintStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckComplaintStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckComplaintStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckComplaintStatusRequestMultiError, or nil if none found.
func (m *CheckComplaintStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckComplaintStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	if len(errors) > 0 {
		return CheckComplaintStatusRequestMultiError(errors)
	}

	return nil
}

// CheckComplaintStatusRequestMultiError is an error wrapping multiple
// validation errors returned by CheckComplaintStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckComplaintStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckComplaintStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckComplaintStatusRequestMultiError) AllErrors() []error { return m }

// CheckComplaintStatusRequestValidationError is the validation error returned
// by CheckComplaintStatusRequest.Validate if the designated constraints
// aren't met.
type CheckComplaintStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckComplaintStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckComplaintStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckComplaintStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckComplaintStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckComplaintStatusRequestValidationError) ErrorName() string {
	return "CheckComplaintStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckComplaintStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckComplaintStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckComplaintStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckComplaintStatusRequestValidationError{}

// Validate checks the field values on CheckComplaintStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckComplaintStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckComplaintStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckComplaintStatusResponseMultiError, or nil if none found.
func (m *CheckComplaintStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckComplaintStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckComplaintStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckComplaintStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckComplaintStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRef() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckComplaintStatusResponseValidationError{
						field:  fmt.Sprintf("Ref[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckComplaintStatusResponseValidationError{
						field:  fmt.Sprintf("Ref[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckComplaintStatusResponseValidationError{
					field:  fmt.Sprintf("Ref[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ComplaintDisputeState

	if len(errors) > 0 {
		return CheckComplaintStatusResponseMultiError(errors)
	}

	return nil
}

// CheckComplaintStatusResponseMultiError is an error wrapping multiple
// validation errors returned by CheckComplaintStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckComplaintStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckComplaintStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckComplaintStatusResponseMultiError) AllErrors() []error { return m }

// CheckComplaintStatusResponseValidationError is the validation error returned
// by CheckComplaintStatusResponse.Validate if the designated constraints
// aren't met.
type CheckComplaintStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckComplaintStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckComplaintStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckComplaintStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckComplaintStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckComplaintStatusResponseValidationError) ErrorName() string {
	return "CheckComplaintStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckComplaintStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckComplaintStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckComplaintStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckComplaintStatusResponseValidationError{}

// Validate checks the field values on ChangeUpiPinSetStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeUpiPinSetStateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeUpiPinSetStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangeUpiPinSetStateRequestMultiError, or nil if none found.
func (m *ChangeUpiPinSetStateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeUpiPinSetStateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	// no validation rules for PinSetState

	if len(errors) > 0 {
		return ChangeUpiPinSetStateRequestMultiError(errors)
	}

	return nil
}

// ChangeUpiPinSetStateRequestMultiError is an error wrapping multiple
// validation errors returned by ChangeUpiPinSetStateRequest.ValidateAll() if
// the designated constraints aren't met.
type ChangeUpiPinSetStateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeUpiPinSetStateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeUpiPinSetStateRequestMultiError) AllErrors() []error { return m }

// ChangeUpiPinSetStateRequestValidationError is the validation error returned
// by ChangeUpiPinSetStateRequest.Validate if the designated constraints
// aren't met.
type ChangeUpiPinSetStateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeUpiPinSetStateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeUpiPinSetStateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeUpiPinSetStateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeUpiPinSetStateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeUpiPinSetStateRequestValidationError) ErrorName() string {
	return "ChangeUpiPinSetStateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeUpiPinSetStateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeUpiPinSetStateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeUpiPinSetStateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeUpiPinSetStateRequestValidationError{}

// Validate checks the field values on ChangeUpiPinSetStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeUpiPinSetStateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeUpiPinSetStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangeUpiPinSetStateResponseMultiError, or nil if none found.
func (m *ChangeUpiPinSetStateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeUpiPinSetStateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChangeUpiPinSetStateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChangeUpiPinSetStateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChangeUpiPinSetStateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ChangeUpiPinSetStateResponseMultiError(errors)
	}

	return nil
}

// ChangeUpiPinSetStateResponseMultiError is an error wrapping multiple
// validation errors returned by ChangeUpiPinSetStateResponse.ValidateAll() if
// the designated constraints aren't met.
type ChangeUpiPinSetStateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeUpiPinSetStateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeUpiPinSetStateResponseMultiError) AllErrors() []error { return m }

// ChangeUpiPinSetStateResponseValidationError is the validation error returned
// by ChangeUpiPinSetStateResponse.Validate if the designated constraints
// aren't met.
type ChangeUpiPinSetStateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeUpiPinSetStateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeUpiPinSetStateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeUpiPinSetStateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeUpiPinSetStateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeUpiPinSetStateResponseValidationError) ErrorName() string {
	return "ChangeUpiPinSetStateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeUpiPinSetStateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeUpiPinSetStateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeUpiPinSetStateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeUpiPinSetStateResponseValidationError{}

// Validate checks the field values on GetVerifiedVpasByPhoneNumberRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetVerifiedVpasByPhoneNumberRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVerifiedVpasByPhoneNumberRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetVerifiedVpasByPhoneNumberRequestMultiError, or nil if none found.
func (m *GetVerifiedVpasByPhoneNumberRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVerifiedVpasByPhoneNumberRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDevice() == nil {
		err := GetVerifiedVpasByPhoneNumberRequestValidationError{
			field:  "Device",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVerifiedVpasByPhoneNumberRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVerifiedVpasByPhoneNumberRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVerifiedVpasByPhoneNumberRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVerifiedVpasByPhoneNumberRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVerifiedVpasByPhoneNumberRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVerifiedVpasByPhoneNumberRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayerActorId

	if len(errors) > 0 {
		return GetVerifiedVpasByPhoneNumberRequestMultiError(errors)
	}

	return nil
}

// GetVerifiedVpasByPhoneNumberRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetVerifiedVpasByPhoneNumberRequest.ValidateAll() if the designated
// constraints aren't met.
type GetVerifiedVpasByPhoneNumberRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVerifiedVpasByPhoneNumberRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVerifiedVpasByPhoneNumberRequestMultiError) AllErrors() []error { return m }

// GetVerifiedVpasByPhoneNumberRequestValidationError is the validation error
// returned by GetVerifiedVpasByPhoneNumberRequest.Validate if the designated
// constraints aren't met.
type GetVerifiedVpasByPhoneNumberRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVerifiedVpasByPhoneNumberRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVerifiedVpasByPhoneNumberRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVerifiedVpasByPhoneNumberRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVerifiedVpasByPhoneNumberRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVerifiedVpasByPhoneNumberRequestValidationError) ErrorName() string {
	return "GetVerifiedVpasByPhoneNumberRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetVerifiedVpasByPhoneNumberRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVerifiedVpasByPhoneNumberRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVerifiedVpasByPhoneNumberRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVerifiedVpasByPhoneNumberRequestValidationError{}

// Validate checks the field values on GetVerifiedVpasByPhoneNumberResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetVerifiedVpasByPhoneNumberResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVerifiedVpasByPhoneNumberResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetVerifiedVpasByPhoneNumberResponseMultiError, or nil if none found.
func (m *GetVerifiedVpasByPhoneNumberResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVerifiedVpasByPhoneNumberResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetVerifiedVpasByPhoneNumberResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetVerifiedVpasByPhoneNumberResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetVerifiedVpasByPhoneNumberResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVpaInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetVerifiedVpasByPhoneNumberResponseValidationError{
						field:  fmt.Sprintf("VpaInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetVerifiedVpasByPhoneNumberResponseValidationError{
						field:  fmt.Sprintf("VpaInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetVerifiedVpasByPhoneNumberResponseValidationError{
					field:  fmt.Sprintf("VpaInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetVerifiedVpasByPhoneNumberResponseMultiError(errors)
	}

	return nil
}

// GetVerifiedVpasByPhoneNumberResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetVerifiedVpasByPhoneNumberResponse.ValidateAll() if the designated
// constraints aren't met.
type GetVerifiedVpasByPhoneNumberResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVerifiedVpasByPhoneNumberResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVerifiedVpasByPhoneNumberResponseMultiError) AllErrors() []error { return m }

// GetVerifiedVpasByPhoneNumberResponseValidationError is the validation error
// returned by GetVerifiedVpasByPhoneNumberResponse.Validate if the designated
// constraints aren't met.
type GetVerifiedVpasByPhoneNumberResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVerifiedVpasByPhoneNumberResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVerifiedVpasByPhoneNumberResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVerifiedVpasByPhoneNumberResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVerifiedVpasByPhoneNumberResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVerifiedVpasByPhoneNumberResponseValidationError) ErrorName() string {
	return "GetVerifiedVpasByPhoneNumberResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetVerifiedVpasByPhoneNumberResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVerifiedVpasByPhoneNumberResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVerifiedVpasByPhoneNumberResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVerifiedVpasByPhoneNumberResponseValidationError{}

// Validate checks the field values on ValidateAddressAndCreatePiRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ValidateAddressAndCreatePiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateAddressAndCreatePiRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ValidateAddressAndCreatePiRequestMultiError, or nil if none found.
func (m *ValidateAddressAndCreatePiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateAddressAndCreatePiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAddressAndCreatePiRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAddressAndCreatePiRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAddressAndCreatePiRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpiVpa

	// no validation rules for PayerActorId

	// no validation rules for Ownership

	// no validation rules for VpaValidationMode

	if len(errors) > 0 {
		return ValidateAddressAndCreatePiRequestMultiError(errors)
	}

	return nil
}

// ValidateAddressAndCreatePiRequestMultiError is an error wrapping multiple
// validation errors returned by
// ValidateAddressAndCreatePiRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateAddressAndCreatePiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateAddressAndCreatePiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateAddressAndCreatePiRequestMultiError) AllErrors() []error { return m }

// ValidateAddressAndCreatePiRequestValidationError is the validation error
// returned by ValidateAddressAndCreatePiRequest.Validate if the designated
// constraints aren't met.
type ValidateAddressAndCreatePiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateAddressAndCreatePiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateAddressAndCreatePiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateAddressAndCreatePiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateAddressAndCreatePiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateAddressAndCreatePiRequestValidationError) ErrorName() string {
	return "ValidateAddressAndCreatePiRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateAddressAndCreatePiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateAddressAndCreatePiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateAddressAndCreatePiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateAddressAndCreatePiRequestValidationError{}

// Validate checks the field values on ValidateAddressAndCreatePiResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ValidateAddressAndCreatePiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateAddressAndCreatePiResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ValidateAddressAndCreatePiResponseMultiError, or nil if none found.
func (m *ValidateAddressAndCreatePiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateAddressAndCreatePiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAddressAndCreatePiResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAddressAndCreatePiResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAddressAndCreatePiResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerName

	// no validation rules for Mcc

	if all {
		switch v := interface{}(m.GetMerchant()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAddressAndCreatePiResponseValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAddressAndCreatePiResponseValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchant()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAddressAndCreatePiResponseValidationError{
				field:  "Merchant",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PiId

	if len(errors) > 0 {
		return ValidateAddressAndCreatePiResponseMultiError(errors)
	}

	return nil
}

// ValidateAddressAndCreatePiResponseMultiError is an error wrapping multiple
// validation errors returned by
// ValidateAddressAndCreatePiResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateAddressAndCreatePiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateAddressAndCreatePiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateAddressAndCreatePiResponseMultiError) AllErrors() []error { return m }

// ValidateAddressAndCreatePiResponseValidationError is the validation error
// returned by ValidateAddressAndCreatePiResponse.Validate if the designated
// constraints aren't met.
type ValidateAddressAndCreatePiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateAddressAndCreatePiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateAddressAndCreatePiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateAddressAndCreatePiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateAddressAndCreatePiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateAddressAndCreatePiResponseValidationError) ErrorName() string {
	return "ValidateAddressAndCreatePiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateAddressAndCreatePiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateAddressAndCreatePiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateAddressAndCreatePiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateAddressAndCreatePiResponseValidationError{}

// Validate checks the field values on ValidateUpiNumberAndCreatePiRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ValidateUpiNumberAndCreatePiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateUpiNumberAndCreatePiRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ValidateUpiNumberAndCreatePiRequestMultiError, or nil if none found.
func (m *ValidateUpiNumberAndCreatePiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateUpiNumberAndCreatePiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDevice() == nil {
		err := ValidateUpiNumberAndCreatePiRequestValidationError{
			field:  "Device",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateUpiNumberAndCreatePiRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateUpiNumberAndCreatePiRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateUpiNumberAndCreatePiRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpiNumber

	// no validation rules for PayerActorId

	// no validation rules for Ownership

	if len(errors) > 0 {
		return ValidateUpiNumberAndCreatePiRequestMultiError(errors)
	}

	return nil
}

// ValidateUpiNumberAndCreatePiRequestMultiError is an error wrapping multiple
// validation errors returned by
// ValidateUpiNumberAndCreatePiRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateUpiNumberAndCreatePiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateUpiNumberAndCreatePiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateUpiNumberAndCreatePiRequestMultiError) AllErrors() []error { return m }

// ValidateUpiNumberAndCreatePiRequestValidationError is the validation error
// returned by ValidateUpiNumberAndCreatePiRequest.Validate if the designated
// constraints aren't met.
type ValidateUpiNumberAndCreatePiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateUpiNumberAndCreatePiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateUpiNumberAndCreatePiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateUpiNumberAndCreatePiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateUpiNumberAndCreatePiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateUpiNumberAndCreatePiRequestValidationError) ErrorName() string {
	return "ValidateUpiNumberAndCreatePiRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateUpiNumberAndCreatePiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateUpiNumberAndCreatePiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateUpiNumberAndCreatePiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateUpiNumberAndCreatePiRequestValidationError{}

// Validate checks the field values on ValidateUpiNumberAndCreatePiResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ValidateUpiNumberAndCreatePiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateUpiNumberAndCreatePiResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ValidateUpiNumberAndCreatePiResponseMultiError, or nil if none found.
func (m *ValidateUpiNumberAndCreatePiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateUpiNumberAndCreatePiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateUpiNumberAndCreatePiResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateUpiNumberAndCreatePiResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateUpiNumberAndCreatePiResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerName

	// no validation rules for Mcc

	if all {
		switch v := interface{}(m.GetMerchant()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateUpiNumberAndCreatePiResponseValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateUpiNumberAndCreatePiResponseValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchant()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateUpiNumberAndCreatePiResponseValidationError{
				field:  "Merchant",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PiId

	// no validation rules for Vpa

	if all {
		switch v := interface{}(m.GetPspBadgeIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateUpiNumberAndCreatePiResponseValidationError{
					field:  "PspBadgeIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateUpiNumberAndCreatePiResponseValidationError{
					field:  "PspBadgeIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPspBadgeIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateUpiNumberAndCreatePiResponseValidationError{
				field:  "PspBadgeIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateUpiNumberAndCreatePiResponseMultiError(errors)
	}

	return nil
}

// ValidateUpiNumberAndCreatePiResponseMultiError is an error wrapping multiple
// validation errors returned by
// ValidateUpiNumberAndCreatePiResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateUpiNumberAndCreatePiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateUpiNumberAndCreatePiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateUpiNumberAndCreatePiResponseMultiError) AllErrors() []error { return m }

// ValidateUpiNumberAndCreatePiResponseValidationError is the validation error
// returned by ValidateUpiNumberAndCreatePiResponse.Validate if the designated
// constraints aren't met.
type ValidateUpiNumberAndCreatePiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateUpiNumberAndCreatePiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateUpiNumberAndCreatePiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateUpiNumberAndCreatePiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateUpiNumberAndCreatePiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateUpiNumberAndCreatePiResponseValidationError) ErrorName() string {
	return "ValidateUpiNumberAndCreatePiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateUpiNumberAndCreatePiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateUpiNumberAndCreatePiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateUpiNumberAndCreatePiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateUpiNumberAndCreatePiResponseValidationError{}

// Validate checks the field values on ValidateInternationalPaymentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ValidateInternationalPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateInternationalPaymentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ValidateInternationalPaymentRequestMultiError, or nil if none found.
func (m *ValidateInternationalPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateInternationalPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetBaseAmountQuoteCurrency()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateInternationalPaymentRequestValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateInternationalPaymentRequestValidationError{
					field:  "BaseAmountQuoteCurrency",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseAmountQuoteCurrency()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateInternationalPaymentRequestValidationError{
				field:  "BaseAmountQuoteCurrency",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalAmountInr()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateInternationalPaymentRequestValidationError{
					field:  "TotalAmountInr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateInternationalPaymentRequestValidationError{
					field:  "TotalAmountInr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAmountInr()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateInternationalPaymentRequestValidationError{
				field:  "TotalAmountInr",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateInternationalPaymentRequestMultiError(errors)
	}

	return nil
}

// ValidateInternationalPaymentRequestMultiError is an error wrapping multiple
// validation errors returned by
// ValidateInternationalPaymentRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateInternationalPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateInternationalPaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateInternationalPaymentRequestMultiError) AllErrors() []error { return m }

// ValidateInternationalPaymentRequestValidationError is the validation error
// returned by ValidateInternationalPaymentRequest.Validate if the designated
// constraints aren't met.
type ValidateInternationalPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateInternationalPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateInternationalPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateInternationalPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateInternationalPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateInternationalPaymentRequestValidationError) ErrorName() string {
	return "ValidateInternationalPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateInternationalPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateInternationalPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateInternationalPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateInternationalPaymentRequestValidationError{}

// Validate checks the field values on ValidateInternationalPaymentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ValidateInternationalPaymentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateInternationalPaymentResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ValidateInternationalPaymentResponseMultiError, or nil if none found.
func (m *ValidateInternationalPaymentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateInternationalPaymentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateInternationalPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateInternationalPaymentResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateInternationalPaymentResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUpiInternationalPaymentCharges() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ValidateInternationalPaymentResponseValidationError{
						field:  fmt.Sprintf("UpiInternationalPaymentCharges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ValidateInternationalPaymentResponseValidationError{
						field:  fmt.Sprintf("UpiInternationalPaymentCharges[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ValidateInternationalPaymentResponseValidationError{
					field:  fmt.Sprintf("UpiInternationalPaymentCharges[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ValidateInternationalPaymentResponseMultiError(errors)
	}

	return nil
}

// ValidateInternationalPaymentResponseMultiError is an error wrapping multiple
// validation errors returned by
// ValidateInternationalPaymentResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateInternationalPaymentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateInternationalPaymentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateInternationalPaymentResponseMultiError) AllErrors() []error { return m }

// ValidateInternationalPaymentResponseValidationError is the validation error
// returned by ValidateInternationalPaymentResponse.Validate if the designated
// constraints aren't met.
type ValidateInternationalPaymentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateInternationalPaymentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateInternationalPaymentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateInternationalPaymentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateInternationalPaymentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateInternationalPaymentResponseValidationError) ErrorName() string {
	return "ValidateInternationalPaymentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateInternationalPaymentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateInternationalPaymentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateInternationalPaymentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateInternationalPaymentResponseValidationError{}

// Validate checks the field values on
// GetUpiInternationalQrInfoFromCacheRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUpiInternationalQrInfoFromCacheRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUpiInternationalQrInfoFromCacheRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetUpiInternationalQrInfoFromCacheRequestMultiError, or nil if none found.
func (m *GetUpiInternationalQrInfoFromCacheRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUpiInternationalQrInfoFromCacheRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetUpiInternationalQrInfoFromCacheRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetUpiInternationalQrInfoFromCacheRequestMultiError(errors)
	}

	return nil
}

// GetUpiInternationalQrInfoFromCacheRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetUpiInternationalQrInfoFromCacheRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUpiInternationalQrInfoFromCacheRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUpiInternationalQrInfoFromCacheRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUpiInternationalQrInfoFromCacheRequestMultiError) AllErrors() []error { return m }

// GetUpiInternationalQrInfoFromCacheRequestValidationError is the validation
// error returned by GetUpiInternationalQrInfoFromCacheRequest.Validate if the
// designated constraints aren't met.
type GetUpiInternationalQrInfoFromCacheRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUpiInternationalQrInfoFromCacheRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUpiInternationalQrInfoFromCacheRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUpiInternationalQrInfoFromCacheRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUpiInternationalQrInfoFromCacheRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUpiInternationalQrInfoFromCacheRequestValidationError) ErrorName() string {
	return "GetUpiInternationalQrInfoFromCacheRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUpiInternationalQrInfoFromCacheRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUpiInternationalQrInfoFromCacheRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUpiInternationalQrInfoFromCacheRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUpiInternationalQrInfoFromCacheRequestValidationError{}

// Validate checks the field values on
// GetUpiInternationalQrInfoFromCacheResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUpiInternationalQrInfoFromCacheResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUpiInternationalQrInfoFromCacheResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetUpiInternationalQrInfoFromCacheResponseMultiError, or nil if none found.
func (m *GetUpiInternationalQrInfoFromCacheResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUpiInternationalQrInfoFromCacheResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUpiInternationalQrInfoFromCacheResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUpiInternationalQrInfoFromCacheResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUpiInternationalQrInfoFromCacheResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpiInternationalQrInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUpiInternationalQrInfoFromCacheResponseValidationError{
					field:  "UpiInternationalQrInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUpiInternationalQrInfoFromCacheResponseValidationError{
					field:  "UpiInternationalQrInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpiInternationalQrInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUpiInternationalQrInfoFromCacheResponseValidationError{
				field:  "UpiInternationalQrInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUpiInternationalQrInfoFromCacheResponseMultiError(errors)
	}

	return nil
}

// GetUpiInternationalQrInfoFromCacheResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetUpiInternationalQrInfoFromCacheResponse.ValidateAll() if the designated
// constraints aren't met.
type GetUpiInternationalQrInfoFromCacheResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUpiInternationalQrInfoFromCacheResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUpiInternationalQrInfoFromCacheResponseMultiError) AllErrors() []error { return m }

// GetUpiInternationalQrInfoFromCacheResponseValidationError is the validation
// error returned by GetUpiInternationalQrInfoFromCacheResponse.Validate if
// the designated constraints aren't met.
type GetUpiInternationalQrInfoFromCacheResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUpiInternationalQrInfoFromCacheResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUpiInternationalQrInfoFromCacheResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUpiInternationalQrInfoFromCacheResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUpiInternationalQrInfoFromCacheResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUpiInternationalQrInfoFromCacheResponseValidationError) ErrorName() string {
	return "GetUpiInternationalQrInfoFromCacheResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUpiInternationalQrInfoFromCacheResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUpiInternationalQrInfoFromCacheResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUpiInternationalQrInfoFromCacheResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUpiInternationalQrInfoFromCacheResponseValidationError{}

// Validate checks the field values on GetExternalVpasRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExternalVpasRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExternalVpasRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExternalVpasRequestMultiError, or nil if none found.
func (m *GetExternalVpasRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExternalVpasRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPrimaryActorId()) < 1 {
		err := GetExternalVpasRequestValidationError{
			field:  "PrimaryActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Identifier.(type) {
	case *GetExternalVpasRequest_ActorId:
		if v == nil {
			err := GetExternalVpasRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	case *GetExternalVpasRequest_Email:
		if v == nil {
			err := GetExternalVpasRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Email
	case *GetExternalVpasRequest_PhoneNumber:
		if v == nil {
			err := GetExternalVpasRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPhoneNumber()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExternalVpasRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExternalVpasRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExternalVpasRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetExternalVpasRequestMultiError(errors)
	}

	return nil
}

// GetExternalVpasRequestMultiError is an error wrapping multiple validation
// errors returned by GetExternalVpasRequest.ValidateAll() if the designated
// constraints aren't met.
type GetExternalVpasRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExternalVpasRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExternalVpasRequestMultiError) AllErrors() []error { return m }

// GetExternalVpasRequestValidationError is the validation error returned by
// GetExternalVpasRequest.Validate if the designated constraints aren't met.
type GetExternalVpasRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExternalVpasRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExternalVpasRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExternalVpasRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExternalVpasRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExternalVpasRequestValidationError) ErrorName() string {
	return "GetExternalVpasRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExternalVpasRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExternalVpasRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExternalVpasRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExternalVpasRequestValidationError{}

// Validate checks the field values on GetExternalVpasResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExternalVpasResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExternalVpasResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExternalVpasResponseMultiError, or nil if none found.
func (m *GetExternalVpasResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExternalVpasResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetExternalVpasResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetExternalVpasResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetExternalVpasResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVpaInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExternalVpasResponseValidationError{
						field:  fmt.Sprintf("VpaInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExternalVpasResponseValidationError{
						field:  fmt.Sprintf("VpaInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExternalVpasResponseValidationError{
					field:  fmt.Sprintf("VpaInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetExternalVpasResponseMultiError(errors)
	}

	return nil
}

// GetExternalVpasResponseMultiError is an error wrapping multiple validation
// errors returned by GetExternalVpasResponse.ValidateAll() if the designated
// constraints aren't met.
type GetExternalVpasResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExternalVpasResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExternalVpasResponseMultiError) AllErrors() []error { return m }

// GetExternalVpasResponseValidationError is the validation error returned by
// GetExternalVpasResponse.Validate if the designated constraints aren't met.
type GetExternalVpasResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExternalVpasResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExternalVpasResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExternalVpasResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExternalVpasResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExternalVpasResponseValidationError) ErrorName() string {
	return "GetExternalVpasResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetExternalVpasResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExternalVpasResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExternalVpasResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExternalVpasResponseValidationError{}

// Validate checks the field values on GetNPCICLParametersV1Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNPCICLParametersV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNPCICLParametersV1Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNPCICLParametersV1RequestMultiError, or nil if none found.
func (m *GetNPCICLParametersV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNPCICLParametersV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetNPCICLParametersV1RequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetAccountId()) < 1 {
		err := GetNPCICLParametersV1RequestValidationError{
			field:  "AccountId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _GetNPCICLParametersV1Request_UpiFlowType_NotInLookup[m.GetUpiFlowType()]; ok {
		err := GetNPCICLParametersV1RequestValidationError{
			field:  "UpiFlowType",
			reason: "value must not be in list [UPI_FLOW_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UpiPinSetOptionType

	if len(errors) > 0 {
		return GetNPCICLParametersV1RequestMultiError(errors)
	}

	return nil
}

// GetNPCICLParametersV1RequestMultiError is an error wrapping multiple
// validation errors returned by GetNPCICLParametersV1Request.ValidateAll() if
// the designated constraints aren't met.
type GetNPCICLParametersV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNPCICLParametersV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNPCICLParametersV1RequestMultiError) AllErrors() []error { return m }

// GetNPCICLParametersV1RequestValidationError is the validation error returned
// by GetNPCICLParametersV1Request.Validate if the designated constraints
// aren't met.
type GetNPCICLParametersV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNPCICLParametersV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNPCICLParametersV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNPCICLParametersV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNPCICLParametersV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNPCICLParametersV1RequestValidationError) ErrorName() string {
	return "GetNPCICLParametersV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNPCICLParametersV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNPCICLParametersV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNPCICLParametersV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNPCICLParametersV1RequestValidationError{}

var _GetNPCICLParametersV1Request_UpiFlowType_NotInLookup = map[UpiFlowType]struct{}{
	0: {},
}

// Validate checks the field values on GetNPCICLParametersV1Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNPCICLParametersV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNPCICLParametersV1Response with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetNPCICLParametersV1ResponseMultiError, or nil if none found.
func (m *GetNPCICLParametersV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNPCICLParametersV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNPCICLParametersV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNPCICLParametersV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNPCICLParametersV1ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.NPCICLParams.(type) {
	case *GetNPCICLParametersV1Response_UpiLiteTransactionParams:
		if v == nil {
			err := GetNPCICLParametersV1ResponseValidationError{
				field:  "NPCICLParams",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpiLiteTransactionParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNPCICLParametersV1ResponseValidationError{
						field:  "UpiLiteTransactionParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNPCICLParametersV1ResponseValidationError{
						field:  "UpiLiteTransactionParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpiLiteTransactionParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNPCICLParametersV1ResponseValidationError{
					field:  "UpiLiteTransactionParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetNPCICLParametersV1Response_UpiLiteBalanceParams:
		if v == nil {
			err := GetNPCICLParametersV1ResponseValidationError{
				field:  "NPCICLParams",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpiLiteBalanceParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetNPCICLParametersV1ResponseValidationError{
						field:  "UpiLiteBalanceParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetNPCICLParametersV1ResponseValidationError{
						field:  "UpiLiteBalanceParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpiLiteBalanceParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetNPCICLParametersV1ResponseValidationError{
					field:  "UpiLiteBalanceParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetNPCICLParametersV1ResponseMultiError(errors)
	}

	return nil
}

// GetNPCICLParametersV1ResponseMultiError is an error wrapping multiple
// validation errors returned by GetNPCICLParametersV1Response.ValidateAll()
// if the designated constraints aren't met.
type GetNPCICLParametersV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNPCICLParametersV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNPCICLParametersV1ResponseMultiError) AllErrors() []error { return m }

// GetNPCICLParametersV1ResponseValidationError is the validation error
// returned by GetNPCICLParametersV1Response.Validate if the designated
// constraints aren't met.
type GetNPCICLParametersV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNPCICLParametersV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNPCICLParametersV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNPCICLParametersV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNPCICLParametersV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNPCICLParametersV1ResponseValidationError) ErrorName() string {
	return "GetNPCICLParametersV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNPCICLParametersV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNPCICLParametersV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNPCICLParametersV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNPCICLParametersV1ResponseValidationError{}

// Validate checks the field values on IsNewAddFundsVpaEnabledForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *IsNewAddFundsVpaEnabledForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// IsNewAddFundsVpaEnabledForActorRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// IsNewAddFundsVpaEnabledForActorRequestMultiError, or nil if none found.
func (m *IsNewAddFundsVpaEnabledForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsNewAddFundsVpaEnabledForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return IsNewAddFundsVpaEnabledForActorRequestMultiError(errors)
	}

	return nil
}

// IsNewAddFundsVpaEnabledForActorRequestMultiError is an error wrapping
// multiple validation errors returned by
// IsNewAddFundsVpaEnabledForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type IsNewAddFundsVpaEnabledForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsNewAddFundsVpaEnabledForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsNewAddFundsVpaEnabledForActorRequestMultiError) AllErrors() []error { return m }

// IsNewAddFundsVpaEnabledForActorRequestValidationError is the validation
// error returned by IsNewAddFundsVpaEnabledForActorRequest.Validate if the
// designated constraints aren't met.
type IsNewAddFundsVpaEnabledForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsNewAddFundsVpaEnabledForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsNewAddFundsVpaEnabledForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsNewAddFundsVpaEnabledForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsNewAddFundsVpaEnabledForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsNewAddFundsVpaEnabledForActorRequestValidationError) ErrorName() string {
	return "IsNewAddFundsVpaEnabledForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsNewAddFundsVpaEnabledForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsNewAddFundsVpaEnabledForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsNewAddFundsVpaEnabledForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsNewAddFundsVpaEnabledForActorRequestValidationError{}

// Validate checks the field values on IsNewAddFundsVpaEnabledForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *IsNewAddFundsVpaEnabledForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// IsNewAddFundsVpaEnabledForActorResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// IsNewAddFundsVpaEnabledForActorResponseMultiError, or nil if none found.
func (m *IsNewAddFundsVpaEnabledForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsNewAddFundsVpaEnabledForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsNewAddFundsVpaEnabledForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsNewAddFundsVpaEnabledForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsNewAddFundsVpaEnabledForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEnabled

	if len(errors) > 0 {
		return IsNewAddFundsVpaEnabledForActorResponseMultiError(errors)
	}

	return nil
}

// IsNewAddFundsVpaEnabledForActorResponseMultiError is an error wrapping
// multiple validation errors returned by
// IsNewAddFundsVpaEnabledForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type IsNewAddFundsVpaEnabledForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsNewAddFundsVpaEnabledForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsNewAddFundsVpaEnabledForActorResponseMultiError) AllErrors() []error { return m }

// IsNewAddFundsVpaEnabledForActorResponseValidationError is the validation
// error returned by IsNewAddFundsVpaEnabledForActorResponse.Validate if the
// designated constraints aren't met.
type IsNewAddFundsVpaEnabledForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsNewAddFundsVpaEnabledForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsNewAddFundsVpaEnabledForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsNewAddFundsVpaEnabledForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsNewAddFundsVpaEnabledForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsNewAddFundsVpaEnabledForActorResponseValidationError) ErrorName() string {
	return "IsNewAddFundsVpaEnabledForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsNewAddFundsVpaEnabledForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsNewAddFundsVpaEnabledForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsNewAddFundsVpaEnabledForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsNewAddFundsVpaEnabledForActorResponseValidationError{}

// Validate checks the field values on CheckTxnStatusResponse_Ref with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckTxnStatusResponse_Ref) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckTxnStatusResponse_Ref with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckTxnStatusResponse_RefMultiError, or nil if none found.
func (m *CheckTxnStatusResponse_Ref) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckTxnStatusResponse_Ref) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for SeqNum

	// no validation rules for Vpa

	// no validation rules for SettAmount

	// no validation rules for OrgAmount

	// no validation rules for SettCurrency

	// no validation rules for ApprovalNum

	if all {
		switch v := interface{}(m.GetAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckTxnStatusResponse_RefValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckTxnStatusResponse_RefValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckTxnStatusResponse_RefValidationError{
				field:  "AccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RespCode

	// no validation rules for ReversalRespCode

	// no validation rules for Name

	if len(errors) > 0 {
		return CheckTxnStatusResponse_RefMultiError(errors)
	}

	return nil
}

// CheckTxnStatusResponse_RefMultiError is an error wrapping multiple
// validation errors returned by CheckTxnStatusResponse_Ref.ValidateAll() if
// the designated constraints aren't met.
type CheckTxnStatusResponse_RefMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckTxnStatusResponse_RefMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckTxnStatusResponse_RefMultiError) AllErrors() []error { return m }

// CheckTxnStatusResponse_RefValidationError is the validation error returned
// by CheckTxnStatusResponse_Ref.Validate if the designated constraints aren't met.
type CheckTxnStatusResponse_RefValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckTxnStatusResponse_RefValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckTxnStatusResponse_RefValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckTxnStatusResponse_RefValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckTxnStatusResponse_RefValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckTxnStatusResponse_RefValidationError) ErrorName() string {
	return "CheckTxnStatusResponse_RefValidationError"
}

// Error satisfies the builtin error interface
func (e CheckTxnStatusResponse_RefValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckTxnStatusResponse_Ref.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckTxnStatusResponse_RefValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckTxnStatusResponse_RefValidationError{}

// Validate checks the field values on GetPinFlowParametersResponse_BankConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPinFlowParametersResponse_BankConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPinFlowParametersResponse_BankConfig with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetPinFlowParametersResponse_BankConfigMultiError, or nil if none found.
func (m *GetPinFlowParametersResponse_BankConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPinFlowParametersResponse_BankConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayerBankName

	// no validation rules for BackgroundColor

	// no validation rules for Color

	// no validation rules for ResendOtpFeature

	if len(errors) > 0 {
		return GetPinFlowParametersResponse_BankConfigMultiError(errors)
	}

	return nil
}

// GetPinFlowParametersResponse_BankConfigMultiError is an error wrapping
// multiple validation errors returned by
// GetPinFlowParametersResponse_BankConfig.ValidateAll() if the designated
// constraints aren't met.
type GetPinFlowParametersResponse_BankConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPinFlowParametersResponse_BankConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPinFlowParametersResponse_BankConfigMultiError) AllErrors() []error { return m }

// GetPinFlowParametersResponse_BankConfigValidationError is the validation
// error returned by GetPinFlowParametersResponse_BankConfig.Validate if the
// designated constraints aren't met.
type GetPinFlowParametersResponse_BankConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPinFlowParametersResponse_BankConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPinFlowParametersResponse_BankConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPinFlowParametersResponse_BankConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPinFlowParametersResponse_BankConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPinFlowParametersResponse_BankConfigValidationError) ErrorName() string {
	return "GetPinFlowParametersResponse_BankConfigValidationError"
}

// Error satisfies the builtin error interface
func (e GetPinFlowParametersResponse_BankConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPinFlowParametersResponse_BankConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPinFlowParametersResponse_BankConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPinFlowParametersResponse_BankConfigValidationError{}

// Validate checks the field values on
// GetTransactionParametersResponse_BankConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetTransactionParametersResponse_BankConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTransactionParametersResponse_BankConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetTransactionParametersResponse_BankConfigMultiError, or nil if none found.
func (m *GetTransactionParametersResponse_BankConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionParametersResponse_BankConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayerBankName

	// no validation rules for BackgroundColor

	// no validation rules for Color

	// no validation rules for ResendOtpFeature

	if len(errors) > 0 {
		return GetTransactionParametersResponse_BankConfigMultiError(errors)
	}

	return nil
}

// GetTransactionParametersResponse_BankConfigMultiError is an error wrapping
// multiple validation errors returned by
// GetTransactionParametersResponse_BankConfig.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionParametersResponse_BankConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionParametersResponse_BankConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionParametersResponse_BankConfigMultiError) AllErrors() []error { return m }

// GetTransactionParametersResponse_BankConfigValidationError is the validation
// error returned by GetTransactionParametersResponse_BankConfig.Validate if
// the designated constraints aren't met.
type GetTransactionParametersResponse_BankConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionParametersResponse_BankConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionParametersResponse_BankConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionParametersResponse_BankConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionParametersResponse_BankConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionParametersResponse_BankConfigValidationError) ErrorName() string {
	return "GetTransactionParametersResponse_BankConfigValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionParametersResponse_BankConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionParametersResponse_BankConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionParametersResponse_BankConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionParametersResponse_BankConfigValidationError{}
