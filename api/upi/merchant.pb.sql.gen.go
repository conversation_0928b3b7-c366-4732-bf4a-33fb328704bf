// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/upi/merchant.pb.go

package upi

import (
	"database/sql/driver"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing RestrictedAccountTypeDetails while reading from DB
func (a *RestrictedAccountTypeDetails) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	unmarshalOptions := &protojson.UnmarshalOptions{}
	unmarshalOptions.DiscardUnknown = true
	return unmarshalOptions.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the RestrictedAccountTypeDetails in string format in DB
func (a *RestrictedAccountTypeDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for RestrictedAccountTypeDetails
func (a *RestrictedAccountTypeDetails) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for RestrictedAccountTypeDetails
func (a *RestrictedAccountTypeDetails) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
