// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/upi/consumer_service.proto

package upi

import (
	context "context"
	notification "github.com/epifi/gamma/api/auth/notification"
	onboarding "github.com/epifi/gamma/api/user/onboarding"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Consumer_ProcessReqAuth_FullMethodName                     = "/upi.Consumer/ProcessReqAuth"
	Consumer_ProcessResPay_FullMethodName                      = "/upi.Consumer/ProcessResPay"
	Consumer_ProcessReqTxnConfirmation_FullMethodName          = "/upi.Consumer/ProcessReqTxnConfirmation"
	Consumer_ProcessReqValAddress_FullMethodName               = "/upi.Consumer/ProcessReqValAddress"
	Consumer_ProcessListPspKeys_FullMethodName                 = "/upi.Consumer/ProcessListPspKeys"
	Consumer_ProcessListVae_FullMethodName                     = "/upi.Consumer/ProcessListVae"
	Consumer_ProcessOnboardingEvent_FullMethodName             = "/upi.Consumer/ProcessOnboardingEvent"
	Consumer_ProcessAuthFactorUpdateEvent_FullMethodName       = "/upi.Consumer/ProcessAuthFactorUpdateEvent"
	Consumer_ProcessReqTxnConfirmationComplaint_FullMethodName = "/upi.Consumer/ProcessReqTxnConfirmationComplaint"
	Consumer_ProcessRespComplaint_FullMethodName               = "/upi.Consumer/ProcessRespComplaint"
)

// ConsumerClient is the client API for Consumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Deprecated: Do not use.
type ConsumerClient interface {
	// ProcessReqAuth processes the `ReqAuthDetails` callback from NPCI.
	// We get ReqAuth from federal inorder to resolve the account reference number from VPA belonging to epiFi.
	//
	// For pay-
	// On receiving following actions are taken in sequence-
	//  1. check if a transaction exists for the txn-id (or txn reference id).
	//  2. In case yes then update the transaction state machine and send back `RespAuthDetails` after resolving account
	//     details.
	//  3. In case no then create order and transactions with the same reference id and send back `RespAuthDetails` after
	//     resolving account details (This will happen in case the payer is not epiFi's)
	//
	// For Collect (where payee is an external user)-
	//
	//  1. Create order and transaction and send out collect notification to the user app.
	//     `RespAuth` in this case is sent from the payment service once the user authorizes the payment with PIN.
	//
	//     This method is invoked by queue subscriber to consume order related queue packet.
	ProcessReqAuth(ctx context.Context, in *ProcessReqAuthRequest, opts ...grpc.CallOption) (*ProcessReqAuthResponse, error)
	// ProcessResPay processes the `RespPay` callback from NPCI.
	// RespPay is sent by NPCI in response to the `RespPay`. It contains transaction state information whether a transaction
	// succeeded or failed.
	// On receiving transaction state machine is updated accordingly.
	//
	// This method is invoked by queue subscriber to consume order related queue packet.
	ProcessResPay(ctx context.Context, in *ProcessResPayRequest, opts ...grpc.CallOption) (*ProcessResPayResponse, error)
	// ProcessReqTxnConfirmation processes the `ReqTxnConfirmation` event.
	// NPCI sends `ReqTxnConfirmation`  if epiFi is the payer for `COLLECT` request and payee for `Pay` request.
	// In both scenarios we need to update the transaction state machine and send back `RespTransactionConfirmation`.
	ProcessReqTxnConfirmation(ctx context.Context, in *ProcessReqTxnConfirmationRequest, opts ...grpc.CallOption) (*ProcessReqTxnConfirmationResponse, error)
	// ProcessReqValAddress processed the `ReqValAddress` event
	// NPCI sends `ReqValAddress` for epifi users to validate if a VPA is valid or not
	// Need to check if the upi is valid or not and return with a RespValidateAddress callback
	ProcessReqValAddress(ctx context.Context, in *ProcessReqValAddressRequest, opts ...grpc.CallOption) (*ProcessReqValAddressResponse, error)
	// ProcessListPspKeys process the ListPspKeys events.
	// NPCI sends `ListKeysResp` as a response to ListPspKeys request type.
	// These keys are used to validate QR and intent bases UPI payments.
	// ListPspKeys will be called once in a day.
	// Consumer can expect more than one callback response for a ListPspKeys request
	// This RPC will parse response and store it cache/storage
	ProcessListPspKeys(ctx context.Context, in *ProcessListPspKeysRequest, opts ...grpc.CallOption) (*ProcessListPspKeysResponse, error)
	// ProcessListVae processed the `RespListVae` event
	// NPCI sends `RespListVae` for well known verified merchants like IRCTC, LIC
	// ReqListVae will be called once in a day.
	// Consumer can expect more than one callback response for a ListVae request
	// This RPC will parse response and store it cache/storage
	ProcessListVae(ctx context.Context, in *ProcessListVaeRequest, opts ...grpc.CallOption) (*ProcessListVaeResponse, error)
	// RPC process request to trigger event post any specific onboarding stage
	// For example : We need to trigger an in-app notification when user lands on home screen post successful onboarding
	// that UPI outgoing payments won't work till 24 hours of account creation
	ProcessOnboardingEvent(ctx context.Context, in *onboarding.OnboardingStageUpdate, opts ...grpc.CallOption) (*ProcessOnboardingEventResponse, error)
	// RPC to process auth factor update event.
	// It will perform below operation:
	//  1. On update of user phone number associated with account,
	//     this RPC will change UPI pin set status to RE_OOBE_PIN_NOT_SET that will force user to reset UPI pin.
	//  2. It will send background app notification to reload user-session.
	ProcessAuthFactorUpdateEvent(ctx context.Context, in *notification.AuthFactorUpdateEvent, opts ...grpc.CallOption) (*ProcessAuthFactorUpdateEventResponse, error)
	// ProcessReqTxnConfirmationComplaint processes the `ReqTxnConfirmation` event for complaint status.
	// NPCI sends `ReqTxnConfirmation`  if epiFi there is any update on Complaint status raised by user.
	// We process the ReqTxnConfirmation and publish a event to CX and send vendor(UPI) with RespTxnConfirmation response.
	ProcessReqTxnConfirmationComplaint(ctx context.Context, in *ProcessReqTxnConfirmationComplaintRequest, opts ...grpc.CallOption) (*ProcessReqTxnConfirmationComplaintResponse, error)
	// ProcessRespComplaint process the 'RespComplaint' event from NPCI for a complaint
	// NPCI will send this even in case of any update on the rasied complaint
	// We will process the event and publish relevant info to the CX service
	ProcessRespComplaint(ctx context.Context, in *ProcessRespComplaintRequest, opts ...grpc.CallOption) (*ProcessRespComplaintResponse, error)
}

type consumerClient struct {
	cc grpc.ClientConnInterface
}

// Deprecated: Do not use.
func NewConsumerClient(cc grpc.ClientConnInterface) ConsumerClient {
	return &consumerClient{cc}
}

func (c *consumerClient) ProcessReqAuth(ctx context.Context, in *ProcessReqAuthRequest, opts ...grpc.CallOption) (*ProcessReqAuthResponse, error) {
	out := new(ProcessReqAuthResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessReqAuth_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessResPay(ctx context.Context, in *ProcessResPayRequest, opts ...grpc.CallOption) (*ProcessResPayResponse, error) {
	out := new(ProcessResPayResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessResPay_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessReqTxnConfirmation(ctx context.Context, in *ProcessReqTxnConfirmationRequest, opts ...grpc.CallOption) (*ProcessReqTxnConfirmationResponse, error) {
	out := new(ProcessReqTxnConfirmationResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessReqTxnConfirmation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessReqValAddress(ctx context.Context, in *ProcessReqValAddressRequest, opts ...grpc.CallOption) (*ProcessReqValAddressResponse, error) {
	out := new(ProcessReqValAddressResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessReqValAddress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessListPspKeys(ctx context.Context, in *ProcessListPspKeysRequest, opts ...grpc.CallOption) (*ProcessListPspKeysResponse, error) {
	out := new(ProcessListPspKeysResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessListPspKeys_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessListVae(ctx context.Context, in *ProcessListVaeRequest, opts ...grpc.CallOption) (*ProcessListVaeResponse, error) {
	out := new(ProcessListVaeResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessListVae_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessOnboardingEvent(ctx context.Context, in *onboarding.OnboardingStageUpdate, opts ...grpc.CallOption) (*ProcessOnboardingEventResponse, error) {
	out := new(ProcessOnboardingEventResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessOnboardingEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessAuthFactorUpdateEvent(ctx context.Context, in *notification.AuthFactorUpdateEvent, opts ...grpc.CallOption) (*ProcessAuthFactorUpdateEventResponse, error) {
	out := new(ProcessAuthFactorUpdateEventResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessAuthFactorUpdateEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessReqTxnConfirmationComplaint(ctx context.Context, in *ProcessReqTxnConfirmationComplaintRequest, opts ...grpc.CallOption) (*ProcessReqTxnConfirmationComplaintResponse, error) {
	out := new(ProcessReqTxnConfirmationComplaintResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessReqTxnConfirmationComplaint_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessRespComplaint(ctx context.Context, in *ProcessRespComplaintRequest, opts ...grpc.CallOption) (*ProcessRespComplaintResponse, error) {
	out := new(ProcessRespComplaintResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessRespComplaint_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConsumerServer is the server API for Consumer service.
// All implementations should embed UnimplementedConsumerServer
// for forward compatibility
//
// Deprecated: Do not use.
type ConsumerServer interface {
	// ProcessReqAuth processes the `ReqAuthDetails` callback from NPCI.
	// We get ReqAuth from federal inorder to resolve the account reference number from VPA belonging to epiFi.
	//
	// For pay-
	// On receiving following actions are taken in sequence-
	//  1. check if a transaction exists for the txn-id (or txn reference id).
	//  2. In case yes then update the transaction state machine and send back `RespAuthDetails` after resolving account
	//     details.
	//  3. In case no then create order and transactions with the same reference id and send back `RespAuthDetails` after
	//     resolving account details (This will happen in case the payer is not epiFi's)
	//
	// For Collect (where payee is an external user)-
	//
	//  1. Create order and transaction and send out collect notification to the user app.
	//     `RespAuth` in this case is sent from the payment service once the user authorizes the payment with PIN.
	//
	//     This method is invoked by queue subscriber to consume order related queue packet.
	ProcessReqAuth(context.Context, *ProcessReqAuthRequest) (*ProcessReqAuthResponse, error)
	// ProcessResPay processes the `RespPay` callback from NPCI.
	// RespPay is sent by NPCI in response to the `RespPay`. It contains transaction state information whether a transaction
	// succeeded or failed.
	// On receiving transaction state machine is updated accordingly.
	//
	// This method is invoked by queue subscriber to consume order related queue packet.
	ProcessResPay(context.Context, *ProcessResPayRequest) (*ProcessResPayResponse, error)
	// ProcessReqTxnConfirmation processes the `ReqTxnConfirmation` event.
	// NPCI sends `ReqTxnConfirmation`  if epiFi is the payer for `COLLECT` request and payee for `Pay` request.
	// In both scenarios we need to update the transaction state machine and send back `RespTransactionConfirmation`.
	ProcessReqTxnConfirmation(context.Context, *ProcessReqTxnConfirmationRequest) (*ProcessReqTxnConfirmationResponse, error)
	// ProcessReqValAddress processed the `ReqValAddress` event
	// NPCI sends `ReqValAddress` for epifi users to validate if a VPA is valid or not
	// Need to check if the upi is valid or not and return with a RespValidateAddress callback
	ProcessReqValAddress(context.Context, *ProcessReqValAddressRequest) (*ProcessReqValAddressResponse, error)
	// ProcessListPspKeys process the ListPspKeys events.
	// NPCI sends `ListKeysResp` as a response to ListPspKeys request type.
	// These keys are used to validate QR and intent bases UPI payments.
	// ListPspKeys will be called once in a day.
	// Consumer can expect more than one callback response for a ListPspKeys request
	// This RPC will parse response and store it cache/storage
	ProcessListPspKeys(context.Context, *ProcessListPspKeysRequest) (*ProcessListPspKeysResponse, error)
	// ProcessListVae processed the `RespListVae` event
	// NPCI sends `RespListVae` for well known verified merchants like IRCTC, LIC
	// ReqListVae will be called once in a day.
	// Consumer can expect more than one callback response for a ListVae request
	// This RPC will parse response and store it cache/storage
	ProcessListVae(context.Context, *ProcessListVaeRequest) (*ProcessListVaeResponse, error)
	// RPC process request to trigger event post any specific onboarding stage
	// For example : We need to trigger an in-app notification when user lands on home screen post successful onboarding
	// that UPI outgoing payments won't work till 24 hours of account creation
	ProcessOnboardingEvent(context.Context, *onboarding.OnboardingStageUpdate) (*ProcessOnboardingEventResponse, error)
	// RPC to process auth factor update event.
	// It will perform below operation:
	//  1. On update of user phone number associated with account,
	//     this RPC will change UPI pin set status to RE_OOBE_PIN_NOT_SET that will force user to reset UPI pin.
	//  2. It will send background app notification to reload user-session.
	ProcessAuthFactorUpdateEvent(context.Context, *notification.AuthFactorUpdateEvent) (*ProcessAuthFactorUpdateEventResponse, error)
	// ProcessReqTxnConfirmationComplaint processes the `ReqTxnConfirmation` event for complaint status.
	// NPCI sends `ReqTxnConfirmation`  if epiFi there is any update on Complaint status raised by user.
	// We process the ReqTxnConfirmation and publish a event to CX and send vendor(UPI) with RespTxnConfirmation response.
	ProcessReqTxnConfirmationComplaint(context.Context, *ProcessReqTxnConfirmationComplaintRequest) (*ProcessReqTxnConfirmationComplaintResponse, error)
	// ProcessRespComplaint process the 'RespComplaint' event from NPCI for a complaint
	// NPCI will send this even in case of any update on the rasied complaint
	// We will process the event and publish relevant info to the CX service
	ProcessRespComplaint(context.Context, *ProcessRespComplaintRequest) (*ProcessRespComplaintResponse, error)
}

// UnimplementedConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedConsumerServer struct {
}

func (UnimplementedConsumerServer) ProcessReqAuth(context.Context, *ProcessReqAuthRequest) (*ProcessReqAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessReqAuth not implemented")
}
func (UnimplementedConsumerServer) ProcessResPay(context.Context, *ProcessResPayRequest) (*ProcessResPayResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessResPay not implemented")
}
func (UnimplementedConsumerServer) ProcessReqTxnConfirmation(context.Context, *ProcessReqTxnConfirmationRequest) (*ProcessReqTxnConfirmationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessReqTxnConfirmation not implemented")
}
func (UnimplementedConsumerServer) ProcessReqValAddress(context.Context, *ProcessReqValAddressRequest) (*ProcessReqValAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessReqValAddress not implemented")
}
func (UnimplementedConsumerServer) ProcessListPspKeys(context.Context, *ProcessListPspKeysRequest) (*ProcessListPspKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessListPspKeys not implemented")
}
func (UnimplementedConsumerServer) ProcessListVae(context.Context, *ProcessListVaeRequest) (*ProcessListVaeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessListVae not implemented")
}
func (UnimplementedConsumerServer) ProcessOnboardingEvent(context.Context, *onboarding.OnboardingStageUpdate) (*ProcessOnboardingEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessOnboardingEvent not implemented")
}
func (UnimplementedConsumerServer) ProcessAuthFactorUpdateEvent(context.Context, *notification.AuthFactorUpdateEvent) (*ProcessAuthFactorUpdateEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessAuthFactorUpdateEvent not implemented")
}
func (UnimplementedConsumerServer) ProcessReqTxnConfirmationComplaint(context.Context, *ProcessReqTxnConfirmationComplaintRequest) (*ProcessReqTxnConfirmationComplaintResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessReqTxnConfirmationComplaint not implemented")
}
func (UnimplementedConsumerServer) ProcessRespComplaint(context.Context, *ProcessRespComplaintRequest) (*ProcessRespComplaintResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessRespComplaint not implemented")
}

// UnsafeConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConsumerServer will
// result in compilation errors.
type UnsafeConsumerServer interface {
	mustEmbedUnimplementedConsumerServer()
}

// Deprecated: Do not use.
func RegisterConsumerServer(s grpc.ServiceRegistrar, srv ConsumerServer) {
	s.RegisterService(&Consumer_ServiceDesc, srv)
}

func _Consumer_ProcessReqAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessReqAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessReqAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessReqAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessReqAuth(ctx, req.(*ProcessReqAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessResPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessResPayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessResPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessResPay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessResPay(ctx, req.(*ProcessResPayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessReqTxnConfirmation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessReqTxnConfirmationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessReqTxnConfirmation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessReqTxnConfirmation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessReqTxnConfirmation(ctx, req.(*ProcessReqTxnConfirmationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessReqValAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessReqValAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessReqValAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessReqValAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessReqValAddress(ctx, req.(*ProcessReqValAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessListPspKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessListPspKeysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessListPspKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessListPspKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessListPspKeys(ctx, req.(*ProcessListPspKeysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessListVae_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessListVaeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessListVae(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessListVae_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessListVae(ctx, req.(*ProcessListVaeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessOnboardingEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(onboarding.OnboardingStageUpdate)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessOnboardingEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessOnboardingEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessOnboardingEvent(ctx, req.(*onboarding.OnboardingStageUpdate))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessAuthFactorUpdateEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(notification.AuthFactorUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessAuthFactorUpdateEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessAuthFactorUpdateEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessAuthFactorUpdateEvent(ctx, req.(*notification.AuthFactorUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessReqTxnConfirmationComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessReqTxnConfirmationComplaintRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessReqTxnConfirmationComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessReqTxnConfirmationComplaint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessReqTxnConfirmationComplaint(ctx, req.(*ProcessReqTxnConfirmationComplaintRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessRespComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessRespComplaintRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessRespComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessRespComplaint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessRespComplaint(ctx, req.(*ProcessRespComplaintRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Consumer_ServiceDesc is the grpc.ServiceDesc for Consumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Consumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "upi.Consumer",
	HandlerType: (*ConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessReqAuth",
			Handler:    _Consumer_ProcessReqAuth_Handler,
		},
		{
			MethodName: "ProcessResPay",
			Handler:    _Consumer_ProcessResPay_Handler,
		},
		{
			MethodName: "ProcessReqTxnConfirmation",
			Handler:    _Consumer_ProcessReqTxnConfirmation_Handler,
		},
		{
			MethodName: "ProcessReqValAddress",
			Handler:    _Consumer_ProcessReqValAddress_Handler,
		},
		{
			MethodName: "ProcessListPspKeys",
			Handler:    _Consumer_ProcessListPspKeys_Handler,
		},
		{
			MethodName: "ProcessListVae",
			Handler:    _Consumer_ProcessListVae_Handler,
		},
		{
			MethodName: "ProcessOnboardingEvent",
			Handler:    _Consumer_ProcessOnboardingEvent_Handler,
		},
		{
			MethodName: "ProcessAuthFactorUpdateEvent",
			Handler:    _Consumer_ProcessAuthFactorUpdateEvent_Handler,
		},
		{
			MethodName: "ProcessReqTxnConfirmationComplaint",
			Handler:    _Consumer_ProcessReqTxnConfirmationComplaint_Handler,
		},
		{
			MethodName: "ProcessRespComplaint",
			Handler:    _Consumer_ProcessRespComplaint_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/upi/consumer_service.proto",
}
