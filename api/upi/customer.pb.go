// protolint:disable MAX_LINE_LENGTH
// Defines proto messages linked to a customer.
// NPCI defines a customer as, an individual person or an entity having an account and wishes to pay and/or receive money.
// A customer can be both a payer as well as a payee.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/customer.proto

package upi

import (
	accounts "github.com/epifi/gamma/api/accounts"
	account "github.com/epifi/gamma/api/typesv2/account"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Type of customer involved in a transactions.
type CustomerType int32

const (
	CustomerType_CUSTOMER_TYPE_UNSPECIFIED CustomerType = 0
	// A regular customer who uses UPI for solely personal transactions.
	CustomerType_PERSON CustomerType = 1
	// A merchant customer who uses UPI for business transactions.
	// A merchant has to declare information like MCC codes, etc. with UPI.
	CustomerType_ENTITY CustomerType = 2
)

// Enum value maps for CustomerType.
var (
	CustomerType_name = map[int32]string{
		0: "CUSTOMER_TYPE_UNSPECIFIED",
		1: "PERSON",
		2: "ENTITY",
	}
	CustomerType_value = map[string]int32{
		"CUSTOMER_TYPE_UNSPECIFIED": 0,
		"PERSON":                    1,
		"ENTITY":                    2,
	}
)

func (x CustomerType) Enum() *CustomerType {
	p := new(CustomerType)
	*p = x
	return p
}

func (x CustomerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_customer_proto_enumTypes[0].Descriptor()
}

func (CustomerType) Type() protoreflect.EnumType {
	return &file_api_upi_customer_proto_enumTypes[0]
}

func (x CustomerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerType.Descriptor instead.
func (CustomerType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_customer_proto_rawDescGZIP(), []int{0}
}

// Identifier supported by NPCI
type CustomerInformation_Identity_Type int32

const (
	CustomerInformation_Identity_TYPE_UNSPECIFIED CustomerInformation_Identity_Type = 0
	CustomerInformation_Identity_PAN              CustomerInformation_Identity_Type = 1
	CustomerInformation_Identity_AADHAAR          CustomerInformation_Identity_Type = 2
	CustomerInformation_Identity_ACCOUNT          CustomerInformation_Identity_Type = 3
)

// Enum value maps for CustomerInformation_Identity_Type.
var (
	CustomerInformation_Identity_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "PAN",
		2: "AADHAAR",
		3: "ACCOUNT",
	}
	CustomerInformation_Identity_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"PAN":              1,
		"AADHAAR":          2,
		"ACCOUNT":          3,
	}
)

func (x CustomerInformation_Identity_Type) Enum() *CustomerInformation_Identity_Type {
	p := new(CustomerInformation_Identity_Type)
	*p = x
	return p
}

func (x CustomerInformation_Identity_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomerInformation_Identity_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_customer_proto_enumTypes[1].Descriptor()
}

func (CustomerInformation_Identity_Type) Type() protoreflect.EnumType {
	return &file_api_upi_customer_proto_enumTypes[1]
}

func (x CustomerInformation_Identity_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomerInformation_Identity_Type.Descriptor instead.
func (CustomerInformation_Identity_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_customer_proto_rawDescGZIP(), []int{1, 0, 0}
}

// NPCI defines a customer as, an individual person or an entity having an account and wishes to pay and/or receive money.
// A customer can be both a payer as well as a payee.
type Customer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payment address (such as Aadhaar number, Mobile number, Debit/Credit Card, virtual payment address, etc.)
	PaymentAddress string `protobuf:"bytes,1,opt,name=payment_address,json=paymentAddress,proto3" json:"payment_address,omitempty"`
	// name of the customer
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// type of customer PERSON or ENTITY
	Type CustomerType `protobuf:"varint,3,opt,name=type,proto3,enum=upi.CustomerType" json:"type,omitempty"`
	// Information related to customer identity
	Info *CustomerInformation `protobuf:"bytes,4,opt,name=info,proto3" json:"info,omitempty"`
	// Details related to customer device
	Device *Device `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
	// Amount to be transacted in and out of the account (in for payee and out for payer)
	Amount *money.Money `protobuf:"bytes,6,opt,name=amount,proto3" json:"amount,omitempty"`
	// credentials used to authenticate customer's request
	// Populated based on request type. e.g.
	// for request of `PAY` type payer customer credential are populated.
	// for request of `COLLECT` type payee customer credential is populated.
	// for requests like SET PIN there are multiple cred blocks populated.
	Creds []*CredBlock `protobuf:"bytes,7,rep,name=creds,proto3" json:"creds,omitempty"`
	// Customer address is used for routing the request in the system and figuring out the account involved.
	// NPCI supports customer payment address of type ACCOUNT, AADHAAR, MOBILE and CARD, etc.
	// To begin with we only have need of customer account payment address hence. Keeping the
	// request simple
	AccountDetails *CustomerAccountDetails `protobuf:"bytes,8,opt,name=account_details,json=accountDetails,proto3" json:"account_details,omitempty"`
	// Merchant Classification Code
	MCC string `protobuf:"bytes,9,opt,name=MCC,proto3" json:"MCC,omitempty"`
	// merchant info, need to populate this if the customer is merchant
	Merchant *MerchantDetails `protobuf:"bytes,10,opt,name=merchant,proto3" json:"merchant,omitempty"`
	// Details for registering upi number for the customer
	RegIdDetails *RegIdDetails `protobuf:"bytes,11,opt,name=reg_id_details,json=regIdDetails,proto3" json:"reg_id_details,omitempty"`
	// Details of the consent given by the customer
	// for eg. consent for upi number migration from other psp
	Consent *Consent `protobuf:"bytes,12,opt,name=consent,proto3" json:"consent,omitempty"`
	// central mapper id/upi number used for the transaction done to a upi number
	CmId string `protobuf:"bytes,13,opt,name=cm_id,json=cmId,proto3" json:"cm_id,omitempty"`
	// customer's consent for using aadhaar for flows like pin set
	AadhaarConsent bool `protobuf:"varint,14,opt,name=aadhaar_consent,json=aadhaarConsent,proto3" json:"aadhaar_consent,omitempty"`
	// details of institution used for transaction
	Institution *Institution `protobuf:"bytes,15,opt,name=institution,proto3" json:"institution,omitempty"`
	// sequence number of the transaction
	SeqNum string `protobuf:"bytes,16,opt,name=seq_num,json=seqNum,proto3" json:"seq_num,omitempty"`
	// Details for registering upi number for the customer
	ReqIdDetails *RegIdDetails `protobuf:"bytes,17,opt,name=req_id_details,json=reqIdDetails,proto3" json:"req_id_details,omitempty"`
	// Forex charges information (in case of international payments)
	FxList []*FxDetail `protobuf:"bytes,18,rep,name=fx_list,json=fxList,proto3" json:"fx_list,omitempty"`
}

func (x *Customer) Reset() {
	*x = Customer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_customer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customer) ProtoMessage() {}

func (x *Customer) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_customer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customer.ProtoReflect.Descriptor instead.
func (*Customer) Descriptor() ([]byte, []int) {
	return file_api_upi_customer_proto_rawDescGZIP(), []int{0}
}

func (x *Customer) GetPaymentAddress() string {
	if x != nil {
		return x.PaymentAddress
	}
	return ""
}

func (x *Customer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Customer) GetType() CustomerType {
	if x != nil {
		return x.Type
	}
	return CustomerType_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *Customer) GetInfo() *CustomerInformation {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *Customer) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *Customer) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *Customer) GetCreds() []*CredBlock {
	if x != nil {
		return x.Creds
	}
	return nil
}

func (x *Customer) GetAccountDetails() *CustomerAccountDetails {
	if x != nil {
		return x.AccountDetails
	}
	return nil
}

func (x *Customer) GetMCC() string {
	if x != nil {
		return x.MCC
	}
	return ""
}

func (x *Customer) GetMerchant() *MerchantDetails {
	if x != nil {
		return x.Merchant
	}
	return nil
}

func (x *Customer) GetRegIdDetails() *RegIdDetails {
	if x != nil {
		return x.RegIdDetails
	}
	return nil
}

func (x *Customer) GetConsent() *Consent {
	if x != nil {
		return x.Consent
	}
	return nil
}

func (x *Customer) GetCmId() string {
	if x != nil {
		return x.CmId
	}
	return ""
}

func (x *Customer) GetAadhaarConsent() bool {
	if x != nil {
		return x.AadhaarConsent
	}
	return false
}

func (x *Customer) GetInstitution() *Institution {
	if x != nil {
		return x.Institution
	}
	return nil
}

func (x *Customer) GetSeqNum() string {
	if x != nil {
		return x.SeqNum
	}
	return ""
}

func (x *Customer) GetReqIdDetails() *RegIdDetails {
	if x != nil {
		return x.ReqIdDetails
	}
	return nil
}

func (x *Customer) GetFxList() []*FxDetail {
	if x != nil {
		return x.FxList
	}
	return nil
}

// Information related to customer identity
type CustomerInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identity       *CustomerInformation_Identity `protobuf:"bytes,1,opt,name=identity,proto3" json:"identity,omitempty"`
	RatingVerified bool                          `protobuf:"varint,2,opt,name=rating_verified,json=ratingVerified,proto3" json:"rating_verified,omitempty"`
}

func (x *CustomerInformation) Reset() {
	*x = CustomerInformation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_customer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerInformation) ProtoMessage() {}

func (x *CustomerInformation) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_customer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerInformation.ProtoReflect.Descriptor instead.
func (*CustomerInformation) Descriptor() ([]byte, []int) {
	return file_api_upi_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerInformation) GetIdentity() *CustomerInformation_Identity {
	if x != nil {
		return x.Identity
	}
	return nil
}

func (x *CustomerInformation) GetRatingVerified() bool {
	if x != nil {
		return x.RatingVerified
	}
	return false
}

// Customer account details. It is used by NPCI send DEBIT and CREDIT requests to respective banks.
type CustomerAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountNumber string        `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Ifsc          string        `protobuf:"bytes,2,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	Type          accounts.Type `protobuf:"varint,3,opt,name=type,proto3,enum=accounts.Type" json:"type,omitempty"`
	// lite reference number for payments using lite account
	Lrn string `protobuf:"bytes,4,opt,name=lrn,proto3" json:"lrn,omitempty"`
	// Account Product Offering associated with the AccountType.
	// The value has to be used along with AccountType for accurate identification of the account.
	//
	// For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
	Apo account.AccountProductOffering `protobuf:"varint,5,opt,name=apo,proto3,enum=api.typesv2.account.AccountProductOffering" json:"apo,omitempty"`
}

func (x *CustomerAccountDetails) Reset() {
	*x = CustomerAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_customer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAccountDetails) ProtoMessage() {}

func (x *CustomerAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_customer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAccountDetails.ProtoReflect.Descriptor instead.
func (*CustomerAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_upi_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerAccountDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *CustomerAccountDetails) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *CustomerAccountDetails) GetType() accounts.Type {
	if x != nil {
		return x.Type
	}
	return accounts.Type(0)
}

func (x *CustomerAccountDetails) GetLrn() string {
	if x != nil {
		return x.Lrn
	}
	return ""
}

func (x *CustomerAccountDetails) GetApo() account.AccountProductOffering {
	if x != nil {
		return x.Apo
	}
	return account.AccountProductOffering(0)
}

// Set of rules that governs the payment
// Caller can optionally set them based on the requirement.
type Rules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Defines expiry time of the request. NPCI times out a request in case it doesn't receive a response from payer or
	// payee PSP with in defined expiry time.
	// So for request of type `COLLECT`, expire time defines the time period till which a payer can initiate
	// the payment.
	//
	// It is also used as a threshold beyond which if caller doesn't get a response from NPCI, it can call for status check.
	// The standard guidelines says called can check status after expiry time + 90 seconds.
	//
	// Request expiry time can vary from 1 minute to max 64800 minutes.
	// If not explicitly set by caller. The default expiry is set to 30 minutes.
	ExpireAfter uint32 `protobuf:"varint,1,opt,name=expire_after,json=expireAfter,proto3" json:"expire_after,omitempty"`
	// TODO(nitesh): add documentation when we get clarity over the field.
	MinAmount *money.Money `protobuf:"bytes,2,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
}

func (x *Rules) Reset() {
	*x = Rules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_customer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rules) ProtoMessage() {}

func (x *Rules) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_customer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rules.ProtoReflect.Descriptor instead.
func (*Rules) Descriptor() ([]byte, []int) {
	return file_api_upi_customer_proto_rawDescGZIP(), []int{3}
}

func (x *Rules) GetExpireAfter() uint32 {
	if x != nil {
		return x.ExpireAfter
	}
	return 0
}

func (x *Rules) GetMinAmount() *money.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

type Institution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// details of the payment present in QR
	QrPayload string `protobuf:"bytes,1,opt,name=qr_payload,json=qrPayload,proto3" json:"qr_payload,omitempty"`
	// country code of the merchant
	ConCode string `protobuf:"bytes,2,opt,name=con_code,json=conCode,proto3" json:"con_code,omitempty"`
	// Institute Id of the acquiring bank
	NetInstId string `protobuf:"bytes,3,opt,name=net_inst_id,json=netInstId,proto3" json:"net_inst_id,omitempty"`
}

func (x *Institution) Reset() {
	*x = Institution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_customer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Institution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Institution) ProtoMessage() {}

func (x *Institution) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_customer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Institution.ProtoReflect.Descriptor instead.
func (*Institution) Descriptor() ([]byte, []int) {
	return file_api_upi_customer_proto_rawDescGZIP(), []int{4}
}

func (x *Institution) GetQrPayload() string {
	if x != nil {
		return x.QrPayload
	}
	return ""
}

func (x *Institution) GetConCode() string {
	if x != nil {
		return x.ConCode
	}
	return ""
}

func (x *Institution) GetNetInstId() string {
	if x != nil {
		return x.NetInstId
	}
	return ""
}

// Forex charges detail in case of international transactions
type FxDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// amount in quote currency
	BaseAmount float64 `protobuf:"fixed64,1,opt,name=base_amount,json=baseAmount,proto3" json:"base_amount,omitempty"`
	// quote currency
	BaseCurrency string `protobuf:"bytes,2,opt,name=base_currency,json=baseCurrency,proto3" json:"base_currency,omitempty"`
	// amount in INR per base_amount in quote currency
	ConversionRate float64 `protobuf:"fixed64,3,opt,name=conversion_rate,json=conversionRate,proto3" json:"conversion_rate,omitempty"`
	// charge amount  in INR for converting 1 unit of base_amount of base_currency
	Markup   float64 `protobuf:"fixed64,4,opt,name=markup,proto3" json:"markup,omitempty"`
	IsActive bool    `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
}

func (x *FxDetail) Reset() {
	*x = FxDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_customer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FxDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FxDetail) ProtoMessage() {}

func (x *FxDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_customer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FxDetail.ProtoReflect.Descriptor instead.
func (*FxDetail) Descriptor() ([]byte, []int) {
	return file_api_upi_customer_proto_rawDescGZIP(), []int{5}
}

func (x *FxDetail) GetBaseAmount() float64 {
	if x != nil {
		return x.BaseAmount
	}
	return 0
}

func (x *FxDetail) GetBaseCurrency() string {
	if x != nil {
		return x.BaseCurrency
	}
	return ""
}

func (x *FxDetail) GetConversionRate() float64 {
	if x != nil {
		return x.ConversionRate
	}
	return 0
}

func (x *FxDetail) GetMarkup() float64 {
	if x != nil {
		return x.Markup
	}
	return 0
}

func (x *FxDetail) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

// Information related customer identity
// Note- Payer Identity Is mandatory for “pay” and optional for “collect”
// Payee identity is mandatory for "collect " and optional for "pay"
type CustomerInformation_Identity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id of the identifier
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Type of the identifier
	Type CustomerInformation_Identity_Type `protobuf:"varint,2,opt,name=type,proto3,enum=upi.CustomerInformation_Identity_Type" json:"type,omitempty"`
	// Name as per the identifier
	VerifiedName string `protobuf:"bytes,3,opt,name=verified_name,json=verifiedName,proto3" json:"verified_name,omitempty"`
}

func (x *CustomerInformation_Identity) Reset() {
	*x = CustomerInformation_Identity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_customer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerInformation_Identity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerInformation_Identity) ProtoMessage() {}

func (x *CustomerInformation_Identity) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_customer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerInformation_Identity.ProtoReflect.Descriptor instead.
func (*CustomerInformation_Identity) Descriptor() ([]byte, []int) {
	return file_api_upi_customer_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CustomerInformation_Identity) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CustomerInformation_Identity) GetType() CustomerInformation_Identity_Type {
	if x != nil {
		return x.Type
	}
	return CustomerInformation_Identity_TYPE_UNSPECIFIED
}

func (x *CustomerInformation_Identity) GetVerifiedName() string {
	if x != nil {
		return x.VerifiedName
	}
	return ""
}

var File_api_upi_customer_proto protoreflect.FileDescriptor

var file_api_upi_customer_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x75, 0x70, 0x69, 0x1a, 0x1f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69,
	0x2f, 0x75, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xea, 0x05, 0x0a, 0x08, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x12, 0x23, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x05, 0x63, 0x72, 0x65, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x05, 0x63, 0x72, 0x65, 0x64, 0x73, 0x12, 0x44, 0x0a, 0x0f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x10, 0x0a, 0x03, 0x4d, 0x43, 0x43, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d, 0x43,
	0x43, 0x12, 0x30, 0x0a, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x08, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x5f, 0x69, 0x64, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x52, 0x65, 0x67, 0x49, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c,
	0x72, 0x65, 0x67, 0x49, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x12, 0x13, 0x0a, 0x05, 0x63, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6d, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x61, 0x64,
	0x68, 0x61, 0x61, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x43, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x12, 0x32, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x49, 0x6e,
	0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x69,
	0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x12,
	0x37, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x52, 0x65,
	0x67, 0x49, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x49,
	0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x07, 0x66, 0x78, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x46, 0x78, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x66, 0x78, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0xbc, 0x02, 0x0a, 0x13, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x61, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x1a, 0xbc, 0x01, 0x0a, 0x08, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3a, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3f,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03,
	0x50, 0x41, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52,
	0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x03, 0x22,
	0xc8, 0x01, 0x0a, 0x16, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x66, 0x73, 0x63, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x72, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x72, 0x6e, 0x12, 0x3d, 0x0a, 0x03, 0x61,
	0x70, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x03, 0x61, 0x70, 0x6f, 0x22, 0x5d, 0x0a, 0x05, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x66,
	0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09,
	0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x67, 0x0a, 0x0b, 0x49, 0x6e, 0x73,
	0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x72, 0x5f, 0x70,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x72,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x6e, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x49, 0x64, 0x22, 0xae, 0x01, 0x0a, 0x08, 0x46, 0x78, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x1f, 0x0a, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x62, 0x61, 0x73, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x61, 0x72, 0x6b, 0x75, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06,
	0x6d, 0x61, 0x72, 0x6b, 0x75, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x2a, 0x45, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x10, 0x02, 0x42, 0x40, 0x0a, 0x1e, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x5a, 0x1e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_customer_proto_rawDescOnce sync.Once
	file_api_upi_customer_proto_rawDescData = file_api_upi_customer_proto_rawDesc
)

func file_api_upi_customer_proto_rawDescGZIP() []byte {
	file_api_upi_customer_proto_rawDescOnce.Do(func() {
		file_api_upi_customer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_customer_proto_rawDescData)
	})
	return file_api_upi_customer_proto_rawDescData
}

var file_api_upi_customer_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_upi_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_upi_customer_proto_goTypes = []interface{}{
	(CustomerType)(0),                      // 0: upi.CustomerType
	(CustomerInformation_Identity_Type)(0), // 1: upi.CustomerInformation.Identity.Type
	(*Customer)(nil),                       // 2: upi.Customer
	(*CustomerInformation)(nil),            // 3: upi.CustomerInformation
	(*CustomerAccountDetails)(nil),         // 4: upi.CustomerAccountDetails
	(*Rules)(nil),                          // 5: upi.Rules
	(*Institution)(nil),                    // 6: upi.Institution
	(*FxDetail)(nil),                       // 7: upi.FxDetail
	(*CustomerInformation_Identity)(nil),   // 8: upi.CustomerInformation.Identity
	(*Device)(nil),                         // 9: upi.Device
	(*money.Money)(nil),                    // 10: google.type.Money
	(*CredBlock)(nil),                      // 11: upi.CredBlock
	(*MerchantDetails)(nil),                // 12: upi.MerchantDetails
	(*RegIdDetails)(nil),                   // 13: upi.RegIdDetails
	(*Consent)(nil),                        // 14: upi.Consent
	(accounts.Type)(0),                     // 15: accounts.Type
	(account.AccountProductOffering)(0),    // 16: api.typesv2.account.AccountProductOffering
}
var file_api_upi_customer_proto_depIdxs = []int32{
	0,  // 0: upi.Customer.type:type_name -> upi.CustomerType
	3,  // 1: upi.Customer.info:type_name -> upi.CustomerInformation
	9,  // 2: upi.Customer.device:type_name -> upi.Device
	10, // 3: upi.Customer.amount:type_name -> google.type.Money
	11, // 4: upi.Customer.creds:type_name -> upi.CredBlock
	4,  // 5: upi.Customer.account_details:type_name -> upi.CustomerAccountDetails
	12, // 6: upi.Customer.merchant:type_name -> upi.MerchantDetails
	13, // 7: upi.Customer.reg_id_details:type_name -> upi.RegIdDetails
	14, // 8: upi.Customer.consent:type_name -> upi.Consent
	6,  // 9: upi.Customer.institution:type_name -> upi.Institution
	13, // 10: upi.Customer.req_id_details:type_name -> upi.RegIdDetails
	7,  // 11: upi.Customer.fx_list:type_name -> upi.FxDetail
	8,  // 12: upi.CustomerInformation.identity:type_name -> upi.CustomerInformation.Identity
	15, // 13: upi.CustomerAccountDetails.type:type_name -> accounts.Type
	16, // 14: upi.CustomerAccountDetails.apo:type_name -> api.typesv2.account.AccountProductOffering
	10, // 15: upi.Rules.min_amount:type_name -> google.type.Money
	1,  // 16: upi.CustomerInformation.Identity.type:type_name -> upi.CustomerInformation.Identity.Type
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_api_upi_customer_proto_init() }
func file_api_upi_customer_proto_init() {
	if File_api_upi_customer_proto != nil {
		return
	}
	file_api_upi_cred_block_proto_init()
	file_api_upi_device_proto_init()
	file_api_upi_merchant_proto_init()
	file_api_upi_upi_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_upi_customer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Customer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_customer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerInformation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_customer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_customer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_customer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Institution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_customer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FxDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_customer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerInformation_Identity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_customer_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_customer_proto_goTypes,
		DependencyIndexes: file_api_upi_customer_proto_depIdxs,
		EnumInfos:         file_api_upi_customer_proto_enumTypes,
		MessageInfos:      file_api_upi_customer_proto_msgTypes,
	}.Build()
	File_api_upi_customer_proto = out.File
	file_api_upi_customer_proto_rawDesc = nil
	file_api_upi_customer_proto_goTypes = nil
	file_api_upi_customer_proto_depIdxs = nil
}
