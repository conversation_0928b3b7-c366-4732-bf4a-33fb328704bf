//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/upi_events.proto

package upi

import (
	queue "github.com/epifi/be-common/api/queue"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EventType int32

const (
	// unspecified
	EventType_EVENT_TYPE_UNSPECIFIED EventType = 0
	// upi pin set is done successfully
	EventType_EVENT_TYPE_UPI_PIN_SET EventType = 1
	// tpap account connected successfully
	EventType_EVENT_TYPE_TPAP_ACCOUNT_CONNECTED EventType = 2
)

// Enum value maps for EventType.
var (
	EventType_name = map[int32]string{
		0: "EVENT_TYPE_UNSPECIFIED",
		1: "EVENT_TYPE_UPI_PIN_SET",
		2: "EVENT_TYPE_TPAP_ACCOUNT_CONNECTED",
	}
	EventType_value = map[string]int32{
		"EVENT_TYPE_UNSPECIFIED":            0,
		"EVENT_TYPE_UPI_PIN_SET":            1,
		"EVENT_TYPE_TPAP_ACCOUNT_CONNECTED": 2,
	}
)

func (x EventType) Enum() *EventType {
	p := new(EventType)
	*p = x
	return p
}

func (x EventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EventType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_events_proto_enumTypes[0].Descriptor()
}

func (EventType) Type() protoreflect.EnumType {
	return &file_api_upi_upi_events_proto_enumTypes[0]
}

func (x EventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EventType.Descriptor instead.
func (EventType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_events_proto_rawDescGZIP(), []int{0}
}

// This event is to emitted only if upi-pin-set is done successfully
type UpiEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// account id of the user
	AccountId string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// type of event
	EventType EventType `protobuf:"varint,4,opt,name=event_type,json=eventType,proto3,enum=upi.EventType" json:"event_type,omitempty"`
	// timestamp at which the event happened.
	ActionTimestamp *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=action_timestamp,json=actionTimestamp,proto3" json:"action_timestamp,omitempty"`
	// info regarding the event
	//
	// Types that are assignable to EventInfo:
	//
	//	*UpiEvent_UpiPinSetInfo
	EventInfo isUpiEvent_EventInfo `protobuf_oneof:"event_info"`
}

func (x *UpiEvent) Reset() {
	*x = UpiEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_events_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiEvent) ProtoMessage() {}

func (x *UpiEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_events_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiEvent.ProtoReflect.Descriptor instead.
func (*UpiEvent) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_events_proto_rawDescGZIP(), []int{0}
}

func (x *UpiEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpiEvent) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpiEvent) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *UpiEvent) GetEventType() EventType {
	if x != nil {
		return x.EventType
	}
	return EventType_EVENT_TYPE_UNSPECIFIED
}

func (x *UpiEvent) GetActionTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ActionTimestamp
	}
	return nil
}

func (m *UpiEvent) GetEventInfo() isUpiEvent_EventInfo {
	if m != nil {
		return m.EventInfo
	}
	return nil
}

func (x *UpiEvent) GetUpiPinSetInfo() *UpiPinSetInfo {
	if x, ok := x.GetEventInfo().(*UpiEvent_UpiPinSetInfo); ok {
		return x.UpiPinSetInfo
	}
	return nil
}

type isUpiEvent_EventInfo interface {
	isUpiEvent_EventInfo()
}

type UpiEvent_UpiPinSetInfo struct {
	UpiPinSetInfo *UpiPinSetInfo `protobuf:"bytes,6,opt,name=upi_pin_set_info,json=upiPinSetInfo,proto3,oneof"`
}

func (*UpiEvent_UpiPinSetInfo) isUpiEvent_EventInfo() {}

type UpiPinSetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpiPinSetInfo) Reset() {
	*x = UpiPinSetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_events_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiPinSetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiPinSetInfo) ProtoMessage() {}

func (x *UpiPinSetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_events_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiPinSetInfo.ProtoReflect.Descriptor instead.
func (*UpiPinSetInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_events_proto_rawDescGZIP(), []int{1}
}

var File_api_upi_upi_events_proto protoreflect.FileDescriptor

var file_api_upi_upi_events_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x75, 0x70, 0x69, 0x1a,
	0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xcc, 0x02, 0x0a, 0x08, 0x55, 0x70, 0x69, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a,
	0x10, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x3d, 0x0a, 0x10, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f,
	0x73, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x0d, 0x75, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x42, 0x0c, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x22, 0x0f, 0x0a, 0x0d, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x2a, 0x6a, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1a, 0x0a, 0x16, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x45,
	0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49,
	0x4e, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x45, 0x56, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x50, 0x41, 0x50, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x42, 0x40,
	0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69,
	0x5a, 0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_upi_events_proto_rawDescOnce sync.Once
	file_api_upi_upi_events_proto_rawDescData = file_api_upi_upi_events_proto_rawDesc
)

func file_api_upi_upi_events_proto_rawDescGZIP() []byte {
	file_api_upi_upi_events_proto_rawDescOnce.Do(func() {
		file_api_upi_upi_events_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_upi_events_proto_rawDescData)
	})
	return file_api_upi_upi_events_proto_rawDescData
}

var file_api_upi_upi_events_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_upi_events_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_upi_upi_events_proto_goTypes = []interface{}{
	(EventType)(0),                      // 0: upi.EventType
	(*UpiEvent)(nil),                    // 1: upi.UpiEvent
	(*UpiPinSetInfo)(nil),               // 2: upi.UpiPinSetInfo
	(*queue.ConsumerRequestHeader)(nil), // 3: queue.ConsumerRequestHeader
	(*timestamppb.Timestamp)(nil),       // 4: google.protobuf.Timestamp
}
var file_api_upi_upi_events_proto_depIdxs = []int32{
	3, // 0: upi.UpiEvent.request_header:type_name -> queue.ConsumerRequestHeader
	0, // 1: upi.UpiEvent.event_type:type_name -> upi.EventType
	4, // 2: upi.UpiEvent.action_timestamp:type_name -> google.protobuf.Timestamp
	2, // 3: upi.UpiEvent.upi_pin_set_info:type_name -> upi.UpiPinSetInfo
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_upi_upi_events_proto_init() }
func file_api_upi_upi_events_proto_init() {
	if File_api_upi_upi_events_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_upi_events_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_events_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiPinSetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_upi_upi_events_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*UpiEvent_UpiPinSetInfo)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_upi_events_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_upi_events_proto_goTypes,
		DependencyIndexes: file_api_upi_upi_events_proto_depIdxs,
		EnumInfos:         file_api_upi_upi_events_proto_enumTypes,
		MessageInfos:      file_api_upi_upi_events_proto_msgTypes,
	}.Build()
	File_api_upi_upi_events_proto = out.File
	file_api_upi_upi_events_proto_rawDesc = nil
	file_api_upi_upi_events_proto_goTypes = nil
	file_api_upi_upi_events_proto_depIdxs = nil
}
