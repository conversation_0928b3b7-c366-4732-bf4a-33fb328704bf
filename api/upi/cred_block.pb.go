// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/cred_block.proto

package upi

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// type of the cred block
type CredBlock_CredBlockType int32

const (
	// unspecified
	CredBlock_CRED_BLOCK_TYPE_UNSPECIFIED CredBlock_CredBlockType = 0
	// type pin
	CredBlock_PIN CredBlock_CredBlockType = 1
	// is used to get token for registering NPCI common library with the device
	// The registration flow is as follows
	//  1. Execute “Get Challenge” service to receive a challenge from common library.
	//  2. Use the generated challenge to get the token from PSP server
	//  3. Use the token to register the app with common library
	CredBlock_CHALLENGE CredBlock_CredBlockType = 2
	// Will be use to request OTP and validate OTP
	CredBlock_OTP CredBlock_CredBlockType = 3
	// It will be used to capture card details in cred block
	CredBlock_CARD CredBlock_CredBlockType = 4
	// It will used to capture upi mandate signed token
	CredBlock_UPI_MANDATE CredBlock_CredBlockType = 5
	// It will be used to activate upi lite for a tpap account
	CredBlock_DEVICE CredBlock_CredBlockType = 6
	// Authorization Request Cryptogram, CL generates the ARQC for every transaction,
	// the UPI Lite system will validate the details sent by the CL (Common Library).
	CredBlock_ARQC CredBlock_CredBlockType = 7
	// Authorization Response Cryptogram, The CL validate ARPC sent by UPI Lite system and the
	// result of validation of ARQC and ARPC transaction shall impact the next transaction.
	CredBlock_ARPC CredBlock_CredBlockType = 8
)

// Enum value maps for CredBlock_CredBlockType.
var (
	CredBlock_CredBlockType_name = map[int32]string{
		0: "CRED_BLOCK_TYPE_UNSPECIFIED",
		1: "PIN",
		2: "CHALLENGE",
		3: "OTP",
		4: "CARD",
		5: "UPI_MANDATE",
		6: "DEVICE",
		7: "ARQC",
		8: "ARPC",
	}
	CredBlock_CredBlockType_value = map[string]int32{
		"CRED_BLOCK_TYPE_UNSPECIFIED": 0,
		"PIN":                         1,
		"CHALLENGE":                   2,
		"OTP":                         3,
		"CARD":                        4,
		"UPI_MANDATE":                 5,
		"DEVICE":                      6,
		"ARQC":                        7,
		"ARPC":                        8,
	}
)

func (x CredBlock_CredBlockType) Enum() *CredBlock_CredBlockType {
	p := new(CredBlock_CredBlockType)
	*p = x
	return p
}

func (x CredBlock_CredBlockType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CredBlock_CredBlockType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_cred_block_proto_enumTypes[0].Descriptor()
}

func (CredBlock_CredBlockType) Type() protoreflect.EnumType {
	return &file_api_upi_cred_block_proto_enumTypes[0]
}

func (x CredBlock_CredBlockType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CredBlock_CredBlockType.Descriptor instead.
func (CredBlock_CredBlockType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_cred_block_proto_rawDescGZIP(), []int{0, 0}
}

// sub type of the cred block
type CredBlock_CredBlockSubType int32

const (
	// unspecified
	CredBlock_CRED_BLOCK_SUB_TYPE_UNSPECIFIED CredBlock_CredBlockSubType = 0
	// is used for type = challenge
	// indicates that the token is fetched for the first time
	CredBlock_INITIAL CredBlock_CredBlockSubType = 1
	// UPI PIN
	CredBlock_MPIN CredBlock_CredBlockSubType = 2
	// ATM PIN
	CredBlock_ATMPIN CredBlock_CredBlockSubType = 3
	// CardDetails : It will contain encrypted data for last 6 digit of card, CVV, expiry date
	CredBlock_CARDDETAILS CredBlock_CredBlockSubType = 4
	// SMS subtype to capture otp from SMS
	CredBlock_SMS CredBlock_CredBlockSubType = 5
	// Email subtype to capture otp from email
	CredBlock_EMAIL CredBlock_CredBlockSubType = 6
	// TODO (vivek): add description after clarity on this
	CredBlock_HOTP CredBlock_CredBlockSubType = 7
	// TODO (vivek): add description after clarity on this
	CredBlock_TOTP CredBlock_CredBlockSubType = 8
	// New pin provided by CL when user Change PIN
	CredBlock_NMPIN CredBlock_CredBlockSubType = 9
	// After INITIAL fetch, when fetching for a new token.
	// pass ROTATE
	CredBlock_ROTATE CredBlock_CredBlockSubType = 10
	// Digitally signed token. It will be used to pass mandate sign token cred block
	CredBlock_DS CredBlock_CredBlockSubType = 11
	// Aadhaar subtype required in upi pin set / reset
	CredBlock_AADHAAR CredBlock_CredBlockSubType = 12
	// IDENTITY - will be used during activation of
	// upi lite account for an upi account
	CredBlock_IDENTITY CredBlock_CredBlockSubType = 13
	// subtype required for top up and payments via upi lite
	CredBlock_SIGNATURE CredBlock_CredBlockSubType = 14
)

// Enum value maps for CredBlock_CredBlockSubType.
var (
	CredBlock_CredBlockSubType_name = map[int32]string{
		0:  "CRED_BLOCK_SUB_TYPE_UNSPECIFIED",
		1:  "INITIAL",
		2:  "MPIN",
		3:  "ATMPIN",
		4:  "CARDDETAILS",
		5:  "SMS",
		6:  "EMAIL",
		7:  "HOTP",
		8:  "TOTP",
		9:  "NMPIN",
		10: "ROTATE",
		11: "DS",
		12: "AADHAAR",
		13: "IDENTITY",
		14: "SIGNATURE",
	}
	CredBlock_CredBlockSubType_value = map[string]int32{
		"CRED_BLOCK_SUB_TYPE_UNSPECIFIED": 0,
		"INITIAL":                         1,
		"MPIN":                            2,
		"ATMPIN":                          3,
		"CARDDETAILS":                     4,
		"SMS":                             5,
		"EMAIL":                           6,
		"HOTP":                            7,
		"TOTP":                            8,
		"NMPIN":                           9,
		"ROTATE":                          10,
		"DS":                              11,
		"AADHAAR":                         12,
		"IDENTITY":                        13,
		"SIGNATURE":                       14,
	}
)

func (x CredBlock_CredBlockSubType) Enum() *CredBlock_CredBlockSubType {
	p := new(CredBlock_CredBlockSubType)
	*p = x
	return p
}

func (x CredBlock_CredBlockSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CredBlock_CredBlockSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_cred_block_proto_enumTypes[1].Descriptor()
}

func (CredBlock_CredBlockSubType) Type() protoreflect.EnumType {
	return &file_api_upi_cred_block_proto_enumTypes[1]
}

func (x CredBlock_CredBlockSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CredBlock_CredBlockSubType.Descriptor instead.
func (CredBlock_CredBlockSubType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_cred_block_proto_rawDescGZIP(), []int{0, 1}
}

// To remove circular dependencies, we are re-categorising proto file
// Deprecated: in favour of upi/auth/cred_block.proto
type CredBlock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type of the cred block eg. PIN
	Type CredBlock_CredBlockType `protobuf:"varint,1,opt,name=type,proto3,enum=upi.CredBlock_CredBlockType" json:"type,omitempty"`
	// sub type of the cred block -  eg. MPIN, ATMPIN etc.
	SubType CredBlock_CredBlockSubType `protobuf:"varint,2,opt,name=sub_type,json=subType,proto3,enum=upi.CredBlock_CredBlockSubType" json:"sub_type,omitempty"`
	// data to be passed in the cred block
	// contains encrypted text passed by the client
	Data *CredBlock_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CredBlock) Reset() {
	*x = CredBlock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_cred_block_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CredBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CredBlock) ProtoMessage() {}

func (x *CredBlock) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_cred_block_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CredBlock.ProtoReflect.Descriptor instead.
func (*CredBlock) Descriptor() ([]byte, []int) {
	return file_api_upi_cred_block_proto_rawDescGZIP(), []int{0}
}

func (x *CredBlock) GetType() CredBlock_CredBlockType {
	if x != nil {
		return x.Type
	}
	return CredBlock_CRED_BLOCK_TYPE_UNSPECIFIED
}

func (x *CredBlock) GetSubType() CredBlock_CredBlockSubType {
	if x != nil {
		return x.SubType
	}
	return CredBlock_CRED_BLOCK_SUB_TYPE_UNSPECIFIED
}

func (x *CredBlock) GetData() *CredBlock_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CredBlock_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// code for the cred block - NPCI
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// Key Index and Code helps to uniquely identify a key that is used to the encrypt the card data
	// Format of Key Index is yyyymmdd// key index
	Ki string `protobuf:"bytes,2,opt,name=ki,proto3" json:"ki,omitempty"`
	// encrypted text
	Text string `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *CredBlock_Data) Reset() {
	*x = CredBlock_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_cred_block_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CredBlock_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CredBlock_Data) ProtoMessage() {}

func (x *CredBlock_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_cred_block_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CredBlock_Data.ProtoReflect.Descriptor instead.
func (*CredBlock_Data) Descriptor() ([]byte, []int) {
	return file_api_upi_cred_block_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CredBlock_Data) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CredBlock_Data) GetKi() string {
	if x != nil {
		return x.Ki
	}
	return ""
}

func (x *CredBlock_Data) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

var File_api_upi_cred_block_proto protoreflect.FileDescriptor

var file_api_upi_cred_block_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x75, 0x70, 0x69, 0x22,
	0xd0, 0x04, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x30, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x3a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x75, 0x62, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x1a, 0x3e, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x6b, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6b, 0x69,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x52, 0x45, 0x44, 0x5f, 0x42,
	0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x49, 0x4e, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x43, 0x48, 0x41, 0x4c, 0x4c, 0x45, 0x4e, 0x47, 0x45, 0x10, 0x02, 0x12,
	0x07, 0x0a, 0x03, 0x4f, 0x54, 0x50, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x52, 0x44,
	0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x06, 0x12,
	0x08, 0x0a, 0x04, 0x41, 0x52, 0x51, 0x43, 0x10, 0x07, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x52, 0x50,
	0x43, 0x10, 0x08, 0x22, 0xdc, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x52, 0x45, 0x44,
	0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x50,
	0x49, 0x4e, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x54, 0x4d, 0x50, 0x49, 0x4e, 0x10, 0x03,
	0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x41, 0x52, 0x44, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x04, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x4d, 0x53, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x4f, 0x54, 0x50, 0x10, 0x07, 0x12,
	0x08, 0x0a, 0x04, 0x54, 0x4f, 0x54, 0x50, 0x10, 0x08, 0x12, 0x09, 0x0a, 0x05, 0x4e, 0x4d, 0x50,
	0x49, 0x4e, 0x10, 0x09, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x4f, 0x54, 0x41, 0x54, 0x45, 0x10, 0x0a,
	0x12, 0x06, 0x0a, 0x02, 0x44, 0x53, 0x10, 0x0b, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x41, 0x44, 0x48,
	0x41, 0x41, 0x52, 0x10, 0x0c, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54,
	0x59, 0x10, 0x0d, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x10, 0x0e, 0x42, 0x40, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x70, 0x69, 0x5a, 0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x75, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_cred_block_proto_rawDescOnce sync.Once
	file_api_upi_cred_block_proto_rawDescData = file_api_upi_cred_block_proto_rawDesc
)

func file_api_upi_cred_block_proto_rawDescGZIP() []byte {
	file_api_upi_cred_block_proto_rawDescOnce.Do(func() {
		file_api_upi_cred_block_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_cred_block_proto_rawDescData)
	})
	return file_api_upi_cred_block_proto_rawDescData
}

var file_api_upi_cred_block_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_upi_cred_block_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_upi_cred_block_proto_goTypes = []interface{}{
	(CredBlock_CredBlockType)(0),    // 0: upi.CredBlock.CredBlockType
	(CredBlock_CredBlockSubType)(0), // 1: upi.CredBlock.CredBlockSubType
	(*CredBlock)(nil),               // 2: upi.CredBlock
	(*CredBlock_Data)(nil),          // 3: upi.CredBlock.Data
}
var file_api_upi_cred_block_proto_depIdxs = []int32{
	0, // 0: upi.CredBlock.type:type_name -> upi.CredBlock.CredBlockType
	1, // 1: upi.CredBlock.sub_type:type_name -> upi.CredBlock.CredBlockSubType
	3, // 2: upi.CredBlock.data:type_name -> upi.CredBlock.Data
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_upi_cred_block_proto_init() }
func file_api_upi_cred_block_proto_init() {
	if File_api_upi_cred_block_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_cred_block_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CredBlock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_cred_block_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CredBlock_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_cred_block_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_cred_block_proto_goTypes,
		DependencyIndexes: file_api_upi_cred_block_proto_depIdxs,
		EnumInfos:         file_api_upi_cred_block_proto_enumTypes,
		MessageInfos:      file_api_upi_cred_block_proto_msgTypes,
	}.Build()
	File_api_upi_cred_block_proto = out.File
	file_api_upi_cred_block_proto_rawDesc = nil
	file_api_upi_cred_block_proto_goTypes = nil
	file_api_upi_cred_block_proto_depIdxs = nil
}
