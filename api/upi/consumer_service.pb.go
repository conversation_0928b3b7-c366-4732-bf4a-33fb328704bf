// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/consumer_service.proto

package upi

import (
	queue "github.com/epifi/be-common/api/queue"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	notification "github.com/epifi/gamma/api/auth/notification"
	onboarding "github.com/epifi/gamma/api/user/onboarding"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessReqAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PartnerBank   vendorgateway.Vendor         `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
	// to unmarshal the data.
	RawData []byte `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessReqAuthRequest) Reset() {
	*x = ProcessReqAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqAuthRequest) ProtoMessage() {}

func (x *ProcessReqAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqAuthRequest.ProtoReflect.Descriptor instead.
func (*ProcessReqAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessReqAuthRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessReqAuthRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessReqAuthRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type ProcessReqAuthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessReqAuthResponse) Reset() {
	*x = ProcessReqAuthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqAuthResponse) ProtoMessage() {}

func (x *ProcessReqAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqAuthResponse.ProtoReflect.Descriptor instead.
func (*ProcessReqAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessReqAuthResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessResPayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PartnerBank   vendorgateway.Vendor         `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
	// to unmarshal the data.
	RawData []byte `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessResPayRequest) Reset() {
	*x = ProcessResPayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessResPayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessResPayRequest) ProtoMessage() {}

func (x *ProcessResPayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessResPayRequest.ProtoReflect.Descriptor instead.
func (*ProcessResPayRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessResPayRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessResPayRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessResPayRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type ProcessResPayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessResPayResponse) Reset() {
	*x = ProcessResPayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessResPayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessResPayResponse) ProtoMessage() {}

func (x *ProcessResPayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessResPayResponse.ProtoReflect.Descriptor instead.
func (*ProcessResPayResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessResPayResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessReqTxnConfirmationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PartnerBank   vendorgateway.Vendor         `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
	// to unmarshal the data.
	RawData []byte `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessReqTxnConfirmationRequest) Reset() {
	*x = ProcessReqTxnConfirmationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqTxnConfirmationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqTxnConfirmationRequest) ProtoMessage() {}

func (x *ProcessReqTxnConfirmationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqTxnConfirmationRequest.ProtoReflect.Descriptor instead.
func (*ProcessReqTxnConfirmationRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{4}
}

func (x *ProcessReqTxnConfirmationRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessReqTxnConfirmationRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessReqTxnConfirmationRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type ProcessReqTxnConfirmationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessReqTxnConfirmationResponse) Reset() {
	*x = ProcessReqTxnConfirmationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqTxnConfirmationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqTxnConfirmationResponse) ProtoMessage() {}

func (x *ProcessReqTxnConfirmationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqTxnConfirmationResponse.ProtoReflect.Descriptor instead.
func (*ProcessReqTxnConfirmationResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{5}
}

func (x *ProcessReqTxnConfirmationResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessReqValAddressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PartnerBank   vendorgateway.Vendor         `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
	// to unmarshal the data.
	RawData []byte `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessReqValAddressRequest) Reset() {
	*x = ProcessReqValAddressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqValAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqValAddressRequest) ProtoMessage() {}

func (x *ProcessReqValAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqValAddressRequest.ProtoReflect.Descriptor instead.
func (*ProcessReqValAddressRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{6}
}

func (x *ProcessReqValAddressRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessReqValAddressRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessReqValAddressRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type ProcessReqValAddressResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessReqValAddressResponse) Reset() {
	*x = ProcessReqValAddressResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqValAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqValAddressResponse) ProtoMessage() {}

func (x *ProcessReqValAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqValAddressResponse.ProtoReflect.Descriptor instead.
func (*ProcessReqValAddressResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{7}
}

func (x *ProcessReqValAddressResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessListPspKeysRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PartnerBank   vendorgateway.Vendor         `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
	// to unmarshal the data.
	RawData []byte `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessListPspKeysRequest) Reset() {
	*x = ProcessListPspKeysRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessListPspKeysRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessListPspKeysRequest) ProtoMessage() {}

func (x *ProcessListPspKeysRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessListPspKeysRequest.ProtoReflect.Descriptor instead.
func (*ProcessListPspKeysRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{8}
}

func (x *ProcessListPspKeysRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessListPspKeysRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessListPspKeysRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type ProcessListPspKeysResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessListPspKeysResponse) Reset() {
	*x = ProcessListPspKeysResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessListPspKeysResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessListPspKeysResponse) ProtoMessage() {}

func (x *ProcessListPspKeysResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessListPspKeysResponse.ProtoReflect.Descriptor instead.
func (*ProcessListPspKeysResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{9}
}

func (x *ProcessListPspKeysResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessListVaeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PartnerBank   vendorgateway.Vendor         `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
	// to unmarshal the data.
	RawData []byte `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessListVaeRequest) Reset() {
	*x = ProcessListVaeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessListVaeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessListVaeRequest) ProtoMessage() {}

func (x *ProcessListVaeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessListVaeRequest.ProtoReflect.Descriptor instead.
func (*ProcessListVaeRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{10}
}

func (x *ProcessListVaeRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessListVaeRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessListVaeRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type ProcessListVaeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessListVaeResponse) Reset() {
	*x = ProcessListVaeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessListVaeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessListVaeResponse) ProtoMessage() {}

func (x *ProcessListVaeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessListVaeResponse.ProtoReflect.Descriptor instead.
func (*ProcessListVaeResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{11}
}

func (x *ProcessListVaeResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessOnboardingEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessOnboardingEventResponse) Reset() {
	*x = ProcessOnboardingEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessOnboardingEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessOnboardingEventResponse) ProtoMessage() {}

func (x *ProcessOnboardingEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessOnboardingEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessOnboardingEventResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{12}
}

func (x *ProcessOnboardingEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessAuthFactorUpdateEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessAuthFactorUpdateEventResponse) Reset() {
	*x = ProcessAuthFactorUpdateEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAuthFactorUpdateEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAuthFactorUpdateEventResponse) ProtoMessage() {}

func (x *ProcessAuthFactorUpdateEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAuthFactorUpdateEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessAuthFactorUpdateEventResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{13}
}

func (x *ProcessAuthFactorUpdateEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessReqTxnConfirmationComplaintRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PartnerBank   vendorgateway.Vendor         `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
	// to unmarshal the data.
	RawData []byte `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessReqTxnConfirmationComplaintRequest) Reset() {
	*x = ProcessReqTxnConfirmationComplaintRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqTxnConfirmationComplaintRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqTxnConfirmationComplaintRequest) ProtoMessage() {}

func (x *ProcessReqTxnConfirmationComplaintRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqTxnConfirmationComplaintRequest.ProtoReflect.Descriptor instead.
func (*ProcessReqTxnConfirmationComplaintRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{14}
}

func (x *ProcessReqTxnConfirmationComplaintRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessReqTxnConfirmationComplaintRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessReqTxnConfirmationComplaintRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type ProcessReqTxnConfirmationComplaintResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessReqTxnConfirmationComplaintResponse) Reset() {
	*x = ProcessReqTxnConfirmationComplaintResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqTxnConfirmationComplaintResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqTxnConfirmationComplaintResponse) ProtoMessage() {}

func (x *ProcessReqTxnConfirmationComplaintResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqTxnConfirmationComplaintResponse.ProtoReflect.Descriptor instead.
func (*ProcessReqTxnConfirmationComplaintResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{15}
}

func (x *ProcessReqTxnConfirmationComplaintResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessRespComplaintRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PartnerBank   vendorgateway.Vendor         `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
	// to unmarshal the data.
	RawData []byte `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessRespComplaintRequest) Reset() {
	*x = ProcessRespComplaintRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessRespComplaintRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessRespComplaintRequest) ProtoMessage() {}

func (x *ProcessRespComplaintRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessRespComplaintRequest.ProtoReflect.Descriptor instead.
func (*ProcessRespComplaintRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{16}
}

func (x *ProcessRespComplaintRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessRespComplaintRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessRespComplaintRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

type ProcessRespComplaintResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessRespComplaintResponse) Reset() {
	*x = ProcessRespComplaintResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_consumer_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessRespComplaintResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessRespComplaintResponse) ProtoMessage() {}

func (x *ProcessRespComplaintResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_consumer_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessRespComplaintResponse.ProtoReflect.Descriptor instead.
func (*ProcessRespComplaintResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_consumer_service_proto_rawDescGZIP(), []int{17}
}

func (x *ProcessRespComplaintResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_upi_consumer_service_proto protoreflect.FileDescriptor

var file_api_upi_consumer_service_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x03, 0x75, 0x70, 0x69, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x75, 0x74,
	0x68, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69,
	0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb1, 0x01, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f,
	0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x22, 0x60, 0x0a, 0x16, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb0, 0x01, 0x0a, 0x14,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42,
	0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x22, 0x5f,
	0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x50, 0x61, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22,
	0xbc, 0x01, 0x0a, 0x20, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x54, 0x78,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42,
	0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x22, 0x6b,
	0x0a, 0x21, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x54, 0x78, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb7, 0x01, 0x0a, 0x1b,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x56, 0x61, 0x6c, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61,
	0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61,
	0x77, 0x44, 0x61, 0x74, 0x61, 0x22, 0x66, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x56, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb5, 0x01,
	0x0a, 0x19, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x73, 0x70,
	0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61,
	0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61,
	0x77, 0x44, 0x61, 0x74, 0x61, 0x22, 0x64, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x73, 0x70, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb1, 0x01, 0x0a, 0x15,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0c, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x22,
	0x60, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x68, 0x0a, 0x1e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6e, 0x0a, 0x24, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xc5, 0x01, 0x0a, 0x29,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x54, 0x78, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38,
	0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x74, 0x0a, 0x2a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x54, 0x78, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb7, 0x01, 0x0a, 0x1b, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38,
	0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x66, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0xd5, 0x07, 0x0a, 0x08,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x41, 0x75, 0x74, 0x68, 0x12, 0x1a, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x41, 0x75, 0x74, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x50, 0x61, 0x79, 0x12, 0x19, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x73, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x6c, 0x0a, 0x19, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x54, 0x78, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x54, 0x78, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x54, 0x78, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a,
	0x14, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x56, 0x61, 0x6c, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x56, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x56, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x12,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x73, 0x70, 0x4b, 0x65,
	0x79, 0x73, 0x12, 0x1e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x73, 0x70, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x73, 0x70, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x65, 0x12, 0x1a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x67, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x4f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x67, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x1a, 0x23, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x68, 0x0a, 0x1c, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x2e, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x22, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x54, 0x78, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x54, 0x78, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x54, 0x78, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x5d, 0x0a, 0x14, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x20, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x61, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x1a, 0x03,
	0x88, 0x02, 0x01, 0x42, 0x40, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x70, 0x69, 0x5a, 0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x75, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_consumer_service_proto_rawDescOnce sync.Once
	file_api_upi_consumer_service_proto_rawDescData = file_api_upi_consumer_service_proto_rawDesc
)

func file_api_upi_consumer_service_proto_rawDescGZIP() []byte {
	file_api_upi_consumer_service_proto_rawDescOnce.Do(func() {
		file_api_upi_consumer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_consumer_service_proto_rawDescData)
	})
	return file_api_upi_consumer_service_proto_rawDescData
}

var file_api_upi_consumer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_api_upi_consumer_service_proto_goTypes = []interface{}{
	(*ProcessReqAuthRequest)(nil),                      // 0: upi.ProcessReqAuthRequest
	(*ProcessReqAuthResponse)(nil),                     // 1: upi.ProcessReqAuthResponse
	(*ProcessResPayRequest)(nil),                       // 2: upi.ProcessResPayRequest
	(*ProcessResPayResponse)(nil),                      // 3: upi.ProcessResPayResponse
	(*ProcessReqTxnConfirmationRequest)(nil),           // 4: upi.ProcessReqTxnConfirmationRequest
	(*ProcessReqTxnConfirmationResponse)(nil),          // 5: upi.ProcessReqTxnConfirmationResponse
	(*ProcessReqValAddressRequest)(nil),                // 6: upi.ProcessReqValAddressRequest
	(*ProcessReqValAddressResponse)(nil),               // 7: upi.ProcessReqValAddressResponse
	(*ProcessListPspKeysRequest)(nil),                  // 8: upi.ProcessListPspKeysRequest
	(*ProcessListPspKeysResponse)(nil),                 // 9: upi.ProcessListPspKeysResponse
	(*ProcessListVaeRequest)(nil),                      // 10: upi.ProcessListVaeRequest
	(*ProcessListVaeResponse)(nil),                     // 11: upi.ProcessListVaeResponse
	(*ProcessOnboardingEventResponse)(nil),             // 12: upi.ProcessOnboardingEventResponse
	(*ProcessAuthFactorUpdateEventResponse)(nil),       // 13: upi.ProcessAuthFactorUpdateEventResponse
	(*ProcessReqTxnConfirmationComplaintRequest)(nil),  // 14: upi.ProcessReqTxnConfirmationComplaintRequest
	(*ProcessReqTxnConfirmationComplaintResponse)(nil), // 15: upi.ProcessReqTxnConfirmationComplaintResponse
	(*ProcessRespComplaintRequest)(nil),                // 16: upi.ProcessRespComplaintRequest
	(*ProcessRespComplaintResponse)(nil),               // 17: upi.ProcessRespComplaintResponse
	(*queue.ConsumerRequestHeader)(nil),                // 18: queue.ConsumerRequestHeader
	(vendorgateway.Vendor)(0),                          // 19: vendorgateway.Vendor
	(*queue.ConsumerResponseHeader)(nil),               // 20: queue.ConsumerResponseHeader
	(*onboarding.OnboardingStageUpdate)(nil),           // 21: user.onboarding.OnboardingStageUpdate
	(*notification.AuthFactorUpdateEvent)(nil),         // 22: auth.AuthFactorUpdateEvent
}
var file_api_upi_consumer_service_proto_depIdxs = []int32{
	18, // 0: upi.ProcessReqAuthRequest.request_header:type_name -> queue.ConsumerRequestHeader
	19, // 1: upi.ProcessReqAuthRequest.partner_bank:type_name -> vendorgateway.Vendor
	20, // 2: upi.ProcessReqAuthResponse.response_header:type_name -> queue.ConsumerResponseHeader
	18, // 3: upi.ProcessResPayRequest.request_header:type_name -> queue.ConsumerRequestHeader
	19, // 4: upi.ProcessResPayRequest.partner_bank:type_name -> vendorgateway.Vendor
	20, // 5: upi.ProcessResPayResponse.response_header:type_name -> queue.ConsumerResponseHeader
	18, // 6: upi.ProcessReqTxnConfirmationRequest.request_header:type_name -> queue.ConsumerRequestHeader
	19, // 7: upi.ProcessReqTxnConfirmationRequest.partner_bank:type_name -> vendorgateway.Vendor
	20, // 8: upi.ProcessReqTxnConfirmationResponse.response_header:type_name -> queue.ConsumerResponseHeader
	18, // 9: upi.ProcessReqValAddressRequest.request_header:type_name -> queue.ConsumerRequestHeader
	19, // 10: upi.ProcessReqValAddressRequest.partner_bank:type_name -> vendorgateway.Vendor
	20, // 11: upi.ProcessReqValAddressResponse.response_header:type_name -> queue.ConsumerResponseHeader
	18, // 12: upi.ProcessListPspKeysRequest.request_header:type_name -> queue.ConsumerRequestHeader
	19, // 13: upi.ProcessListPspKeysRequest.partner_bank:type_name -> vendorgateway.Vendor
	20, // 14: upi.ProcessListPspKeysResponse.response_header:type_name -> queue.ConsumerResponseHeader
	18, // 15: upi.ProcessListVaeRequest.request_header:type_name -> queue.ConsumerRequestHeader
	19, // 16: upi.ProcessListVaeRequest.partner_bank:type_name -> vendorgateway.Vendor
	20, // 17: upi.ProcessListVaeResponse.response_header:type_name -> queue.ConsumerResponseHeader
	20, // 18: upi.ProcessOnboardingEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	20, // 19: upi.ProcessAuthFactorUpdateEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	18, // 20: upi.ProcessReqTxnConfirmationComplaintRequest.request_header:type_name -> queue.ConsumerRequestHeader
	19, // 21: upi.ProcessReqTxnConfirmationComplaintRequest.partner_bank:type_name -> vendorgateway.Vendor
	20, // 22: upi.ProcessReqTxnConfirmationComplaintResponse.response_header:type_name -> queue.ConsumerResponseHeader
	18, // 23: upi.ProcessRespComplaintRequest.request_header:type_name -> queue.ConsumerRequestHeader
	19, // 24: upi.ProcessRespComplaintRequest.partner_bank:type_name -> vendorgateway.Vendor
	20, // 25: upi.ProcessRespComplaintResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0,  // 26: upi.Consumer.ProcessReqAuth:input_type -> upi.ProcessReqAuthRequest
	2,  // 27: upi.Consumer.ProcessResPay:input_type -> upi.ProcessResPayRequest
	4,  // 28: upi.Consumer.ProcessReqTxnConfirmation:input_type -> upi.ProcessReqTxnConfirmationRequest
	6,  // 29: upi.Consumer.ProcessReqValAddress:input_type -> upi.ProcessReqValAddressRequest
	8,  // 30: upi.Consumer.ProcessListPspKeys:input_type -> upi.ProcessListPspKeysRequest
	10, // 31: upi.Consumer.ProcessListVae:input_type -> upi.ProcessListVaeRequest
	21, // 32: upi.Consumer.ProcessOnboardingEvent:input_type -> user.onboarding.OnboardingStageUpdate
	22, // 33: upi.Consumer.ProcessAuthFactorUpdateEvent:input_type -> auth.AuthFactorUpdateEvent
	14, // 34: upi.Consumer.ProcessReqTxnConfirmationComplaint:input_type -> upi.ProcessReqTxnConfirmationComplaintRequest
	16, // 35: upi.Consumer.ProcessRespComplaint:input_type -> upi.ProcessRespComplaintRequest
	1,  // 36: upi.Consumer.ProcessReqAuth:output_type -> upi.ProcessReqAuthResponse
	3,  // 37: upi.Consumer.ProcessResPay:output_type -> upi.ProcessResPayResponse
	5,  // 38: upi.Consumer.ProcessReqTxnConfirmation:output_type -> upi.ProcessReqTxnConfirmationResponse
	7,  // 39: upi.Consumer.ProcessReqValAddress:output_type -> upi.ProcessReqValAddressResponse
	9,  // 40: upi.Consumer.ProcessListPspKeys:output_type -> upi.ProcessListPspKeysResponse
	11, // 41: upi.Consumer.ProcessListVae:output_type -> upi.ProcessListVaeResponse
	12, // 42: upi.Consumer.ProcessOnboardingEvent:output_type -> upi.ProcessOnboardingEventResponse
	13, // 43: upi.Consumer.ProcessAuthFactorUpdateEvent:output_type -> upi.ProcessAuthFactorUpdateEventResponse
	15, // 44: upi.Consumer.ProcessReqTxnConfirmationComplaint:output_type -> upi.ProcessReqTxnConfirmationComplaintResponse
	17, // 45: upi.Consumer.ProcessRespComplaint:output_type -> upi.ProcessRespComplaintResponse
	36, // [36:46] is the sub-list for method output_type
	26, // [26:36] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_api_upi_consumer_service_proto_init() }
func file_api_upi_consumer_service_proto_init() {
	if File_api_upi_consumer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_consumer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqAuthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessResPayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessResPayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqTxnConfirmationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqTxnConfirmationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqValAddressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqValAddressResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessListPspKeysRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessListPspKeysResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessListVaeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessListVaeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessOnboardingEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAuthFactorUpdateEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqTxnConfirmationComplaintRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqTxnConfirmationComplaintResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessRespComplaintRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_consumer_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessRespComplaintResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_consumer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_upi_consumer_service_proto_goTypes,
		DependencyIndexes: file_api_upi_consumer_service_proto_depIdxs,
		MessageInfos:      file_api_upi_consumer_service_proto_msgTypes,
	}.Build()
	File_api_upi_consumer_service_proto = out.File
	file_api_upi_consumer_service_proto_rawDesc = nil
	file_api_upi_consumer_service_proto_goTypes = nil
	file_api_upi_consumer_service_proto_depIdxs = nil
}
