// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/upi/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	VerifyVpaMethod                              = "VerifyVpa"
	ProcessUserDevicePropertiesUpdateEventMethod = "ProcessUserDevicePropertiesUpdateEvent"
	ProcessOrderUpdateEventForComplianceMethod   = "ProcessOrderUpdateEventForCompliance"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &VerifyVpaRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *VerifyVpaRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterVerifyVpaMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterVerifyVpaMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, VerifyVpaMethod)
}

// RegisterProcessUserDevicePropertiesUpdateEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessUserDevicePropertiesUpdateEventMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessUserDevicePropertiesUpdateEventMethod)
}

// RegisterProcessOrderUpdateEventForComplianceMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessOrderUpdateEventForComplianceMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessOrderUpdateEventForComplianceMethod)
}
