package upi

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"

	"golang.org/x/net/context"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

// GetUPIAccountDetails returns customer account details for UPI pi
func GetUPIAccountDetails(p *piPb.PaymentInstrument) (*CustomerAccountDetails, error) {
	switch p.GetType() {
	case piPb.PaymentInstrumentType_UPI:
		return &CustomerAccountDetails{
			AccountNumber: p.GetUpi().GetAccountReferenceNumber(),
			Ifsc:          p.GetUpi().GetIfscCode(),
			Type:          p.GetUpi().GetAccountType(),
			Apo:           p.GetUpi().GetApo(),
		}, nil
	default:
		return nil, fmt.Errorf("expected PI of type UPI got %s", p.GetType())
	}
}

// GetUPICustomerInfo returns customer identity info from upi pi
func GetUPICustomerInfo(p *piPb.PaymentInstrument, ph *commontypes.PhoneNumber) (*CustomerInformation, error) {
	switch p.GetType() {
	case piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_UPI_LITE:
		return &CustomerInformation{
			Identity: &CustomerInformation_Identity{
				Id:           ph.ToString(),
				Type:         CustomerInformation_Identity_ACCOUNT,
				VerifiedName: p.GetName(),
			},
			RatingVerified: true, // TODO(nitesh): figure out when can this rating be false
		}, nil
	default:
		return nil, fmt.Errorf("expected PI of type UPI got %s", p.GetType())
	}
}

// GetUpiLiteAccountDetails -returns customer account details for UPI Lite pi
func GetUpiLiteAccountDetails(ctx context.Context, pi *piPb.PaymentInstrument, piClient piPb.PiClient) (*CustomerAccountDetails, error) {
	if pi.GetType() != piPb.PaymentInstrumentType_UPI_LITE {
		return nil, fmt.Errorf("expected PI of type UPI Lite got %s", pi.GetType())
	}

	piResp, err := piClient.GetPiById(ctx, &piPb.GetPiByIdRequest{
		Id: pi.GetUpiLite().GetPiRefId(),
	})

	if te := epifigrpc.RPCError(piResp, err); te != nil {
		return nil, fmt.Errorf("error in fetching source account pi by id %s %w", pi.GetUpiLite().GetPiRefId(), te)
	}

	return &CustomerAccountDetails{
		AccountNumber: piResp.GetPaymentInstrument().GetUpi().GetAccountReferenceNumber(),
		Ifsc:          piResp.GetPaymentInstrument().GetUpi().GetIfscCode(),
		Type:          piResp.GetPaymentInstrument().GetUpi().GetAccountType(),
		Lrn:           pi.GetUpiLite().GetLrn(),
	}, nil

}
