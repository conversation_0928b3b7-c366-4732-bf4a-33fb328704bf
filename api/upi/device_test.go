package upi_test

import (
	"testing"

	"github.com/epifi/gamma/api/upi"
)

func TestGetDeviceOsVersion(t *testing.T) {
	type args struct {
		app             upi.Device_App
		clientOsVersion string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "android",
			args: args{
				app:             upi.Device_ANDROID,
				clientOsVersion: "11.1",
			},
			want: "Android 11.1",
		},
		{
			name: "ios",
			args: args{
				app:             upi.Device_IOS,
				clientOsVersion: "14.1",
			},
			want: "iOS 14.1",
		},
		{
			name: "device unspecified",
			args: args{
				app:             upi.Device_APP_UNSPECIFIED,
				clientOsVersion: "14.1",
			},
			want: "14.1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := upi.GetDeviceOsVersion(tt.args.app, tt.args.clientOsVersion); got != tt.want {
				t.Errorf("GetDeviceOsVersion() = %v, want %v", got, tt.want)
			}
		})
	}
}
