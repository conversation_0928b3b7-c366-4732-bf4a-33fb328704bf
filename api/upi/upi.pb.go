// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/upi.proto

package upi

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Consolidated upi setup  state
type UpiSetUpState int32

const (
	// Unspecified state
	UpiSetUpState_UPI_SET_UP_STATE_UNSPECIFIED UpiSetUpState = 0
	// INITIATED - Upi account creation was requested and is under processing.
	// The actual request could be either processing at the partner bank or error occurred
	// just after this state.
	UpiSetUpState_INITIATED UpiSetUpState = 1
	// Successfully fetched account details (bank account) from Partner Bank/NPCI
	// And Account reference number is linked to the account
	UpiSetUpState_ACC_REF_LINKED UpiSetUpState = 2
	// Successfully created VPA at partner bank
	UpiSetUpState_VPA_CREATED UpiSetUpState = 3
	// Successfully created PaymentInstrument for VPA
	UpiSetUpState_PI_CREATED UpiSetUpState = 4
	// Successfully AccountPI created for VPA
	UpiSetUpState_PI_ACCOUNT_CREATED UpiSetUpState = 5
	// successfully sent in app notification to set upi pin
	UpiSetUpState_PIN_SET_NOTIFICATION UpiSetUpState = 6
	// successfully sent an sms when vpa is created
	UpiSetUpState_VPA_CREATED_SMS UpiSetUpState = 7
)

// Enum value maps for UpiSetUpState.
var (
	UpiSetUpState_name = map[int32]string{
		0: "UPI_SET_UP_STATE_UNSPECIFIED",
		1: "INITIATED",
		2: "ACC_REF_LINKED",
		3: "VPA_CREATED",
		4: "PI_CREATED",
		5: "PI_ACCOUNT_CREATED",
		6: "PIN_SET_NOTIFICATION",
		7: "VPA_CREATED_SMS",
	}
	UpiSetUpState_value = map[string]int32{
		"UPI_SET_UP_STATE_UNSPECIFIED": 0,
		"INITIATED":                    1,
		"ACC_REF_LINKED":               2,
		"VPA_CREATED":                  3,
		"PI_CREATED":                   4,
		"PI_ACCOUNT_CREATED":           5,
		"PIN_SET_NOTIFICATION":         6,
		"VPA_CREATED_SMS":              7,
	}
)

func (x UpiSetUpState) Enum() *UpiSetUpState {
	p := new(UpiSetUpState)
	*p = x
	return p
}

func (x UpiSetUpState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiSetUpState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[0].Descriptor()
}

func (UpiSetUpState) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[0]
}

func (x UpiSetUpState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiSetUpState.Descriptor instead.
func (UpiSetUpState) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{0}
}

type UpiVersion int32

const (
	UpiVersion_UPI_VERSION_UNSPECIFIED UpiVersion = 0
	// UPI v1.0
	UpiVersion_UPI_VERSION_1 UpiVersion = 1
	// UPI v2.0
	UpiVersion_UPI_VERSION_2 UpiVersion = 2
	// UPI v2.5
	UpiVersion_UPI_VERSION_2_5 UpiVersion = 3
)

// Enum value maps for UpiVersion.
var (
	UpiVersion_name = map[int32]string{
		0: "UPI_VERSION_UNSPECIFIED",
		1: "UPI_VERSION_1",
		2: "UPI_VERSION_2",
		3: "UPI_VERSION_2_5",
	}
	UpiVersion_value = map[string]int32{
		"UPI_VERSION_UNSPECIFIED": 0,
		"UPI_VERSION_1":           1,
		"UPI_VERSION_2":           2,
		"UPI_VERSION_2_5":         3,
	}
)

func (x UpiVersion) Enum() *UpiVersion {
	p := new(UpiVersion)
	*p = x
	return p
}

func (x UpiVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[1].Descriptor()
}

func (UpiVersion) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[1]
}

func (x UpiVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiVersion.Descriptor instead.
func (UpiVersion) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{1}
}

// Types of transaction supported by UPI protocol
// Payments can be initiated by either sender (payer) or receiver (payee)
type TransactionType int32

const (
	TransactionType_TRANSACTION_TYPE_UNSPECIFIED TransactionType = 0
	// The initiating customer pushes funds to the intended beneficiary
	TransactionType_PAY TransactionType = 1
	// The customer pulls funds from the intended remitter using virtual
	// address. The amount in this case gets deducted from remitter post
	// approval
	TransactionType_COLLECT TransactionType = 2
)

// Enum value maps for TransactionType.
var (
	TransactionType_name = map[int32]string{
		0: "TRANSACTION_TYPE_UNSPECIFIED",
		1: "PAY",
		2: "COLLECT",
	}
	TransactionType_value = map[string]int32{
		"TRANSACTION_TYPE_UNSPECIFIED": 0,
		"PAY":                          1,
		"COLLECT":                      2,
	}
)

func (x TransactionType) Enum() *TransactionType {
	p := new(TransactionType)
	*p = x
	return p
}

func (x TransactionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[2].Descriptor()
}

func (TransactionType) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[2]
}

func (x TransactionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionType.Descriptor instead.
func (TransactionType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{2}
}

type UpiAccountInfoFieldMask int32

const (
	UpiAccountInfoFieldMask_UPI_ACCOUNT_INFO_FIELD_MASK_UNSPECIFIED UpiAccountInfoFieldMask = 0
	// PIN_SET_STATE field to update upi pin set state
	UpiAccountInfoFieldMask_PIN_SET_STATE UpiAccountInfoFieldMask = 1
	// The state of the UpiSetUp for an account.
	// State of UPI setup in order
	// INITIATED -> ACC_REF_LINKED -> VPA_CREATED -> PI_CREATED -> PI_ACCOUNT_CREATED
	UpiAccountInfoFieldMask_STATE UpiAccountInfoFieldMask = 2
	// Account ref provided in list account API response
	UpiAccountInfoFieldMask_ACCOUNT_REF UpiAccountInfoFieldMask = 3
	// masked account number against account id
	UpiAccountInfoFieldMask_MASKED_ACCOUNT_NUM         UpiAccountInfoFieldMask = 4
	UpiAccountInfoFieldMask_LIST_ACCOUNT_CALLED_AT     UpiAccountInfoFieldMask = 5
	UpiAccountInfoFieldMask_CONTROL_JSON               UpiAccountInfoFieldMask = 6
	UpiAccountInfoFieldMask_IS_AADHAAR_BANKING_ENABLED UpiAccountInfoFieldMask = 7
	UpiAccountInfoFieldMask_LIST_ACCOUNT_VPA           UpiAccountInfoFieldMask = 8
)

// Enum value maps for UpiAccountInfoFieldMask.
var (
	UpiAccountInfoFieldMask_name = map[int32]string{
		0: "UPI_ACCOUNT_INFO_FIELD_MASK_UNSPECIFIED",
		1: "PIN_SET_STATE",
		2: "STATE",
		3: "ACCOUNT_REF",
		4: "MASKED_ACCOUNT_NUM",
		5: "LIST_ACCOUNT_CALLED_AT",
		6: "CONTROL_JSON",
		7: "IS_AADHAAR_BANKING_ENABLED",
		8: "LIST_ACCOUNT_VPA",
	}
	UpiAccountInfoFieldMask_value = map[string]int32{
		"UPI_ACCOUNT_INFO_FIELD_MASK_UNSPECIFIED": 0,
		"PIN_SET_STATE":              1,
		"STATE":                      2,
		"ACCOUNT_REF":                3,
		"MASKED_ACCOUNT_NUM":         4,
		"LIST_ACCOUNT_CALLED_AT":     5,
		"CONTROL_JSON":               6,
		"IS_AADHAAR_BANKING_ENABLED": 7,
		"LIST_ACCOUNT_VPA":           8,
	}
)

func (x UpiAccountInfoFieldMask) Enum() *UpiAccountInfoFieldMask {
	p := new(UpiAccountInfoFieldMask)
	*p = x
	return p
}

func (x UpiAccountInfoFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiAccountInfoFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[3].Descriptor()
}

func (UpiAccountInfoFieldMask) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[3]
}

func (x UpiAccountInfoFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiAccountInfoFieldMask.Descriptor instead.
func (UpiAccountInfoFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{3}
}

// Enum to represent upi pin set state
type PinSetState int32

const (
	PinSetState_PIN_SET_STATE_UNSPECIFIED PinSetState = 0
	// state for pin not set for first time creation of vpa
	PinSetState_PIN_NOT_SET PinSetState = 1
	// upi pin set state i.e. pin is set for account id
	PinSetState_PIN_SET PinSetState = 2
	// pin not set after re-oobe
	PinSetState_REOOBE_PIN_NOT_SET PinSetState = 3
	// existing to bank user pin set
	// UPI pin is mapped against mobile number at vendor end. This state will be used if user already
	// had an account at vendor and UPI pin was already created for that.
	// This will state will be created only during vpa creation.
	PinSetState_ETB_PIN_SET PinSetState = 4
	// Pin not set state after user made more than 3 wrong auth attempt
	// across various neo-banking and UPI api using secure/upi pin.
	PinSetState_MAX_RETRIES_PIN_NOT_SET PinSetState = 5
	// user has not set the pin for account after device change.
	// Reinstalling app will also be considered as device change
	// NOTE - currently user will need to set the pin after device change only on IOS and not on android
	// as per the new NPCI requirement. (circular - https://drive.google.com/file/d/1N9kkD-tPlafwKcIG4Kwf8Zirfz4PnB3t/view)
	// In this case we will be the source of truth for pin set status and not NPCI. If the user has multiple accounts,
	// the user can set pin for any one account after device change, does not need to re set for other accounts
	PinSetState_PIN_NOT_SET_DEVICE_CHANGE PinSetState = 6
)

// Enum value maps for PinSetState.
var (
	PinSetState_name = map[int32]string{
		0: "PIN_SET_STATE_UNSPECIFIED",
		1: "PIN_NOT_SET",
		2: "PIN_SET",
		3: "REOOBE_PIN_NOT_SET",
		4: "ETB_PIN_SET",
		5: "MAX_RETRIES_PIN_NOT_SET",
		6: "PIN_NOT_SET_DEVICE_CHANGE",
	}
	PinSetState_value = map[string]int32{
		"PIN_SET_STATE_UNSPECIFIED": 0,
		"PIN_NOT_SET":               1,
		"PIN_SET":                   2,
		"REOOBE_PIN_NOT_SET":        3,
		"ETB_PIN_SET":               4,
		"MAX_RETRIES_PIN_NOT_SET":   5,
		"PIN_NOT_SET_DEVICE_CHANGE": 6,
	}
)

func (x PinSetState) Enum() *PinSetState {
	p := new(PinSetState)
	*p = x
	return p
}

func (x PinSetState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PinSetState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[4].Descriptor()
}

func (PinSetState) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[4]
}

func (x PinSetState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PinSetState.Descriptor instead.
func (PinSetState) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{4}
}

// AccountUpiPinInfoFieldMask is the enum representation of all the AccountUpiPinInfo fields.
// Meant to be used as field mask to help with database updates
type AccountUpiPinInfoFieldMask int32

const (
	AccountUpiPinInfoFieldMask_ACCOUNT_UPI_PIN_INFO_FIELD_MASK_UNSPECIFIED AccountUpiPinInfoFieldMask = 0
	AccountUpiPinInfoFieldMask_STATUS                                      AccountUpiPinInfoFieldMask = 1
	AccountUpiPinInfoFieldMask_DETAILED_STATUS_LIST                        AccountUpiPinInfoFieldMask = 2
	AccountUpiPinInfoFieldMask_ID                                          AccountUpiPinInfoFieldMask = 3
	AccountUpiPinInfoFieldMask_ACCOUNT_ID                                  AccountUpiPinInfoFieldMask = 4
	AccountUpiPinInfoFieldMask_USER_ACTION                                 AccountUpiPinInfoFieldMask = 5
	AccountUpiPinInfoFieldMask_CREATED_AT                                  AccountUpiPinInfoFieldMask = 6
	AccountUpiPinInfoFieldMask_UPDATED_AT                                  AccountUpiPinInfoFieldMask = 7
	AccountUpiPinInfoFieldMask_DELETED_AT                                  AccountUpiPinInfoFieldMask = 8
	AccountUpiPinInfoFieldMask_ALL                                         AccountUpiPinInfoFieldMask = 9
)

// Enum value maps for AccountUpiPinInfoFieldMask.
var (
	AccountUpiPinInfoFieldMask_name = map[int32]string{
		0: "ACCOUNT_UPI_PIN_INFO_FIELD_MASK_UNSPECIFIED",
		1: "STATUS",
		2: "DETAILED_STATUS_LIST",
		3: "ID",
		4: "ACCOUNT_ID",
		5: "USER_ACTION",
		6: "CREATED_AT",
		7: "UPDATED_AT",
		8: "DELETED_AT",
		9: "ALL",
	}
	AccountUpiPinInfoFieldMask_value = map[string]int32{
		"ACCOUNT_UPI_PIN_INFO_FIELD_MASK_UNSPECIFIED": 0,
		"STATUS":               1,
		"DETAILED_STATUS_LIST": 2,
		"ID":                   3,
		"ACCOUNT_ID":           4,
		"USER_ACTION":          5,
		"CREATED_AT":           6,
		"UPDATED_AT":           7,
		"DELETED_AT":           8,
		"ALL":                  9,
	}
)

func (x AccountUpiPinInfoFieldMask) Enum() *AccountUpiPinInfoFieldMask {
	p := new(AccountUpiPinInfoFieldMask)
	*p = x
	return p
}

func (x AccountUpiPinInfoFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountUpiPinInfoFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[5].Descriptor()
}

func (AccountUpiPinInfoFieldMask) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[5]
}

func (x AccountUpiPinInfoFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountUpiPinInfoFieldMask.Descriptor instead.
func (AccountUpiPinInfoFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{5}
}

// In register mobile request, expected type varies
// with the type of flow initiated
// E.g. For Debit card initiated flow, expected format is FORMAT2
// Using Aadhaar Number, expected format is FORMAT3
type FormatType int32

const (
	FormatType_FORMAT_TYPE_UNSPECIFIED FormatType = 0
	FormatType_FORMAT_TYPE_FORMAT1     FormatType = 1
	FormatType_FORMAT_TYPE_FORMAT2     FormatType = 2
	FormatType_FORMAT_TYPE_FORMAT3     FormatType = 3
)

// Enum value maps for FormatType.
var (
	FormatType_name = map[int32]string{
		0: "FORMAT_TYPE_UNSPECIFIED",
		1: "FORMAT_TYPE_FORMAT1",
		2: "FORMAT_TYPE_FORMAT2",
		3: "FORMAT_TYPE_FORMAT3",
	}
	FormatType_value = map[string]int32{
		"FORMAT_TYPE_UNSPECIFIED": 0,
		"FORMAT_TYPE_FORMAT1":     1,
		"FORMAT_TYPE_FORMAT2":     2,
		"FORMAT_TYPE_FORMAT3":     3,
	}
)

func (x FormatType) Enum() *FormatType {
	p := new(FormatType)
	*p = x
	return p
}

func (x FormatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FormatType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[6].Descriptor()
}

func (FormatType) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[6]
}

func (x FormatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FormatType.Descriptor instead.
func (FormatType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{6}
}

// UpiInternationalPaymentChargeType - type of charge being
// applied on international upi payment
type UpiInternationalPaymentChargeType int32

const (
	UpiInternationalPaymentChargeType_UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_UNSPECIFIED UpiInternationalPaymentChargeType = 0
	// it denotes transaction breakup is of markup
	UpiInternationalPaymentChargeType_UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_MARKUP UpiInternationalPaymentChargeType = 1
	// it denotes transaction breakup is a GST
	UpiInternationalPaymentChargeType_UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_GST UpiInternationalPaymentChargeType = 2
	// It denotes the base amount without any additional fees/charges
	UpiInternationalPaymentChargeType_UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_AMOUNT UpiInternationalPaymentChargeType = 3
)

// Enum value maps for UpiInternationalPaymentChargeType.
var (
	UpiInternationalPaymentChargeType_name = map[int32]string{
		0: "UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_UNSPECIFIED",
		1: "UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_MARKUP",
		2: "UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_GST",
		3: "UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_AMOUNT",
	}
	UpiInternationalPaymentChargeType_value = map[string]int32{
		"UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_UNSPECIFIED": 0,
		"UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_MARKUP":      1,
		"UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_GST":         2,
		"UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_AMOUNT":      3,
	}
)

func (x UpiInternationalPaymentChargeType) Enum() *UpiInternationalPaymentChargeType {
	p := new(UpiInternationalPaymentChargeType)
	*p = x
	return p
}

func (x UpiInternationalPaymentChargeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiInternationalPaymentChargeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[7].Descriptor()
}

func (UpiInternationalPaymentChargeType) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[7]
}

func (x UpiInternationalPaymentChargeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiInternationalPaymentChargeType.Descriptor instead.
func (UpiInternationalPaymentChargeType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{7}
}

// UpiFlowType - flow type for which NPCI CL params to be computed for
type UpiFlowType int32

const (
	UpiFlowType_UPI_FLOW_TYPE_UNSPECIFIED UpiFlowType = 0
	// It will be used to fetch params required in the flow of fetching the balance of the user
	UpiFlowType_UPI_FLOW_TYPE_BALANCE_ENQUIRY UpiFlowType = 1
	// It will be used to get params to set a PIN for the first time (or) Reset PIN if forgotten
	// User is allowed to set/reset the PIN with card PIN, Otp and Last 6 digits & Expiry of card.
	UpiFlowType_UPI_FLOW_TYPE_SET_PIN UpiFlowType = 2
	// It will be used to get params to change PIN. To be used if user is aware of old PIN
	UpiFlowType_UPI_FLOW_TYPE_CHANGE_PIN UpiFlowType = 3
	// It will be used to get params required for rendering CL UPI PIN page for transaction
	UpiFlowType_UPI_FLOW_TYPE_TRANSACTION UpiFlowType = 4
	// It will be used to get params required for initiating activation / deactivation
	// of international payments for an account
	UpiFlowType_UPI_FLOW_TYPE_INTERNATIONAL_PAYMENTS_ACTION UpiFlowType = 5
	// It will be used to get params required for activation of
	// UPI lite LRN activation
	UpiFlowType_UPI_FLOW_TYPE_LRN_ACTIVATION UpiFlowType = 6
	// It will be used to get params required for upi lite
	// TopUp / Load Money.
	// It will be used for both initial top up
	// during activation and subsequent TopUps.
	UpiFlowType_UPI_FLOW_TYPE_UPI_LITE_TOP_UP UpiFlowType = 7
	// It will be used to get params required for all
	// transactions done via upi lite account / wallet
	UpiFlowType_UPI_FLOW_TYPE_UPI_LITE_PAYMENTS UpiFlowType = 8
	// It will be used to get params required for all
	// transactions done during upi lite deactivation
	UpiFlowType_UPI_FLOW_TYPE_DEACTIVATE_UPI_LITE UpiFlowType = 9
	// It will be used to get params required to fetch
	// upi lite account balance from CL.
	UpiFlowType_UPI_FLOW_TYPE_UPI_LITE_BALANCE UpiFlowType = 10
)

// Enum value maps for UpiFlowType.
var (
	UpiFlowType_name = map[int32]string{
		0:  "UPI_FLOW_TYPE_UNSPECIFIED",
		1:  "UPI_FLOW_TYPE_BALANCE_ENQUIRY",
		2:  "UPI_FLOW_TYPE_SET_PIN",
		3:  "UPI_FLOW_TYPE_CHANGE_PIN",
		4:  "UPI_FLOW_TYPE_TRANSACTION",
		5:  "UPI_FLOW_TYPE_INTERNATIONAL_PAYMENTS_ACTION",
		6:  "UPI_FLOW_TYPE_LRN_ACTIVATION",
		7:  "UPI_FLOW_TYPE_UPI_LITE_TOP_UP",
		8:  "UPI_FLOW_TYPE_UPI_LITE_PAYMENTS",
		9:  "UPI_FLOW_TYPE_DEACTIVATE_UPI_LITE",
		10: "UPI_FLOW_TYPE_UPI_LITE_BALANCE",
	}
	UpiFlowType_value = map[string]int32{
		"UPI_FLOW_TYPE_UNSPECIFIED":                   0,
		"UPI_FLOW_TYPE_BALANCE_ENQUIRY":               1,
		"UPI_FLOW_TYPE_SET_PIN":                       2,
		"UPI_FLOW_TYPE_CHANGE_PIN":                    3,
		"UPI_FLOW_TYPE_TRANSACTION":                   4,
		"UPI_FLOW_TYPE_INTERNATIONAL_PAYMENTS_ACTION": 5,
		"UPI_FLOW_TYPE_LRN_ACTIVATION":                6,
		"UPI_FLOW_TYPE_UPI_LITE_TOP_UP":               7,
		"UPI_FLOW_TYPE_UPI_LITE_PAYMENTS":             8,
		"UPI_FLOW_TYPE_DEACTIVATE_UPI_LITE":           9,
		"UPI_FLOW_TYPE_UPI_LITE_BALANCE":              10,
	}
)

func (x UpiFlowType) Enum() *UpiFlowType {
	p := new(UpiFlowType)
	*p = x
	return p
}

func (x UpiFlowType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiFlowType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[8].Descriptor()
}

func (UpiFlowType) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[8]
}

func (x UpiFlowType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiFlowType.Descriptor instead.
func (UpiFlowType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{8}
}

// UpiPinSetOptionType - represents the options using which user can set/reset pin
// required by the client to recognize the available options
type UpiPinSetOptionType int32

const (
	UpiPinSetOptionType_UPI_PIN_SET_OPTION_TYPE_UNSPECIFIED    UpiPinSetOptionType = 0
	UpiPinSetOptionType_UPI_PIN_SET_OPTION_TYPE_DEBIT_CARD     UpiPinSetOptionType = 1
	UpiPinSetOptionType_UPI_PIN_SET_OPTION_TYPE_AADHAAR_NUMBER UpiPinSetOptionType = 2
)

// Enum value maps for UpiPinSetOptionType.
var (
	UpiPinSetOptionType_name = map[int32]string{
		0: "UPI_PIN_SET_OPTION_TYPE_UNSPECIFIED",
		1: "UPI_PIN_SET_OPTION_TYPE_DEBIT_CARD",
		2: "UPI_PIN_SET_OPTION_TYPE_AADHAAR_NUMBER",
	}
	UpiPinSetOptionType_value = map[string]int32{
		"UPI_PIN_SET_OPTION_TYPE_UNSPECIFIED":    0,
		"UPI_PIN_SET_OPTION_TYPE_DEBIT_CARD":     1,
		"UPI_PIN_SET_OPTION_TYPE_AADHAAR_NUMBER": 2,
	}
)

func (x UpiPinSetOptionType) Enum() *UpiPinSetOptionType {
	p := new(UpiPinSetOptionType)
	*p = x
	return p
}

func (x UpiPinSetOptionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiPinSetOptionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[9].Descriptor()
}

func (UpiPinSetOptionType) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[9]
}

func (x UpiPinSetOptionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiPinSetOptionType.Descriptor instead.
func (UpiPinSetOptionType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{9}
}

type AccountUpiPinInfo_Action int32

const (
	// unspecified
	AccountUpiPinInfo_ACTION_UNSPECIFIED AccountUpiPinInfo_Action = 0
	// pin set
	// pin was set for the first time by the user
	AccountUpiPinInfo_PIN_SET AccountUpiPinInfo_Action = 1
	// pin was changed by the user
	AccountUpiPinInfo_PIN_CHANGE AccountUpiPinInfo_Action = 2
	// pin was reset by the user
	// eg. forget pin
	AccountUpiPinInfo_PIN_RESET AccountUpiPinInfo_Action = 3
)

// Enum value maps for AccountUpiPinInfo_Action.
var (
	AccountUpiPinInfo_Action_name = map[int32]string{
		0: "ACTION_UNSPECIFIED",
		1: "PIN_SET",
		2: "PIN_CHANGE",
		3: "PIN_RESET",
	}
	AccountUpiPinInfo_Action_value = map[string]int32{
		"ACTION_UNSPECIFIED": 0,
		"PIN_SET":            1,
		"PIN_CHANGE":         2,
		"PIN_RESET":          3,
	}
)

func (x AccountUpiPinInfo_Action) Enum() *AccountUpiPinInfo_Action {
	p := new(AccountUpiPinInfo_Action)
	*p = x
	return p
}

func (x AccountUpiPinInfo_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountUpiPinInfo_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[10].Descriptor()
}

func (AccountUpiPinInfo_Action) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[10]
}

func (x AccountUpiPinInfo_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountUpiPinInfo_Action.Descriptor instead.
func (AccountUpiPinInfo_Action) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{5, 0}
}

type AccountUpiPinInfo_Status int32

const (
	AccountUpiPinInfo_STATUS_UNSPECIFIED AccountUpiPinInfo_Status = 0
	// pin set/reset is initiated
	AccountUpiPinInfo_INITIATED AccountUpiPinInfo_Status = 1
	// pin set/reset is successful
	AccountUpiPinInfo_SUCCESS AccountUpiPinInfo_Status = 2
	// pin set/reset is fail
	AccountUpiPinInfo_FAILURE AccountUpiPinInfo_Status = 3
)

// Enum value maps for AccountUpiPinInfo_Status.
var (
	AccountUpiPinInfo_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "INITIATED",
		2: "SUCCESS",
		3: "FAILURE",
	}
	AccountUpiPinInfo_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"INITIATED":          1,
		"SUCCESS":            2,
		"FAILURE":            3,
	}
)

func (x AccountUpiPinInfo_Status) Enum() *AccountUpiPinInfo_Status {
	p := new(AccountUpiPinInfo_Status)
	*p = x
	return p
}

func (x AccountUpiPinInfo_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountUpiPinInfo_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[11].Descriptor()
}

func (AccountUpiPinInfo_Status) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[11]
}

func (x AccountUpiPinInfo_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountUpiPinInfo_Status.Descriptor instead.
func (AccountUpiPinInfo_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{5, 1}
}

type RegIdDetails_RegIdStatus int32

const (
	// unspecified
	RegIdDetails_REGID_STATUS_UNSPECIFIED RegIdDetails_RegIdStatus = 0
	// registering the upi number for first time
	RegIdDetails_REGID_STATUS_NEW RegIdDetails_RegIdStatus = 1
	// Activating existing upi number
	RegIdDetails_REGID_STATUS_ACTIVE RegIdDetails_RegIdStatus = 2
	// De activating exising upi number
	// Deactivated upi numbers will belong to the user
	//
	//	but cannot be used for payments
	RegIdDetails_REGID_STATUS_INACTIVE RegIdDetails_RegIdStatus = 3
	// Deregister the upi number
	// Deregistered upi numbers will be recycled to other users after some expiry
	RegIdDetails_REGID_STATUS_DEREGISTER RegIdDetails_RegIdStatus = 4
)

// Enum value maps for RegIdDetails_RegIdStatus.
var (
	RegIdDetails_RegIdStatus_name = map[int32]string{
		0: "REGID_STATUS_UNSPECIFIED",
		1: "REGID_STATUS_NEW",
		2: "REGID_STATUS_ACTIVE",
		3: "REGID_STATUS_INACTIVE",
		4: "REGID_STATUS_DEREGISTER",
	}
	RegIdDetails_RegIdStatus_value = map[string]int32{
		"REGID_STATUS_UNSPECIFIED": 0,
		"REGID_STATUS_NEW":         1,
		"REGID_STATUS_ACTIVE":      2,
		"REGID_STATUS_INACTIVE":    3,
		"REGID_STATUS_DEREGISTER":  4,
	}
)

func (x RegIdDetails_RegIdStatus) Enum() *RegIdDetails_RegIdStatus {
	p := new(RegIdDetails_RegIdStatus)
	*p = x
	return p
}

func (x RegIdDetails_RegIdStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RegIdDetails_RegIdStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_upi_proto_enumTypes[12].Descriptor()
}

func (RegIdDetails_RegIdStatus) Type() protoreflect.EnumType {
	return &file_api_upi_upi_proto_enumTypes[12]
}

func (x RegIdDetails_RegIdStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RegIdDetails_RegIdStatus.Descriptor instead.
func (RegIdDetails_RegIdStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{9, 0}
}

// UpiAccountInfo defines list of field specific to a account_id and vpa
// This information required for mobile banking registration / pin set flow.
type UpiAccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// AccountId generated while user account creation
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Json string received from calling list account API of UPI
	ControlJson *ControlJson `protobuf:"bytes,2,opt,name=control_json,json=controlJson,proto3" json:"control_json,omitempty"`
	// enum to mark upi pin set state
	PinSetState PinSetState `protobuf:"varint,3,opt,name=pin_set_state,json=pinSetState,proto3,enum=upi.PinSetState" json:"pin_set_state,omitempty"`
	// boolean value to mark Adhaar banking is enabled or not
	IsAadhaarBankingEnabled bool `protobuf:"varint,4,opt,name=is_aadhaar_banking_enabled,json=isAadhaarBankingEnabled,proto3" json:"is_aadhaar_banking_enabled,omitempty"`
	// vpa address allocated for account_id
	Vpa string `protobuf:"bytes,5,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// time of creation of account info
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// time of deletion deleted time
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// IFSC code of account
	IfscCode string `protobuf:"bytes,10,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	// encrypted account number
	AccountRef string `protobuf:"bytes,11,opt,name=account_ref,json=accountRef,proto3" json:"account_ref,omitempty"`
	// masked  account number
	MaskedAccountNumber string `protobuf:"bytes,12,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	// The state of the UpiSetUp for an account.
	// State of UPI setup in order
	//
	//	INITIATED -> ACC_REF_LINKED -> VPA_CREATED -> PI_CREATED -> PI_ACCOUNT_CREATED
	State UpiSetUpState `protobuf:"varint,13,opt,name=state,proto3,enum=upi.UpiSetUpState" json:"state,omitempty"`
	// last/previous list account api trigger time.
	ListAccountCalledAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=list_account_called_at,json=listAccountCalledAt,proto3" json:"list_account_called_at,omitempty"`
}

func (x *UpiAccountInfo) Reset() {
	*x = UpiAccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiAccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiAccountInfo) ProtoMessage() {}

func (x *UpiAccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiAccountInfo.ProtoReflect.Descriptor instead.
func (*UpiAccountInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{0}
}

func (x *UpiAccountInfo) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *UpiAccountInfo) GetControlJson() *ControlJson {
	if x != nil {
		return x.ControlJson
	}
	return nil
}

func (x *UpiAccountInfo) GetPinSetState() PinSetState {
	if x != nil {
		return x.PinSetState
	}
	return PinSetState_PIN_SET_STATE_UNSPECIFIED
}

func (x *UpiAccountInfo) GetIsAadhaarBankingEnabled() bool {
	if x != nil {
		return x.IsAadhaarBankingEnabled
	}
	return false
}

func (x *UpiAccountInfo) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *UpiAccountInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UpiAccountInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UpiAccountInfo) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *UpiAccountInfo) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *UpiAccountInfo) GetAccountRef() string {
	if x != nil {
		return x.AccountRef
	}
	return ""
}

func (x *UpiAccountInfo) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

func (x *UpiAccountInfo) GetState() UpiSetUpState {
	if x != nil {
		return x.State
	}
	return UpiSetUpState_UPI_SET_UP_STATE_UNSPECIFIED
}

func (x *UpiAccountInfo) GetListAccountCalledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ListAccountCalledAt
	}
	return nil
}

// Control json hold array of allowed cred, expire at for international payments.
// {
// "CredAllowed":[{"dLength": "4", "dType": "NUM", "subtype": "MPIN", "type": "PIN"}],
// "InternationalPaymentsExpireAt": "9999-12-31T23:59:59Z",
// "aeba": "Y" or aeba:"N"
// }
type ControlJson struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CredAlloweds []*CredAllowed `protobuf:"bytes,1,rep,name=cred_alloweds,json=CredAllowed,proto3" json:"cred_alloweds,omitempty"`
	// international_payments_expires_at - is the expiry timestamp for
	// international payments for an account
	InternationalPaymentsExpiresAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=international_payments_expires_at,json=InternationalPaymentsExpiresAt,proto3" json:"international_payments_expires_at,omitempty"`
	// `aeba` (Aadhaar enabled bank account). Received in response of ListAccount.
	IsAadhaarEnabledBankAccount bool `protobuf:"varint,3,opt,name=is_aadhaar_enabled_bank_account,json=IsAadhaarEnabledBankAccount,proto3" json:"is_aadhaar_enabled_bank_account,omitempty"`
	// international_payments_activated_at - is the activation timestamp for
	// international payments for an account
	InternationalPaymentsActivatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=international_payments_activated_at,json=InternationalPaymentsActivatedAt,proto3" json:"international_payments_activated_at,omitempty"`
}

func (x *ControlJson) Reset() {
	*x = ControlJson{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ControlJson) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlJson) ProtoMessage() {}

func (x *ControlJson) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlJson.ProtoReflect.Descriptor instead.
func (*ControlJson) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{1}
}

func (x *ControlJson) GetCredAlloweds() []*CredAllowed {
	if x != nil {
		return x.CredAlloweds
	}
	return nil
}

func (x *ControlJson) GetInternationalPaymentsExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InternationalPaymentsExpiresAt
	}
	return nil
}

func (x *ControlJson) GetIsAadhaarEnabledBankAccount() bool {
	if x != nil {
		return x.IsAadhaarEnabledBankAccount
	}
	return false
}

func (x *ControlJson) GetInternationalPaymentsActivatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InternationalPaymentsActivatedAt
	}
	return nil
}

type CredAllowed struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Length of allowed cred input field
	DLength string `protobuf:"bytes,1,opt,name=d_length,json=dLength,proto3" json:"d_length,omitempty"`
	// data type of allowed cred input field i.e. NUM/ALPH
	DType string `protobuf:"bytes,2,opt,name=d_type,json=dType,proto3" json:"d_type,omitempty"`
	// subtype of allowed cred
	SubType string `protobuf:"bytes,3,opt,name=sub_type,json=subtype,proto3" json:"sub_type,omitempty"`
	// type of allowed cred
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *CredAllowed) Reset() {
	*x = CredAllowed{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CredAllowed) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CredAllowed) ProtoMessage() {}

func (x *CredAllowed) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CredAllowed.ProtoReflect.Descriptor instead.
func (*CredAllowed) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{2}
}

func (x *CredAllowed) GetDLength() string {
	if x != nil {
		return x.DLength
	}
	return ""
}

func (x *CredAllowed) GetDType() string {
	if x != nil {
		return x.DType
	}
	return ""
}

func (x *CredAllowed) GetSubType() string {
	if x != nil {
		return x.SubType
	}
	return ""
}

func (x *CredAllowed) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type PspKey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto generate id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Account provider code like ex. NPCI, 400021 etc
	PspOrgId string `protobuf:"bytes,2,opt,name=pspOrgId,proto3" json:"pspOrgId,omitempty"`
	// Type of the Key ex. PKI
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// Key Index Date (yyyymmdd)
	Ki string `protobuf:"bytes,4,opt,name=ki,proto3" json:"ki,omitempty"`
	// base64 encoded key
	KeyValue string `protobuf:"bytes,5,opt,name=key_value,json=keyValue,proto3" json:"key_value,omitempty"`
	// Owner of the Key ex, NPCI/UIDAI
	Owner string `protobuf:"bytes,6,opt,name=owner,proto3" json:"owner,omitempty"`
	// time of creation of
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *PspKey) Reset() {
	*x = PspKey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PspKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PspKey) ProtoMessage() {}

func (x *PspKey) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PspKey.ProtoReflect.Descriptor instead.
func (*PspKey) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{3}
}

func (x *PspKey) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PspKey) GetPspOrgId() string {
	if x != nil {
		return x.PspOrgId
	}
	return ""
}

func (x *PspKey) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PspKey) GetKi() string {
	if x != nil {
		return x.Ki
	}
	return ""
}

func (x *PspKey) GetKeyValue() string {
	if x != nil {
		return x.KeyValue
	}
	return ""
}

func (x *PspKey) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *PspKey) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PspKey) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type VerifiedAddressEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto increment id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// vpa address of merchant
	MerchantVpa string `protobuf:"bytes,2,opt,name=merchant_vpa,json=merchantVpa,proto3" json:"merchant_vpa,omitempty"`
	// name of merchant/store
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// URL Link provided by Merchant
	Url string `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	// Account provider code like ex. NPCI
	KeyCode string `protobuf:"bytes,5,opt,name=key_code,json=keyCode,proto3" json:"key_code,omitempty"`
	// Type of the Key ex. PKI
	Type string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	// Key Index Date (yyyymmdd)
	Ki string `protobuf:"bytes,7,opt,name=ki,proto3" json:"ki,omitempty"`
	// base64 encoded key
	KeyValue string `protobuf:"bytes,8,opt,name=key_value,json=keyValue,proto3" json:"key_value,omitempty"`
	// time of creation of
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *VerifiedAddressEntry) Reset() {
	*x = VerifiedAddressEntry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifiedAddressEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifiedAddressEntry) ProtoMessage() {}

func (x *VerifiedAddressEntry) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifiedAddressEntry.ProtoReflect.Descriptor instead.
func (*VerifiedAddressEntry) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{4}
}

func (x *VerifiedAddressEntry) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VerifiedAddressEntry) GetMerchantVpa() string {
	if x != nil {
		return x.MerchantVpa
	}
	return ""
}

func (x *VerifiedAddressEntry) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VerifiedAddressEntry) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *VerifiedAddressEntry) GetKeyCode() string {
	if x != nil {
		return x.KeyCode
	}
	return ""
}

func (x *VerifiedAddressEntry) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *VerifiedAddressEntry) GetKi() string {
	if x != nil {
		return x.Ki
	}
	return ""
}

func (x *VerifiedAddressEntry) GetKeyValue() string {
	if x != nil {
		return x.KeyValue
	}
	return ""
}

func (x *VerifiedAddressEntry) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VerifiedAddressEntry) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Account Upi pin info contains upi pin related info like creation time, action done by tge user
// to set/reset the pin
type AccountUpiPinInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto generate id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// account id for corresponding to the upi pin
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// action taken by the user due to whic this entry is created.
	UserAction AccountUpiPinInfo_Action `protobuf:"varint,3,opt,name=user_action,json=userAction,proto3,enum=upi.AccountUpiPinInfo_Action" json:"user_action,omitempty"`
	// time of creation of the upi pin info
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// time of updation of the upi pin info
	UpdateAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`
	// time of deletion of the upi pin info
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// status of pin set request
	Status AccountUpiPinInfo_Status `protobuf:"varint,7,opt,name=status,proto3,enum=upi.AccountUpiPinInfo_Status" json:"status,omitempty"`
	// list of status received for pin info
	DetailedStatusList *AccountUpiPinInfo_DetailedStatusList `protobuf:"bytes,8,opt,name=detailed_status_list,json=detailedStatusList,proto3" json:"detailed_status_list,omitempty"`
	// vendor request id used to make upi pin set request
	VendorRequestId string `protobuf:"bytes,9,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
}

func (x *AccountUpiPinInfo) Reset() {
	*x = AccountUpiPinInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountUpiPinInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountUpiPinInfo) ProtoMessage() {}

func (x *AccountUpiPinInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountUpiPinInfo.ProtoReflect.Descriptor instead.
func (*AccountUpiPinInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{5}
}

func (x *AccountUpiPinInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AccountUpiPinInfo) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AccountUpiPinInfo) GetUserAction() AccountUpiPinInfo_Action {
	if x != nil {
		return x.UserAction
	}
	return AccountUpiPinInfo_ACTION_UNSPECIFIED
}

func (x *AccountUpiPinInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AccountUpiPinInfo) GetUpdateAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateAt
	}
	return nil
}

func (x *AccountUpiPinInfo) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *AccountUpiPinInfo) GetStatus() AccountUpiPinInfo_Status {
	if x != nil {
		return x.Status
	}
	return AccountUpiPinInfo_STATUS_UNSPECIFIED
}

func (x *AccountUpiPinInfo) GetDetailedStatusList() *AccountUpiPinInfo_DetailedStatusList {
	if x != nil {
		return x.DetailedStatusList
	}
	return nil
}

func (x *AccountUpiPinInfo) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

// detailed status to map and store vg response status in db
type DetailedStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status code received from vendor
	RawStatusCode string `protobuf:"bytes,1,opt,name=raw_status_code,json=rawStatusCode,proto3" json:"raw_status_code,omitempty"`
	// description of the status code as sent by the vendor bank
	RawStatusDescription string `protobuf:"bytes,2,opt,name=raw_status_description,json=rawStatusDescription,proto3" json:"raw_status_description,omitempty"`
}

func (x *DetailedStatus) Reset() {
	*x = DetailedStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailedStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailedStatus) ProtoMessage() {}

func (x *DetailedStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailedStatus.ProtoReflect.Descriptor instead.
func (*DetailedStatus) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{6}
}

func (x *DetailedStatus) GetRawStatusCode() string {
	if x != nil {
		return x.RawStatusCode
	}
	return ""
}

func (x *DetailedStatus) GetRawStatusDescription() string {
	if x != nil {
		return x.RawStatusDescription
	}
	return ""
}

type UpiProcessedPhoneNumber struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// phone number whose corresponding vpa is verified
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,1,opt,name=phoneNumber,proto3" json:"phoneNumber,omitempty"`
	// time at which VPAs for phone number were verified
	VerifiedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=verified_at,json=verifiedAt,proto3" json:"verified_at,omitempty"`
	// time of creation of verified phone number vpa
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// time of deletion deleted time
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// unique identifier for each row
	Id string `protobuf:"bytes,6,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UpiProcessedPhoneNumber) Reset() {
	*x = UpiProcessedPhoneNumber{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiProcessedPhoneNumber) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiProcessedPhoneNumber) ProtoMessage() {}

func (x *UpiProcessedPhoneNumber) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiProcessedPhoneNumber.ProtoReflect.Descriptor instead.
func (*UpiProcessedPhoneNumber) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{7}
}

func (x *UpiProcessedPhoneNumber) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *UpiProcessedPhoneNumber) GetVerifiedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.VerifiedAt
	}
	return nil
}

func (x *UpiProcessedPhoneNumber) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UpiProcessedPhoneNumber) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UpiProcessedPhoneNumber) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *UpiProcessedPhoneNumber) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// info of the different PSPs used to populate the chat heads
type VpaInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the user (payee)
	PayeeName string `protobuf:"bytes,1,opt,name=payeeName,proto3" json:"payeeName,omitempty"`
	// icon of the psp
	PspBadgeIcon *common.Image `protobuf:"bytes,2,opt,name=psp_badge_icon,json=pspBadgeIcon,proto3" json:"psp_badge_icon,omitempty"`
	// upi id of the user
	Vpa string `protobuf:"bytes,3,opt,name=vpa,proto3" json:"vpa,omitempty"`
}

func (x *VpaInfo) Reset() {
	*x = VpaInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VpaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VpaInfo) ProtoMessage() {}

func (x *VpaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VpaInfo.ProtoReflect.Descriptor instead.
func (*VpaInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{8}
}

func (x *VpaInfo) GetPayeeName() string {
	if x != nil {
		return x.PayeeName
	}
	return ""
}

func (x *VpaInfo) GetPspBadgeIcon() *common.Image {
	if x != nil {
		return x.PspBadgeIcon
	}
	return nil
}

func (x *VpaInfo) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

type RegIdDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name can be NUMERICID/MOBILE depending on if the user wants to register
	// numeric id or mobile number
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// numeric id/mobile number the user wants to register
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// status to be set fot the upi number with vendor
	SetStatus RegIdDetails_RegIdStatus `protobuf:"varint,3,opt,name=set_status,json=setStatus,proto3,enum=upi.RegIdDetails_RegIdStatus" json:"set_status,omitempty"`
	// address linked with the upi number
	// OPTIONAL - will be present on if RegIdDetails is part of the response
	Addr string `protobuf:"bytes,4,opt,name=Addr,proto3" json:"Addr,omitempty"`
	// in case the upi number is deregistered, NPCI will pass expiry time in the response
	// after which the upi number can be recycled to other users
	ExpiryTs *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=expiry_ts,json=expiryTs,proto3" json:"expiry_ts,omitempty"`
	// last modified date of the account information in the UPI system
	LastUpdatedTs *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_updated_ts,json=lastUpdatedTs,proto3" json:"last_updated_ts,omitempty"`
}

func (x *RegIdDetails) Reset() {
	*x = RegIdDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegIdDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegIdDetails) ProtoMessage() {}

func (x *RegIdDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegIdDetails.ProtoReflect.Descriptor instead.
func (*RegIdDetails) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{9}
}

func (x *RegIdDetails) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RegIdDetails) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *RegIdDetails) GetSetStatus() RegIdDetails_RegIdStatus {
	if x != nil {
		return x.SetStatus
	}
	return RegIdDetails_REGID_STATUS_UNSPECIFIED
}

func (x *RegIdDetails) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *RegIdDetails) GetExpiryTs() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTs
	}
	return nil
}

func (x *RegIdDetails) GetLastUpdatedTs() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedTs
	}
	return nil
}

type Consent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// CMREGISTRATION - denotes consent for mobile number migration fro other psp as upi nuber
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// denotes if the user has given consent or not
	Value bool `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	// previous vpa to which he upi number was linked
	PreVpa string `protobuf:"bytes,3,opt,name=pre_vpa,json=preVpa,proto3" json:"pre_vpa,omitempty"`
}

func (x *Consent) Reset() {
	*x = Consent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Consent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Consent) ProtoMessage() {}

func (x *Consent) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Consent.ProtoReflect.Descriptor instead.
func (*Consent) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{10}
}

func (x *Consent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Consent) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

func (x *Consent) GetPreVpa() string {
	if x != nil {
		return x.PreVpa
	}
	return ""
}

// UpiInternationalPaymentCharge -
// info regarding a particular type of charge being
// applied on the upi international payment
type UpiInternationalPaymentCharge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// amount corresponds to the total amount of particular breakup type
	Amount *money.Money `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	// conversion_rate corresponds to the amount of INR for 1 unit of quote_currency
	ConversionRate float32 `protobuf:"fixed32,2,opt,name=conversion_rate,json=conversionRate,proto3" json:"conversion_rate,omitempty"`
	// overhead_charge_percentage overhead charges like GST or Markup actual value in percentage
	OverheadChargePercentage float32 `protobuf:"fixed32,3,opt,name=overhead_charge_percentage,json=overheadChargePercentage,proto3" json:"overhead_charge_percentage,omitempty"`
	// UpiInternationalPaymentChargeType - type of charge being applied on payment
	UpiInternationalPaymentChargeType UpiInternationalPaymentChargeType `protobuf:"varint,4,opt,name=upi_international_payment_charge_type,json=upiInternationalPaymentChargeType,proto3,enum=upi.UpiInternationalPaymentChargeType" json:"upi_international_payment_charge_type,omitempty"`
}

func (x *UpiInternationalPaymentCharge) Reset() {
	*x = UpiInternationalPaymentCharge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiInternationalPaymentCharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiInternationalPaymentCharge) ProtoMessage() {}

func (x *UpiInternationalPaymentCharge) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiInternationalPaymentCharge.ProtoReflect.Descriptor instead.
func (*UpiInternationalPaymentCharge) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{11}
}

func (x *UpiInternationalPaymentCharge) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *UpiInternationalPaymentCharge) GetConversionRate() float32 {
	if x != nil {
		return x.ConversionRate
	}
	return 0
}

func (x *UpiInternationalPaymentCharge) GetOverheadChargePercentage() float32 {
	if x != nil {
		return x.OverheadChargePercentage
	}
	return 0
}

func (x *UpiInternationalPaymentCharge) GetUpiInternationalPaymentChargeType() UpiInternationalPaymentChargeType {
	if x != nil {
		return x.UpiInternationalPaymentChargeType
	}
	return UpiInternationalPaymentChargeType_UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_UNSPECIFIED
}

type UpiLiteTransactionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// An enum that represents the type of public key i.e., NPCI or UIDAI
	// Currently, RPC sends a hardcoded NPCI response
	KeyCode typesv2.KeyCode `protobuf:"varint,2,opt,name=key_code,json=keyCode,proto3,enum=api.typesv2.KeyCode" json:"key_code,omitempty"`
	// This is digitally signed XML payload received from list-Keys API of UPI.
	// This is mandatory field  required by mobile app to execute the “Get Credential”
	// service of common Library
	KeyXmlPayload string `protobuf:"bytes,3,opt,name=key_xml_payload,json=keyXmlPayload,proto3" json:"key_xml_payload,omitempty"`
	// Based on the number of blocks in the JSON, one or
	// more credential input control will be rendered by the
	// common library. Required by app to execute the “Get Credential”
	ControlJson *ControlJson `protobuf:"bytes,4,opt,name=control_json,json=controlJson,proto3" json:"control_json,omitempty"`
	// This enables to customize UI displayed by common library.
	// Client is not expected to change this and pass as it is to CL
	// Right now we are returning hardcoded json
	//
	//	ex json: {
	//	 {"payerBankName": "Indian Bank Ltd.",
	//	 "backgroundColor": "#FF9933",
	//	 "color": "#FF9933"}
	//
	// Deprecated: Marked as deprecated in api/upi/upi.proto.
	BankConfigJson string `protobuf:"bytes,5,opt,name=bank_config_json,json=bankConfigJson,proto3" json:"bank_config_json,omitempty"`
	// This enables to customize UI displayed by common library.
	BankConfig *UpiLiteTransactionParams_BankConfig `protobuf:"bytes,6,opt,name=bank_config,json=bankConfig,proto3" json:"bank_config,omitempty"`
	// account ref number - required by CL for generating
	// creds for lrn and top up payments.
	AccountRefNumber string `protobuf:"bytes,7,opt,name=account_ref_number,json=accountRefNumber,proto3" json:"account_ref_number,omitempty"`
	// lrn (Lite Reference Number) - required for top up
	// and upi lite payments
	Lrn string `protobuf:"bytes,8,opt,name=lrn,proto3" json:"lrn,omitempty"`
}

func (x *UpiLiteTransactionParams) Reset() {
	*x = UpiLiteTransactionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiLiteTransactionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiLiteTransactionParams) ProtoMessage() {}

func (x *UpiLiteTransactionParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiLiteTransactionParams.ProtoReflect.Descriptor instead.
func (*UpiLiteTransactionParams) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{12}
}

func (x *UpiLiteTransactionParams) GetKeyCode() typesv2.KeyCode {
	if x != nil {
		return x.KeyCode
	}
	return typesv2.KeyCode(0)
}

func (x *UpiLiteTransactionParams) GetKeyXmlPayload() string {
	if x != nil {
		return x.KeyXmlPayload
	}
	return ""
}

func (x *UpiLiteTransactionParams) GetControlJson() *ControlJson {
	if x != nil {
		return x.ControlJson
	}
	return nil
}

// Deprecated: Marked as deprecated in api/upi/upi.proto.
func (x *UpiLiteTransactionParams) GetBankConfigJson() string {
	if x != nil {
		return x.BankConfigJson
	}
	return ""
}

func (x *UpiLiteTransactionParams) GetBankConfig() *UpiLiteTransactionParams_BankConfig {
	if x != nil {
		return x.BankConfig
	}
	return nil
}

func (x *UpiLiteTransactionParams) GetAccountRefNumber() string {
	if x != nil {
		return x.AccountRefNumber
	}
	return ""
}

func (x *UpiLiteTransactionParams) GetLrn() string {
	if x != nil {
		return x.Lrn
	}
	return ""
}

type UpiLiteBalanceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account_ref_number: of the upi account to
	// which upi lite account belongs to.
	AccountRefNumber string `protobuf:"bytes,1,opt,name=account_ref_number,json=accountRefNumber,proto3" json:"account_ref_number,omitempty"`
	// lrn (Lite Reference Number) - unique identifier
	// b/w Fi and NPCI for upi lite account
	Lrn string `protobuf:"bytes,2,opt,name=lrn,proto3" json:"lrn,omitempty"`
}

func (x *UpiLiteBalanceParams) Reset() {
	*x = UpiLiteBalanceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiLiteBalanceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiLiteBalanceParams) ProtoMessage() {}

func (x *UpiLiteBalanceParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiLiteBalanceParams.ProtoReflect.Descriptor instead.
func (*UpiLiteBalanceParams) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{13}
}

func (x *UpiLiteBalanceParams) GetAccountRefNumber() string {
	if x != nil {
		return x.AccountRefNumber
	}
	return ""
}

func (x *UpiLiteBalanceParams) GetLrn() string {
	if x != nil {
		return x.Lrn
	}
	return ""
}

type AccountUpiPinInfo_DetailedStatusList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// detailed status from vendor
	DetailedStatus []*DetailedStatus `protobuf:"bytes,1,rep,name=detailed_status,json=detailedStatus,proto3" json:"detailed_status,omitempty"`
}

func (x *AccountUpiPinInfo_DetailedStatusList) Reset() {
	*x = AccountUpiPinInfo_DetailedStatusList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountUpiPinInfo_DetailedStatusList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountUpiPinInfo_DetailedStatusList) ProtoMessage() {}

func (x *AccountUpiPinInfo_DetailedStatusList) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountUpiPinInfo_DetailedStatusList.ProtoReflect.Descriptor instead.
func (*AccountUpiPinInfo_DetailedStatusList) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{5, 0}
}

func (x *AccountUpiPinInfo_DetailedStatusList) GetDetailedStatus() []*DetailedStatus {
	if x != nil {
		return x.DetailedStatus
	}
	return nil
}

type UpiLiteTransactionParams_BankConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PayerBankName    string `protobuf:"bytes,1,opt,name=payer_bank_name,json=payerBankName,proto3" json:"payer_bank_name,omitempty"`
	BackgroundColor  string `protobuf:"bytes,2,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	Color            string `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
	ResendOtpFeature bool   `protobuf:"varint,4,opt,name=resend_otp_feature,json=resendOtpFeature,proto3" json:"resend_otp_feature,omitempty"`
}

func (x *UpiLiteTransactionParams_BankConfig) Reset() {
	*x = UpiLiteTransactionParams_BankConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_upi_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiLiteTransactionParams_BankConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiLiteTransactionParams_BankConfig) ProtoMessage() {}

func (x *UpiLiteTransactionParams_BankConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_upi_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiLiteTransactionParams_BankConfig.ProtoReflect.Descriptor instead.
func (*UpiLiteTransactionParams_BankConfig) Descriptor() ([]byte, []int) {
	return file_api_upi_upi_proto_rawDescGZIP(), []int{12, 0}
}

func (x *UpiLiteTransactionParams_BankConfig) GetPayerBankName() string {
	if x != nil {
		return x.PayerBankName
	}
	return ""
}

func (x *UpiLiteTransactionParams_BankConfig) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *UpiLiteTransactionParams_BankConfig) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *UpiLiteTransactionParams_BankConfig) GetResendOtpFeature() bool {
	if x != nil {
		return x.ResendOtpFeature
	}
	return false
}

var File_api_upi_upi_proto protoreflect.FileDescriptor

var file_api_upi_upi_proto_rawDesc = []byte{
	0x0a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x03, 0x75, 0x70, 0x69, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x05, 0x0a, 0x0e,
	0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a,
	0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4a, 0x73,
	0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0d, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x70, 0x69, 0x6e,
	0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x69, 0x73, 0x5f, 0x61,
	0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x69, 0x73,
	0x41, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x66, 0x73,
	0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x55, 0x70, 0x69, 0x53, 0x65, 0x74, 0x55, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x4f, 0x0a, 0x16, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x13, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x61, 0x6c,
	0x6c, 0x65, 0x64, 0x41, 0x74, 0x22, 0xdb, 0x02, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x65, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x0b,
	0x43, 0x72, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x65, 0x0a, 0x21, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x1e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73,
	0x41, 0x74, 0x12, 0x44, 0x0a, 0x1f, 0x69, 0x73, 0x5f, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x49, 0x73, 0x41,
	0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x61, 0x6e,
	0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x69, 0x0a, 0x23, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x20, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0x6e, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x15, 0x0a,
	0x06, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x81, 0x02, 0x0a, 0x06, 0x50, 0x73, 0x70, 0x4b, 0x65, 0x79, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x73, 0x70, 0x4f, 0x72, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x73, 0x70, 0x4f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x6b, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6b, 0x69, 0x12, 0x1b,
	0x0a, 0x09, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xc1, 0x02, 0x0a, 0x14, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x76, 0x70, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x56, 0x70, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x65, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6b, 0x69, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6b, 0x69, 0x12, 0x1b, 0x0a, 0x09, 0x6b, 0x65, 0x79, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xde, 0x05, 0x0a, 0x11,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x3e, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x69,
	0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x14, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x12, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x1a,
	0x52, 0x0a, 0x12, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0e, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x4c, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x12, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54,
	0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x49, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x49, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x10,
	0x03, 0x22, 0x49, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12,
	0x0b, 0x0a, 0x07, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x03, 0x22, 0x6e, 0x0a, 0x0e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x61, 0x77, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x61, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xda, 0x02, 0x0a,
	0x17, 0x55, 0x70, 0x69, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x65, 0x64, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0b, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x0b, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x7b, 0x0a, 0x08, 0x56, 0x70, 0x61,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x65, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x65, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x0e, 0x70, 0x73, 0x70, 0x5f, 0x62, 0x61, 0x64, 0x67, 0x65,
	0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x70, 0x73, 0x70, 0x42, 0x61, 0x64, 0x67, 0x65,
	0x49, 0x63, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x22, 0x9c, 0x03, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x49, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x67, 0x49,
	0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x49, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x41, 0x64, 0x64, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x41,
	0x64, 0x64, 0x72, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x73, 0x12, 0x42, 0x0a, 0x0f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x73,
	0x22, 0x92, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x67, 0x49, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1c, 0x0a, 0x18, 0x52, 0x45, 0x47, 0x49, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x52, 0x45, 0x47, 0x49, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e,
	0x45, 0x57, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x45, 0x47, 0x49, 0x44, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x19, 0x0a,
	0x15, 0x52, 0x45, 0x47, 0x49, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x45, 0x47, 0x49,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x52, 0x45, 0x47, 0x49, 0x53,
	0x54, 0x45, 0x52, 0x10, 0x04, 0x22, 0x4c, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x72,
	0x65, 0x5f, 0x76, 0x70, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65,
	0x56, 0x70, 0x61, 0x22, 0xac, 0x02, 0x0a, 0x1d, 0x55, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x6f, 0x76,
	0x65, 0x72, 0x68, 0x65, 0x61, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x18,
	0x6f, 0x76, 0x65, 0x72, 0x68, 0x65, 0x61, 0x64, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x78, 0x0a, 0x25, 0x75, 0x70, 0x69, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70,
	0x69, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x21, 0x75, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x87, 0x04, 0x0a, 0x18, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x2f, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x4b, 0x65, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x6b, 0x65, 0x79, 0x5f, 0x78, 0x6d, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x65, 0x79, 0x58, 0x6d,
	0x6c, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x33, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4a, 0x73, 0x6f, 0x6e,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x2c, 0x0a,
	0x10, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6a, 0x73, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x62, 0x61, 0x6e,
	0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x0b, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e,
	0x42, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x62, 0x61, 0x6e, 0x6b,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x72, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6c, 0x72, 0x6e, 0x1a, 0xa3, 0x01, 0x0a, 0x0a, 0x42, 0x61, 0x6e, 0x6b, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x70, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a,
	0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2c,
	0x0a, 0x12, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x74, 0x70, 0x5f, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x64, 0x4f, 0x74, 0x70, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x22, 0x56, 0x0a, 0x14,
	0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6c, 0x72, 0x6e, 0x2a, 0xbc, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x69, 0x53, 0x65, 0x74, 0x55,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x55, 0x50, 0x49, 0x5f, 0x53, 0x45,
	0x54, 0x5f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x43, 0x43, 0x5f, 0x52,
	0x45, 0x46, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x56,
	0x50, 0x41, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a,
	0x50, 0x49, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12,
	0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x13,
	0x0a, 0x0f, 0x56, 0x50, 0x41, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x53, 0x4d,
	0x53, 0x10, 0x07, 0x2a, 0x64, 0x0a, 0x0a, 0x55, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1b, 0x0a, 0x17, 0x55, 0x50, 0x49, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11,
	0x0a, 0x0d, 0x55, 0x50, 0x49, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x31, 0x10,
	0x01, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x50, 0x49, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x32, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x50, 0x49, 0x5f, 0x56, 0x45, 0x52, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x32, 0x5f, 0x35, 0x10, 0x03, 0x2a, 0x49, 0x0a, 0x0f, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07,
	0x0a, 0x03, 0x50, 0x41, 0x59, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4c, 0x4c, 0x45,
	0x43, 0x54, 0x10, 0x02, 0x2a, 0xf1, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x2b, 0x0a, 0x27, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a,
	0x0d, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x01,
	0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12,
	0x4d, 0x41, 0x53, 0x4b, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e,
	0x55, 0x4d, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x05,
	0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x4a, 0x53, 0x4f, 0x4e,
	0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x53, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52,
	0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44,
	0x10, 0x07, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x56, 0x50, 0x41, 0x10, 0x08, 0x2a, 0xaf, 0x01, 0x0a, 0x0b, 0x50, 0x69, 0x6e,
	0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x49, 0x4e, 0x5f,
	0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x49, 0x4e, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x49, 0x4e, 0x5f,
	0x53, 0x45, 0x54, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x45, 0x4f, 0x4f, 0x42, 0x45, 0x5f,
	0x50, 0x49, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x03, 0x12, 0x0f, 0x0a,
	0x0b, 0x45, 0x54, 0x42, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x04, 0x12, 0x1b,
	0x0a, 0x17, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x50, 0x49,
	0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x50,
	0x49, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x06, 0x2a, 0xd5, 0x01, 0x0a, 0x1a, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2f, 0x0a, 0x2b, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x49, 0x4e, 0x46,
	0x4f, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x02,
	0x12, 0x06, 0x0a, 0x02, 0x49, 0x44, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x53, 0x45, 0x52,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c, 0x4c,
	0x10, 0x09, 0x2a, 0x74, 0x0a, 0x0a, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x17, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a,
	0x13, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f, 0x52,
	0x4d, 0x41, 0x54, 0x31, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x32, 0x10, 0x02, 0x12,
	0x17, 0x0a, 0x13, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46,
	0x4f, 0x52, 0x4d, 0x41, 0x54, 0x33, 0x10, 0x03, 0x2a, 0xed, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x69,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35,
	0x0a, 0x31, 0x55, 0x50, 0x49, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c, 0x55, 0x50, 0x49, 0x5f, 0x49, 0x4e, 0x54,
	0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d,
	0x41, 0x52, 0x4b, 0x55, 0x50, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x55, 0x50, 0x49, 0x5f, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x47, 0x53, 0x54, 0x10, 0x02, 0x12, 0x30, 0x0a, 0x2c, 0x55, 0x50, 0x49, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x03, 0x2a, 0x8d, 0x03, 0x0a, 0x0b, 0x55, 0x70, 0x69,
	0x46, 0x6c, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x50, 0x49, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x50, 0x49, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x50,
	0x49, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x5f,
	0x50, 0x49, 0x4e, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x50, 0x49,
	0x4e, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x04, 0x12, 0x2f, 0x0a, 0x2b, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41,
	0x4c, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x52, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x06, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f,
	0x54, 0x4f, 0x50, 0x5f, 0x55, 0x50, 0x10, 0x07, 0x12, 0x23, 0x0a, 0x1f, 0x55, 0x50, 0x49, 0x5f,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49,
	0x54, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x08, 0x12, 0x25, 0x0a,
	0x21, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44,
	0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49,
	0x54, 0x45, 0x10, 0x09, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x42,
	0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x0a, 0x2a, 0x92, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x69,
	0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x27, 0x0a, 0x23, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f,
	0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x55, 0x50, 0x49,
	0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10,
	0x01, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54,
	0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x41, 0x44,
	0x48, 0x41, 0x41, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x02, 0x42, 0x40, 0x0a,
	0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x5a,
	0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_upi_proto_rawDescOnce sync.Once
	file_api_upi_upi_proto_rawDescData = file_api_upi_upi_proto_rawDesc
)

func file_api_upi_upi_proto_rawDescGZIP() []byte {
	file_api_upi_upi_proto_rawDescOnce.Do(func() {
		file_api_upi_upi_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_upi_proto_rawDescData)
	})
	return file_api_upi_upi_proto_rawDescData
}

var file_api_upi_upi_proto_enumTypes = make([]protoimpl.EnumInfo, 13)
var file_api_upi_upi_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_api_upi_upi_proto_goTypes = []interface{}{
	(UpiSetUpState)(0),                           // 0: upi.UpiSetUpState
	(UpiVersion)(0),                              // 1: upi.UpiVersion
	(TransactionType)(0),                         // 2: upi.TransactionType
	(UpiAccountInfoFieldMask)(0),                 // 3: upi.UpiAccountInfoFieldMask
	(PinSetState)(0),                             // 4: upi.PinSetState
	(AccountUpiPinInfoFieldMask)(0),              // 5: upi.AccountUpiPinInfoFieldMask
	(FormatType)(0),                              // 6: upi.FormatType
	(UpiInternationalPaymentChargeType)(0),       // 7: upi.UpiInternationalPaymentChargeType
	(UpiFlowType)(0),                             // 8: upi.UpiFlowType
	(UpiPinSetOptionType)(0),                     // 9: upi.UpiPinSetOptionType
	(AccountUpiPinInfo_Action)(0),                // 10: upi.AccountUpiPinInfo.Action
	(AccountUpiPinInfo_Status)(0),                // 11: upi.AccountUpiPinInfo.Status
	(RegIdDetails_RegIdStatus)(0),                // 12: upi.RegIdDetails.RegIdStatus
	(*UpiAccountInfo)(nil),                       // 13: upi.UpiAccountInfo
	(*ControlJson)(nil),                          // 14: upi.ControlJson
	(*CredAllowed)(nil),                          // 15: upi.CredAllowed
	(*PspKey)(nil),                               // 16: upi.PspKey
	(*VerifiedAddressEntry)(nil),                 // 17: upi.VerifiedAddressEntry
	(*AccountUpiPinInfo)(nil),                    // 18: upi.AccountUpiPinInfo
	(*DetailedStatus)(nil),                       // 19: upi.DetailedStatus
	(*UpiProcessedPhoneNumber)(nil),              // 20: upi.UpiProcessedPhoneNumber
	(*VpaInfo)(nil),                              // 21: upi.Vpa_info
	(*RegIdDetails)(nil),                         // 22: upi.RegIdDetails
	(*Consent)(nil),                              // 23: upi.Consent
	(*UpiInternationalPaymentCharge)(nil),        // 24: upi.UpiInternationalPaymentCharge
	(*UpiLiteTransactionParams)(nil),             // 25: upi.UpiLiteTransactionParams
	(*UpiLiteBalanceParams)(nil),                 // 26: upi.UpiLiteBalanceParams
	(*AccountUpiPinInfo_DetailedStatusList)(nil), // 27: upi.AccountUpiPinInfo.DetailedStatusList
	(*UpiLiteTransactionParams_BankConfig)(nil),  // 28: upi.UpiLiteTransactionParams.BankConfig
	(*timestamppb.Timestamp)(nil),                // 29: google.protobuf.Timestamp
	(*common.PhoneNumber)(nil),                   // 30: api.typesv2.common.PhoneNumber
	(*common.Image)(nil),                         // 31: api.typesv2.common.Image
	(*money.Money)(nil),                          // 32: google.type.Money
	(typesv2.KeyCode)(0),                         // 33: api.typesv2.KeyCode
}
var file_api_upi_upi_proto_depIdxs = []int32{
	14, // 0: upi.UpiAccountInfo.control_json:type_name -> upi.ControlJson
	4,  // 1: upi.UpiAccountInfo.pin_set_state:type_name -> upi.PinSetState
	29, // 2: upi.UpiAccountInfo.created_at:type_name -> google.protobuf.Timestamp
	29, // 3: upi.UpiAccountInfo.updated_at:type_name -> google.protobuf.Timestamp
	29, // 4: upi.UpiAccountInfo.deleted_at:type_name -> google.protobuf.Timestamp
	0,  // 5: upi.UpiAccountInfo.state:type_name -> upi.UpiSetUpState
	29, // 6: upi.UpiAccountInfo.list_account_called_at:type_name -> google.protobuf.Timestamp
	15, // 7: upi.ControlJson.cred_alloweds:type_name -> upi.CredAllowed
	29, // 8: upi.ControlJson.international_payments_expires_at:type_name -> google.protobuf.Timestamp
	29, // 9: upi.ControlJson.international_payments_activated_at:type_name -> google.protobuf.Timestamp
	29, // 10: upi.PspKey.created_at:type_name -> google.protobuf.Timestamp
	29, // 11: upi.PspKey.updated_at:type_name -> google.protobuf.Timestamp
	29, // 12: upi.VerifiedAddressEntry.created_at:type_name -> google.protobuf.Timestamp
	29, // 13: upi.VerifiedAddressEntry.updated_at:type_name -> google.protobuf.Timestamp
	10, // 14: upi.AccountUpiPinInfo.user_action:type_name -> upi.AccountUpiPinInfo.Action
	29, // 15: upi.AccountUpiPinInfo.created_at:type_name -> google.protobuf.Timestamp
	29, // 16: upi.AccountUpiPinInfo.update_at:type_name -> google.protobuf.Timestamp
	29, // 17: upi.AccountUpiPinInfo.deleted_at:type_name -> google.protobuf.Timestamp
	11, // 18: upi.AccountUpiPinInfo.status:type_name -> upi.AccountUpiPinInfo.Status
	27, // 19: upi.AccountUpiPinInfo.detailed_status_list:type_name -> upi.AccountUpiPinInfo.DetailedStatusList
	30, // 20: upi.UpiProcessedPhoneNumber.phoneNumber:type_name -> api.typesv2.common.PhoneNumber
	29, // 21: upi.UpiProcessedPhoneNumber.verified_at:type_name -> google.protobuf.Timestamp
	29, // 22: upi.UpiProcessedPhoneNumber.created_at:type_name -> google.protobuf.Timestamp
	29, // 23: upi.UpiProcessedPhoneNumber.updated_at:type_name -> google.protobuf.Timestamp
	29, // 24: upi.UpiProcessedPhoneNumber.deleted_at:type_name -> google.protobuf.Timestamp
	31, // 25: upi.Vpa_info.psp_badge_icon:type_name -> api.typesv2.common.Image
	12, // 26: upi.RegIdDetails.set_status:type_name -> upi.RegIdDetails.RegIdStatus
	29, // 27: upi.RegIdDetails.expiry_ts:type_name -> google.protobuf.Timestamp
	29, // 28: upi.RegIdDetails.last_updated_ts:type_name -> google.protobuf.Timestamp
	32, // 29: upi.UpiInternationalPaymentCharge.amount:type_name -> google.type.Money
	7,  // 30: upi.UpiInternationalPaymentCharge.upi_international_payment_charge_type:type_name -> upi.UpiInternationalPaymentChargeType
	33, // 31: upi.UpiLiteTransactionParams.key_code:type_name -> api.typesv2.KeyCode
	14, // 32: upi.UpiLiteTransactionParams.control_json:type_name -> upi.ControlJson
	28, // 33: upi.UpiLiteTransactionParams.bank_config:type_name -> upi.UpiLiteTransactionParams.BankConfig
	19, // 34: upi.AccountUpiPinInfo.DetailedStatusList.detailed_status:type_name -> upi.DetailedStatus
	35, // [35:35] is the sub-list for method output_type
	35, // [35:35] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_api_upi_upi_proto_init() }
func file_api_upi_upi_proto_init() {
	if File_api_upi_upi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_upi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiAccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ControlJson); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CredAllowed); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PspKey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifiedAddressEntry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountUpiPinInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailedStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiProcessedPhoneNumber); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VpaInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegIdDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Consent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiInternationalPaymentCharge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiLiteTransactionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiLiteBalanceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountUpiPinInfo_DetailedStatusList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_upi_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiLiteTransactionParams_BankConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_upi_proto_rawDesc,
			NumEnums:      13,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_upi_proto_goTypes,
		DependencyIndexes: file_api_upi_upi_proto_depIdxs,
		EnumInfos:         file_api_upi_upi_proto_enumTypes,
		MessageInfos:      file_api_upi_upi_proto_msgTypes,
	}.Build()
	File_api_upi_upi_proto = out.File
	file_api_upi_upi_proto_rawDesc = nil
	file_api_upi_upi_proto_goTypes = nil
	file_api_upi_upi_proto_depIdxs = nil
}
