// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/device.proto

package upi

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Device_App int32

const (
	Device_APP_UNSPECIFIED Device_App = 0
	// signifies that request came from  android app
	Device_ANDROID Device_App = 1
	// signifies that request came from ios app
	Device_IOS Device_App = 2
)

// Enum value maps for Device_App.
var (
	Device_App_name = map[int32]string{
		0: "APP_UNSPECIFIED",
		1: "ANDROID",
		2: "IOS",
	}
	Device_App_value = map[string]int32{
		"APP_UNSPECIFIED": 0,
		"ANDROID":         1,
		"IOS":             2,
	}
)

func (x Device_App) Enum() *Device_App {
	p := new(Device_App)
	*p = x
	return p
}

func (x Device_App) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Device_App) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_device_proto_enumTypes[0].Descriptor()
}

func (Device_App) Type() protoreflect.EnumType {
	return &file_api_upi_device_proto_enumTypes[0]
}

func (x Device_App) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Device_App.Descriptor instead.
func (Device_App) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_device_proto_rawDescGZIP(), []int{0, 0}
}

// Device details belonging to the requesting actor
// Device details are necessary to be passed as they are used for risk analytics for UPI APIs
type Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location of the customer who initiated the request.
	Geocode *latlng.LatLng `protobuf:"bytes,1,opt,name=geocode,proto3" json:"geocode,omitempty"`
	// device id of the customer device who initiated the request
	// TODO(nitesh): add validation
	Id string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	// OS version of the customer device who initiated the request e.g. Android5.1.1
	// TODO(nitesh): add validation
	OsVersion string `protobuf:"bytes,3,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
	// IP address belonging to the customer device.
	// TODO(nitesh): add validation
	IpAddr string `protobuf:"bytes,4,opt,name=ip_addr,json=ipAddr,proto3" json:"ip_addr,omitempty"`
	// app from which request came.
	App Device_App `protobuf:"varint,5,opt,name=app,proto3,enum=upi.Device_App" json:"app,omitempty"`
	// obfuscated GPS coordinates location identifier.
	// Since, location identifier is a sensitive user information. It's important
	// for us to restrict the exposure keeping user's privacy in mind.
	LocationToken string `protobuf:"bytes,6,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
	// type of device, e.g. MOB represents a mobile device
	Type string `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	// TODO(nitesh): figure out what does this field means and add description
	Capability  string              `protobuf:"bytes,8,opt,name=capability,proto3" json:"capability,omitempty"`
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,9,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *Device) Reset() {
	*x = Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_device_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_device_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_api_upi_device_proto_rawDescGZIP(), []int{0}
}

func (x *Device) GetGeocode() *latlng.LatLng {
	if x != nil {
		return x.Geocode
	}
	return nil
}

func (x *Device) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Device) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *Device) GetIpAddr() string {
	if x != nil {
		return x.IpAddr
	}
	return ""
}

func (x *Device) GetApp() Device_App {
	if x != nil {
		return x.App
	}
	return Device_APP_UNSPECIFIED
}

func (x *Device) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

func (x *Device) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Device) GetCapability() string {
	if x != nil {
		return x.Capability
	}
	return ""
}

func (x *Device) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

var File_api_upi_device_proto protoreflect.FileDescriptor

var file_api_upi_device_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x75, 0x70, 0x69, 0x1a, 0x25, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x02, 0x0a,
	0x06, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x67, 0x65, 0x6f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x07, 0x67,
	0x65, 0x6f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x73, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x12, 0x21,
	0x0a, 0x03, 0x61, 0x70, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x03, 0x61, 0x70,
	0x70, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x61, 0x70, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x42, 0x0a, 0x0c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0x30, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x50, 0x50, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53,
	0x10, 0x02, 0x42, 0x40, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x70, 0x69, 0x5a, 0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x75, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_device_proto_rawDescOnce sync.Once
	file_api_upi_device_proto_rawDescData = file_api_upi_device_proto_rawDesc
)

func file_api_upi_device_proto_rawDescGZIP() []byte {
	file_api_upi_device_proto_rawDescOnce.Do(func() {
		file_api_upi_device_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_device_proto_rawDescData)
	})
	return file_api_upi_device_proto_rawDescData
}

var file_api_upi_device_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_device_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_upi_device_proto_goTypes = []interface{}{
	(Device_App)(0),            // 0: upi.Device.App
	(*Device)(nil),             // 1: upi.Device
	(*latlng.LatLng)(nil),      // 2: google.type.LatLng
	(*common.PhoneNumber)(nil), // 3: api.typesv2.common.PhoneNumber
}
var file_api_upi_device_proto_depIdxs = []int32{
	2, // 0: upi.Device.geocode:type_name -> google.type.LatLng
	0, // 1: upi.Device.app:type_name -> upi.Device.App
	3, // 2: upi.Device.phone_number:type_name -> api.typesv2.common.PhoneNumber
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_upi_device_proto_init() }
func file_api_upi_device_proto_init() {
	if File_api_upi_device_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_device_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_device_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_device_proto_goTypes,
		DependencyIndexes: file_api_upi_device_proto_depIdxs,
		EnumInfos:         file_api_upi_device_proto_enumTypes,
		MessageInfos:      file_api_upi_device_proto_msgTypes,
	}.Build()
	File_api_upi_device_proto = out.File
	file_api_upi_device_proto_rawDesc = nil
	file_api_upi_device_proto_goTypes = nil
	file_api_upi_device_proto_depIdxs = nil
}
