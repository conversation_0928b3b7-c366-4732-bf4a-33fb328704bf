// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/consumer/service.proto

package consumer

import (
	queue "github.com/epifi/be-common/api/queue"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MigrateVpaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor id of actor whose vpa needs to be migrated
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// client req to trigger internal account linking flow
	ClientReqId string `protobuf:"bytes,3,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *MigrateVpaRequest) Reset() {
	*x = MigrateVpaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_consumer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigrateVpaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateVpaRequest) ProtoMessage() {}

func (x *MigrateVpaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_consumer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateVpaRequest.ProtoReflect.Descriptor instead.
func (*MigrateVpaRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_consumer_service_proto_rawDescGZIP(), []int{0}
}

func (x *MigrateVpaRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *MigrateVpaRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *MigrateVpaRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type MigrateVpaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *MigrateVpaResponse) Reset() {
	*x = MigrateVpaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_consumer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MigrateVpaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MigrateVpaResponse) ProtoMessage() {}

func (x *MigrateVpaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_consumer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MigrateVpaResponse.ProtoReflect.Descriptor instead.
func (*MigrateVpaResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_consumer_service_proto_rawDescGZIP(), []int{1}
}

func (x *MigrateVpaResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// Request struct for ProcessReqMapperConfirmation
type ProcessReqMapperConfirmationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common request header across all the consumer grpc services.
	// contains important information related to message retry state.
	// passed along by queue subscriber.
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	PartnerBank   vendorgateway.Vendor         `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
	// to unmarshal the data.
	RawData []byte `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessReqMapperConfirmationRequest) Reset() {
	*x = ProcessReqMapperConfirmationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_consumer_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqMapperConfirmationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqMapperConfirmationRequest) ProtoMessage() {}

func (x *ProcessReqMapperConfirmationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_consumer_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqMapperConfirmationRequest.ProtoReflect.Descriptor instead.
func (*ProcessReqMapperConfirmationRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_consumer_service_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessReqMapperConfirmationRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessReqMapperConfirmationRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *ProcessReqMapperConfirmationRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

// Response struct for ProcessReqMapperConfirmation
type ProcessReqMapperConfirmationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common response header across all the consumer grpc services.
	// the queue subscriber code depends on this header to take further action on the queue message.
	// i.e. removing message to the queue or increasing timeouts
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessReqMapperConfirmationResponse) Reset() {
	*x = ProcessReqMapperConfirmationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_consumer_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessReqMapperConfirmationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessReqMapperConfirmationResponse) ProtoMessage() {}

func (x *ProcessReqMapperConfirmationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_consumer_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessReqMapperConfirmationResponse.ProtoReflect.Descriptor instead.
func (*ProcessReqMapperConfirmationResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_consumer_service_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessReqMapperConfirmationResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_upi_onboarding_consumer_service_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_consumer_service_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x75, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x97, 0x01, 0x0a, 0x11, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x65, 0x56, 0x70, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64,
	0x22, 0x5c, 0x0a, 0x12, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x56, 0x70, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xbf,
	0x01, 0x0a, 0x23, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x4d, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0c, 0x70,
	0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x6e, 0x0a, 0x24, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x4d, 0x61,
	0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x32, 0x8f, 0x02, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x65, 0x0a,
	0x0a, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x56, 0x70, 0x61, 0x12, 0x2a, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x56, 0x70, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x2e, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x56, 0x70, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9b, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x4d, 0x61, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x4d, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x4d, 0x61, 0x70, 0x70, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x68, 0x0a, 0x32, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_consumer_service_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_consumer_service_proto_rawDescData = file_api_upi_onboarding_consumer_service_proto_rawDesc
)

func file_api_upi_onboarding_consumer_service_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_consumer_service_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_consumer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_consumer_service_proto_rawDescData)
	})
	return file_api_upi_onboarding_consumer_service_proto_rawDescData
}

var file_api_upi_onboarding_consumer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_upi_onboarding_consumer_service_proto_goTypes = []interface{}{
	(*MigrateVpaRequest)(nil),                    // 0: upi.onboarding.consumer.MigrateVpaRequest
	(*MigrateVpaResponse)(nil),                   // 1: upi.onboarding.consumer.MigrateVpaResponse
	(*ProcessReqMapperConfirmationRequest)(nil),  // 2: upi.onboarding.consumer.ProcessReqMapperConfirmationRequest
	(*ProcessReqMapperConfirmationResponse)(nil), // 3: upi.onboarding.consumer.ProcessReqMapperConfirmationResponse
	(*queue.ConsumerRequestHeader)(nil),          // 4: queue.ConsumerRequestHeader
	(*queue.ConsumerResponseHeader)(nil),         // 5: queue.ConsumerResponseHeader
	(vendorgateway.Vendor)(0),                    // 6: vendorgateway.Vendor
}
var file_api_upi_onboarding_consumer_service_proto_depIdxs = []int32{
	4, // 0: upi.onboarding.consumer.MigrateVpaRequest.request_header:type_name -> queue.ConsumerRequestHeader
	5, // 1: upi.onboarding.consumer.MigrateVpaResponse.response_header:type_name -> queue.ConsumerResponseHeader
	4, // 2: upi.onboarding.consumer.ProcessReqMapperConfirmationRequest.request_header:type_name -> queue.ConsumerRequestHeader
	6, // 3: upi.onboarding.consumer.ProcessReqMapperConfirmationRequest.partner_bank:type_name -> vendorgateway.Vendor
	5, // 4: upi.onboarding.consumer.ProcessReqMapperConfirmationResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0, // 5: upi.onboarding.consumer.Consumer.MigrateVpa:input_type -> upi.onboarding.consumer.MigrateVpaRequest
	2, // 6: upi.onboarding.consumer.Consumer.ProcessReqMapperConfirmation:input_type -> upi.onboarding.consumer.ProcessReqMapperConfirmationRequest
	1, // 7: upi.onboarding.consumer.Consumer.MigrateVpa:output_type -> upi.onboarding.consumer.MigrateVpaResponse
	3, // 8: upi.onboarding.consumer.Consumer.ProcessReqMapperConfirmation:output_type -> upi.onboarding.consumer.ProcessReqMapperConfirmationResponse
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_consumer_service_proto_init() }
func file_api_upi_onboarding_consumer_service_proto_init() {
	if File_api_upi_onboarding_consumer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_consumer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigrateVpaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_consumer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MigrateVpaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_consumer_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqMapperConfirmationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_consumer_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessReqMapperConfirmationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_consumer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_upi_onboarding_consumer_service_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_consumer_service_proto_depIdxs,
		MessageInfos:      file_api_upi_onboarding_consumer_service_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_consumer_service_proto = out.File
	file_api_upi_onboarding_consumer_service_proto_rawDesc = nil
	file_api_upi_onboarding_consumer_service_proto_goTypes = nil
	file_api_upi_onboarding_consumer_service_proto_depIdxs = nil
}
