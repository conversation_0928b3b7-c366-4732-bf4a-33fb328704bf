// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/upi/onboarding/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	MigrateVpaMethod                   = "MigrateVpa"
	ProcessReqMapperConfirmationMethod = "ProcessReqMapperConfirmation"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &MigrateVpaRequest{}
var _ queue.ConsumerRequest = &ProcessReqMapperConfirmationRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *MigrateVpaRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessReqMapperConfirmationRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterMigrateVpaMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterMigrateVpaMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, MigrateVpaMethod)
}

// RegisterProcessReqMapperConfirmationMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessReqMapperConfirmationMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessReqMapperConfirmationMethod)
}
