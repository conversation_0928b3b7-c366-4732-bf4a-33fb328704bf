// Code generated by MockGen. DO NOT EDIT.
// Source: api/upi/onboarding/consumer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consumer "github.com/epifi/gamma/api/upi/onboarding/consumer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockConsumerClient is a mock of ConsumerClient interface.
type MockConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerClientMockRecorder
}

// MockConsumerClientMockRecorder is the mock recorder for MockConsumerClient.
type MockConsumerClientMockRecorder struct {
	mock *MockConsumerClient
}

// NewMockConsumerClient creates a new mock instance.
func NewMockConsumerClient(ctrl *gomock.Controller) *MockConsumerClient {
	mock := &MockConsumerClient{ctrl: ctrl}
	mock.recorder = &MockConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerClient) EXPECT() *MockConsumerClientMockRecorder {
	return m.recorder
}

// MigrateVpa mocks base method.
func (m *MockConsumerClient) MigrateVpa(ctx context.Context, in *consumer.MigrateVpaRequest, opts ...grpc.CallOption) (*consumer.MigrateVpaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MigrateVpa", varargs...)
	ret0, _ := ret[0].(*consumer.MigrateVpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MigrateVpa indicates an expected call of MigrateVpa.
func (mr *MockConsumerClientMockRecorder) MigrateVpa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MigrateVpa", reflect.TypeOf((*MockConsumerClient)(nil).MigrateVpa), varargs...)
}

// ProcessReqMapperConfirmation mocks base method.
func (m *MockConsumerClient) ProcessReqMapperConfirmation(ctx context.Context, in *consumer.ProcessReqMapperConfirmationRequest, opts ...grpc.CallOption) (*consumer.ProcessReqMapperConfirmationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessReqMapperConfirmation", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessReqMapperConfirmationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessReqMapperConfirmation indicates an expected call of ProcessReqMapperConfirmation.
func (mr *MockConsumerClientMockRecorder) ProcessReqMapperConfirmation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessReqMapperConfirmation", reflect.TypeOf((*MockConsumerClient)(nil).ProcessReqMapperConfirmation), varargs...)
}

// MockConsumerServer is a mock of ConsumerServer interface.
type MockConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerServerMockRecorder
}

// MockConsumerServerMockRecorder is the mock recorder for MockConsumerServer.
type MockConsumerServerMockRecorder struct {
	mock *MockConsumerServer
}

// NewMockConsumerServer creates a new mock instance.
func NewMockConsumerServer(ctrl *gomock.Controller) *MockConsumerServer {
	mock := &MockConsumerServer{ctrl: ctrl}
	mock.recorder = &MockConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerServer) EXPECT() *MockConsumerServerMockRecorder {
	return m.recorder
}

// MigrateVpa mocks base method.
func (m *MockConsumerServer) MigrateVpa(arg0 context.Context, arg1 *consumer.MigrateVpaRequest) (*consumer.MigrateVpaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MigrateVpa", arg0, arg1)
	ret0, _ := ret[0].(*consumer.MigrateVpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MigrateVpa indicates an expected call of MigrateVpa.
func (mr *MockConsumerServerMockRecorder) MigrateVpa(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MigrateVpa", reflect.TypeOf((*MockConsumerServer)(nil).MigrateVpa), arg0, arg1)
}

// ProcessReqMapperConfirmation mocks base method.
func (m *MockConsumerServer) ProcessReqMapperConfirmation(arg0 context.Context, arg1 *consumer.ProcessReqMapperConfirmationRequest) (*consumer.ProcessReqMapperConfirmationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessReqMapperConfirmation", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessReqMapperConfirmationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessReqMapperConfirmation indicates an expected call of ProcessReqMapperConfirmation.
func (mr *MockConsumerServerMockRecorder) ProcessReqMapperConfirmation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessReqMapperConfirmation", reflect.TypeOf((*MockConsumerServer)(nil).ProcessReqMapperConfirmation), arg0, arg1)
}

// MockUnsafeConsumerServer is a mock of UnsafeConsumerServer interface.
type MockUnsafeConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeConsumerServerMockRecorder
}

// MockUnsafeConsumerServerMockRecorder is the mock recorder for MockUnsafeConsumerServer.
type MockUnsafeConsumerServerMockRecorder struct {
	mock *MockUnsafeConsumerServer
}

// NewMockUnsafeConsumerServer creates a new mock instance.
func NewMockUnsafeConsumerServer(ctrl *gomock.Controller) *MockUnsafeConsumerServer {
	mock := &MockUnsafeConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeConsumerServer) EXPECT() *MockUnsafeConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedConsumerServer mocks base method.
func (m *MockUnsafeConsumerServer) mustEmbedUnimplementedConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedConsumerServer")
}

// mustEmbedUnimplementedConsumerServer indicates an expected call of mustEmbedUnimplementedConsumerServer.
func (mr *MockUnsafeConsumerServerMockRecorder) mustEmbedUnimplementedConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedConsumerServer", reflect.TypeOf((*MockUnsafeConsumerServer)(nil).mustEmbedUnimplementedConsumerServer))
}
