// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/upi/onboarding/consumer/service.proto

package consumer

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Consumer_MigrateVpa_FullMethodName                   = "/upi.onboarding.consumer.Consumer/MigrateVpa"
	Consumer_ProcessReqMapperConfirmation_FullMethodName = "/upi.onboarding.consumer.Consumer/ProcessReqMapperConfirmation"
)

// ConsumerClient is the client API for Consumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConsumerClient interface {
	// MigrateVpa process the event for vpa migration consent
	// User consent is taken for vpa migration and is published, MigrateVpa consumes that and calls LinkInternalAccount RPC
	// That rpc creates a vpa for internal fi account
	MigrateVpa(ctx context.Context, in *MigrateVpaRequest, opts ...grpc.CallOption) (*MigrateVpaResponse, error)
	// During ‘PORT’ (Transfer of Mobile Number from One PSP to Other PSP),
	// the Previous PSP will be notified through ReqMapperConfirmation API.
	ProcessReqMapperConfirmation(ctx context.Context, in *ProcessReqMapperConfirmationRequest, opts ...grpc.CallOption) (*ProcessReqMapperConfirmationResponse, error)
}

type consumerClient struct {
	cc grpc.ClientConnInterface
}

func NewConsumerClient(cc grpc.ClientConnInterface) ConsumerClient {
	return &consumerClient{cc}
}

func (c *consumerClient) MigrateVpa(ctx context.Context, in *MigrateVpaRequest, opts ...grpc.CallOption) (*MigrateVpaResponse, error) {
	out := new(MigrateVpaResponse)
	err := c.cc.Invoke(ctx, Consumer_MigrateVpa_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consumerClient) ProcessReqMapperConfirmation(ctx context.Context, in *ProcessReqMapperConfirmationRequest, opts ...grpc.CallOption) (*ProcessReqMapperConfirmationResponse, error) {
	out := new(ProcessReqMapperConfirmationResponse)
	err := c.cc.Invoke(ctx, Consumer_ProcessReqMapperConfirmation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConsumerServer is the server API for Consumer service.
// All implementations should embed UnimplementedConsumerServer
// for forward compatibility
type ConsumerServer interface {
	// MigrateVpa process the event for vpa migration consent
	// User consent is taken for vpa migration and is published, MigrateVpa consumes that and calls LinkInternalAccount RPC
	// That rpc creates a vpa for internal fi account
	MigrateVpa(context.Context, *MigrateVpaRequest) (*MigrateVpaResponse, error)
	// During ‘PORT’ (Transfer of Mobile Number from One PSP to Other PSP),
	// the Previous PSP will be notified through ReqMapperConfirmation API.
	ProcessReqMapperConfirmation(context.Context, *ProcessReqMapperConfirmationRequest) (*ProcessReqMapperConfirmationResponse, error)
}

// UnimplementedConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedConsumerServer struct {
}

func (UnimplementedConsumerServer) MigrateVpa(context.Context, *MigrateVpaRequest) (*MigrateVpaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MigrateVpa not implemented")
}
func (UnimplementedConsumerServer) ProcessReqMapperConfirmation(context.Context, *ProcessReqMapperConfirmationRequest) (*ProcessReqMapperConfirmationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessReqMapperConfirmation not implemented")
}

// UnsafeConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConsumerServer will
// result in compilation errors.
type UnsafeConsumerServer interface {
	mustEmbedUnimplementedConsumerServer()
}

func RegisterConsumerServer(s grpc.ServiceRegistrar, srv ConsumerServer) {
	s.RegisterService(&Consumer_ServiceDesc, srv)
}

func _Consumer_MigrateVpa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MigrateVpaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).MigrateVpa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_MigrateVpa_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).MigrateVpa(ctx, req.(*MigrateVpaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consumer_ProcessReqMapperConfirmation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessReqMapperConfirmationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsumerServer).ProcessReqMapperConfirmation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consumer_ProcessReqMapperConfirmation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsumerServer).ProcessReqMapperConfirmation(ctx, req.(*ProcessReqMapperConfirmationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Consumer_ServiceDesc is the grpc.ServiceDesc for Consumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Consumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "upi.onboarding.consumer.Consumer",
	HandlerType: (*ConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MigrateVpa",
			Handler:    _Consumer_MigrateVpa_Handler,
		},
		{
			MethodName: "ProcessReqMapperConfirmation",
			Handler:    _Consumer_ProcessReqMapperConfirmation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/upi/onboarding/consumer/service.proto",
}
