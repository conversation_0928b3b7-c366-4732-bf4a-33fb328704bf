// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/onboarding/consumer/service.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on MigrateVpaRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MigrateVpaRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MigrateVpaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MigrateVpaRequestMultiError, or nil if none found.
func (m *MigrateVpaRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MigrateVpaRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MigrateVpaRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MigrateVpaRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MigrateVpaRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return MigrateVpaRequestMultiError(errors)
	}

	return nil
}

// MigrateVpaRequestMultiError is an error wrapping multiple validation errors
// returned by MigrateVpaRequest.ValidateAll() if the designated constraints
// aren't met.
type MigrateVpaRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MigrateVpaRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MigrateVpaRequestMultiError) AllErrors() []error { return m }

// MigrateVpaRequestValidationError is the validation error returned by
// MigrateVpaRequest.Validate if the designated constraints aren't met.
type MigrateVpaRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MigrateVpaRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MigrateVpaRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MigrateVpaRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MigrateVpaRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MigrateVpaRequestValidationError) ErrorName() string {
	return "MigrateVpaRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MigrateVpaRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMigrateVpaRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MigrateVpaRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MigrateVpaRequestValidationError{}

// Validate checks the field values on MigrateVpaResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MigrateVpaResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MigrateVpaResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MigrateVpaResponseMultiError, or nil if none found.
func (m *MigrateVpaResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MigrateVpaResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MigrateVpaResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MigrateVpaResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MigrateVpaResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MigrateVpaResponseMultiError(errors)
	}

	return nil
}

// MigrateVpaResponseMultiError is an error wrapping multiple validation errors
// returned by MigrateVpaResponse.ValidateAll() if the designated constraints
// aren't met.
type MigrateVpaResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MigrateVpaResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MigrateVpaResponseMultiError) AllErrors() []error { return m }

// MigrateVpaResponseValidationError is the validation error returned by
// MigrateVpaResponse.Validate if the designated constraints aren't met.
type MigrateVpaResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MigrateVpaResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MigrateVpaResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MigrateVpaResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MigrateVpaResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MigrateVpaResponseValidationError) ErrorName() string {
	return "MigrateVpaResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MigrateVpaResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMigrateVpaResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MigrateVpaResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MigrateVpaResponseValidationError{}

// Validate checks the field values on ProcessReqMapperConfirmationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessReqMapperConfirmationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReqMapperConfirmationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessReqMapperConfirmationRequestMultiError, or nil if none found.
func (m *ProcessReqMapperConfirmationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqMapperConfirmationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqMapperConfirmationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqMapperConfirmationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqMapperConfirmationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerBank

	// no validation rules for RawData

	if len(errors) > 0 {
		return ProcessReqMapperConfirmationRequestMultiError(errors)
	}

	return nil
}

// ProcessReqMapperConfirmationRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessReqMapperConfirmationRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessReqMapperConfirmationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqMapperConfirmationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqMapperConfirmationRequestMultiError) AllErrors() []error { return m }

// ProcessReqMapperConfirmationRequestValidationError is the validation error
// returned by ProcessReqMapperConfirmationRequest.Validate if the designated
// constraints aren't met.
type ProcessReqMapperConfirmationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqMapperConfirmationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqMapperConfirmationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqMapperConfirmationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqMapperConfirmationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqMapperConfirmationRequestValidationError) ErrorName() string {
	return "ProcessReqMapperConfirmationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqMapperConfirmationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqMapperConfirmationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqMapperConfirmationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqMapperConfirmationRequestValidationError{}

// Validate checks the field values on ProcessReqMapperConfirmationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessReqMapperConfirmationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessReqMapperConfirmationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessReqMapperConfirmationResponseMultiError, or nil if none found.
func (m *ProcessReqMapperConfirmationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessReqMapperConfirmationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessReqMapperConfirmationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessReqMapperConfirmationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessReqMapperConfirmationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessReqMapperConfirmationResponseMultiError(errors)
	}

	return nil
}

// ProcessReqMapperConfirmationResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessReqMapperConfirmationResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessReqMapperConfirmationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessReqMapperConfirmationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessReqMapperConfirmationResponseMultiError) AllErrors() []error { return m }

// ProcessReqMapperConfirmationResponseValidationError is the validation error
// returned by ProcessReqMapperConfirmationResponse.Validate if the designated
// constraints aren't met.
type ProcessReqMapperConfirmationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessReqMapperConfirmationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessReqMapperConfirmationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessReqMapperConfirmationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessReqMapperConfirmationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessReqMapperConfirmationResponseValidationError) ErrorName() string {
	return "ProcessReqMapperConfirmationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessReqMapperConfirmationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessReqMapperConfirmationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessReqMapperConfirmationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessReqMapperConfirmationResponseValidationError{}
