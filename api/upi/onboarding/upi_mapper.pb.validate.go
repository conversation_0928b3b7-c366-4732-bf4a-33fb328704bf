// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/onboarding/upi_mapper.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = enums.UpiAccountPreference(0)
)

// Validate checks the field values on VpaInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VpaInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VpaInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in VpaInfoMultiError, or nil if none found.
func (m *VpaInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *VpaInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vpa

	// no validation rules for PspLogoUrl

	// no validation rules for PspName

	if len(errors) > 0 {
		return VpaInfoMultiError(errors)
	}

	return nil
}

// VpaInfoMultiError is an error wrapping multiple validation errors returned
// by VpaInfo.ValidateAll() if the designated constraints aren't met.
type VpaInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VpaInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VpaInfoMultiError) AllErrors() []error { return m }

// VpaInfoValidationError is the validation error returned by VpaInfo.Validate
// if the designated constraints aren't met.
type VpaInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VpaInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VpaInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VpaInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VpaInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VpaInfoValidationError) ErrorName() string { return "VpaInfoValidationError" }

// Error satisfies the builtin error interface
func (e VpaInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVpaInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VpaInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VpaInfoValidationError{}

// Validate checks the field values on AccountInfoForUpiMapper with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountInfoForUpiMapper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountInfoForUpiMapper with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountInfoForUpiMapperMultiError, or nil if none found.
func (m *AccountInfoForUpiMapper) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountInfoForUpiMapper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BankName

	// no validation rules for BankIfsc

	// no validation rules for AccountType

	// no validation rules for MaskedAccountNumber

	// no validation rules for AccountPreference

	// no validation rules for DerivedAccountId

	// no validation rules for Vpa

	if len(errors) > 0 {
		return AccountInfoForUpiMapperMultiError(errors)
	}

	return nil
}

// AccountInfoForUpiMapperMultiError is an error wrapping multiple validation
// errors returned by AccountInfoForUpiMapper.ValidateAll() if the designated
// constraints aren't met.
type AccountInfoForUpiMapperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountInfoForUpiMapperMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountInfoForUpiMapperMultiError) AllErrors() []error { return m }

// AccountInfoForUpiMapperValidationError is the validation error returned by
// AccountInfoForUpiMapper.Validate if the designated constraints aren't met.
type AccountInfoForUpiMapperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountInfoForUpiMapperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountInfoForUpiMapperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountInfoForUpiMapperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountInfoForUpiMapperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountInfoForUpiMapperValidationError) ErrorName() string {
	return "AccountInfoForUpiMapperValidationError"
}

// Error satisfies the builtin error interface
func (e AccountInfoForUpiMapperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountInfoForUpiMapper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountInfoForUpiMapperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountInfoForUpiMapperValidationError{}

// Validate checks the field values on UpiNumberDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpiNumberDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiNumberDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiNumberDetailMultiError, or nil if none found.
func (m *UpiNumberDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UpiNumberType

	// no validation rules for UpiNumber

	// no validation rules for UpiNumberState

	// no validation rules for UpiOnboardingStatus

	if all {
		switch v := interface{}(m.GetUpiNumberExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberDetailValidationError{
					field:  "UpiNumberExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberDetailValidationError{
					field:  "UpiNumberExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpiNumberExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberDetailValidationError{
				field:  "UpiNumberExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiNumberDetailMultiError(errors)
	}

	return nil
}

// UpiNumberDetailMultiError is an error wrapping multiple validation errors
// returned by UpiNumberDetail.ValidateAll() if the designated constraints
// aren't met.
type UpiNumberDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberDetailMultiError) AllErrors() []error { return m }

// UpiNumberDetailValidationError is the validation error returned by
// UpiNumberDetail.Validate if the designated constraints aren't met.
type UpiNumberDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiNumberDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiNumberDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiNumberDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiNumberDetailValidationError) ErrorName() string { return "UpiNumberDetailValidationError" }

// Error satisfies the builtin error interface
func (e UpiNumberDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberDetailValidationError{}

// Validate checks the field values on AccountMapperDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountMapperDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountMapperDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountMapperDetailsMultiError, or nil if none found.
func (m *AccountMapperDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountMapperDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccountInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountMapperDetailsValidationError{
					field:  "AccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountMapperDetailsValidationError{
					field:  "AccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountMapperDetailsValidationError{
				field:  "AccountInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUpiNumberDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountMapperDetailsValidationError{
						field:  fmt.Sprintf("UpiNumberDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountMapperDetailsValidationError{
						field:  fmt.Sprintf("UpiNumberDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountMapperDetailsValidationError{
					field:  fmt.Sprintf("UpiNumberDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountMapperDetailsMultiError(errors)
	}

	return nil
}

// AccountMapperDetailsMultiError is an error wrapping multiple validation
// errors returned by AccountMapperDetails.ValidateAll() if the designated
// constraints aren't met.
type AccountMapperDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountMapperDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountMapperDetailsMultiError) AllErrors() []error { return m }

// AccountMapperDetailsValidationError is the validation error returned by
// AccountMapperDetails.Validate if the designated constraints aren't met.
type AccountMapperDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountMapperDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountMapperDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountMapperDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountMapperDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountMapperDetailsValidationError) ErrorName() string {
	return "AccountMapperDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AccountMapperDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountMapperDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountMapperDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountMapperDetailsValidationError{}
