// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/bank_info.proto

package onboarding

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UPIFeature enum represents the various features supported by a bank
// in the context of the Unified Payments Interface (UPI) system.
type BankInfo_UPIFeature int32

const (
	BankInfo_UPI_FEATURE_UNSPECIFIED BankInfo_UPIFeature = 0
	BankInfo_UPI_FEATURE_MANDATE     BankInfo_UPIFeature = 1
	BankInfo_UPI_FEATURE_REFUND      BankInfo_UPIFeature = 2
	BankInfo_UPI_FEATURE_AADHAAR     BankInfo_UPIFeature = 3
)

// Enum value maps for BankInfo_UPIFeature.
var (
	BankInfo_UPIFeature_name = map[int32]string{
		0: "UPI_FEATURE_UNSPECIFIED",
		1: "UPI_FEATURE_MANDATE",
		2: "UPI_FEATURE_REFUND",
		3: "UPI_FEATURE_AADHAAR",
	}
	BankInfo_UPIFeature_value = map[string]int32{
		"UPI_FEATURE_UNSPECIFIED": 0,
		"UPI_FEATURE_MANDATE":     1,
		"UPI_FEATURE_REFUND":      2,
		"UPI_FEATURE_AADHAAR":     3,
	}
)

func (x BankInfo_UPIFeature) Enum() *BankInfo_UPIFeature {
	p := new(BankInfo_UPIFeature)
	*p = x
	return p
}

func (x BankInfo_UPIFeature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankInfo_UPIFeature) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_bank_info_proto_enumTypes[0].Descriptor()
}

func (BankInfo_UPIFeature) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_bank_info_proto_enumTypes[0]
}

func (x BankInfo_UPIFeature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankInfo_UPIFeature.Descriptor instead.
func (BankInfo_UPIFeature) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_bank_info_proto_rawDescGZIP(), []int{0, 0}
}

// BankInfo stores various info regarding the bank like the name,ifsc, logo etc
type BankInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name denotes the bank name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// logo denotes the image of the bank
	Logo string `protobuf:"bytes,2,opt,name=logo,proto3" json:"logo,omitempty"`
	// ifsc_code denotes the ifsc code of the bank
	IfscCode string `protobuf:"bytes,3,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	// supported_upi_features contains the list of UPI features supported by the bank
	SupportedUpiFeatures []BankInfo_UPIFeature `protobuf:"varint,4,rep,packed,name=supported_upi_features,json=supportedUpiFeatures,proto3,enum=upi.onboarding.BankInfo_UPIFeature" json:"supported_upi_features,omitempty"`
}

func (x *BankInfo) Reset() {
	*x = BankInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_bank_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankInfo) ProtoMessage() {}

func (x *BankInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_bank_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankInfo.ProtoReflect.Descriptor instead.
func (*BankInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_bank_info_proto_rawDescGZIP(), []int{0}
}

func (x *BankInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BankInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *BankInfo) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *BankInfo) GetSupportedUpiFeatures() []BankInfo_UPIFeature {
	if x != nil {
		return x.SupportedUpiFeatures
	}
	return nil
}

var File_api_upi_onboarding_bank_info_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_bank_info_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x22, 0x9f, 0x02, 0x0a, 0x08, 0x42, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x66, 0x73,
	0x63, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x66,
	0x73, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x59, 0x0a, 0x16, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x64, 0x5f, 0x75, 0x70, 0x69, 0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x55, 0x50, 0x49, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x14, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x55, 0x70, 0x69, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x22, 0x73, 0x0a, 0x0a, 0x55, 0x50, 0x49, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x1b, 0x0a, 0x17, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13,
	0x55, 0x50, 0x49, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x44,
	0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x17, 0x0a,
	0x13, 0x55, 0x50, 0x49, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x41, 0x44,
	0x48, 0x41, 0x41, 0x52, 0x10, 0x03, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_bank_info_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_bank_info_proto_rawDescData = file_api_upi_onboarding_bank_info_proto_rawDesc
)

func file_api_upi_onboarding_bank_info_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_bank_info_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_bank_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_bank_info_proto_rawDescData)
	})
	return file_api_upi_onboarding_bank_info_proto_rawDescData
}

var file_api_upi_onboarding_bank_info_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_bank_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_upi_onboarding_bank_info_proto_goTypes = []interface{}{
	(BankInfo_UPIFeature)(0), // 0: upi.onboarding.BankInfo.UPIFeature
	(*BankInfo)(nil),         // 1: upi.onboarding.BankInfo
}
var file_api_upi_onboarding_bank_info_proto_depIdxs = []int32{
	0, // 0: upi.onboarding.BankInfo.supported_upi_features:type_name -> upi.onboarding.BankInfo.UPIFeature
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_bank_info_proto_init() }
func file_api_upi_onboarding_bank_info_proto_init() {
	if File_api_upi_onboarding_bank_info_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_bank_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_bank_info_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_bank_info_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_bank_info_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_bank_info_proto_enumTypes,
		MessageInfos:      file_api_upi_onboarding_bank_info_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_bank_info_proto = out.File
	file_api_upi_onboarding_bank_info_proto_rawDesc = nil
	file_api_upi_onboarding_bank_info_proto_goTypes = nil
	file_api_upi_onboarding_bank_info_proto_depIdxs = nil
}
