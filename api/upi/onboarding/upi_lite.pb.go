// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/upi_lite.proto

package onboarding

import (
	order "github.com/epifi/gamma/api/order"
	paymentinstrument "github.com/epifi/gamma/api/paymentinstrument"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// order will be created by the workflow
// client needs to trigger initiatePayment
// for further fund transfer flow
type InitiatePayment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId        string                               `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	PaymentDetails *order.PaymentDetails                `protobuf:"bytes,2,opt,name=payment_details,json=paymentDetails,proto3" json:"payment_details,omitempty"`
	PayerPi        *paymentinstrument.PaymentInstrument `protobuf:"bytes,3,opt,name=payer_pi,json=payerPi,proto3" json:"payer_pi,omitempty"`
	PayeePi        *paymentinstrument.PaymentInstrument `protobuf:"bytes,4,opt,name=payee_pi,json=payeePi,proto3" json:"payee_pi,omitempty"`
	PayerAccountId string                               `protobuf:"bytes,5,opt,name=payer_account_id,json=payerAccountId,proto3" json:"payer_account_id,omitempty"`
}

func (x *InitiatePayment) Reset() {
	*x = InitiatePayment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_lite_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiatePayment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiatePayment) ProtoMessage() {}

func (x *InitiatePayment) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_lite_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiatePayment.ProtoReflect.Descriptor instead.
func (*InitiatePayment) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_lite_proto_rawDescGZIP(), []int{0}
}

func (x *InitiatePayment) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *InitiatePayment) GetPaymentDetails() *order.PaymentDetails {
	if x != nil {
		return x.PaymentDetails
	}
	return nil
}

func (x *InitiatePayment) GetPayerPi() *paymentinstrument.PaymentInstrument {
	if x != nil {
		return x.PayerPi
	}
	return nil
}

func (x *InitiatePayment) GetPayeePi() *paymentinstrument.PaymentInstrument {
	if x != nil {
		return x.PayeePi
	}
	return nil
}

func (x *InitiatePayment) GetPayerAccountId() string {
	if x != nil {
		return x.PayerAccountId
	}
	return ""
}

// once workflow is successful then client needs to call
// SyncUpiLite to fetch the latest info regarding upi lite
// account / LRN from vendor.
type SyncUpiLite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id of the upi lite account
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *SyncUpiLite) Reset() {
	*x = SyncUpiLite{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_lite_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncUpiLite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncUpiLite) ProtoMessage() {}

func (x *SyncUpiLite) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_lite_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncUpiLite.ProtoReflect.Descriptor instead.
func (*SyncUpiLite) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_lite_proto_rawDescGZIP(), []int{1}
}

func (x *SyncUpiLite) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

// If workflow is still in progress, then client
// needs to retry workflow polling as per the
// specification in PollWorkflow
type PollWorkflow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// duration after which client should
	// poll the workflow again.
	RetryTimer *durationpb.Duration `protobuf:"bytes,1,opt,name=retry_timer,json=retryTimer,proto3" json:"retry_timer,omitempty"`
}

func (x *PollWorkflow) Reset() {
	*x = PollWorkflow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_lite_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollWorkflow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollWorkflow) ProtoMessage() {}

func (x *PollWorkflow) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_lite_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollWorkflow.ProtoReflect.Descriptor instead.
func (*PollWorkflow) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_lite_proto_rawDescGZIP(), []int{2}
}

func (x *PollWorkflow) GetRetryTimer() *durationpb.Duration {
	if x != nil {
		return x.RetryTimer
	}
	return nil
}

var File_api_upi_onboarding_upi_lite_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_upi_lite_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x98,
	0x02, 0x0a, 0x0f, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3e, 0x0a,
	0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3f, 0x0a,
	0x08, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x70, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x70, 0x61, 0x79, 0x65, 0x72, 0x50, 0x69, 0x12, 0x3f,
	0x0a, 0x08, 0x70, 0x61, 0x79, 0x65, 0x65, 0x5f, 0x70, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x70, 0x61, 0x79, 0x65, 0x65, 0x50, 0x69, 0x12,
	0x28, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x65, 0x72,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x2c, 0x0a, 0x0b, 0x53, 0x79, 0x6e,
	0x63, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x4a, 0x0a, 0x0c, 0x50, 0x6f, 0x6c, 0x6c, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x3a, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x72, 0x79, 0x54, 0x69,
	0x6d, 0x65, 0x72, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69,
	0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_upi_lite_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_upi_lite_proto_rawDescData = file_api_upi_onboarding_upi_lite_proto_rawDesc
)

func file_api_upi_onboarding_upi_lite_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_upi_lite_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_upi_lite_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_upi_lite_proto_rawDescData)
	})
	return file_api_upi_onboarding_upi_lite_proto_rawDescData
}

var file_api_upi_onboarding_upi_lite_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_upi_onboarding_upi_lite_proto_goTypes = []interface{}{
	(*InitiatePayment)(nil),                     // 0: upi.onboarding.InitiatePayment
	(*SyncUpiLite)(nil),                         // 1: upi.onboarding.SyncUpiLite
	(*PollWorkflow)(nil),                        // 2: upi.onboarding.PollWorkflow
	(*order.PaymentDetails)(nil),                // 3: order.PaymentDetails
	(*paymentinstrument.PaymentInstrument)(nil), // 4: paymentinstrument.PaymentInstrument
	(*durationpb.Duration)(nil),                 // 5: google.protobuf.Duration
}
var file_api_upi_onboarding_upi_lite_proto_depIdxs = []int32{
	3, // 0: upi.onboarding.InitiatePayment.payment_details:type_name -> order.PaymentDetails
	4, // 1: upi.onboarding.InitiatePayment.payer_pi:type_name -> paymentinstrument.PaymentInstrument
	4, // 2: upi.onboarding.InitiatePayment.payee_pi:type_name -> paymentinstrument.PaymentInstrument
	5, // 3: upi.onboarding.PollWorkflow.retry_timer:type_name -> google.protobuf.Duration
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_upi_lite_proto_init() }
func file_api_upi_onboarding_upi_lite_proto_init() {
	if File_api_upi_onboarding_upi_lite_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_upi_lite_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiatePayment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_lite_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncUpiLite); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_lite_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollWorkflow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_upi_lite_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_upi_lite_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_upi_lite_proto_depIdxs,
		MessageInfos:      file_api_upi_onboarding_upi_lite_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_upi_lite_proto = out.File
	file_api_upi_onboarding_upi_lite_proto_rawDesc = nil
	file_api_upi_onboarding_upi_lite_proto_goTypes = nil
	file_api_upi_onboarding_upi_lite_proto_depIdxs = nil
}
