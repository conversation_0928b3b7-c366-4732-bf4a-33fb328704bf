// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/onboarding/actor_upi_number_resolution.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ActorUpiNumberResolution with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActorUpiNumberResolution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActorUpiNumberResolution with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActorUpiNumberResolutionMultiError, or nil if none found.
func (m *ActorUpiNumberResolution) ValidateAll() error {
	return m.validate(true)
}

func (m *ActorUpiNumberResolution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for UpiNumber

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActorUpiNumberResolutionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActorUpiNumberResolutionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActorUpiNumberResolutionValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActorUpiNumberResolutionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActorUpiNumberResolutionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActorUpiNumberResolutionValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActorUpiNumberResolutionValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActorUpiNumberResolutionValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActorUpiNumberResolutionValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActorUpiNumberResolutionMultiError(errors)
	}

	return nil
}

// ActorUpiNumberResolutionMultiError is an error wrapping multiple validation
// errors returned by ActorUpiNumberResolution.ValidateAll() if the designated
// constraints aren't met.
type ActorUpiNumberResolutionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActorUpiNumberResolutionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActorUpiNumberResolutionMultiError) AllErrors() []error { return m }

// ActorUpiNumberResolutionValidationError is the validation error returned by
// ActorUpiNumberResolution.Validate if the designated constraints aren't met.
type ActorUpiNumberResolutionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActorUpiNumberResolutionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActorUpiNumberResolutionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActorUpiNumberResolutionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActorUpiNumberResolutionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActorUpiNumberResolutionValidationError) ErrorName() string {
	return "ActorUpiNumberResolutionValidationError"
}

// Error satisfies the builtin error interface
func (e ActorUpiNumberResolutionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActorUpiNumberResolution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActorUpiNumberResolutionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActorUpiNumberResolutionValidationError{}
