// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/onboarding/account_info.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	account "github.com/epifi/gamma/api/typesv2/account"

	accounts "github.com/epifi/gamma/api/accounts"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = account.AccountProductOffering(0)

	_ = accounts.Type(0)
)

// Validate checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountInfoMultiError, or
// nil if none found.
func (m *AccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MaskedAccountNumber

	// no validation rules for IfscCode

	// no validation rules for AccountType

	// no validation rules for Apo

	if len(errors) > 0 {
		return AccountInfoMultiError(errors)
	}

	return nil
}

// AccountInfoMultiError is an error wrapping multiple validation errors
// returned by AccountInfo.ValidateAll() if the designated constraints aren't met.
type AccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountInfoMultiError) AllErrors() []error { return m }

// AccountInfoValidationError is the validation error returned by
// AccountInfo.Validate if the designated constraints aren't met.
type AccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountInfoValidationError) ErrorName() string { return "AccountInfoValidationError" }

// Error satisfies the builtin error interface
func (e AccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountInfoValidationError{}
