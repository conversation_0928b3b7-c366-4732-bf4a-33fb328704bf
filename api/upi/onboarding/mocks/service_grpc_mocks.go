// Code generated by MockGen. DO NOT EDIT.
// Source: api/upi/onboarding/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	onboarding "github.com/epifi/gamma/api/upi/onboarding"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockUpiOnboardingClient is a mock of UpiOnboardingClient interface.
type MockUpiOnboardingClient struct {
	ctrl     *gomock.Controller
	recorder *MockUpiOnboardingClientMockRecorder
}

// MockUpiOnboardingClientMockRecorder is the mock recorder for MockUpiOnboardingClient.
type MockUpiOnboardingClientMockRecorder struct {
	mock *MockUpiOnboardingClient
}

// NewMockUpiOnboardingClient creates a new mock instance.
func NewMockUpiOnboardingClient(ctrl *gomock.Controller) *MockUpiOnboardingClient {
	mock := &MockUpiOnboardingClient{ctrl: ctrl}
	mock.recorder = &MockUpiOnboardingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUpiOnboardingClient) EXPECT() *MockUpiOnboardingClientMockRecorder {
	return m.recorder
}

// ActivateInternationalPaymentsWithVendor mocks base method.
func (m *MockUpiOnboardingClient) ActivateInternationalPaymentsWithVendor(ctx context.Context, in *onboarding.ActivateInternationalPaymentsWithVendorRequest, opts ...grpc.CallOption) (*onboarding.ActivateInternationalPaymentsWithVendorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ActivateInternationalPaymentsWithVendor", varargs...)
	ret0, _ := ret[0].(*onboarding.ActivateInternationalPaymentsWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivateInternationalPaymentsWithVendor indicates an expected call of ActivateInternationalPaymentsWithVendor.
func (mr *MockUpiOnboardingClientMockRecorder) ActivateInternationalPaymentsWithVendor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivateInternationalPaymentsWithVendor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).ActivateInternationalPaymentsWithVendor), varargs...)
}

// ActivateUpiAccount mocks base method.
func (m *MockUpiOnboardingClient) ActivateUpiAccount(ctx context.Context, in *onboarding.ActivateUpiAccountRequest, opts ...grpc.CallOption) (*onboarding.ActivateUpiAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ActivateUpiAccount", varargs...)
	ret0, _ := ret[0].(*onboarding.ActivateUpiAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivateUpiAccount indicates an expected call of ActivateUpiAccount.
func (mr *MockUpiOnboardingClientMockRecorder) ActivateUpiAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivateUpiAccount", reflect.TypeOf((*MockUpiOnboardingClient)(nil).ActivateUpiAccount), varargs...)
}

// BalanceEnquiry mocks base method.
func (m *MockUpiOnboardingClient) BalanceEnquiry(ctx context.Context, in *onboarding.BalanceEnquiryRequest, opts ...grpc.CallOption) (*onboarding.BalanceEnquiryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BalanceEnquiry", varargs...)
	ret0, _ := ret[0].(*onboarding.BalanceEnquiryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BalanceEnquiry indicates an expected call of BalanceEnquiry.
func (mr *MockUpiOnboardingClientMockRecorder) BalanceEnquiry(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BalanceEnquiry", reflect.TypeOf((*MockUpiOnboardingClient)(nil).BalanceEnquiry), varargs...)
}

// CheckEligibilityForOneClickFlowPopUp mocks base method.
func (m *MockUpiOnboardingClient) CheckEligibilityForOneClickFlowPopUp(ctx context.Context, in *onboarding.CheckEligibilityForOneClickFlowPopUpRequest, opts ...grpc.CallOption) (*onboarding.CheckEligibilityForOneClickFlowPopUpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckEligibilityForOneClickFlowPopUp", varargs...)
	ret0, _ := ret[0].(*onboarding.CheckEligibilityForOneClickFlowPopUpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckEligibilityForOneClickFlowPopUp indicates an expected call of CheckEligibilityForOneClickFlowPopUp.
func (mr *MockUpiOnboardingClientMockRecorder) CheckEligibilityForOneClickFlowPopUp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckEligibilityForOneClickFlowPopUp", reflect.TypeOf((*MockUpiOnboardingClient)(nil).CheckEligibilityForOneClickFlowPopUp), varargs...)
}

// CheckIfVpaMigrationRequired mocks base method.
func (m *MockUpiOnboardingClient) CheckIfVpaMigrationRequired(ctx context.Context, in *onboarding.CheckIfVpaMigrationRequiredRequest, opts ...grpc.CallOption) (*onboarding.CheckIfVpaMigrationRequiredResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIfVpaMigrationRequired", varargs...)
	ret0, _ := ret[0].(*onboarding.CheckIfVpaMigrationRequiredResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfVpaMigrationRequired indicates an expected call of CheckIfVpaMigrationRequired.
func (mr *MockUpiOnboardingClientMockRecorder) CheckIfVpaMigrationRequired(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfVpaMigrationRequired", reflect.TypeOf((*MockUpiOnboardingClient)(nil).CheckIfVpaMigrationRequired), varargs...)
}

// CheckUpiLiteActionStatus mocks base method.
func (m *MockUpiOnboardingClient) CheckUpiLiteActionStatus(ctx context.Context, in *onboarding.CheckUpiLiteActionStatusRequest, opts ...grpc.CallOption) (*onboarding.CheckUpiLiteActionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckUpiLiteActionStatus", varargs...)
	ret0, _ := ret[0].(*onboarding.CheckUpiLiteActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUpiLiteActionStatus indicates an expected call of CheckUpiLiteActionStatus.
func (mr *MockUpiOnboardingClientMockRecorder) CheckUpiLiteActionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUpiLiteActionStatus", reflect.TypeOf((*MockUpiOnboardingClient)(nil).CheckUpiLiteActionStatus), varargs...)
}

// CheckUpiNumberStatusWithVendor mocks base method.
func (m *MockUpiOnboardingClient) CheckUpiNumberStatusWithVendor(ctx context.Context, in *onboarding.CheckUpiNumberStatusWithVendorRequest, opts ...grpc.CallOption) (*onboarding.CheckUpiNumberStatusWithVendorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckUpiNumberStatusWithVendor", varargs...)
	ret0, _ := ret[0].(*onboarding.CheckUpiNumberStatusWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUpiNumberStatusWithVendor indicates an expected call of CheckUpiNumberStatusWithVendor.
func (mr *MockUpiOnboardingClientMockRecorder) CheckUpiNumberStatusWithVendor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUpiNumberStatusWithVendor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).CheckUpiNumberStatusWithVendor), varargs...)
}

// CreateInternalPiForVpa mocks base method.
func (m *MockUpiOnboardingClient) CreateInternalPiForVpa(ctx context.Context, in *onboarding.CreateInternalPiForVpaRequest, opts ...grpc.CallOption) (*onboarding.CreateInternalPiForVpaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateInternalPiForVpa", varargs...)
	ret0, _ := ret[0].(*onboarding.CreateInternalPiForVpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInternalPiForVpa indicates an expected call of CreateInternalPiForVpa.
func (mr *MockUpiOnboardingClientMockRecorder) CreateInternalPiForVpa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInternalPiForVpa", reflect.TypeOf((*MockUpiOnboardingClient)(nil).CreateInternalPiForVpa), varargs...)
}

// CreateVpa mocks base method.
func (m *MockUpiOnboardingClient) CreateVpa(ctx context.Context, in *onboarding.CreateVpaRequest, opts ...grpc.CallOption) (*onboarding.CreateVpaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateVpa", varargs...)
	ret0, _ := ret[0].(*onboarding.CreateVpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVpa indicates an expected call of CreateVpa.
func (mr *MockUpiOnboardingClientMockRecorder) CreateVpa(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVpa", reflect.TypeOf((*MockUpiOnboardingClient)(nil).CreateVpa), varargs...)
}

// DeActivatePi mocks base method.
func (m *MockUpiOnboardingClient) DeActivatePi(ctx context.Context, in *onboarding.DeActivatePiRequest, opts ...grpc.CallOption) (*onboarding.DeActivatePiResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeActivatePi", varargs...)
	ret0, _ := ret[0].(*onboarding.DeActivatePiResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeActivatePi indicates an expected call of DeActivatePi.
func (mr *MockUpiOnboardingClientMockRecorder) DeActivatePi(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeActivatePi", reflect.TypeOf((*MockUpiOnboardingClient)(nil).DeActivatePi), varargs...)
}

// DeactivateInternationalPaymentsWithVendor mocks base method.
func (m *MockUpiOnboardingClient) DeactivateInternationalPaymentsWithVendor(ctx context.Context, in *onboarding.DeactivateInternationalPaymentsWithVendorRequest, opts ...grpc.CallOption) (*onboarding.DeactivateInternationalPaymentsWithVendorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeactivateInternationalPaymentsWithVendor", varargs...)
	ret0, _ := ret[0].(*onboarding.DeactivateInternationalPaymentsWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeactivateInternationalPaymentsWithVendor indicates an expected call of DeactivateInternationalPaymentsWithVendor.
func (mr *MockUpiOnboardingClientMockRecorder) DeactivateInternationalPaymentsWithVendor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateInternationalPaymentsWithVendor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).DeactivateInternationalPaymentsWithVendor), varargs...)
}

// DelinkUpiAccountWithVendor mocks base method.
func (m *MockUpiOnboardingClient) DelinkUpiAccountWithVendor(ctx context.Context, in *onboarding.DelinkUpiAccountWithVendorRequest, opts ...grpc.CallOption) (*onboarding.DelinkUpiAccountWithVendorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelinkUpiAccountWithVendor", varargs...)
	ret0, _ := ret[0].(*onboarding.DelinkUpiAccountWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelinkUpiAccountWithVendor indicates an expected call of DelinkUpiAccountWithVendor.
func (mr *MockUpiOnboardingClientMockRecorder) DelinkUpiAccountWithVendor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelinkUpiAccountWithVendor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).DelinkUpiAccountWithVendor), varargs...)
}

// DelinkUpiNumber mocks base method.
func (m *MockUpiOnboardingClient) DelinkUpiNumber(ctx context.Context, in *onboarding.DelinkUpiNumberRequest, opts ...grpc.CallOption) (*onboarding.DelinkUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelinkUpiNumber", varargs...)
	ret0, _ := ret[0].(*onboarding.DelinkUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelinkUpiNumber indicates an expected call of DelinkUpiNumber.
func (mr *MockUpiOnboardingClientMockRecorder) DelinkUpiNumber(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelinkUpiNumber", reflect.TypeOf((*MockUpiOnboardingClient)(nil).DelinkUpiNumber), varargs...)
}

// DisableOrEnableUpiNumber mocks base method.
func (m *MockUpiOnboardingClient) DisableOrEnableUpiNumber(ctx context.Context, in *onboarding.DisableOrEnableUpiNumberRequest, opts ...grpc.CallOption) (*onboarding.DisableOrEnableUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DisableOrEnableUpiNumber", varargs...)
	ret0, _ := ret[0].(*onboarding.DisableOrEnableUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableOrEnableUpiNumber indicates an expected call of DisableOrEnableUpiNumber.
func (mr *MockUpiOnboardingClientMockRecorder) DisableOrEnableUpiNumber(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableOrEnableUpiNumber", reflect.TypeOf((*MockUpiOnboardingClient)(nil).DisableOrEnableUpiNumber), varargs...)
}

// GetAccount mocks base method.
func (m *MockUpiOnboardingClient) GetAccount(ctx context.Context, in *onboarding.GetAccountRequest, opts ...grpc.CallOption) (*onboarding.GetAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccount", varargs...)
	ret0, _ := ret[0].(*onboarding.GetAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccount indicates an expected call of GetAccount.
func (mr *MockUpiOnboardingClientMockRecorder) GetAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccount", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetAccount), varargs...)
}

// GetAccountByIdentifier mocks base method.
func (m *MockUpiOnboardingClient) GetAccountByIdentifier(ctx context.Context, in *onboarding.GetAccountByIdentifierRequest, opts ...grpc.CallOption) (*onboarding.GetAccountByIdentifierResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountByIdentifier", varargs...)
	ret0, _ := ret[0].(*onboarding.GetAccountByIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByIdentifier indicates an expected call of GetAccountByIdentifier.
func (mr *MockUpiOnboardingClientMockRecorder) GetAccountByIdentifier(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByIdentifier", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetAccountByIdentifier), varargs...)
}

// GetAccounts mocks base method.
func (m *MockUpiOnboardingClient) GetAccounts(ctx context.Context, in *onboarding.GetAccountsRequest, opts ...grpc.CallOption) (*onboarding.GetAccountsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccounts", varargs...)
	ret0, _ := ret[0].(*onboarding.GetAccountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccounts indicates an expected call of GetAccounts.
func (mr *MockUpiOnboardingClientMockRecorder) GetAccounts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccounts", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetAccounts), varargs...)
}

// GetAccountsForUpiLiteActivation mocks base method.
func (m *MockUpiOnboardingClient) GetAccountsForUpiLiteActivation(ctx context.Context, in *onboarding.GetAccountsForUpiLiteActivationRequest, opts ...grpc.CallOption) (*onboarding.GetAccountsForUpiLiteActivationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountsForUpiLiteActivation", varargs...)
	ret0, _ := ret[0].(*onboarding.GetAccountsForUpiLiteActivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountsForUpiLiteActivation indicates an expected call of GetAccountsForUpiLiteActivation.
func (mr *MockUpiOnboardingClientMockRecorder) GetAccountsForUpiLiteActivation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountsForUpiLiteActivation", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetAccountsForUpiLiteActivation), varargs...)
}

// GetConsolidatedUpiNumberDetails mocks base method.
func (m *MockUpiOnboardingClient) GetConsolidatedUpiNumberDetails(ctx context.Context, in *onboarding.GetConsolidatedUpiNumberDetailsRequest, opts ...grpc.CallOption) (*onboarding.GetConsolidatedUpiNumberDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConsolidatedUpiNumberDetails", varargs...)
	ret0, _ := ret[0].(*onboarding.GetConsolidatedUpiNumberDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsolidatedUpiNumberDetails indicates an expected call of GetConsolidatedUpiNumberDetails.
func (mr *MockUpiOnboardingClientMockRecorder) GetConsolidatedUpiNumberDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsolidatedUpiNumberDetails", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetConsolidatedUpiNumberDetails), varargs...)
}

// GetInternationPaymentDetailsForAccount mocks base method.
func (m *MockUpiOnboardingClient) GetInternationPaymentDetailsForAccount(ctx context.Context, in *onboarding.GetInternationPaymentDetailsForAccountRequest, opts ...grpc.CallOption) (*onboarding.GetInternationPaymentDetailsForAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInternationPaymentDetailsForAccount", varargs...)
	ret0, _ := ret[0].(*onboarding.GetInternationPaymentDetailsForAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInternationPaymentDetailsForAccount indicates an expected call of GetInternationPaymentDetailsForAccount.
func (mr *MockUpiOnboardingClientMockRecorder) GetInternationPaymentDetailsForAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternationPaymentDetailsForAccount", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetInternationPaymentDetailsForAccount), varargs...)
}

// GetInternationalPaymentActionStatus mocks base method.
func (m *MockUpiOnboardingClient) GetInternationalPaymentActionStatus(ctx context.Context, in *onboarding.GetInternationalPaymentActionStatusRequest, opts ...grpc.CallOption) (*onboarding.GetInternationalPaymentActionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInternationalPaymentActionStatus", varargs...)
	ret0, _ := ret[0].(*onboarding.GetInternationalPaymentActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInternationalPaymentActionStatus indicates an expected call of GetInternationalPaymentActionStatus.
func (mr *MockUpiOnboardingClientMockRecorder) GetInternationalPaymentActionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternationalPaymentActionStatus", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetInternationalPaymentActionStatus), varargs...)
}

// GetLatestUpiOnboardingDetailForAccount mocks base method.
func (m *MockUpiOnboardingClient) GetLatestUpiOnboardingDetailForAccount(ctx context.Context, in *onboarding.GetLatestUpiOnboardingDetailForAccountRequest, opts ...grpc.CallOption) (*onboarding.GetLatestUpiOnboardingDetailForAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestUpiOnboardingDetailForAccount", varargs...)
	ret0, _ := ret[0].(*onboarding.GetLatestUpiOnboardingDetailForAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestUpiOnboardingDetailForAccount indicates an expected call of GetLatestUpiOnboardingDetailForAccount.
func (mr *MockUpiOnboardingClientMockRecorder) GetLatestUpiOnboardingDetailForAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestUpiOnboardingDetailForAccount", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetLatestUpiOnboardingDetailForAccount), varargs...)
}

// GetLatestUpiRequestLogForAccount mocks base method.
func (m *MockUpiOnboardingClient) GetLatestUpiRequestLogForAccount(ctx context.Context, in *onboarding.GetLatestUpiRequestLogForAccountRequest, opts ...grpc.CallOption) (*onboarding.GetLatestUpiRequestLogForAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestUpiRequestLogForAccount", varargs...)
	ret0, _ := ret[0].(*onboarding.GetLatestUpiRequestLogForAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestUpiRequestLogForAccount indicates an expected call of GetLatestUpiRequestLogForAccount.
func (mr *MockUpiOnboardingClientMockRecorder) GetLatestUpiRequestLogForAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestUpiRequestLogForAccount", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetLatestUpiRequestLogForAccount), varargs...)
}

// GetMapperInfo mocks base method.
func (m *MockUpiOnboardingClient) GetMapperInfo(ctx context.Context, in *onboarding.GetMapperInfoRequest, opts ...grpc.CallOption) (*onboarding.GetMapperInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMapperInfo", varargs...)
	ret0, _ := ret[0].(*onboarding.GetMapperInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMapperInfo indicates an expected call of GetMapperInfo.
func (mr *MockUpiOnboardingClientMockRecorder) GetMapperInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMapperInfo", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetMapperInfo), varargs...)
}

// GetPinStatus mocks base method.
func (m *MockUpiOnboardingClient) GetPinStatus(ctx context.Context, in *onboarding.GetPinStatusRequest, opts ...grpc.CallOption) (*onboarding.GetPinStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPinStatus", varargs...)
	ret0, _ := ret[0].(*onboarding.GetPinStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPinStatus indicates an expected call of GetPinStatus.
func (mr *MockUpiOnboardingClientMockRecorder) GetPinStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPinStatus", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetPinStatus), varargs...)
}

// GetTpapFeatureStatus mocks base method.
func (m *MockUpiOnboardingClient) GetTpapFeatureStatus(ctx context.Context, in *onboarding.GetTpapFeatureStatusRequest, opts ...grpc.CallOption) (*onboarding.GetTpapFeatureStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTpapFeatureStatus", varargs...)
	ret0, _ := ret[0].(*onboarding.GetTpapFeatureStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTpapFeatureStatus indicates an expected call of GetTpapFeatureStatus.
func (mr *MockUpiOnboardingClientMockRecorder) GetTpapFeatureStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTpapFeatureStatus", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetTpapFeatureStatus), varargs...)
}

// GetUpiAccountsActionStatus mocks base method.
func (m *MockUpiOnboardingClient) GetUpiAccountsActionStatus(ctx context.Context, in *onboarding.GetUpiAccountsActionStatusRequest, opts ...grpc.CallOption) (*onboarding.GetUpiAccountsActionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUpiAccountsActionStatus", varargs...)
	ret0, _ := ret[0].(*onboarding.GetUpiAccountsActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiAccountsActionStatus indicates an expected call of GetUpiAccountsActionStatus.
func (mr *MockUpiOnboardingClientMockRecorder) GetUpiAccountsActionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiAccountsActionStatus", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetUpiAccountsActionStatus), varargs...)
}

// GetUpiLiteInfo mocks base method.
func (m *MockUpiOnboardingClient) GetUpiLiteInfo(ctx context.Context, in *onboarding.GetUpiLiteInfoRequest, opts ...grpc.CallOption) (*onboarding.GetUpiLiteInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUpiLiteInfo", varargs...)
	ret0, _ := ret[0].(*onboarding.GetUpiLiteInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiLiteInfo indicates an expected call of GetUpiLiteInfo.
func (mr *MockUpiOnboardingClientMockRecorder) GetUpiLiteInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiLiteInfo", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetUpiLiteInfo), varargs...)
}

// GetUpiNumberActionStatus mocks base method.
func (m *MockUpiOnboardingClient) GetUpiNumberActionStatus(ctx context.Context, in *onboarding.GetUpiNumberActionStatusRequest, opts ...grpc.CallOption) (*onboarding.GetUpiNumberActionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUpiNumberActionStatus", varargs...)
	ret0, _ := ret[0].(*onboarding.GetUpiNumberActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiNumberActionStatus indicates an expected call of GetUpiNumberActionStatus.
func (mr *MockUpiOnboardingClientMockRecorder) GetUpiNumberActionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiNumberActionStatus", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetUpiNumberActionStatus), varargs...)
}

// GetUpiNumberDetails mocks base method.
func (m *MockUpiOnboardingClient) GetUpiNumberDetails(ctx context.Context, in *onboarding.GetUpiNumberDetailsRequest, opts ...grpc.CallOption) (*onboarding.GetUpiNumberDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUpiNumberDetails", varargs...)
	ret0, _ := ret[0].(*onboarding.GetUpiNumberDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiNumberDetails indicates an expected call of GetUpiNumberDetails.
func (mr *MockUpiOnboardingClientMockRecorder) GetUpiNumberDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiNumberDetails", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetUpiNumberDetails), varargs...)
}

// GetUpiNumberPiMapping mocks base method.
func (m *MockUpiOnboardingClient) GetUpiNumberPiMapping(ctx context.Context, in *onboarding.GetUpiNumberPiMappingRequest, opts ...grpc.CallOption) (*onboarding.GetUpiNumberPiMappingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUpiNumberPiMapping", varargs...)
	ret0, _ := ret[0].(*onboarding.GetUpiNumberPiMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiNumberPiMapping indicates an expected call of GetUpiNumberPiMapping.
func (mr *MockUpiOnboardingClientMockRecorder) GetUpiNumberPiMapping(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiNumberPiMapping", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetUpiNumberPiMapping), varargs...)
}

// GetUpiPinSetOptionsForAccountId mocks base method.
func (m *MockUpiOnboardingClient) GetUpiPinSetOptionsForAccountId(ctx context.Context, in *onboarding.GetUpiPinSetOptionsForAccountIdRequest, opts ...grpc.CallOption) (*onboarding.GetUpiPinSetOptionsForAccountIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUpiPinSetOptionsForAccountId", varargs...)
	ret0, _ := ret[0].(*onboarding.GetUpiPinSetOptionsForAccountIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiPinSetOptionsForAccountId indicates an expected call of GetUpiPinSetOptionsForAccountId.
func (mr *MockUpiOnboardingClientMockRecorder) GetUpiPinSetOptionsForAccountId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiPinSetOptionsForAccountId", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetUpiPinSetOptionsForAccountId), varargs...)
}

// GetVpaNameForActor mocks base method.
func (m *MockUpiOnboardingClient) GetVpaNameForActor(ctx context.Context, in *onboarding.GetVpaNameForActorRequest, opts ...grpc.CallOption) (*onboarding.GetVpaNameForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVpaNameForActor", varargs...)
	ret0, _ := ret[0].(*onboarding.GetVpaNameForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVpaNameForActor indicates an expected call of GetVpaNameForActor.
func (mr *MockUpiOnboardingClientMockRecorder) GetVpaNameForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVpaNameForActor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).GetVpaNameForActor), varargs...)
}

// InitiateDelinkUpiAccount mocks base method.
func (m *MockUpiOnboardingClient) InitiateDelinkUpiAccount(ctx context.Context, in *onboarding.InitiateDelinkUpiAccountRequest, opts ...grpc.CallOption) (*onboarding.InitiateDelinkUpiAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateDelinkUpiAccount", varargs...)
	ret0, _ := ret[0].(*onboarding.InitiateDelinkUpiAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateDelinkUpiAccount indicates an expected call of InitiateDelinkUpiAccount.
func (mr *MockUpiOnboardingClientMockRecorder) InitiateDelinkUpiAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateDelinkUpiAccount", reflect.TypeOf((*MockUpiOnboardingClient)(nil).InitiateDelinkUpiAccount), varargs...)
}

// InitiateDelinkUpiNumber mocks base method.
func (m *MockUpiOnboardingClient) InitiateDelinkUpiNumber(ctx context.Context, in *onboarding.InitiateDelinkUpiNumberRequest, opts ...grpc.CallOption) (*onboarding.InitiateDelinkUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateDelinkUpiNumber", varargs...)
	ret0, _ := ret[0].(*onboarding.InitiateDelinkUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateDelinkUpiNumber indicates an expected call of InitiateDelinkUpiNumber.
func (mr *MockUpiOnboardingClientMockRecorder) InitiateDelinkUpiNumber(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateDelinkUpiNumber", reflect.TypeOf((*MockUpiOnboardingClient)(nil).InitiateDelinkUpiNumber), varargs...)
}

// InitiateInternationalPaymentsActivation mocks base method.
func (m *MockUpiOnboardingClient) InitiateInternationalPaymentsActivation(ctx context.Context, in *onboarding.InitiateInternationalPaymentsActivationRequest, opts ...grpc.CallOption) (*onboarding.InitiateInternationalPaymentsActivationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateInternationalPaymentsActivation", varargs...)
	ret0, _ := ret[0].(*onboarding.InitiateInternationalPaymentsActivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateInternationalPaymentsActivation indicates an expected call of InitiateInternationalPaymentsActivation.
func (mr *MockUpiOnboardingClientMockRecorder) InitiateInternationalPaymentsActivation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateInternationalPaymentsActivation", reflect.TypeOf((*MockUpiOnboardingClient)(nil).InitiateInternationalPaymentsActivation), varargs...)
}

// InitiateInternationalPaymentsDeactivation mocks base method.
func (m *MockUpiOnboardingClient) InitiateInternationalPaymentsDeactivation(ctx context.Context, in *onboarding.InitiateInternationalPaymentsDeactivationRequest, opts ...grpc.CallOption) (*onboarding.InitiateInternationalPaymentsDeactivationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateInternationalPaymentsDeactivation", varargs...)
	ret0, _ := ret[0].(*onboarding.InitiateInternationalPaymentsDeactivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateInternationalPaymentsDeactivation indicates an expected call of InitiateInternationalPaymentsDeactivation.
func (mr *MockUpiOnboardingClientMockRecorder) InitiateInternationalPaymentsDeactivation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateInternationalPaymentsDeactivation", reflect.TypeOf((*MockUpiOnboardingClient)(nil).InitiateInternationalPaymentsDeactivation), varargs...)
}

// InitiateLinkUpiNumber mocks base method.
func (m *MockUpiOnboardingClient) InitiateLinkUpiNumber(ctx context.Context, in *onboarding.InitiateLinkUpiNumberRequest, opts ...grpc.CallOption) (*onboarding.InitiateLinkUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateLinkUpiNumber", varargs...)
	ret0, _ := ret[0].(*onboarding.InitiateLinkUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateLinkUpiNumber indicates an expected call of InitiateLinkUpiNumber.
func (mr *MockUpiOnboardingClientMockRecorder) InitiateLinkUpiNumber(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateLinkUpiNumber", reflect.TypeOf((*MockUpiOnboardingClient)(nil).InitiateLinkUpiNumber), varargs...)
}

// InitiateUpiLiteActivation mocks base method.
func (m *MockUpiOnboardingClient) InitiateUpiLiteActivation(ctx context.Context, in *onboarding.InitiateUpiLiteActivationRequest, opts ...grpc.CallOption) (*onboarding.InitiateUpiLiteActivationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateUpiLiteActivation", varargs...)
	ret0, _ := ret[0].(*onboarding.InitiateUpiLiteActivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateUpiLiteActivation indicates an expected call of InitiateUpiLiteActivation.
func (mr *MockUpiOnboardingClientMockRecorder) InitiateUpiLiteActivation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateUpiLiteActivation", reflect.TypeOf((*MockUpiOnboardingClient)(nil).InitiateUpiLiteActivation), varargs...)
}

// InitiateUpiLiteDeactivation mocks base method.
func (m *MockUpiOnboardingClient) InitiateUpiLiteDeactivation(ctx context.Context, in *onboarding.InitiateUpiLiteDeactivationRequest, opts ...grpc.CallOption) (*onboarding.InitiateUpiLiteDeactivationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateUpiLiteDeactivation", varargs...)
	ret0, _ := ret[0].(*onboarding.InitiateUpiLiteDeactivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateUpiLiteDeactivation indicates an expected call of InitiateUpiLiteDeactivation.
func (mr *MockUpiOnboardingClientMockRecorder) InitiateUpiLiteDeactivation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateUpiLiteDeactivation", reflect.TypeOf((*MockUpiOnboardingClient)(nil).InitiateUpiLiteDeactivation), varargs...)
}

// InternationalPaymentsActionEnquiryWithVendor mocks base method.
func (m *MockUpiOnboardingClient) InternationalPaymentsActionEnquiryWithVendor(ctx context.Context, in *onboarding.InternationalPaymentsActionEnquiryWithVendorRequest, opts ...grpc.CallOption) (*onboarding.InternationalPaymentsActionEnquiryWithVendorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InternationalPaymentsActionEnquiryWithVendor", varargs...)
	ret0, _ := ret[0].(*onboarding.InternationalPaymentsActionEnquiryWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InternationalPaymentsActionEnquiryWithVendor indicates an expected call of InternationalPaymentsActionEnquiryWithVendor.
func (mr *MockUpiOnboardingClientMockRecorder) InternationalPaymentsActionEnquiryWithVendor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternationalPaymentsActionEnquiryWithVendor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).InternationalPaymentsActionEnquiryWithVendor), varargs...)
}

// IsAccountActionAllowed mocks base method.
func (m *MockUpiOnboardingClient) IsAccountActionAllowed(ctx context.Context, in *onboarding.IsAccountActionAllowedRequest, opts ...grpc.CallOption) (*onboarding.IsAccountActionAllowedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsAccountActionAllowed", varargs...)
	ret0, _ := ret[0].(*onboarding.IsAccountActionAllowedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsAccountActionAllowed indicates an expected call of IsAccountActionAllowed.
func (mr *MockUpiOnboardingClientMockRecorder) IsAccountActionAllowed(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsAccountActionAllowed", reflect.TypeOf((*MockUpiOnboardingClient)(nil).IsAccountActionAllowed), varargs...)
}

// IsCcLinkingEnabledForActor mocks base method.
func (m *MockUpiOnboardingClient) IsCcLinkingEnabledForActor(ctx context.Context, in *onboarding.IsCcLinkingEnabledForActorRequest, opts ...grpc.CallOption) (*onboarding.IsCcLinkingEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsCcLinkingEnabledForActor", varargs...)
	ret0, _ := ret[0].(*onboarding.IsCcLinkingEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsCcLinkingEnabledForActor indicates an expected call of IsCcLinkingEnabledForActor.
func (mr *MockUpiOnboardingClientMockRecorder) IsCcLinkingEnabledForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCcLinkingEnabledForActor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).IsCcLinkingEnabledForActor), varargs...)
}

// IsFeatureEnabledForActor mocks base method.
func (m *MockUpiOnboardingClient) IsFeatureEnabledForActor(ctx context.Context, in *onboarding.IsFeatureEnabledForActorRequest, opts ...grpc.CallOption) (*onboarding.IsFeatureEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsFeatureEnabledForActor", varargs...)
	ret0, _ := ret[0].(*onboarding.IsFeatureEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsFeatureEnabledForActor indicates an expected call of IsFeatureEnabledForActor.
func (mr *MockUpiOnboardingClientMockRecorder) IsFeatureEnabledForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsFeatureEnabledForActor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).IsFeatureEnabledForActor), varargs...)
}

// IsMapperEnabledForActor mocks base method.
func (m *MockUpiOnboardingClient) IsMapperEnabledForActor(ctx context.Context, in *onboarding.IsMapperEnabledForActorRequest, opts ...grpc.CallOption) (*onboarding.IsMapperEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsMapperEnabledForActor", varargs...)
	ret0, _ := ret[0].(*onboarding.IsMapperEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsMapperEnabledForActor indicates an expected call of IsMapperEnabledForActor.
func (mr *MockUpiOnboardingClientMockRecorder) IsMapperEnabledForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMapperEnabledForActor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).IsMapperEnabledForActor), varargs...)
}

// IsTpapEnabledForActor mocks base method.
func (m *MockUpiOnboardingClient) IsTpapEnabledForActor(ctx context.Context, in *onboarding.IsTpapEnabledForActorRequest, opts ...grpc.CallOption) (*onboarding.IsTpapEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsTpapEnabledForActor", varargs...)
	ret0, _ := ret[0].(*onboarding.IsTpapEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTpapEnabledForActor indicates an expected call of IsTpapEnabledForActor.
func (mr *MockUpiOnboardingClientMockRecorder) IsTpapEnabledForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTpapEnabledForActor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).IsTpapEnabledForActor), varargs...)
}

// IsUpiInternationalPaymentEnabledForActor mocks base method.
func (m *MockUpiOnboardingClient) IsUpiInternationalPaymentEnabledForActor(ctx context.Context, in *onboarding.IsUpiInternationalPaymentEnabledForActorRequest, opts ...grpc.CallOption) (*onboarding.IsUpiInternationalPaymentEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsUpiInternationalPaymentEnabledForActor", varargs...)
	ret0, _ := ret[0].(*onboarding.IsUpiInternationalPaymentEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUpiInternationalPaymentEnabledForActor indicates an expected call of IsUpiInternationalPaymentEnabledForActor.
func (mr *MockUpiOnboardingClientMockRecorder) IsUpiInternationalPaymentEnabledForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUpiInternationalPaymentEnabledForActor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).IsUpiInternationalPaymentEnabledForActor), varargs...)
}

// IsUpiLiteEnabledForActor mocks base method.
func (m *MockUpiOnboardingClient) IsUpiLiteEnabledForActor(ctx context.Context, in *onboarding.IsUpiLiteEnabledForActorRequest, opts ...grpc.CallOption) (*onboarding.IsUpiLiteEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsUpiLiteEnabledForActor", varargs...)
	ret0, _ := ret[0].(*onboarding.IsUpiLiteEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUpiLiteEnabledForActor indicates an expected call of IsUpiLiteEnabledForActor.
func (mr *MockUpiOnboardingClientMockRecorder) IsUpiLiteEnabledForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUpiLiteEnabledForActor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).IsUpiLiteEnabledForActor), varargs...)
}

// IsUpiPinSetUsingAadhaarEnabledForActor mocks base method.
func (m *MockUpiOnboardingClient) IsUpiPinSetUsingAadhaarEnabledForActor(ctx context.Context, in *onboarding.IsUpiPinSetUsingAadhaarEnabledForActorRequest, opts ...grpc.CallOption) (*onboarding.IsUpiPinSetUsingAadhaarEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsUpiPinSetUsingAadhaarEnabledForActor", varargs...)
	ret0, _ := ret[0].(*onboarding.IsUpiPinSetUsingAadhaarEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUpiPinSetUsingAadhaarEnabledForActor indicates an expected call of IsUpiPinSetUsingAadhaarEnabledForActor.
func (mr *MockUpiOnboardingClientMockRecorder) IsUpiPinSetUsingAadhaarEnabledForActor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUpiPinSetUsingAadhaarEnabledForActor", reflect.TypeOf((*MockUpiOnboardingClient)(nil).IsUpiPinSetUsingAadhaarEnabledForActor), varargs...)
}

// LinkInternalAccount mocks base method.
func (m *MockUpiOnboardingClient) LinkInternalAccount(ctx context.Context, in *onboarding.LinkInternalAccountRequest, opts ...grpc.CallOption) (*onboarding.LinkInternalAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LinkInternalAccount", varargs...)
	ret0, _ := ret[0].(*onboarding.LinkInternalAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkInternalAccount indicates an expected call of LinkInternalAccount.
func (mr *MockUpiOnboardingClientMockRecorder) LinkInternalAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkInternalAccount", reflect.TypeOf((*MockUpiOnboardingClient)(nil).LinkInternalAccount), varargs...)
}

// LinkUpiAccounts mocks base method.
func (m *MockUpiOnboardingClient) LinkUpiAccounts(ctx context.Context, in *onboarding.LinkUpiAccountsRequest, opts ...grpc.CallOption) (*onboarding.LinkUpiAccountsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LinkUpiAccounts", varargs...)
	ret0, _ := ret[0].(*onboarding.LinkUpiAccountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkUpiAccounts indicates an expected call of LinkUpiAccounts.
func (mr *MockUpiOnboardingClientMockRecorder) LinkUpiAccounts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkUpiAccounts", reflect.TypeOf((*MockUpiOnboardingClient)(nil).LinkUpiAccounts), varargs...)
}

// LinkUpiNumber mocks base method.
func (m *MockUpiOnboardingClient) LinkUpiNumber(ctx context.Context, in *onboarding.LinkUpiNumberRequest, opts ...grpc.CallOption) (*onboarding.LinkUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LinkUpiNumber", varargs...)
	ret0, _ := ret[0].(*onboarding.LinkUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkUpiNumber indicates an expected call of LinkUpiNumber.
func (mr *MockUpiOnboardingClientMockRecorder) LinkUpiNumber(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkUpiNumber", reflect.TypeOf((*MockUpiOnboardingClient)(nil).LinkUpiNumber), varargs...)
}

// ListAccount mocks base method.
func (m *MockUpiOnboardingClient) ListAccount(ctx context.Context, in *onboarding.ListAccountRequest, opts ...grpc.CallOption) (*onboarding.ListAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAccount", varargs...)
	ret0, _ := ret[0].(*onboarding.ListAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccount indicates an expected call of ListAccount.
func (mr *MockUpiOnboardingClientMockRecorder) ListAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccount", reflect.TypeOf((*MockUpiOnboardingClient)(nil).ListAccount), varargs...)
}

// ListAccountProviders mocks base method.
func (m *MockUpiOnboardingClient) ListAccountProviders(ctx context.Context, in *onboarding.ListAccountProvidersRequest, opts ...grpc.CallOption) (*onboarding.ListAccountProvidersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAccountProviders", varargs...)
	ret0, _ := ret[0].(*onboarding.ListAccountProvidersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccountProviders indicates an expected call of ListAccountProviders.
func (mr *MockUpiOnboardingClientMockRecorder) ListAccountProviders(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccountProviders", reflect.TypeOf((*MockUpiOnboardingClient)(nil).ListAccountProviders), varargs...)
}

// SyncUpiLiteInfo mocks base method.
func (m *MockUpiOnboardingClient) SyncUpiLiteInfo(ctx context.Context, in *onboarding.SyncUpiLiteInfoRequest, opts ...grpc.CallOption) (*onboarding.SyncUpiLiteInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SyncUpiLiteInfo", varargs...)
	ret0, _ := ret[0].(*onboarding.SyncUpiLiteInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncUpiLiteInfo indicates an expected call of SyncUpiLiteInfo.
func (mr *MockUpiOnboardingClientMockRecorder) SyncUpiLiteInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncUpiLiteInfo", reflect.TypeOf((*MockUpiOnboardingClient)(nil).SyncUpiLiteInfo), varargs...)
}

// UpdateAccountPreference mocks base method.
func (m *MockUpiOnboardingClient) UpdateAccountPreference(ctx context.Context, in *onboarding.UpdateAccountPreferenceRequest, opts ...grpc.CallOption) (*onboarding.UpdateAccountPreferenceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAccountPreference", varargs...)
	ret0, _ := ret[0].(*onboarding.UpdateAccountPreferenceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAccountPreference indicates an expected call of UpdateAccountPreference.
func (mr *MockUpiOnboardingClientMockRecorder) UpdateAccountPreference(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountPreference", reflect.TypeOf((*MockUpiOnboardingClient)(nil).UpdateAccountPreference), varargs...)
}

// UpdateDefaultMerchantPaymentPreference mocks base method.
func (m *MockUpiOnboardingClient) UpdateDefaultMerchantPaymentPreference(ctx context.Context, in *onboarding.UpdateDefaultMerchantPaymentPreferenceRequest, opts ...grpc.CallOption) (*onboarding.UpdateDefaultMerchantPaymentPreferenceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateDefaultMerchantPaymentPreference", varargs...)
	ret0, _ := ret[0].(*onboarding.UpdateDefaultMerchantPaymentPreferenceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateDefaultMerchantPaymentPreference indicates an expected call of UpdateDefaultMerchantPaymentPreference.
func (mr *MockUpiOnboardingClientMockRecorder) UpdateDefaultMerchantPaymentPreference(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDefaultMerchantPaymentPreference", reflect.TypeOf((*MockUpiOnboardingClient)(nil).UpdateDefaultMerchantPaymentPreference), varargs...)
}

// ValidateAadhaarNoForUpiPinSet mocks base method.
func (m *MockUpiOnboardingClient) ValidateAadhaarNoForUpiPinSet(ctx context.Context, in *onboarding.ValidateAadhaarNoForUpiPinSetRequest, opts ...grpc.CallOption) (*onboarding.ValidateAadhaarNoForUpiPinSetResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateAadhaarNoForUpiPinSet", varargs...)
	ret0, _ := ret[0].(*onboarding.ValidateAadhaarNoForUpiPinSetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateAadhaarNoForUpiPinSet indicates an expected call of ValidateAadhaarNoForUpiPinSet.
func (mr *MockUpiOnboardingClientMockRecorder) ValidateAadhaarNoForUpiPinSet(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateAadhaarNoForUpiPinSet", reflect.TypeOf((*MockUpiOnboardingClient)(nil).ValidateAadhaarNoForUpiPinSet), varargs...)
}

// ValidateUpiInternationalQr mocks base method.
func (m *MockUpiOnboardingClient) ValidateUpiInternationalQr(ctx context.Context, in *onboarding.ValidateUpiInternationalQrRequest, opts ...grpc.CallOption) (*onboarding.ValidateUpiInternationalQrResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateUpiInternationalQr", varargs...)
	ret0, _ := ret[0].(*onboarding.ValidateUpiInternationalQrResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateUpiInternationalQr indicates an expected call of ValidateUpiInternationalQr.
func (mr *MockUpiOnboardingClientMockRecorder) ValidateUpiInternationalQr(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateUpiInternationalQr", reflect.TypeOf((*MockUpiOnboardingClient)(nil).ValidateUpiInternationalQr), varargs...)
}

// MockUpiOnboardingServer is a mock of UpiOnboardingServer interface.
type MockUpiOnboardingServer struct {
	ctrl     *gomock.Controller
	recorder *MockUpiOnboardingServerMockRecorder
}

// MockUpiOnboardingServerMockRecorder is the mock recorder for MockUpiOnboardingServer.
type MockUpiOnboardingServerMockRecorder struct {
	mock *MockUpiOnboardingServer
}

// NewMockUpiOnboardingServer creates a new mock instance.
func NewMockUpiOnboardingServer(ctrl *gomock.Controller) *MockUpiOnboardingServer {
	mock := &MockUpiOnboardingServer{ctrl: ctrl}
	mock.recorder = &MockUpiOnboardingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUpiOnboardingServer) EXPECT() *MockUpiOnboardingServerMockRecorder {
	return m.recorder
}

// ActivateInternationalPaymentsWithVendor mocks base method.
func (m *MockUpiOnboardingServer) ActivateInternationalPaymentsWithVendor(arg0 context.Context, arg1 *onboarding.ActivateInternationalPaymentsWithVendorRequest) (*onboarding.ActivateInternationalPaymentsWithVendorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ActivateInternationalPaymentsWithVendor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.ActivateInternationalPaymentsWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivateInternationalPaymentsWithVendor indicates an expected call of ActivateInternationalPaymentsWithVendor.
func (mr *MockUpiOnboardingServerMockRecorder) ActivateInternationalPaymentsWithVendor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivateInternationalPaymentsWithVendor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).ActivateInternationalPaymentsWithVendor), arg0, arg1)
}

// ActivateUpiAccount mocks base method.
func (m *MockUpiOnboardingServer) ActivateUpiAccount(arg0 context.Context, arg1 *onboarding.ActivateUpiAccountRequest) (*onboarding.ActivateUpiAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ActivateUpiAccount", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.ActivateUpiAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ActivateUpiAccount indicates an expected call of ActivateUpiAccount.
func (mr *MockUpiOnboardingServerMockRecorder) ActivateUpiAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ActivateUpiAccount", reflect.TypeOf((*MockUpiOnboardingServer)(nil).ActivateUpiAccount), arg0, arg1)
}

// BalanceEnquiry mocks base method.
func (m *MockUpiOnboardingServer) BalanceEnquiry(arg0 context.Context, arg1 *onboarding.BalanceEnquiryRequest) (*onboarding.BalanceEnquiryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BalanceEnquiry", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.BalanceEnquiryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BalanceEnquiry indicates an expected call of BalanceEnquiry.
func (mr *MockUpiOnboardingServerMockRecorder) BalanceEnquiry(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BalanceEnquiry", reflect.TypeOf((*MockUpiOnboardingServer)(nil).BalanceEnquiry), arg0, arg1)
}

// CheckEligibilityForOneClickFlowPopUp mocks base method.
func (m *MockUpiOnboardingServer) CheckEligibilityForOneClickFlowPopUp(arg0 context.Context, arg1 *onboarding.CheckEligibilityForOneClickFlowPopUpRequest) (*onboarding.CheckEligibilityForOneClickFlowPopUpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckEligibilityForOneClickFlowPopUp", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.CheckEligibilityForOneClickFlowPopUpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckEligibilityForOneClickFlowPopUp indicates an expected call of CheckEligibilityForOneClickFlowPopUp.
func (mr *MockUpiOnboardingServerMockRecorder) CheckEligibilityForOneClickFlowPopUp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckEligibilityForOneClickFlowPopUp", reflect.TypeOf((*MockUpiOnboardingServer)(nil).CheckEligibilityForOneClickFlowPopUp), arg0, arg1)
}

// CheckIfVpaMigrationRequired mocks base method.
func (m *MockUpiOnboardingServer) CheckIfVpaMigrationRequired(arg0 context.Context, arg1 *onboarding.CheckIfVpaMigrationRequiredRequest) (*onboarding.CheckIfVpaMigrationRequiredResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIfVpaMigrationRequired", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.CheckIfVpaMigrationRequiredResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfVpaMigrationRequired indicates an expected call of CheckIfVpaMigrationRequired.
func (mr *MockUpiOnboardingServerMockRecorder) CheckIfVpaMigrationRequired(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfVpaMigrationRequired", reflect.TypeOf((*MockUpiOnboardingServer)(nil).CheckIfVpaMigrationRequired), arg0, arg1)
}

// CheckUpiLiteActionStatus mocks base method.
func (m *MockUpiOnboardingServer) CheckUpiLiteActionStatus(arg0 context.Context, arg1 *onboarding.CheckUpiLiteActionStatusRequest) (*onboarding.CheckUpiLiteActionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUpiLiteActionStatus", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.CheckUpiLiteActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUpiLiteActionStatus indicates an expected call of CheckUpiLiteActionStatus.
func (mr *MockUpiOnboardingServerMockRecorder) CheckUpiLiteActionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUpiLiteActionStatus", reflect.TypeOf((*MockUpiOnboardingServer)(nil).CheckUpiLiteActionStatus), arg0, arg1)
}

// CheckUpiNumberStatusWithVendor mocks base method.
func (m *MockUpiOnboardingServer) CheckUpiNumberStatusWithVendor(arg0 context.Context, arg1 *onboarding.CheckUpiNumberStatusWithVendorRequest) (*onboarding.CheckUpiNumberStatusWithVendorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckUpiNumberStatusWithVendor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.CheckUpiNumberStatusWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckUpiNumberStatusWithVendor indicates an expected call of CheckUpiNumberStatusWithVendor.
func (mr *MockUpiOnboardingServerMockRecorder) CheckUpiNumberStatusWithVendor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckUpiNumberStatusWithVendor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).CheckUpiNumberStatusWithVendor), arg0, arg1)
}

// CreateInternalPiForVpa mocks base method.
func (m *MockUpiOnboardingServer) CreateInternalPiForVpa(arg0 context.Context, arg1 *onboarding.CreateInternalPiForVpaRequest) (*onboarding.CreateInternalPiForVpaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInternalPiForVpa", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.CreateInternalPiForVpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInternalPiForVpa indicates an expected call of CreateInternalPiForVpa.
func (mr *MockUpiOnboardingServerMockRecorder) CreateInternalPiForVpa(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInternalPiForVpa", reflect.TypeOf((*MockUpiOnboardingServer)(nil).CreateInternalPiForVpa), arg0, arg1)
}

// CreateVpa mocks base method.
func (m *MockUpiOnboardingServer) CreateVpa(arg0 context.Context, arg1 *onboarding.CreateVpaRequest) (*onboarding.CreateVpaResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateVpa", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.CreateVpaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVpa indicates an expected call of CreateVpa.
func (mr *MockUpiOnboardingServerMockRecorder) CreateVpa(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVpa", reflect.TypeOf((*MockUpiOnboardingServer)(nil).CreateVpa), arg0, arg1)
}

// DeActivatePi mocks base method.
func (m *MockUpiOnboardingServer) DeActivatePi(arg0 context.Context, arg1 *onboarding.DeActivatePiRequest) (*onboarding.DeActivatePiResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeActivatePi", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.DeActivatePiResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeActivatePi indicates an expected call of DeActivatePi.
func (mr *MockUpiOnboardingServerMockRecorder) DeActivatePi(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeActivatePi", reflect.TypeOf((*MockUpiOnboardingServer)(nil).DeActivatePi), arg0, arg1)
}

// DeactivateInternationalPaymentsWithVendor mocks base method.
func (m *MockUpiOnboardingServer) DeactivateInternationalPaymentsWithVendor(arg0 context.Context, arg1 *onboarding.DeactivateInternationalPaymentsWithVendorRequest) (*onboarding.DeactivateInternationalPaymentsWithVendorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeactivateInternationalPaymentsWithVendor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.DeactivateInternationalPaymentsWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeactivateInternationalPaymentsWithVendor indicates an expected call of DeactivateInternationalPaymentsWithVendor.
func (mr *MockUpiOnboardingServerMockRecorder) DeactivateInternationalPaymentsWithVendor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateInternationalPaymentsWithVendor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).DeactivateInternationalPaymentsWithVendor), arg0, arg1)
}

// DelinkUpiAccountWithVendor mocks base method.
func (m *MockUpiOnboardingServer) DelinkUpiAccountWithVendor(arg0 context.Context, arg1 *onboarding.DelinkUpiAccountWithVendorRequest) (*onboarding.DelinkUpiAccountWithVendorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelinkUpiAccountWithVendor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.DelinkUpiAccountWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelinkUpiAccountWithVendor indicates an expected call of DelinkUpiAccountWithVendor.
func (mr *MockUpiOnboardingServerMockRecorder) DelinkUpiAccountWithVendor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelinkUpiAccountWithVendor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).DelinkUpiAccountWithVendor), arg0, arg1)
}

// DelinkUpiNumber mocks base method.
func (m *MockUpiOnboardingServer) DelinkUpiNumber(arg0 context.Context, arg1 *onboarding.DelinkUpiNumberRequest) (*onboarding.DelinkUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelinkUpiNumber", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.DelinkUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelinkUpiNumber indicates an expected call of DelinkUpiNumber.
func (mr *MockUpiOnboardingServerMockRecorder) DelinkUpiNumber(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelinkUpiNumber", reflect.TypeOf((*MockUpiOnboardingServer)(nil).DelinkUpiNumber), arg0, arg1)
}

// DisableOrEnableUpiNumber mocks base method.
func (m *MockUpiOnboardingServer) DisableOrEnableUpiNumber(arg0 context.Context, arg1 *onboarding.DisableOrEnableUpiNumberRequest) (*onboarding.DisableOrEnableUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableOrEnableUpiNumber", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.DisableOrEnableUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableOrEnableUpiNumber indicates an expected call of DisableOrEnableUpiNumber.
func (mr *MockUpiOnboardingServerMockRecorder) DisableOrEnableUpiNumber(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableOrEnableUpiNumber", reflect.TypeOf((*MockUpiOnboardingServer)(nil).DisableOrEnableUpiNumber), arg0, arg1)
}

// GetAccount mocks base method.
func (m *MockUpiOnboardingServer) GetAccount(arg0 context.Context, arg1 *onboarding.GetAccountRequest) (*onboarding.GetAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccount", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccount indicates an expected call of GetAccount.
func (mr *MockUpiOnboardingServerMockRecorder) GetAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccount", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetAccount), arg0, arg1)
}

// GetAccountByIdentifier mocks base method.
func (m *MockUpiOnboardingServer) GetAccountByIdentifier(arg0 context.Context, arg1 *onboarding.GetAccountByIdentifierRequest) (*onboarding.GetAccountByIdentifierResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountByIdentifier", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetAccountByIdentifierResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountByIdentifier indicates an expected call of GetAccountByIdentifier.
func (mr *MockUpiOnboardingServerMockRecorder) GetAccountByIdentifier(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountByIdentifier", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetAccountByIdentifier), arg0, arg1)
}

// GetAccounts mocks base method.
func (m *MockUpiOnboardingServer) GetAccounts(arg0 context.Context, arg1 *onboarding.GetAccountsRequest) (*onboarding.GetAccountsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccounts", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetAccountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccounts indicates an expected call of GetAccounts.
func (mr *MockUpiOnboardingServerMockRecorder) GetAccounts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccounts", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetAccounts), arg0, arg1)
}

// GetAccountsForUpiLiteActivation mocks base method.
func (m *MockUpiOnboardingServer) GetAccountsForUpiLiteActivation(arg0 context.Context, arg1 *onboarding.GetAccountsForUpiLiteActivationRequest) (*onboarding.GetAccountsForUpiLiteActivationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountsForUpiLiteActivation", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetAccountsForUpiLiteActivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountsForUpiLiteActivation indicates an expected call of GetAccountsForUpiLiteActivation.
func (mr *MockUpiOnboardingServerMockRecorder) GetAccountsForUpiLiteActivation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountsForUpiLiteActivation", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetAccountsForUpiLiteActivation), arg0, arg1)
}

// GetConsolidatedUpiNumberDetails mocks base method.
func (m *MockUpiOnboardingServer) GetConsolidatedUpiNumberDetails(arg0 context.Context, arg1 *onboarding.GetConsolidatedUpiNumberDetailsRequest) (*onboarding.GetConsolidatedUpiNumberDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConsolidatedUpiNumberDetails", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetConsolidatedUpiNumberDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConsolidatedUpiNumberDetails indicates an expected call of GetConsolidatedUpiNumberDetails.
func (mr *MockUpiOnboardingServerMockRecorder) GetConsolidatedUpiNumberDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConsolidatedUpiNumberDetails", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetConsolidatedUpiNumberDetails), arg0, arg1)
}

// GetInternationPaymentDetailsForAccount mocks base method.
func (m *MockUpiOnboardingServer) GetInternationPaymentDetailsForAccount(arg0 context.Context, arg1 *onboarding.GetInternationPaymentDetailsForAccountRequest) (*onboarding.GetInternationPaymentDetailsForAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInternationPaymentDetailsForAccount", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetInternationPaymentDetailsForAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInternationPaymentDetailsForAccount indicates an expected call of GetInternationPaymentDetailsForAccount.
func (mr *MockUpiOnboardingServerMockRecorder) GetInternationPaymentDetailsForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternationPaymentDetailsForAccount", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetInternationPaymentDetailsForAccount), arg0, arg1)
}

// GetInternationalPaymentActionStatus mocks base method.
func (m *MockUpiOnboardingServer) GetInternationalPaymentActionStatus(arg0 context.Context, arg1 *onboarding.GetInternationalPaymentActionStatusRequest) (*onboarding.GetInternationalPaymentActionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInternationalPaymentActionStatus", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetInternationalPaymentActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInternationalPaymentActionStatus indicates an expected call of GetInternationalPaymentActionStatus.
func (mr *MockUpiOnboardingServerMockRecorder) GetInternationalPaymentActionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternationalPaymentActionStatus", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetInternationalPaymentActionStatus), arg0, arg1)
}

// GetLatestUpiOnboardingDetailForAccount mocks base method.
func (m *MockUpiOnboardingServer) GetLatestUpiOnboardingDetailForAccount(arg0 context.Context, arg1 *onboarding.GetLatestUpiOnboardingDetailForAccountRequest) (*onboarding.GetLatestUpiOnboardingDetailForAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestUpiOnboardingDetailForAccount", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetLatestUpiOnboardingDetailForAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestUpiOnboardingDetailForAccount indicates an expected call of GetLatestUpiOnboardingDetailForAccount.
func (mr *MockUpiOnboardingServerMockRecorder) GetLatestUpiOnboardingDetailForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestUpiOnboardingDetailForAccount", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetLatestUpiOnboardingDetailForAccount), arg0, arg1)
}

// GetLatestUpiRequestLogForAccount mocks base method.
func (m *MockUpiOnboardingServer) GetLatestUpiRequestLogForAccount(arg0 context.Context, arg1 *onboarding.GetLatestUpiRequestLogForAccountRequest) (*onboarding.GetLatestUpiRequestLogForAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestUpiRequestLogForAccount", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetLatestUpiRequestLogForAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestUpiRequestLogForAccount indicates an expected call of GetLatestUpiRequestLogForAccount.
func (mr *MockUpiOnboardingServerMockRecorder) GetLatestUpiRequestLogForAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestUpiRequestLogForAccount", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetLatestUpiRequestLogForAccount), arg0, arg1)
}

// GetMapperInfo mocks base method.
func (m *MockUpiOnboardingServer) GetMapperInfo(arg0 context.Context, arg1 *onboarding.GetMapperInfoRequest) (*onboarding.GetMapperInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMapperInfo", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetMapperInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMapperInfo indicates an expected call of GetMapperInfo.
func (mr *MockUpiOnboardingServerMockRecorder) GetMapperInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMapperInfo", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetMapperInfo), arg0, arg1)
}

// GetPinStatus mocks base method.
func (m *MockUpiOnboardingServer) GetPinStatus(arg0 context.Context, arg1 *onboarding.GetPinStatusRequest) (*onboarding.GetPinStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPinStatus", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetPinStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPinStatus indicates an expected call of GetPinStatus.
func (mr *MockUpiOnboardingServerMockRecorder) GetPinStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPinStatus", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetPinStatus), arg0, arg1)
}

// GetTpapFeatureStatus mocks base method.
func (m *MockUpiOnboardingServer) GetTpapFeatureStatus(arg0 context.Context, arg1 *onboarding.GetTpapFeatureStatusRequest) (*onboarding.GetTpapFeatureStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTpapFeatureStatus", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetTpapFeatureStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTpapFeatureStatus indicates an expected call of GetTpapFeatureStatus.
func (mr *MockUpiOnboardingServerMockRecorder) GetTpapFeatureStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTpapFeatureStatus", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetTpapFeatureStatus), arg0, arg1)
}

// GetUpiAccountsActionStatus mocks base method.
func (m *MockUpiOnboardingServer) GetUpiAccountsActionStatus(arg0 context.Context, arg1 *onboarding.GetUpiAccountsActionStatusRequest) (*onboarding.GetUpiAccountsActionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpiAccountsActionStatus", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetUpiAccountsActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiAccountsActionStatus indicates an expected call of GetUpiAccountsActionStatus.
func (mr *MockUpiOnboardingServerMockRecorder) GetUpiAccountsActionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiAccountsActionStatus", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetUpiAccountsActionStatus), arg0, arg1)
}

// GetUpiLiteInfo mocks base method.
func (m *MockUpiOnboardingServer) GetUpiLiteInfo(arg0 context.Context, arg1 *onboarding.GetUpiLiteInfoRequest) (*onboarding.GetUpiLiteInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpiLiteInfo", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetUpiLiteInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiLiteInfo indicates an expected call of GetUpiLiteInfo.
func (mr *MockUpiOnboardingServerMockRecorder) GetUpiLiteInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiLiteInfo", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetUpiLiteInfo), arg0, arg1)
}

// GetUpiNumberActionStatus mocks base method.
func (m *MockUpiOnboardingServer) GetUpiNumberActionStatus(arg0 context.Context, arg1 *onboarding.GetUpiNumberActionStatusRequest) (*onboarding.GetUpiNumberActionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpiNumberActionStatus", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetUpiNumberActionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiNumberActionStatus indicates an expected call of GetUpiNumberActionStatus.
func (mr *MockUpiOnboardingServerMockRecorder) GetUpiNumberActionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiNumberActionStatus", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetUpiNumberActionStatus), arg0, arg1)
}

// GetUpiNumberDetails mocks base method.
func (m *MockUpiOnboardingServer) GetUpiNumberDetails(arg0 context.Context, arg1 *onboarding.GetUpiNumberDetailsRequest) (*onboarding.GetUpiNumberDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpiNumberDetails", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetUpiNumberDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiNumberDetails indicates an expected call of GetUpiNumberDetails.
func (mr *MockUpiOnboardingServerMockRecorder) GetUpiNumberDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiNumberDetails", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetUpiNumberDetails), arg0, arg1)
}

// GetUpiNumberPiMapping mocks base method.
func (m *MockUpiOnboardingServer) GetUpiNumberPiMapping(arg0 context.Context, arg1 *onboarding.GetUpiNumberPiMappingRequest) (*onboarding.GetUpiNumberPiMappingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpiNumberPiMapping", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetUpiNumberPiMappingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiNumberPiMapping indicates an expected call of GetUpiNumberPiMapping.
func (mr *MockUpiOnboardingServerMockRecorder) GetUpiNumberPiMapping(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiNumberPiMapping", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetUpiNumberPiMapping), arg0, arg1)
}

// GetUpiPinSetOptionsForAccountId mocks base method.
func (m *MockUpiOnboardingServer) GetUpiPinSetOptionsForAccountId(arg0 context.Context, arg1 *onboarding.GetUpiPinSetOptionsForAccountIdRequest) (*onboarding.GetUpiPinSetOptionsForAccountIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpiPinSetOptionsForAccountId", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetUpiPinSetOptionsForAccountIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpiPinSetOptionsForAccountId indicates an expected call of GetUpiPinSetOptionsForAccountId.
func (mr *MockUpiOnboardingServerMockRecorder) GetUpiPinSetOptionsForAccountId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpiPinSetOptionsForAccountId", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetUpiPinSetOptionsForAccountId), arg0, arg1)
}

// GetVpaNameForActor mocks base method.
func (m *MockUpiOnboardingServer) GetVpaNameForActor(arg0 context.Context, arg1 *onboarding.GetVpaNameForActorRequest) (*onboarding.GetVpaNameForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVpaNameForActor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.GetVpaNameForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVpaNameForActor indicates an expected call of GetVpaNameForActor.
func (mr *MockUpiOnboardingServerMockRecorder) GetVpaNameForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVpaNameForActor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).GetVpaNameForActor), arg0, arg1)
}

// InitiateDelinkUpiAccount mocks base method.
func (m *MockUpiOnboardingServer) InitiateDelinkUpiAccount(arg0 context.Context, arg1 *onboarding.InitiateDelinkUpiAccountRequest) (*onboarding.InitiateDelinkUpiAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateDelinkUpiAccount", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.InitiateDelinkUpiAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateDelinkUpiAccount indicates an expected call of InitiateDelinkUpiAccount.
func (mr *MockUpiOnboardingServerMockRecorder) InitiateDelinkUpiAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateDelinkUpiAccount", reflect.TypeOf((*MockUpiOnboardingServer)(nil).InitiateDelinkUpiAccount), arg0, arg1)
}

// InitiateDelinkUpiNumber mocks base method.
func (m *MockUpiOnboardingServer) InitiateDelinkUpiNumber(arg0 context.Context, arg1 *onboarding.InitiateDelinkUpiNumberRequest) (*onboarding.InitiateDelinkUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateDelinkUpiNumber", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.InitiateDelinkUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateDelinkUpiNumber indicates an expected call of InitiateDelinkUpiNumber.
func (mr *MockUpiOnboardingServerMockRecorder) InitiateDelinkUpiNumber(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateDelinkUpiNumber", reflect.TypeOf((*MockUpiOnboardingServer)(nil).InitiateDelinkUpiNumber), arg0, arg1)
}

// InitiateInternationalPaymentsActivation mocks base method.
func (m *MockUpiOnboardingServer) InitiateInternationalPaymentsActivation(arg0 context.Context, arg1 *onboarding.InitiateInternationalPaymentsActivationRequest) (*onboarding.InitiateInternationalPaymentsActivationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateInternationalPaymentsActivation", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.InitiateInternationalPaymentsActivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateInternationalPaymentsActivation indicates an expected call of InitiateInternationalPaymentsActivation.
func (mr *MockUpiOnboardingServerMockRecorder) InitiateInternationalPaymentsActivation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateInternationalPaymentsActivation", reflect.TypeOf((*MockUpiOnboardingServer)(nil).InitiateInternationalPaymentsActivation), arg0, arg1)
}

// InitiateInternationalPaymentsDeactivation mocks base method.
func (m *MockUpiOnboardingServer) InitiateInternationalPaymentsDeactivation(arg0 context.Context, arg1 *onboarding.InitiateInternationalPaymentsDeactivationRequest) (*onboarding.InitiateInternationalPaymentsDeactivationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateInternationalPaymentsDeactivation", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.InitiateInternationalPaymentsDeactivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateInternationalPaymentsDeactivation indicates an expected call of InitiateInternationalPaymentsDeactivation.
func (mr *MockUpiOnboardingServerMockRecorder) InitiateInternationalPaymentsDeactivation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateInternationalPaymentsDeactivation", reflect.TypeOf((*MockUpiOnboardingServer)(nil).InitiateInternationalPaymentsDeactivation), arg0, arg1)
}

// InitiateLinkUpiNumber mocks base method.
func (m *MockUpiOnboardingServer) InitiateLinkUpiNumber(arg0 context.Context, arg1 *onboarding.InitiateLinkUpiNumberRequest) (*onboarding.InitiateLinkUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateLinkUpiNumber", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.InitiateLinkUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateLinkUpiNumber indicates an expected call of InitiateLinkUpiNumber.
func (mr *MockUpiOnboardingServerMockRecorder) InitiateLinkUpiNumber(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateLinkUpiNumber", reflect.TypeOf((*MockUpiOnboardingServer)(nil).InitiateLinkUpiNumber), arg0, arg1)
}

// InitiateUpiLiteActivation mocks base method.
func (m *MockUpiOnboardingServer) InitiateUpiLiteActivation(arg0 context.Context, arg1 *onboarding.InitiateUpiLiteActivationRequest) (*onboarding.InitiateUpiLiteActivationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateUpiLiteActivation", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.InitiateUpiLiteActivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateUpiLiteActivation indicates an expected call of InitiateUpiLiteActivation.
func (mr *MockUpiOnboardingServerMockRecorder) InitiateUpiLiteActivation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateUpiLiteActivation", reflect.TypeOf((*MockUpiOnboardingServer)(nil).InitiateUpiLiteActivation), arg0, arg1)
}

// InitiateUpiLiteDeactivation mocks base method.
func (m *MockUpiOnboardingServer) InitiateUpiLiteDeactivation(arg0 context.Context, arg1 *onboarding.InitiateUpiLiteDeactivationRequest) (*onboarding.InitiateUpiLiteDeactivationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateUpiLiteDeactivation", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.InitiateUpiLiteDeactivationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateUpiLiteDeactivation indicates an expected call of InitiateUpiLiteDeactivation.
func (mr *MockUpiOnboardingServerMockRecorder) InitiateUpiLiteDeactivation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateUpiLiteDeactivation", reflect.TypeOf((*MockUpiOnboardingServer)(nil).InitiateUpiLiteDeactivation), arg0, arg1)
}

// InternationalPaymentsActionEnquiryWithVendor mocks base method.
func (m *MockUpiOnboardingServer) InternationalPaymentsActionEnquiryWithVendor(arg0 context.Context, arg1 *onboarding.InternationalPaymentsActionEnquiryWithVendorRequest) (*onboarding.InternationalPaymentsActionEnquiryWithVendorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InternationalPaymentsActionEnquiryWithVendor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.InternationalPaymentsActionEnquiryWithVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InternationalPaymentsActionEnquiryWithVendor indicates an expected call of InternationalPaymentsActionEnquiryWithVendor.
func (mr *MockUpiOnboardingServerMockRecorder) InternationalPaymentsActionEnquiryWithVendor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternationalPaymentsActionEnquiryWithVendor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).InternationalPaymentsActionEnquiryWithVendor), arg0, arg1)
}

// IsAccountActionAllowed mocks base method.
func (m *MockUpiOnboardingServer) IsAccountActionAllowed(arg0 context.Context, arg1 *onboarding.IsAccountActionAllowedRequest) (*onboarding.IsAccountActionAllowedResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsAccountActionAllowed", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.IsAccountActionAllowedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsAccountActionAllowed indicates an expected call of IsAccountActionAllowed.
func (mr *MockUpiOnboardingServerMockRecorder) IsAccountActionAllowed(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsAccountActionAllowed", reflect.TypeOf((*MockUpiOnboardingServer)(nil).IsAccountActionAllowed), arg0, arg1)
}

// IsCcLinkingEnabledForActor mocks base method.
func (m *MockUpiOnboardingServer) IsCcLinkingEnabledForActor(arg0 context.Context, arg1 *onboarding.IsCcLinkingEnabledForActorRequest) (*onboarding.IsCcLinkingEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsCcLinkingEnabledForActor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.IsCcLinkingEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsCcLinkingEnabledForActor indicates an expected call of IsCcLinkingEnabledForActor.
func (mr *MockUpiOnboardingServerMockRecorder) IsCcLinkingEnabledForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCcLinkingEnabledForActor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).IsCcLinkingEnabledForActor), arg0, arg1)
}

// IsFeatureEnabledForActor mocks base method.
func (m *MockUpiOnboardingServer) IsFeatureEnabledForActor(arg0 context.Context, arg1 *onboarding.IsFeatureEnabledForActorRequest) (*onboarding.IsFeatureEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsFeatureEnabledForActor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.IsFeatureEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsFeatureEnabledForActor indicates an expected call of IsFeatureEnabledForActor.
func (mr *MockUpiOnboardingServerMockRecorder) IsFeatureEnabledForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsFeatureEnabledForActor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).IsFeatureEnabledForActor), arg0, arg1)
}

// IsMapperEnabledForActor mocks base method.
func (m *MockUpiOnboardingServer) IsMapperEnabledForActor(arg0 context.Context, arg1 *onboarding.IsMapperEnabledForActorRequest) (*onboarding.IsMapperEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsMapperEnabledForActor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.IsMapperEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsMapperEnabledForActor indicates an expected call of IsMapperEnabledForActor.
func (mr *MockUpiOnboardingServerMockRecorder) IsMapperEnabledForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMapperEnabledForActor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).IsMapperEnabledForActor), arg0, arg1)
}

// IsTpapEnabledForActor mocks base method.
func (m *MockUpiOnboardingServer) IsTpapEnabledForActor(arg0 context.Context, arg1 *onboarding.IsTpapEnabledForActorRequest) (*onboarding.IsTpapEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTpapEnabledForActor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.IsTpapEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTpapEnabledForActor indicates an expected call of IsTpapEnabledForActor.
func (mr *MockUpiOnboardingServerMockRecorder) IsTpapEnabledForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTpapEnabledForActor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).IsTpapEnabledForActor), arg0, arg1)
}

// IsUpiInternationalPaymentEnabledForActor mocks base method.
func (m *MockUpiOnboardingServer) IsUpiInternationalPaymentEnabledForActor(arg0 context.Context, arg1 *onboarding.IsUpiInternationalPaymentEnabledForActorRequest) (*onboarding.IsUpiInternationalPaymentEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUpiInternationalPaymentEnabledForActor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.IsUpiInternationalPaymentEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUpiInternationalPaymentEnabledForActor indicates an expected call of IsUpiInternationalPaymentEnabledForActor.
func (mr *MockUpiOnboardingServerMockRecorder) IsUpiInternationalPaymentEnabledForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUpiInternationalPaymentEnabledForActor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).IsUpiInternationalPaymentEnabledForActor), arg0, arg1)
}

// IsUpiLiteEnabledForActor mocks base method.
func (m *MockUpiOnboardingServer) IsUpiLiteEnabledForActor(arg0 context.Context, arg1 *onboarding.IsUpiLiteEnabledForActorRequest) (*onboarding.IsUpiLiteEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUpiLiteEnabledForActor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.IsUpiLiteEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUpiLiteEnabledForActor indicates an expected call of IsUpiLiteEnabledForActor.
func (mr *MockUpiOnboardingServerMockRecorder) IsUpiLiteEnabledForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUpiLiteEnabledForActor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).IsUpiLiteEnabledForActor), arg0, arg1)
}

// IsUpiPinSetUsingAadhaarEnabledForActor mocks base method.
func (m *MockUpiOnboardingServer) IsUpiPinSetUsingAadhaarEnabledForActor(arg0 context.Context, arg1 *onboarding.IsUpiPinSetUsingAadhaarEnabledForActorRequest) (*onboarding.IsUpiPinSetUsingAadhaarEnabledForActorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsUpiPinSetUsingAadhaarEnabledForActor", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.IsUpiPinSetUsingAadhaarEnabledForActorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsUpiPinSetUsingAadhaarEnabledForActor indicates an expected call of IsUpiPinSetUsingAadhaarEnabledForActor.
func (mr *MockUpiOnboardingServerMockRecorder) IsUpiPinSetUsingAadhaarEnabledForActor(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsUpiPinSetUsingAadhaarEnabledForActor", reflect.TypeOf((*MockUpiOnboardingServer)(nil).IsUpiPinSetUsingAadhaarEnabledForActor), arg0, arg1)
}

// LinkInternalAccount mocks base method.
func (m *MockUpiOnboardingServer) LinkInternalAccount(arg0 context.Context, arg1 *onboarding.LinkInternalAccountRequest) (*onboarding.LinkInternalAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkInternalAccount", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.LinkInternalAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkInternalAccount indicates an expected call of LinkInternalAccount.
func (mr *MockUpiOnboardingServerMockRecorder) LinkInternalAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkInternalAccount", reflect.TypeOf((*MockUpiOnboardingServer)(nil).LinkInternalAccount), arg0, arg1)
}

// LinkUpiAccounts mocks base method.
func (m *MockUpiOnboardingServer) LinkUpiAccounts(arg0 context.Context, arg1 *onboarding.LinkUpiAccountsRequest) (*onboarding.LinkUpiAccountsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkUpiAccounts", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.LinkUpiAccountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkUpiAccounts indicates an expected call of LinkUpiAccounts.
func (mr *MockUpiOnboardingServerMockRecorder) LinkUpiAccounts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkUpiAccounts", reflect.TypeOf((*MockUpiOnboardingServer)(nil).LinkUpiAccounts), arg0, arg1)
}

// LinkUpiNumber mocks base method.
func (m *MockUpiOnboardingServer) LinkUpiNumber(arg0 context.Context, arg1 *onboarding.LinkUpiNumberRequest) (*onboarding.LinkUpiNumberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkUpiNumber", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.LinkUpiNumberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LinkUpiNumber indicates an expected call of LinkUpiNumber.
func (mr *MockUpiOnboardingServerMockRecorder) LinkUpiNumber(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkUpiNumber", reflect.TypeOf((*MockUpiOnboardingServer)(nil).LinkUpiNumber), arg0, arg1)
}

// ListAccount mocks base method.
func (m *MockUpiOnboardingServer) ListAccount(arg0 context.Context, arg1 *onboarding.ListAccountRequest) (*onboarding.ListAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAccount", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.ListAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccount indicates an expected call of ListAccount.
func (mr *MockUpiOnboardingServerMockRecorder) ListAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccount", reflect.TypeOf((*MockUpiOnboardingServer)(nil).ListAccount), arg0, arg1)
}

// ListAccountProviders mocks base method.
func (m *MockUpiOnboardingServer) ListAccountProviders(arg0 context.Context, arg1 *onboarding.ListAccountProvidersRequest) (*onboarding.ListAccountProvidersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAccountProviders", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.ListAccountProvidersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAccountProviders indicates an expected call of ListAccountProviders.
func (mr *MockUpiOnboardingServerMockRecorder) ListAccountProviders(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAccountProviders", reflect.TypeOf((*MockUpiOnboardingServer)(nil).ListAccountProviders), arg0, arg1)
}

// SyncUpiLiteInfo mocks base method.
func (m *MockUpiOnboardingServer) SyncUpiLiteInfo(arg0 context.Context, arg1 *onboarding.SyncUpiLiteInfoRequest) (*onboarding.SyncUpiLiteInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncUpiLiteInfo", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.SyncUpiLiteInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncUpiLiteInfo indicates an expected call of SyncUpiLiteInfo.
func (mr *MockUpiOnboardingServerMockRecorder) SyncUpiLiteInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncUpiLiteInfo", reflect.TypeOf((*MockUpiOnboardingServer)(nil).SyncUpiLiteInfo), arg0, arg1)
}

// UpdateAccountPreference mocks base method.
func (m *MockUpiOnboardingServer) UpdateAccountPreference(arg0 context.Context, arg1 *onboarding.UpdateAccountPreferenceRequest) (*onboarding.UpdateAccountPreferenceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountPreference", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.UpdateAccountPreferenceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAccountPreference indicates an expected call of UpdateAccountPreference.
func (mr *MockUpiOnboardingServerMockRecorder) UpdateAccountPreference(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountPreference", reflect.TypeOf((*MockUpiOnboardingServer)(nil).UpdateAccountPreference), arg0, arg1)
}

// UpdateDefaultMerchantPaymentPreference mocks base method.
func (m *MockUpiOnboardingServer) UpdateDefaultMerchantPaymentPreference(arg0 context.Context, arg1 *onboarding.UpdateDefaultMerchantPaymentPreferenceRequest) (*onboarding.UpdateDefaultMerchantPaymentPreferenceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDefaultMerchantPaymentPreference", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.UpdateDefaultMerchantPaymentPreferenceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateDefaultMerchantPaymentPreference indicates an expected call of UpdateDefaultMerchantPaymentPreference.
func (mr *MockUpiOnboardingServerMockRecorder) UpdateDefaultMerchantPaymentPreference(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDefaultMerchantPaymentPreference", reflect.TypeOf((*MockUpiOnboardingServer)(nil).UpdateDefaultMerchantPaymentPreference), arg0, arg1)
}

// ValidateAadhaarNoForUpiPinSet mocks base method.
func (m *MockUpiOnboardingServer) ValidateAadhaarNoForUpiPinSet(arg0 context.Context, arg1 *onboarding.ValidateAadhaarNoForUpiPinSetRequest) (*onboarding.ValidateAadhaarNoForUpiPinSetResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateAadhaarNoForUpiPinSet", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.ValidateAadhaarNoForUpiPinSetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateAadhaarNoForUpiPinSet indicates an expected call of ValidateAadhaarNoForUpiPinSet.
func (mr *MockUpiOnboardingServerMockRecorder) ValidateAadhaarNoForUpiPinSet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateAadhaarNoForUpiPinSet", reflect.TypeOf((*MockUpiOnboardingServer)(nil).ValidateAadhaarNoForUpiPinSet), arg0, arg1)
}

// ValidateUpiInternationalQr mocks base method.
func (m *MockUpiOnboardingServer) ValidateUpiInternationalQr(arg0 context.Context, arg1 *onboarding.ValidateUpiInternationalQrRequest) (*onboarding.ValidateUpiInternationalQrResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateUpiInternationalQr", arg0, arg1)
	ret0, _ := ret[0].(*onboarding.ValidateUpiInternationalQrResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateUpiInternationalQr indicates an expected call of ValidateUpiInternationalQr.
func (mr *MockUpiOnboardingServerMockRecorder) ValidateUpiInternationalQr(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateUpiInternationalQr", reflect.TypeOf((*MockUpiOnboardingServer)(nil).ValidateUpiInternationalQr), arg0, arg1)
}

// MockUnsafeUpiOnboardingServer is a mock of UnsafeUpiOnboardingServer interface.
type MockUnsafeUpiOnboardingServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeUpiOnboardingServerMockRecorder
}

// MockUnsafeUpiOnboardingServerMockRecorder is the mock recorder for MockUnsafeUpiOnboardingServer.
type MockUnsafeUpiOnboardingServerMockRecorder struct {
	mock *MockUnsafeUpiOnboardingServer
}

// NewMockUnsafeUpiOnboardingServer creates a new mock instance.
func NewMockUnsafeUpiOnboardingServer(ctrl *gomock.Controller) *MockUnsafeUpiOnboardingServer {
	mock := &MockUnsafeUpiOnboardingServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeUpiOnboardingServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeUpiOnboardingServer) EXPECT() *MockUnsafeUpiOnboardingServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedUpiOnboardingServer mocks base method.
func (m *MockUnsafeUpiOnboardingServer) mustEmbedUnimplementedUpiOnboardingServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedUpiOnboardingServer")
}

// mustEmbedUnimplementedUpiOnboardingServer indicates an expected call of mustEmbedUnimplementedUpiOnboardingServer.
func (mr *MockUnsafeUpiOnboardingServerMockRecorder) mustEmbedUnimplementedUpiOnboardingServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedUpiOnboardingServer", reflect.TypeOf((*MockUnsafeUpiOnboardingServer)(nil).mustEmbedUnimplementedUpiOnboardingServer))
}
