// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/actor_vpa_name_map.proto

package onboarding

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActorVpaNameMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for the mapping
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// actor id to which the vpa name belongs to
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// vpa name belonging to the actor
	// For vpa abcd@fbl abcd is considered as vpa
	VpaName string `protobuf:"bytes,3,opt,name=vpa_name,json=vpaName,proto3" json:"vpa_name,omitempty"`
	// time of creation of mapping
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time of mapping
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// time of deletion of mapping
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *ActorVpaNameMap) Reset() {
	*x = ActorVpaNameMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_actor_vpa_name_map_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActorVpaNameMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActorVpaNameMap) ProtoMessage() {}

func (x *ActorVpaNameMap) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_actor_vpa_name_map_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActorVpaNameMap.ProtoReflect.Descriptor instead.
func (*ActorVpaNameMap) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_actor_vpa_name_map_proto_rawDescGZIP(), []int{0}
}

func (x *ActorVpaNameMap) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ActorVpaNameMap) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ActorVpaNameMap) GetVpaName() string {
	if x != nil {
		return x.VpaName
	}
	return ""
}

func (x *ActorVpaNameMap) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ActorVpaNameMap) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ActorVpaNameMap) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_upi_onboarding_actor_vpa_name_map_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_actor_vpa_name_map_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x76, 0x70, 0x61, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x75,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x88,
	0x02, 0x0a, 0x0f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x70, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x4d,
	0x61, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x76, 0x70, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x70, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_actor_vpa_name_map_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_actor_vpa_name_map_proto_rawDescData = file_api_upi_onboarding_actor_vpa_name_map_proto_rawDesc
)

func file_api_upi_onboarding_actor_vpa_name_map_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_actor_vpa_name_map_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_actor_vpa_name_map_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_actor_vpa_name_map_proto_rawDescData)
	})
	return file_api_upi_onboarding_actor_vpa_name_map_proto_rawDescData
}

var file_api_upi_onboarding_actor_vpa_name_map_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_upi_onboarding_actor_vpa_name_map_proto_goTypes = []interface{}{
	(*ActorVpaNameMap)(nil),       // 0: upi.onboarding.ActorVpaNameMap
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_api_upi_onboarding_actor_vpa_name_map_proto_depIdxs = []int32{
	1, // 0: upi.onboarding.ActorVpaNameMap.created_at:type_name -> google.protobuf.Timestamp
	1, // 1: upi.onboarding.ActorVpaNameMap.updated_at:type_name -> google.protobuf.Timestamp
	1, // 2: upi.onboarding.ActorVpaNameMap.deleted_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_actor_vpa_name_map_proto_init() }
func file_api_upi_onboarding_actor_vpa_name_map_proto_init() {
	if File_api_upi_onboarding_actor_vpa_name_map_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_actor_vpa_name_map_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActorVpaNameMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_actor_vpa_name_map_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_actor_vpa_name_map_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_actor_vpa_name_map_proto_depIdxs,
		MessageInfos:      file_api_upi_onboarding_actor_vpa_name_map_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_actor_vpa_name_map_proto = out.File
	file_api_upi_onboarding_actor_vpa_name_map_proto_rawDesc = nil
	file_api_upi_onboarding_actor_vpa_name_map_proto_goTypes = nil
	file_api_upi_onboarding_actor_vpa_name_map_proto_depIdxs = nil
}
