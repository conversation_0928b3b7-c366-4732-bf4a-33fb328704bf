// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/upi_number_pi_mapping.proto

package onboarding

import (
	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpiNumberPiMappingFieldMask int32

const (
	// unspecified
	UpiNumberPiMappingFieldMask_UPI_NUMBER_PI_MAPPING_FIELD_MASK_UNSPECIFIED UpiNumberPiMappingFieldMask = 0
	UpiNumberPiMappingFieldMask_UPI_NUMBER_PI_MAPPING_FIELD_MASK_PI_ID       UpiNumberPiMappingFieldMask = 1
	UpiNumberPiMappingFieldMask_UPI_NUMBER_PI_MAPPING_FIELD_MASK_UPI_NUMBER  UpiNumberPiMappingFieldMask = 2
	UpiNumberPiMappingFieldMask_UPI_NUMBER_PI_MAPPING_FIELD_MASK_STATE       UpiNumberPiMappingFieldMask = 3
	UpiNumberPiMappingFieldMask_UPI_NUMBER_PI_MAPPING_FIELD_MASK_EXPIRE_AT   UpiNumberPiMappingFieldMask = 4
)

// Enum value maps for UpiNumberPiMappingFieldMask.
var (
	UpiNumberPiMappingFieldMask_name = map[int32]string{
		0: "UPI_NUMBER_PI_MAPPING_FIELD_MASK_UNSPECIFIED",
		1: "UPI_NUMBER_PI_MAPPING_FIELD_MASK_PI_ID",
		2: "UPI_NUMBER_PI_MAPPING_FIELD_MASK_UPI_NUMBER",
		3: "UPI_NUMBER_PI_MAPPING_FIELD_MASK_STATE",
		4: "UPI_NUMBER_PI_MAPPING_FIELD_MASK_EXPIRE_AT",
	}
	UpiNumberPiMappingFieldMask_value = map[string]int32{
		"UPI_NUMBER_PI_MAPPING_FIELD_MASK_UNSPECIFIED": 0,
		"UPI_NUMBER_PI_MAPPING_FIELD_MASK_PI_ID":       1,
		"UPI_NUMBER_PI_MAPPING_FIELD_MASK_UPI_NUMBER":  2,
		"UPI_NUMBER_PI_MAPPING_FIELD_MASK_STATE":       3,
		"UPI_NUMBER_PI_MAPPING_FIELD_MASK_EXPIRE_AT":   4,
	}
)

func (x UpiNumberPiMappingFieldMask) Enum() *UpiNumberPiMappingFieldMask {
	p := new(UpiNumberPiMappingFieldMask)
	*p = x
	return p
}

func (x UpiNumberPiMappingFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiNumberPiMappingFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_upi_number_pi_mapping_proto_enumTypes[0].Descriptor()
}

func (UpiNumberPiMappingFieldMask) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_upi_number_pi_mapping_proto_enumTypes[0]
}

func (x UpiNumberPiMappingFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiNumberPiMappingFieldMask.Descriptor instead.
func (UpiNumberPiMappingFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDescGZIP(), []int{0}
}

type UpiNumberPiMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for the upi number to pi mapping
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// pi id linked to the upi number
	PiId string `protobuf:"bytes,2,opt,name=pi_id,json=piId,proto3" json:"pi_id,omitempty"`
	// upi number linked to the pi id
	UpiNumber string `protobuf:"bytes,3,opt,name=upi_number,json=upiNumber,proto3" json:"upi_number,omitempty"`
	// state of the upi number mapping
	State enums.UpiNumberState `protobuf:"varint,4,opt,name=state,proto3,enum=upi.onboarding.enums.UpiNumberState" json:"state,omitempty"`
	// time at which the upi number will expire in case it is deregistered
	ExpireAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	// time of creation of mapping
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time of mapping
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// time of deletion of mapping
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *UpiNumberPiMapping) Reset() {
	*x = UpiNumberPiMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_number_pi_mapping_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiNumberPiMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiNumberPiMapping) ProtoMessage() {}

func (x *UpiNumberPiMapping) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_number_pi_mapping_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiNumberPiMapping.ProtoReflect.Descriptor instead.
func (*UpiNumberPiMapping) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDescGZIP(), []int{0}
}

func (x *UpiNumberPiMapping) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpiNumberPiMapping) GetPiId() string {
	if x != nil {
		return x.PiId
	}
	return ""
}

func (x *UpiNumberPiMapping) GetUpiNumber() string {
	if x != nil {
		return x.UpiNumber
	}
	return ""
}

func (x *UpiNumberPiMapping) GetState() enums.UpiNumberState {
	if x != nil {
		return x.State
	}
	return enums.UpiNumberState(0)
}

func (x *UpiNumberPiMapping) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

func (x *UpiNumberPiMapping) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UpiNumberPiMapping) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UpiNumberPiMapping) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_upi_onboarding_upi_number_pi_mapping_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x70, 0x69, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xfe, 0x02, 0x0a, 0x12, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x50, 0x69, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x70, 0x69, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x69, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3a, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x2a, 0x88, 0x02, 0x0a, 0x1b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x50, 0x69, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d,
	0x61, 0x73, 0x6b, 0x12, 0x30, 0x0a, 0x2c, 0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45,
	0x52, 0x5f, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x5f, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x49, 0x5f, 0x49, 0x44, 0x10,
	0x01, 0x12, 0x2f, 0x0a, 0x2b, 0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f,
	0x50, 0x49, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x10, 0x02, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52,
	0x5f, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x2e,
	0x0a, 0x2a, 0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x50, 0x49, 0x5f,
	0x4d, 0x41, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x5f, 0x41, 0x54, 0x10, 0x04, 0x42, 0x56,
	0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x29, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDescData = file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDesc
)

func file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDescData)
	})
	return file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDescData
}

var file_api_upi_onboarding_upi_number_pi_mapping_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_upi_number_pi_mapping_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_upi_onboarding_upi_number_pi_mapping_proto_goTypes = []interface{}{
	(UpiNumberPiMappingFieldMask)(0), // 0: upi.onboarding.UpiNumberPiMappingFieldMask
	(*UpiNumberPiMapping)(nil),       // 1: upi.onboarding.UpiNumberPiMapping
	(enums.UpiNumberState)(0),        // 2: upi.onboarding.enums.UpiNumberState
	(*timestamppb.Timestamp)(nil),    // 3: google.protobuf.Timestamp
}
var file_api_upi_onboarding_upi_number_pi_mapping_proto_depIdxs = []int32{
	2, // 0: upi.onboarding.UpiNumberPiMapping.state:type_name -> upi.onboarding.enums.UpiNumberState
	3, // 1: upi.onboarding.UpiNumberPiMapping.expire_at:type_name -> google.protobuf.Timestamp
	3, // 2: upi.onboarding.UpiNumberPiMapping.created_at:type_name -> google.protobuf.Timestamp
	3, // 3: upi.onboarding.UpiNumberPiMapping.updated_at:type_name -> google.protobuf.Timestamp
	3, // 4: upi.onboarding.UpiNumberPiMapping.deleted_at:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_upi_number_pi_mapping_proto_init() }
func file_api_upi_onboarding_upi_number_pi_mapping_proto_init() {
	if File_api_upi_onboarding_upi_number_pi_mapping_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_upi_number_pi_mapping_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiNumberPiMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_upi_number_pi_mapping_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_upi_number_pi_mapping_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_upi_number_pi_mapping_proto_enumTypes,
		MessageInfos:      file_api_upi_onboarding_upi_number_pi_mapping_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_upi_number_pi_mapping_proto = out.File
	file_api_upi_onboarding_upi_number_pi_mapping_proto_rawDesc = nil
	file_api_upi_onboarding_upi_number_pi_mapping_proto_goTypes = nil
	file_api_upi_onboarding_upi_number_pi_mapping_proto_depIdxs = nil
}
