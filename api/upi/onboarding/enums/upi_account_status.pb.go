// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/enums/upi_account_status.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UpiAccountStatus denotes the status of the upi account
type UpiAccountStatus int32

const (
	UpiAccountStatus_UPI_ACCOUNT_STATUS_UNSPECIFIED UpiAccountStatus = 0
	// upi account is created for the user
	UpiAccountStatus_UPI_ACCOUNT_STATUS_CREATED UpiAccountStatus = 1
	// upi account is in active state
	UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE UpiAccountStatus = 2
	// upi account is inactive
	// this can occur in case of device update
	UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE UpiAccountStatus = 3
	// user has delinked the upi account
	UpiAccountStatus_UPI_ACCOUNT_STATUS_DELINKED UpiAccountStatus = 4
	// upi account is deleted
	// this can occur after phone factor update if the account does not comes in list account
	UpiAccountStatus_UPI_ACCOUNT_STATUS_DELETED UpiAccountStatus = 5
)

// Enum value maps for UpiAccountStatus.
var (
	UpiAccountStatus_name = map[int32]string{
		0: "UPI_ACCOUNT_STATUS_UNSPECIFIED",
		1: "UPI_ACCOUNT_STATUS_CREATED",
		2: "UPI_ACCOUNT_STATUS_ACTIVE",
		3: "UPI_ACCOUNT_STATUS_INACTIVE",
		4: "UPI_ACCOUNT_STATUS_DELINKED",
		5: "UPI_ACCOUNT_STATUS_DELETED",
	}
	UpiAccountStatus_value = map[string]int32{
		"UPI_ACCOUNT_STATUS_UNSPECIFIED": 0,
		"UPI_ACCOUNT_STATUS_CREATED":     1,
		"UPI_ACCOUNT_STATUS_ACTIVE":      2,
		"UPI_ACCOUNT_STATUS_INACTIVE":    3,
		"UPI_ACCOUNT_STATUS_DELINKED":    4,
		"UPI_ACCOUNT_STATUS_DELETED":     5,
	}
)

func (x UpiAccountStatus) Enum() *UpiAccountStatus {
	p := new(UpiAccountStatus)
	*p = x
	return p
}

func (x UpiAccountStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiAccountStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_account_status_proto_enumTypes[0].Descriptor()
}

func (UpiAccountStatus) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_account_status_proto_enumTypes[0]
}

func (x UpiAccountStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiAccountStatus.Descriptor instead.
func (UpiAccountStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_account_status_proto_rawDescGZIP(), []int{0}
}

var File_api_upi_onboarding_enums_upi_account_status_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_enums_upi_account_status_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x14, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xd7, 0x01, 0x0a, 0x10, 0x55, 0x70,
	0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22,
	0x0a, 0x1e, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44,
	0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10,
	0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x4e, 0x4b, 0x45,
	0x44, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x44, 0x10, 0x05, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_enums_upi_account_status_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_enums_upi_account_status_proto_rawDescData = file_api_upi_onboarding_enums_upi_account_status_proto_rawDesc
)

func file_api_upi_onboarding_enums_upi_account_status_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_enums_upi_account_status_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_enums_upi_account_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_enums_upi_account_status_proto_rawDescData)
	})
	return file_api_upi_onboarding_enums_upi_account_status_proto_rawDescData
}

var file_api_upi_onboarding_enums_upi_account_status_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_enums_upi_account_status_proto_goTypes = []interface{}{
	(UpiAccountStatus)(0), // 0: upi.onboarding.enums.UpiAccountStatus
}
var file_api_upi_onboarding_enums_upi_account_status_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_enums_upi_account_status_proto_init() }
func file_api_upi_onboarding_enums_upi_account_status_proto_init() {
	if File_api_upi_onboarding_enums_upi_account_status_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_enums_upi_account_status_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_enums_upi_account_status_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_enums_upi_account_status_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_enums_upi_account_status_proto_enumTypes,
	}.Build()
	File_api_upi_onboarding_enums_upi_account_status_proto = out.File
	file_api_upi_onboarding_enums_upi_account_status_proto_rawDesc = nil
	file_api_upi_onboarding_enums_upi_account_status_proto_goTypes = nil
	file_api_upi_onboarding_enums_upi_account_status_proto_depIdxs = nil
}
