// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/enums/upi_pin_set_status.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UpiPinSetStatus tells us the whether the upi pin is set or not for the account
type UpiPinSetStatus int32

const (
	UpiPinSetStatus_UPI_PIN_SET_STATUS_UNSPECIFIED UpiPinSetStatus = 0
	// pin is not set for the upi account for the user
	UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_NOT_SET UpiPinSetStatus = 1
	// pin is set for the upi account for the user
	UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET UpiPinSetStatus = 2
	// user has not set the pin for account after reonboarding
	UpiPinSetStatus_UPI_PIN_SET_STATUS_REOBE_PIN_NOT_SET UpiPinSetStatus = 3
	// user has already set the pin on other psp
	UpiPinSetStatus_UPI_PIN_SET_STATUS_ETB_PIN_SET UpiPinSetStatus = 4
	// user has not been able to set pin after maximum attempts
	UpiPinSetStatus_UPI_PIN_SET_STATUS_MAX_RETRIES_PIN_NOT_SET UpiPinSetStatus = 5
	// user has not set the pin for account after device change.
	// Reinstalling app will also be considered as device change
	// NOTE - currently user will need to set the pin after device change only on IOS and not on android
	// as per the new NPCI requirement. (circular - https://drive.google.com/file/d/1N9kkD-tPlafwKcIG4Kwf8Zirfz4PnB3t/view)
	// In this case we will be the source of truth for pin set status and not NPCI. If the user has multiple accounts,
	// the user can set pin for any one account after device change, does not need to re set for other accounts
	UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_NOT_SET_DEVICE_CHANGE UpiPinSetStatus = 6
)

// Enum value maps for UpiPinSetStatus.
var (
	UpiPinSetStatus_name = map[int32]string{
		0: "UPI_PIN_SET_STATUS_UNSPECIFIED",
		1: "UPI_PIN_SET_STATUS_PIN_NOT_SET",
		2: "UPI_PIN_SET_STATUS_PIN_SET",
		3: "UPI_PIN_SET_STATUS_REOBE_PIN_NOT_SET",
		4: "UPI_PIN_SET_STATUS_ETB_PIN_SET",
		5: "UPI_PIN_SET_STATUS_MAX_RETRIES_PIN_NOT_SET",
		6: "UPI_PIN_SET_STATUS_PIN_NOT_SET_DEVICE_CHANGE",
	}
	UpiPinSetStatus_value = map[string]int32{
		"UPI_PIN_SET_STATUS_UNSPECIFIED":               0,
		"UPI_PIN_SET_STATUS_PIN_NOT_SET":               1,
		"UPI_PIN_SET_STATUS_PIN_SET":                   2,
		"UPI_PIN_SET_STATUS_REOBE_PIN_NOT_SET":         3,
		"UPI_PIN_SET_STATUS_ETB_PIN_SET":               4,
		"UPI_PIN_SET_STATUS_MAX_RETRIES_PIN_NOT_SET":   5,
		"UPI_PIN_SET_STATUS_PIN_NOT_SET_DEVICE_CHANGE": 6,
	}
)

func (x UpiPinSetStatus) Enum() *UpiPinSetStatus {
	p := new(UpiPinSetStatus)
	*p = x
	return p
}

func (x UpiPinSetStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiPinSetStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_pin_set_status_proto_enumTypes[0].Descriptor()
}

func (UpiPinSetStatus) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_pin_set_status_proto_enumTypes[0]
}

func (x UpiPinSetStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiPinSetStatus.Descriptor instead.
func (UpiPinSetStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDescGZIP(), []int{0}
}

var File_api_upi_onboarding_enums_upi_pin_set_status_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x70,
	0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x14, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xa9, 0x02, 0x0a, 0x0f, 0x55, 0x70,
	0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a,
	0x1e, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x53, 0x45, 0x54, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e,
	0x5f, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x49, 0x4e, 0x5f,
	0x53, 0x45, 0x54, 0x10, 0x02, 0x12, 0x28, 0x0a, 0x24, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e,
	0x5f, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4f, 0x42,
	0x45, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x03, 0x12,
	0x22, 0x0a, 0x1e, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x54, 0x42, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45,
	0x54, 0x10, 0x04, 0x12, 0x2e, 0x0a, 0x2a, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53,
	0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x52, 0x45,
	0x54, 0x52, 0x49, 0x45, 0x53, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45,
	0x54, 0x10, 0x05, 0x12, 0x30, 0x0a, 0x2c, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53,
	0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x41,
	0x4e, 0x47, 0x45, 0x10, 0x06, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDescData = file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDesc
)

func file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDescData)
	})
	return file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDescData
}

var file_api_upi_onboarding_enums_upi_pin_set_status_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_enums_upi_pin_set_status_proto_goTypes = []interface{}{
	(UpiPinSetStatus)(0), // 0: upi.onboarding.enums.UpiPinSetStatus
}
var file_api_upi_onboarding_enums_upi_pin_set_status_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_enums_upi_pin_set_status_proto_init() }
func file_api_upi_onboarding_enums_upi_pin_set_status_proto_init() {
	if File_api_upi_onboarding_enums_upi_pin_set_status_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_enums_upi_pin_set_status_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_enums_upi_pin_set_status_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_enums_upi_pin_set_status_proto_enumTypes,
	}.Build()
	File_api_upi_onboarding_enums_upi_pin_set_status_proto = out.File
	file_api_upi_onboarding_enums_upi_pin_set_status_proto_rawDesc = nil
	file_api_upi_onboarding_enums_upi_pin_set_status_proto_goTypes = nil
	file_api_upi_onboarding_enums_upi_pin_set_status_proto_depIdxs = nil
}
