// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/enums/upi_request_logs_enums.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Status specifies the status of the request made to an Api
type UpiRequestLogApiStatus int32

const (
	UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_UNSPECIFIED UpiRequestLogApiStatus = 0
	UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_SUCCESS     UpiRequestLogApiStatus = 1
	UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_FAILED      UpiRequestLogApiStatus = 2
	UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_PENDING     UpiRequestLogApiStatus = 3
	UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_CREATED     UpiRequestLogApiStatus = 4
)

// Enum value maps for UpiRequestLogApiStatus.
var (
	UpiRequestLogApiStatus_name = map[int32]string{
		0: "UPI_REQUEST_LOG_API_STATUS_UNSPECIFIED",
		1: "UPI_REQUEST_LOG_API_STATUS_SUCCESS",
		2: "UPI_REQUEST_LOG_API_STATUS_FAILED",
		3: "UPI_REQUEST_LOG_API_STATUS_PENDING",
		4: "UPI_REQUEST_LOG_API_STATUS_CREATED",
	}
	UpiRequestLogApiStatus_value = map[string]int32{
		"UPI_REQUEST_LOG_API_STATUS_UNSPECIFIED": 0,
		"UPI_REQUEST_LOG_API_STATUS_SUCCESS":     1,
		"UPI_REQUEST_LOG_API_STATUS_FAILED":      2,
		"UPI_REQUEST_LOG_API_STATUS_PENDING":     3,
		"UPI_REQUEST_LOG_API_STATUS_CREATED":     4,
	}
)

func (x UpiRequestLogApiStatus) Enum() *UpiRequestLogApiStatus {
	p := new(UpiRequestLogApiStatus)
	*p = x
	return p
}

func (x UpiRequestLogApiStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiRequestLogApiStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes[0].Descriptor()
}

func (UpiRequestLogApiStatus) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes[0]
}

func (x UpiRequestLogApiStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiRequestLogApiStatus.Descriptor instead.
func (UpiRequestLogApiStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescGZIP(), []int{0}
}

// APIType specifies the type of API being called to serve the UPI Request
type UpiRequestLogApiType int32

const (
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_UNSPECIFIED UpiRequestLogApiType = 0
	// Added a seperate enum for link and delink upi account flow, so deprecating this one
	//
	// Deprecated: Marked as deprecated in api/upi/onboarding/enums/upi_request_logs_enums.proto.
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_CREATE_VIRTUAL_ID        UpiRequestLogApiType = 1
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE          UpiRequestLogApiType = 2
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_GENERATE_UPI_OTP         UpiRequestLogApiType = 3
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_CHANGE_PIN               UpiRequestLogApiType = 4
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_LIST_ACCOUNT             UpiRequestLogApiType = 5
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_LIST_ACCOUNT_PROVIDERS   UpiRequestLogApiType = 6
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_CREATE_VIRTUAL_ID_LINK   UpiRequestLogApiType = 7
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_CREATE_VIRTUAL_ID_DELINK UpiRequestLogApiType = 8
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_GET_MAPPER_INFO          UpiRequestLogApiType = 9
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_REG_MAPPER_LINK          UpiRequestLogApiType = 10
	UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_REG_MAPPER_DELINK        UpiRequestLogApiType = 11
)

// Enum value maps for UpiRequestLogApiType.
var (
	UpiRequestLogApiType_name = map[int32]string{
		0:  "UPI_REQUEST_LOG_API_TYPE_UNSPECIFIED",
		1:  "UPI_REQUEST_LOG_API_TYPE_CREATE_VIRTUAL_ID",
		2:  "UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE",
		3:  "UPI_REQUEST_LOG_API_TYPE_GENERATE_UPI_OTP",
		4:  "UPI_REQUEST_LOG_API_TYPE_CHANGE_PIN",
		5:  "UPI_REQUEST_LOG_API_TYPE_LIST_ACCOUNT",
		6:  "UPI_REQUEST_LOG_API_TYPE_LIST_ACCOUNT_PROVIDERS",
		7:  "UPI_REQUEST_LOG_API_TYPE_CREATE_VIRTUAL_ID_LINK",
		8:  "UPI_REQUEST_LOG_API_TYPE_CREATE_VIRTUAL_ID_DELINK",
		9:  "UPI_REQUEST_LOG_API_TYPE_GET_MAPPER_INFO",
		10: "UPI_REQUEST_LOG_API_TYPE_REG_MAPPER_LINK",
		11: "UPI_REQUEST_LOG_API_TYPE_REG_MAPPER_DELINK",
	}
	UpiRequestLogApiType_value = map[string]int32{
		"UPI_REQUEST_LOG_API_TYPE_UNSPECIFIED":              0,
		"UPI_REQUEST_LOG_API_TYPE_CREATE_VIRTUAL_ID":        1,
		"UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE":          2,
		"UPI_REQUEST_LOG_API_TYPE_GENERATE_UPI_OTP":         3,
		"UPI_REQUEST_LOG_API_TYPE_CHANGE_PIN":               4,
		"UPI_REQUEST_LOG_API_TYPE_LIST_ACCOUNT":             5,
		"UPI_REQUEST_LOG_API_TYPE_LIST_ACCOUNT_PROVIDERS":   6,
		"UPI_REQUEST_LOG_API_TYPE_CREATE_VIRTUAL_ID_LINK":   7,
		"UPI_REQUEST_LOG_API_TYPE_CREATE_VIRTUAL_ID_DELINK": 8,
		"UPI_REQUEST_LOG_API_TYPE_GET_MAPPER_INFO":          9,
		"UPI_REQUEST_LOG_API_TYPE_REG_MAPPER_LINK":          10,
		"UPI_REQUEST_LOG_API_TYPE_REG_MAPPER_DELINK":        11,
	}
)

func (x UpiRequestLogApiType) Enum() *UpiRequestLogApiType {
	p := new(UpiRequestLogApiType)
	*p = x
	return p
}

func (x UpiRequestLogApiType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiRequestLogApiType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes[1].Descriptor()
}

func (UpiRequestLogApiType) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes[1]
}

func (x UpiRequestLogApiType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiRequestLogApiType.Descriptor instead.
func (UpiRequestLogApiType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescGZIP(), []int{1}
}

//   - Api type should just contain the api name and further categorisation should be done
//     using UpiRequestLogApiSubType
type UpiRequestLogApiSubType int32

const (
	UpiRequestLogApiSubType_UPI_REQUEST_LOG_API_SUB_TYPE_UNSPECIFIED UpiRequestLogApiSubType = 0
	// used to represent upi pin set using debit card details flow
	UpiRequestLogApiSubType_UPI_REQUEST_LOG_API_SUB_TYPE_REGISTER_MOBILE_DEBIT_CARD UpiRequestLogApiSubType = 1
	// used to represent upi pin set using aadhaar number flow
	UpiRequestLogApiSubType_UPI_REQUEST_LOG_API_SUB_TYPE_REGISTER_MOBILE_AADHAAR_NUMBER UpiRequestLogApiSubType = 2
)

// Enum value maps for UpiRequestLogApiSubType.
var (
	UpiRequestLogApiSubType_name = map[int32]string{
		0: "UPI_REQUEST_LOG_API_SUB_TYPE_UNSPECIFIED",
		1: "UPI_REQUEST_LOG_API_SUB_TYPE_REGISTER_MOBILE_DEBIT_CARD",
		2: "UPI_REQUEST_LOG_API_SUB_TYPE_REGISTER_MOBILE_AADHAAR_NUMBER",
	}
	UpiRequestLogApiSubType_value = map[string]int32{
		"UPI_REQUEST_LOG_API_SUB_TYPE_UNSPECIFIED":                    0,
		"UPI_REQUEST_LOG_API_SUB_TYPE_REGISTER_MOBILE_DEBIT_CARD":     1,
		"UPI_REQUEST_LOG_API_SUB_TYPE_REGISTER_MOBILE_AADHAAR_NUMBER": 2,
	}
)

func (x UpiRequestLogApiSubType) Enum() *UpiRequestLogApiSubType {
	p := new(UpiRequestLogApiSubType)
	*p = x
	return p
}

func (x UpiRequestLogApiSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiRequestLogApiSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes[2].Descriptor()
}

func (UpiRequestLogApiSubType) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes[2]
}

func (x UpiRequestLogApiSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiRequestLogApiSubType.Descriptor instead.
func (UpiRequestLogApiSubType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescGZIP(), []int{2}
}

// DetailedStatusCategory is the category of the Detailed Status
type DetailedStatusCategory int32

const (
	DetailedStatusCategory_DETAILED_STATUS_CATEGORY_UNSPECIFIED       DetailedStatusCategory = 0
	DetailedStatusCategory_DETAILED_STATUS_CATEGORY_TECHNICAL_DECLINE DetailedStatusCategory = 1
	DetailedStatusCategory_DETAILED_STATUS_CATEGORY_BUSINESS_DECLINE  DetailedStatusCategory = 2
	DetailedStatusCategory_DETAILED_STATUS_CATEGORY_SYSTEM_ERROR      DetailedStatusCategory = 3
)

// Enum value maps for DetailedStatusCategory.
var (
	DetailedStatusCategory_name = map[int32]string{
		0: "DETAILED_STATUS_CATEGORY_UNSPECIFIED",
		1: "DETAILED_STATUS_CATEGORY_TECHNICAL_DECLINE",
		2: "DETAILED_STATUS_CATEGORY_BUSINESS_DECLINE",
		3: "DETAILED_STATUS_CATEGORY_SYSTEM_ERROR",
	}
	DetailedStatusCategory_value = map[string]int32{
		"DETAILED_STATUS_CATEGORY_UNSPECIFIED":       0,
		"DETAILED_STATUS_CATEGORY_TECHNICAL_DECLINE": 1,
		"DETAILED_STATUS_CATEGORY_BUSINESS_DECLINE":  2,
		"DETAILED_STATUS_CATEGORY_SYSTEM_ERROR":      3,
	}
)

func (x DetailedStatusCategory) Enum() *DetailedStatusCategory {
	p := new(DetailedStatusCategory)
	*p = x
	return p
}

func (x DetailedStatusCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetailedStatusCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes[3].Descriptor()
}

func (DetailedStatusCategory) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes[3]
}

func (x DetailedStatusCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetailedStatusCategory.Descriptor instead.
func (DetailedStatusCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescGZIP(), []int{3}
}

var File_api_upi_onboarding_enums_upi_request_logs_enums_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x73, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xe3, 0x01,
	0x0a, 0x16, 0x55, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x41,
	0x70, 0x69, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x50, 0x49, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21,
	0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f,
	0x41, 0x50, 0x49, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x02, 0x12, 0x26, 0x0a, 0x22, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x26, 0x0a, 0x22, 0x55,
	0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41,
	0x50, 0x49, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x10, 0x04, 0x2a, 0xd2, 0x04, 0x0a, 0x14, 0x55, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x4c, 0x6f, 0x67, 0x41, 0x70, 0x69, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x24,
	0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f,
	0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x32, 0x0a, 0x2a, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x49, 0x52, 0x54, 0x55, 0x41,
	0x4c, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x2c, 0x0a, 0x28, 0x55, 0x50,
	0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50,
	0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x55, 0x50, 0x49, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x50,
	0x49, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x55, 0x50, 0x49, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x04,
	0x12, 0x29, 0x0a, 0x25, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x53,
	0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x05, 0x12, 0x33, 0x0a, 0x2f, 0x55,
	0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41,
	0x50, 0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x53, 0x10, 0x06,
	0x12, 0x33, 0x0a, 0x2f, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x5f, 0x56, 0x49, 0x52, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x44, 0x5f, 0x4c,
	0x49, 0x4e, 0x4b, 0x10, 0x07, 0x12, 0x35, 0x0a, 0x31, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x49, 0x52, 0x54, 0x55, 0x41, 0x4c,
	0x5f, 0x49, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x08, 0x12, 0x2c, 0x0a, 0x28,
	0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f,
	0x41, 0x50, 0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x4d, 0x41, 0x50,
	0x50, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x09, 0x12, 0x2c, 0x0a, 0x28, 0x55, 0x50,
	0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50,
	0x49, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x45,
	0x52, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x0a, 0x12, 0x2e, 0x0a, 0x2a, 0x55, 0x50, 0x49, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x45, 0x52, 0x5f,
	0x44, 0x45, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x0b, 0x2a, 0xc5, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x69,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x41, 0x70, 0x69, 0x53, 0x75, 0x62,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x28, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x55, 0x42, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x3b, 0x0a, 0x37, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x42, 0x49,
	0x4c, 0x45, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12,
	0x3f, 0x0a, 0x3b, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c,
	0x4f, 0x47, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f,
	0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x02,
	0x2a, 0xcc, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28, 0x0a, 0x24, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x54, 0x45, 0x43, 0x48, 0x4e, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x43, 0x4c,
	0x49, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x2d, 0x0a, 0x29, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52,
	0x59, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49,
	0x4e, 0x45, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59,
	0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x42,
	0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescData = file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDesc
)

func file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescData)
	})
	return file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDescData
}

var file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_upi_onboarding_enums_upi_request_logs_enums_proto_goTypes = []interface{}{
	(UpiRequestLogApiStatus)(0),  // 0: upi.onboarding.enums.UpiRequestLogApiStatus
	(UpiRequestLogApiType)(0),    // 1: upi.onboarding.enums.UpiRequestLogApiType
	(UpiRequestLogApiSubType)(0), // 2: upi.onboarding.enums.UpiRequestLogApiSubType
	(DetailedStatusCategory)(0),  // 3: upi.onboarding.enums.DetailedStatusCategory
}
var file_api_upi_onboarding_enums_upi_request_logs_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_enums_upi_request_logs_enums_proto_init() }
func file_api_upi_onboarding_enums_upi_request_logs_enums_proto_init() {
	if File_api_upi_onboarding_enums_upi_request_logs_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_enums_upi_request_logs_enums_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_enums_upi_request_logs_enums_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_enums_upi_request_logs_enums_proto_enumTypes,
	}.Build()
	File_api_upi_onboarding_enums_upi_request_logs_enums_proto = out.File
	file_api_upi_onboarding_enums_upi_request_logs_enums_proto_rawDesc = nil
	file_api_upi_onboarding_enums_upi_request_logs_enums_proto_goTypes = nil
	file_api_upi_onboarding_enums_upi_request_logs_enums_proto_depIdxs = nil
}
