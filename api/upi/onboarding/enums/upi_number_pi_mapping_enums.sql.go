package enums

import (
	"database/sql/driver"
	"fmt"

	"github.com/epifi/be-common/pkg/logger"
)

// Value Valuer interface implementation for storing the data in string format in DB
func (x UpiNumberState) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *UpiNumberState) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := UpiNumberState_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown type : %v", val))
	}
	*x = UpiNumberState(valInt)
	return nil
}
