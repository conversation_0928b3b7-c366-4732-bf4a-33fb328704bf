// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/enums/upi_pin_set_options.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// user can initiated set pin / reset flow using three options
// Debit card, Credit Card and Aadhaar number
// Based on the type of flow, different parameters need to be
// passed to vendor to initiate set pin request
type UpiPinSetOptionType int32

const (
	UpiPinSetOptionType_UPI_PIN_SET_OPTION_TYPE_UNSPECIFIED       UpiPinSetOptionType = 0
	UpiPinSetOptionType_UPI_PIN_SET_OPTION_TYPE_DEBIT_CARD        UpiPinSetOptionType = 1
	UpiPinSetOptionType_UPI_PIN_SET_OPTION_TYPE_AADHAAR_NUMBER    UpiPinSetOptionType = 2
	UpiPinSetOptionType_UPI_PIN_SET_OPTION_TYPE_RUPAY_CREDIT_CARD UpiPinSetOptionType = 3
)

// Enum value maps for UpiPinSetOptionType.
var (
	UpiPinSetOptionType_name = map[int32]string{
		0: "UPI_PIN_SET_OPTION_TYPE_UNSPECIFIED",
		1: "UPI_PIN_SET_OPTION_TYPE_DEBIT_CARD",
		2: "UPI_PIN_SET_OPTION_TYPE_AADHAAR_NUMBER",
		3: "UPI_PIN_SET_OPTION_TYPE_RUPAY_CREDIT_CARD",
	}
	UpiPinSetOptionType_value = map[string]int32{
		"UPI_PIN_SET_OPTION_TYPE_UNSPECIFIED":       0,
		"UPI_PIN_SET_OPTION_TYPE_DEBIT_CARD":        1,
		"UPI_PIN_SET_OPTION_TYPE_AADHAAR_NUMBER":    2,
		"UPI_PIN_SET_OPTION_TYPE_RUPAY_CREDIT_CARD": 3,
	}
)

func (x UpiPinSetOptionType) Enum() *UpiPinSetOptionType {
	p := new(UpiPinSetOptionType)
	*p = x
	return p
}

func (x UpiPinSetOptionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiPinSetOptionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_pin_set_options_proto_enumTypes[0].Descriptor()
}

func (UpiPinSetOptionType) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_pin_set_options_proto_enumTypes[0]
}

func (x UpiPinSetOptionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiPinSetOptionType.Descriptor instead.
func (UpiPinSetOptionType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDescGZIP(), []int{0}
}

var File_api_upi_onboarding_enums_upi_pin_set_options_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x70,
	0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xc1, 0x01, 0x0a, 0x13, 0x55,
	0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45,
	0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x55,
	0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53,
	0x45, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41,
	0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x02, 0x12,
	0x2d, 0x0a, 0x29, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x4f,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x55, 0x50, 0x41, 0x59,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x03, 0x42, 0x62,
	0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70,
	0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDescData = file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDesc
)

func file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDescData)
	})
	return file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDescData
}

var file_api_upi_onboarding_enums_upi_pin_set_options_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_enums_upi_pin_set_options_proto_goTypes = []interface{}{
	(UpiPinSetOptionType)(0), // 0: upi.onboarding.enums.UpiPinSetOptionType
}
var file_api_upi_onboarding_enums_upi_pin_set_options_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_enums_upi_pin_set_options_proto_init() }
func file_api_upi_onboarding_enums_upi_pin_set_options_proto_init() {
	if File_api_upi_onboarding_enums_upi_pin_set_options_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_enums_upi_pin_set_options_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_enums_upi_pin_set_options_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_enums_upi_pin_set_options_proto_enumTypes,
	}.Build()
	File_api_upi_onboarding_enums_upi_pin_set_options_proto = out.File
	file_api_upi_onboarding_enums_upi_pin_set_options_proto_rawDesc = nil
	file_api_upi_onboarding_enums_upi_pin_set_options_proto_goTypes = nil
	file_api_upi_onboarding_enums_upi_pin_set_options_proto_depIdxs = nil
}
