package enums

import (
	"database/sql/driver"
	"fmt"

	"github.com/epifi/be-common/pkg/logger"
)

// Value Valuer interface implementation for storing the data in string format in DB
func (x UpiRequestLogApiType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *UpiRequestLogApiType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := UpiRequestLogApiType_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown type : %v", val))
	}
	*x = UpiRequestLogApiType(valInt)
	return nil
}

// Value Valuer interface implementation for storing the data in string format in DB
func (x UpiRequestLogApiSubType) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *UpiRequestLogApiSubType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := UpiRequestLogApiSubType_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown type : %v", val))
	}
	*x = UpiRequestLogApiSubType(valInt)
	return nil
}

// Value Valuer interface implementation for storing the data in string format in DB
func (x UpiRequestLogApiStatus) Value() (driver.Value, error) {
	return x.String(), nil
}

// Scan Scanner interface implementation for parsing data while reading from DB
func (x *UpiRequestLogApiStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := UpiRequestLogApiStatus_value[val]
	if !ok {
		logger.ErrorNoCtx(fmt.Sprintf("encountered unknown type : %v", val))
	}
	*x = UpiRequestLogApiStatus(valInt)
	return nil
}
