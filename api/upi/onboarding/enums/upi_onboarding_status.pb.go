// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/enums/upi_onboarding_status.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Current status of the account onboarding.
// CREATED -> INITIATED -> LINKED/FAILED -> DELINKED_INITIATED -> DELINKED/MANUAL_INTERVNETION
// If the user drops in between entry will be marked as INVALID
type UpiOnboardingStatus int32

const (
	UpiOnboardingStatus_UPI_ONBOARDING_STATUS_UNSPECIFIED UpiOnboardingStatus = 0
	// request to link or delink account created with vendor
	UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED UpiOnboardingStatus = 1
	// successfully linked or delinked account
	UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL UpiOnboardingStatus = 2
	// failed to link or delink account
	UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED UpiOnboardingStatus = 3
	// account link/delink in progress
	UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS UpiOnboardingStatus = 4
	// link/delink request went into manual intervention
	UpiOnboardingStatus_UPI_ONBOARDING_STATUS_MANUAL_INTERVENTION UpiOnboardingStatus = 5
	// If the user drops in between entry will be marked as INVALID
	UpiOnboardingStatus_UPI_ONBOARDING_STATUS_INVALID UpiOnboardingStatus = 6
)

// Enum value maps for UpiOnboardingStatus.
var (
	UpiOnboardingStatus_name = map[int32]string{
		0: "UPI_ONBOARDING_STATUS_UNSPECIFIED",
		1: "UPI_ONBOARDING_STATUS_CREATED",
		2: "UPI_ONBOARDING_STATUS_SUCCESSFUL",
		3: "UPI_ONBOARDING_STATUS_FAILED",
		4: "UPI_ONBOARDING_STATUS_IN_PROGRESS",
		5: "UPI_ONBOARDING_STATUS_MANUAL_INTERVENTION",
		6: "UPI_ONBOARDING_STATUS_INVALID",
	}
	UpiOnboardingStatus_value = map[string]int32{
		"UPI_ONBOARDING_STATUS_UNSPECIFIED":         0,
		"UPI_ONBOARDING_STATUS_CREATED":             1,
		"UPI_ONBOARDING_STATUS_SUCCESSFUL":          2,
		"UPI_ONBOARDING_STATUS_FAILED":              3,
		"UPI_ONBOARDING_STATUS_IN_PROGRESS":         4,
		"UPI_ONBOARDING_STATUS_MANUAL_INTERVENTION": 5,
		"UPI_ONBOARDING_STATUS_INVALID":             6,
	}
)

func (x UpiOnboardingStatus) Enum() *UpiOnboardingStatus {
	p := new(UpiOnboardingStatus)
	*p = x
	return p
}

func (x UpiOnboardingStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiOnboardingStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_onboarding_status_proto_enumTypes[0].Descriptor()
}

func (UpiOnboardingStatus) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_onboarding_status_proto_enumTypes[0]
}

func (x UpiOnboardingStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiOnboardingStatus.Descriptor instead.
func (UpiOnboardingStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDescGZIP(), []int{0}
}

var File_api_upi_onboarding_enums_upi_onboarding_status_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDesc = []byte{
	0x0a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xa0, 0x02, 0x0a,
	0x13, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x21, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x55,
	0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x24,
	0x0a, 0x20, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46,
	0x55, 0x4c, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x2d, 0x0a,
	0x29, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x21, 0x0a, 0x1d,
	0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x06, 0x42,
	0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDescData = file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDesc
)

func file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDescData)
	})
	return file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDescData
}

var file_api_upi_onboarding_enums_upi_onboarding_status_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_enums_upi_onboarding_status_proto_goTypes = []interface{}{
	(UpiOnboardingStatus)(0), // 0: upi.onboarding.enums.UpiOnboardingStatus
}
var file_api_upi_onboarding_enums_upi_onboarding_status_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_enums_upi_onboarding_status_proto_init() }
func file_api_upi_onboarding_enums_upi_onboarding_status_proto_init() {
	if File_api_upi_onboarding_enums_upi_onboarding_status_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_enums_upi_onboarding_status_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_enums_upi_onboarding_status_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_enums_upi_onboarding_status_proto_enumTypes,
	}.Build()
	File_api_upi_onboarding_enums_upi_onboarding_status_proto = out.File
	file_api_upi_onboarding_enums_upi_onboarding_status_proto_rawDesc = nil
	file_api_upi_onboarding_enums_upi_onboarding_status_proto_goTypes = nil
	file_api_upi_onboarding_enums_upi_onboarding_status_proto_depIdxs = nil
}
