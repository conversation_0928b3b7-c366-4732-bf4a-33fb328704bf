package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the data  in string format in DB
func (u UpiOnboardingAction) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *UpiOnboardingAction) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := UpiOnboardingAction_value[val]
	*u = UpiOnboardingAction(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (u UpiOnboardingStatus) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *UpiOnboardingStatus) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.E<PERSON>rf("type conversion to string failed, src: %T", val)
	}

	valInt := UpiOnboardingStatus_value[val]
	*u = UpiOnboardingStatus(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (u UpiAccountStatus) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *UpiAccountStatus) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := UpiAccountStatus_value[val]
	*u = UpiAccountStatus(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (u UpiPinSetStatus) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *UpiPinSetStatus) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := UpiPinSetStatus_value[val]
	*u = UpiPinSetStatus(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (u UpiAccountPreference) Value() (driver.Value, error) {
	return u.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (u *UpiAccountPreference) Scan(input interface{}) error {
	if u == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := UpiAccountPreference_value[val]
	*u = UpiAccountPreference(valInt)
	return nil
}

// Valuer interface implementation for storing the UpiControl in string format in DB
func (p UpiControl) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing UpiControl while reading from DB
func (p *UpiControl) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := UpiControl_value[val]
	if !ok {
		return fmt.Errorf("unexpected upi control value: %s", val)
	}
	*p = UpiControl(valInt)
	return nil
}

// Marshaler interface implementation for UpiControl
func (x UpiControl) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for UpiControl
func (x *UpiControl) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = UpiControl(UpiControl_value[val])
	return nil
}
