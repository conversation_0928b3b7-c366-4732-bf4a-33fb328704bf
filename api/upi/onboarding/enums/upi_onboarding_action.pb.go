// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/enums/upi_onboarding_action.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// type of the action taken - LINKING/DELINKING
type UpiOnboardingAction int32

const (
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_UNSPECIFIED UpiOnboardingAction = 0
	// action to link an account
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK UpiOnboardingAction = 1
	// action to delink an account
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_DELINK UpiOnboardingAction = 2
	// action to link upi number
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK UpiOnboardingAction = 3
	// action to delink upi number
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_UPI_NUMBER_DELINK UpiOnboardingAction = 4
	// action to activate International payments
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS UpiOnboardingAction = 5
	// action to deactivate International payments
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS UpiOnboardingAction = 6
	// action to activate upi lite for a upi account
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE UpiOnboardingAction = 7
	// action to deactivate upi lite for a upi account
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE UpiOnboardingAction = 8
	// action to deactivate upi lite account with zero balance
	UpiOnboardingAction_UPI_ONBOARDING_ACTION_ZERO_BALANCE_LRN_DISABLEMENT UpiOnboardingAction = 9
)

// Enum value maps for UpiOnboardingAction.
var (
	UpiOnboardingAction_name = map[int32]string{
		0: "UPI_ONBOARDING_ACTION_UNSPECIFIED",
		1: "UPI_ONBOARDING_ACTION_LINK",
		2: "UPI_ONBOARDING_ACTION_DELINK",
		3: "UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK",
		4: "UPI_ONBOARDING_ACTION_UPI_NUMBER_DELINK",
		5: "UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS",
		6: "UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS",
		7: "UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE",
		8: "UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE",
		9: "UPI_ONBOARDING_ACTION_ZERO_BALANCE_LRN_DISABLEMENT",
	}
	UpiOnboardingAction_value = map[string]int32{
		"UPI_ONBOARDING_ACTION_UNSPECIFIED":                       0,
		"UPI_ONBOARDING_ACTION_LINK":                              1,
		"UPI_ONBOARDING_ACTION_DELINK":                            2,
		"UPI_ONBOARDING_ACTION_UPI_NUMBER_LINK":                   3,
		"UPI_ONBOARDING_ACTION_UPI_NUMBER_DELINK":                 4,
		"UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS":   5,
		"UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS": 6,
		"UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE":                 7,
		"UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE":               8,
		"UPI_ONBOARDING_ACTION_ZERO_BALANCE_LRN_DISABLEMENT":      9,
	}
)

func (x UpiOnboardingAction) Enum() *UpiOnboardingAction {
	p := new(UpiOnboardingAction)
	*p = x
	return p
}

func (x UpiOnboardingAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiOnboardingAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_onboarding_action_proto_enumTypes[0].Descriptor()
}

func (UpiOnboardingAction) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_onboarding_action_proto_enumTypes[0]
}

func (x UpiOnboardingAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiOnboardingAction.Descriptor instead.
func (UpiOnboardingAction) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescGZIP(), []int{0}
}

type UpiAccountType int32

const (
	// unspecified
	UpiAccountType_UPI_ACCOUNT_TYPE_UNSPECIFIED UpiAccountType = 0
	// bank account e.g. savings, current etc
	UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT UpiAccountType = 1
	// credit card
	UpiAccountType_UPI_ACCOUNT_TYPE_CREDIT_CARD UpiAccountType = 2
	// upi lite account
	UpiAccountType_UPI_ACCOUNT_TYPE_UPI_LITE UpiAccountType = 3
)

// Enum value maps for UpiAccountType.
var (
	UpiAccountType_name = map[int32]string{
		0: "UPI_ACCOUNT_TYPE_UNSPECIFIED",
		1: "UPI_ACCOUNT_TYPE_BANK_ACCOUNT",
		2: "UPI_ACCOUNT_TYPE_CREDIT_CARD",
		3: "UPI_ACCOUNT_TYPE_UPI_LITE",
	}
	UpiAccountType_value = map[string]int32{
		"UPI_ACCOUNT_TYPE_UNSPECIFIED":  0,
		"UPI_ACCOUNT_TYPE_BANK_ACCOUNT": 1,
		"UPI_ACCOUNT_TYPE_CREDIT_CARD":  2,
		"UPI_ACCOUNT_TYPE_UPI_LITE":     3,
	}
)

func (x UpiAccountType) Enum() *UpiAccountType {
	p := new(UpiAccountType)
	*p = x
	return p
}

func (x UpiAccountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiAccountType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_onboarding_action_proto_enumTypes[1].Descriptor()
}

func (UpiAccountType) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_onboarding_action_proto_enumTypes[1]
}

func (x UpiAccountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiAccountType.Descriptor instead.
func (UpiAccountType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescGZIP(), []int{1}
}

type AccountPreferenceAction int32

const (
	AccountPreferenceAction_ACCOUNT_PREFERENCE_ACTION_UNSPECIFIED AccountPreferenceAction = 0
	AccountPreferenceAction_ACCOUNT_PREFERENCE_ACTION_ENABLE      AccountPreferenceAction = 1
	AccountPreferenceAction_ACCOUNT_PREFERENCE_ACTION_DISABLE     AccountPreferenceAction = 2
)

// Enum value maps for AccountPreferenceAction.
var (
	AccountPreferenceAction_name = map[int32]string{
		0: "ACCOUNT_PREFERENCE_ACTION_UNSPECIFIED",
		1: "ACCOUNT_PREFERENCE_ACTION_ENABLE",
		2: "ACCOUNT_PREFERENCE_ACTION_DISABLE",
	}
	AccountPreferenceAction_value = map[string]int32{
		"ACCOUNT_PREFERENCE_ACTION_UNSPECIFIED": 0,
		"ACCOUNT_PREFERENCE_ACTION_ENABLE":      1,
		"ACCOUNT_PREFERENCE_ACTION_DISABLE":     2,
	}
)

func (x AccountPreferenceAction) Enum() *AccountPreferenceAction {
	p := new(AccountPreferenceAction)
	*p = x
	return p
}

func (x AccountPreferenceAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountPreferenceAction) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_onboarding_action_proto_enumTypes[2].Descriptor()
}

func (AccountPreferenceAction) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_onboarding_action_proto_enumTypes[2]
}

func (x AccountPreferenceAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountPreferenceAction.Descriptor instead.
func (AccountPreferenceAction) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescGZIP(), []int{2}
}

var File_api_upi_onboarding_enums_upi_onboarding_action_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDesc = []byte{
	0x0a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xe2, 0x03, 0x0a,
	0x13, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x21, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f,
	0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x55,
	0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x55,
	0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x02, 0x12, 0x29, 0x0a,
	0x25, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45,
	0x52, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x03, 0x12, 0x2b, 0x0a, 0x27, 0x55, 0x50, 0x49, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x4c,
	0x49, 0x4e, 0x4b, 0x10, 0x04, 0x12, 0x39, 0x0a, 0x35, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x05,
	0x12, 0x3b, 0x0a, 0x37, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x06, 0x12, 0x2b, 0x0a,
	0x27, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f,
	0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x07, 0x12, 0x2d, 0x0a, 0x29, 0x55, 0x50,
	0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x55,
	0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x08, 0x12, 0x36, 0x0a, 0x32, 0x55, 0x50, 0x49,
	0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x4c, 0x52, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x09, 0x2a, 0x96, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x55, 0x50, 0x49,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x55,
	0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x03, 0x2a, 0x91, 0x01, 0x0a, 0x17, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x45,
	0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45,
	0x4e, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x42, 0x62,
	0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70,
	0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescData = file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDesc
)

func file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescData)
	})
	return file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDescData
}

var file_api_upi_onboarding_enums_upi_onboarding_action_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_upi_onboarding_enums_upi_onboarding_action_proto_goTypes = []interface{}{
	(UpiOnboardingAction)(0),     // 0: upi.onboarding.enums.UpiOnboardingAction
	(UpiAccountType)(0),          // 1: upi.onboarding.enums.UpiAccountType
	(AccountPreferenceAction)(0), // 2: upi.onboarding.enums.AccountPreferenceAction
}
var file_api_upi_onboarding_enums_upi_onboarding_action_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_enums_upi_onboarding_action_proto_init() }
func file_api_upi_onboarding_enums_upi_onboarding_action_proto_init() {
	if File_api_upi_onboarding_enums_upi_onboarding_action_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_enums_upi_onboarding_action_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_enums_upi_onboarding_action_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_enums_upi_onboarding_action_proto_enumTypes,
	}.Build()
	File_api_upi_onboarding_enums_upi_onboarding_action_proto = out.File
	file_api_upi_onboarding_enums_upi_onboarding_action_proto_rawDesc = nil
	file_api_upi_onboarding_enums_upi_onboarding_action_proto_goTypes = nil
	file_api_upi_onboarding_enums_upi_onboarding_action_proto_depIdxs = nil
}
