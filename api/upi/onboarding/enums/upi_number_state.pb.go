// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/enums/upi_number_state.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpiNumberState int32

const (
	// unspecified
	UpiNumberState_UPI_NUMBER_STATE_UNSPECIFIED UpiNumberState = 0
	// upi number is active
	UpiNumberState_UPI_NUMBER_STATE_ACTIVE UpiNumberState = 1
	// upi number is inactive
	UpiNumberState_UPI_NUMBER_STATE_INACTIVE UpiNumberState = 2
	// upi number is deregistered
	// deregistered upi numbers will be recycled to other users
	// after some expiry
	UpiNumberState_UPI_NUMBER_STATE_DEREGISTERED UpiNumberState = 3
	// upi number is available to be linked
	UpiNumberState_UPI_NUMBER_STATE_NEW UpiNumberState = 4
	// upi number is blocked/ blacklisted ID that may be unavailable as per pre-defined compliance checks.
	UpiNumberState_UPI_NUMBER_STATE_BLOCK UpiNumberState = 5
)

// Enum value maps for UpiNumberState.
var (
	UpiNumberState_name = map[int32]string{
		0: "UPI_NUMBER_STATE_UNSPECIFIED",
		1: "UPI_NUMBER_STATE_ACTIVE",
		2: "UPI_NUMBER_STATE_INACTIVE",
		3: "UPI_NUMBER_STATE_DEREGISTERED",
		4: "UPI_NUMBER_STATE_NEW",
		5: "UPI_NUMBER_STATE_BLOCK",
	}
	UpiNumberState_value = map[string]int32{
		"UPI_NUMBER_STATE_UNSPECIFIED":  0,
		"UPI_NUMBER_STATE_ACTIVE":       1,
		"UPI_NUMBER_STATE_INACTIVE":     2,
		"UPI_NUMBER_STATE_DEREGISTERED": 3,
		"UPI_NUMBER_STATE_NEW":          4,
		"UPI_NUMBER_STATE_BLOCK":        5,
	}
)

func (x UpiNumberState) Enum() *UpiNumberState {
	p := new(UpiNumberState)
	*p = x
	return p
}

func (x UpiNumberState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiNumberState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_enums_upi_number_state_proto_enumTypes[0].Descriptor()
}

func (UpiNumberState) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_enums_upi_number_state_proto_enumTypes[0]
}

func (x UpiNumberState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiNumberState.Descriptor instead.
func (UpiNumberState) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_enums_upi_number_state_proto_rawDescGZIP(), []int{0}
}

var File_api_upi_onboarding_enums_upi_number_state_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_enums_upi_number_state_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x14, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xc7, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x69, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x55, 0x50,
	0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17,
	0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x50, 0x49,
	0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x4e,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x55, 0x50, 0x49, 0x5f,
	0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x52,
	0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x55,
	0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f,
	0x4e, 0x45, 0x57, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x50, 0x49, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10,
	0x05, 0x42, 0x62, 0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_enums_upi_number_state_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_enums_upi_number_state_proto_rawDescData = file_api_upi_onboarding_enums_upi_number_state_proto_rawDesc
)

func file_api_upi_onboarding_enums_upi_number_state_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_enums_upi_number_state_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_enums_upi_number_state_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_enums_upi_number_state_proto_rawDescData)
	})
	return file_api_upi_onboarding_enums_upi_number_state_proto_rawDescData
}

var file_api_upi_onboarding_enums_upi_number_state_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_enums_upi_number_state_proto_goTypes = []interface{}{
	(UpiNumberState)(0), // 0: upi.onboarding.enums.UpiNumberState
}
var file_api_upi_onboarding_enums_upi_number_state_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_enums_upi_number_state_proto_init() }
func file_api_upi_onboarding_enums_upi_number_state_proto_init() {
	if File_api_upi_onboarding_enums_upi_number_state_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_enums_upi_number_state_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_enums_upi_number_state_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_enums_upi_number_state_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_enums_upi_number_state_proto_enumTypes,
	}.Build()
	File_api_upi_onboarding_enums_upi_number_state_proto = out.File
	file_api_upi_onboarding_enums_upi_number_state_proto_rawDesc = nil
	file_api_upi_onboarding_enums_upi_number_state_proto_goTypes = nil
	file_api_upi_onboarding_enums_upi_number_state_proto_depIdxs = nil
}
