// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/onboarding/upi_account.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	account "github.com/epifi/gamma/api/typesv2/account"

	accounts "github.com/epifi/gamma/api/accounts"

	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = account.AccountProductOffering(0)

	_ = accounts.Type(0)

	_ = enums.UpiAccountStatus(0)
)

// Validate checks the field values on UpiAccount with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpiAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiAccount with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpiAccountMultiError, or
// nil if none found.
func (m *UpiAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for AccountRefNumber

	// no validation rules for MaskedAccountNumber

	// no validation rules for IfscCode

	// no validation rules for AccountRefId

	// no validation rules for Status

	// no validation rules for AccountType

	// no validation rules for PinSetStatus

	// no validation rules for AccountPreference

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiAccountValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiAccountValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiAccountValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiAccountValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiAccountValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiAccountValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiAccountValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiAccountValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiAccountValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountMetaInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiAccountValidationError{
					field:  "AccountMetaInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiAccountValidationError{
					field:  "AccountMetaInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountMetaInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiAccountValidationError{
				field:  "AccountMetaInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BankName

	// no validation rules for Apo

	if len(errors) > 0 {
		return UpiAccountMultiError(errors)
	}

	return nil
}

// UpiAccountMultiError is an error wrapping multiple validation errors
// returned by UpiAccount.ValidateAll() if the designated constraints aren't met.
type UpiAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiAccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiAccountMultiError) AllErrors() []error { return m }

// UpiAccountValidationError is the validation error returned by
// UpiAccount.Validate if the designated constraints aren't met.
type UpiAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiAccountValidationError) ErrorName() string { return "UpiAccountValidationError" }

// Error satisfies the builtin error interface
func (e UpiAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiAccountValidationError{}
