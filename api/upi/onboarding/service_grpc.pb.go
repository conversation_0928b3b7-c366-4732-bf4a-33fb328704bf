// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/upi/onboarding/service.proto

package onboarding

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UpiOnboarding_CreateInternalPiForVpa_FullMethodName                       = "/upi.onboarding.UpiOnboarding/CreateInternalPiForVpa"
	UpiOnboarding_ListAccountProviders_FullMethodName                         = "/upi.onboarding.UpiOnboarding/ListAccountProviders"
	UpiOnboarding_LinkUpiAccounts_FullMethodName                              = "/upi.onboarding.UpiOnboarding/LinkUpiAccounts"
	UpiOnboarding_GetUpiAccountsActionStatus_FullMethodName                   = "/upi.onboarding.UpiOnboarding/GetUpiAccountsActionStatus"
	UpiOnboarding_ListAccount_FullMethodName                                  = "/upi.onboarding.UpiOnboarding/ListAccount"
	UpiOnboarding_GetAccounts_FullMethodName                                  = "/upi.onboarding.UpiOnboarding/GetAccounts"
	UpiOnboarding_GetVpaNameForActor_FullMethodName                           = "/upi.onboarding.UpiOnboarding/GetVpaNameForActor"
	UpiOnboarding_CreateVpa_FullMethodName                                    = "/upi.onboarding.UpiOnboarding/CreateVpa"
	UpiOnboarding_InitiateDelinkUpiAccount_FullMethodName                     = "/upi.onboarding.UpiOnboarding/InitiateDelinkUpiAccount"
	UpiOnboarding_LinkInternalAccount_FullMethodName                          = "/upi.onboarding.UpiOnboarding/LinkInternalAccount"
	UpiOnboarding_UpdateAccountPreference_FullMethodName                      = "/upi.onboarding.UpiOnboarding/UpdateAccountPreference"
	UpiOnboarding_ActivateUpiAccount_FullMethodName                           = "/upi.onboarding.UpiOnboarding/ActivateUpiAccount"
	UpiOnboarding_DelinkUpiAccountWithVendor_FullMethodName                   = "/upi.onboarding.UpiOnboarding/DelinkUpiAccountWithVendor"
	UpiOnboarding_GetAccount_FullMethodName                                   = "/upi.onboarding.UpiOnboarding/GetAccount"
	UpiOnboarding_DeActivatePi_FullMethodName                                 = "/upi.onboarding.UpiOnboarding/DeActivatePi"
	UpiOnboarding_CheckIfVpaMigrationRequired_FullMethodName                  = "/upi.onboarding.UpiOnboarding/CheckIfVpaMigrationRequired"
	UpiOnboarding_BalanceEnquiry_FullMethodName                               = "/upi.onboarding.UpiOnboarding/BalanceEnquiry"
	UpiOnboarding_GetLatestUpiRequestLogForAccount_FullMethodName             = "/upi.onboarding.UpiOnboarding/GetLatestUpiRequestLogForAccount"
	UpiOnboarding_LinkUpiNumber_FullMethodName                                = "/upi.onboarding.UpiOnboarding/LinkUpiNumber"
	UpiOnboarding_InitiateLinkUpiNumber_FullMethodName                        = "/upi.onboarding.UpiOnboarding/InitiateLinkUpiNumber"
	UpiOnboarding_GetPinStatus_FullMethodName                                 = "/upi.onboarding.UpiOnboarding/GetPinStatus"
	UpiOnboarding_InitiateDelinkUpiNumber_FullMethodName                      = "/upi.onboarding.UpiOnboarding/InitiateDelinkUpiNumber"
	UpiOnboarding_GetUpiNumberActionStatus_FullMethodName                     = "/upi.onboarding.UpiOnboarding/GetUpiNumberActionStatus"
	UpiOnboarding_GetMapperInfo_FullMethodName                                = "/upi.onboarding.UpiOnboarding/GetMapperInfo"
	UpiOnboarding_IsTpapEnabledForActor_FullMethodName                        = "/upi.onboarding.UpiOnboarding/IsTpapEnabledForActor"
	UpiOnboarding_CheckUpiNumberStatusWithVendor_FullMethodName               = "/upi.onboarding.UpiOnboarding/CheckUpiNumberStatusWithVendor"
	UpiOnboarding_DelinkUpiNumber_FullMethodName                              = "/upi.onboarding.UpiOnboarding/DelinkUpiNumber"
	UpiOnboarding_GetUpiNumberDetails_FullMethodName                          = "/upi.onboarding.UpiOnboarding/GetUpiNumberDetails"
	UpiOnboarding_DisableOrEnableUpiNumber_FullMethodName                     = "/upi.onboarding.UpiOnboarding/DisableOrEnableUpiNumber"
	UpiOnboarding_IsMapperEnabledForActor_FullMethodName                      = "/upi.onboarding.UpiOnboarding/IsMapperEnabledForActor"
	UpiOnboarding_ActivateInternationalPaymentsWithVendor_FullMethodName      = "/upi.onboarding.UpiOnboarding/ActivateInternationalPaymentsWithVendor"
	UpiOnboarding_InitiateInternationalPaymentsActivation_FullMethodName      = "/upi.onboarding.UpiOnboarding/InitiateInternationalPaymentsActivation"
	UpiOnboarding_InternationalPaymentsActionEnquiryWithVendor_FullMethodName = "/upi.onboarding.UpiOnboarding/InternationalPaymentsActionEnquiryWithVendor"
	UpiOnboarding_GetInternationalPaymentActionStatus_FullMethodName          = "/upi.onboarding.UpiOnboarding/GetInternationalPaymentActionStatus"
	UpiOnboarding_ValidateAadhaarNoForUpiPinSet_FullMethodName                = "/upi.onboarding.UpiOnboarding/ValidateAadhaarNoForUpiPinSet"
	UpiOnboarding_GetUpiPinSetOptionsForAccountId_FullMethodName              = "/upi.onboarding.UpiOnboarding/GetUpiPinSetOptionsForAccountId"
	UpiOnboarding_DeactivateInternationalPaymentsWithVendor_FullMethodName    = "/upi.onboarding.UpiOnboarding/DeactivateInternationalPaymentsWithVendor"
	UpiOnboarding_InitiateInternationalPaymentsDeactivation_FullMethodName    = "/upi.onboarding.UpiOnboarding/InitiateInternationalPaymentsDeactivation"
	UpiOnboarding_GetInternationPaymentDetailsForAccount_FullMethodName       = "/upi.onboarding.UpiOnboarding/GetInternationPaymentDetailsForAccount"
	UpiOnboarding_ValidateUpiInternationalQr_FullMethodName                   = "/upi.onboarding.UpiOnboarding/ValidateUpiInternationalQr"
	UpiOnboarding_IsUpiPinSetUsingAadhaarEnabledForActor_FullMethodName       = "/upi.onboarding.UpiOnboarding/IsUpiPinSetUsingAadhaarEnabledForActor"
	UpiOnboarding_IsUpiInternationalPaymentEnabledForActor_FullMethodName     = "/upi.onboarding.UpiOnboarding/IsUpiInternationalPaymentEnabledForActor"
	UpiOnboarding_UpdateDefaultMerchantPaymentPreference_FullMethodName       = "/upi.onboarding.UpiOnboarding/UpdateDefaultMerchantPaymentPreference"
	UpiOnboarding_IsCcLinkingEnabledForActor_FullMethodName                   = "/upi.onboarding.UpiOnboarding/IsCcLinkingEnabledForActor"
	UpiOnboarding_InitiateUpiLiteActivation_FullMethodName                    = "/upi.onboarding.UpiOnboarding/InitiateUpiLiteActivation"
	UpiOnboarding_InitiateUpiLiteDeactivation_FullMethodName                  = "/upi.onboarding.UpiOnboarding/InitiateUpiLiteDeactivation"
	UpiOnboarding_CheckUpiLiteActionStatus_FullMethodName                     = "/upi.onboarding.UpiOnboarding/CheckUpiLiteActionStatus"
	UpiOnboarding_SyncUpiLiteInfo_FullMethodName                              = "/upi.onboarding.UpiOnboarding/SyncUpiLiteInfo"
	UpiOnboarding_GetUpiLiteInfo_FullMethodName                               = "/upi.onboarding.UpiOnboarding/GetUpiLiteInfo"
	UpiOnboarding_IsUpiLiteEnabledForActor_FullMethodName                     = "/upi.onboarding.UpiOnboarding/IsUpiLiteEnabledForActor"
	UpiOnboarding_GetAccountsForUpiLiteActivation_FullMethodName              = "/upi.onboarding.UpiOnboarding/GetAccountsForUpiLiteActivation"
	UpiOnboarding_IsFeatureEnabledForActor_FullMethodName                     = "/upi.onboarding.UpiOnboarding/IsFeatureEnabledForActor"
	UpiOnboarding_GetLatestUpiOnboardingDetailForAccount_FullMethodName       = "/upi.onboarding.UpiOnboarding/GetLatestUpiOnboardingDetailForAccount"
	UpiOnboarding_GetTpapFeatureStatus_FullMethodName                         = "/upi.onboarding.UpiOnboarding/GetTpapFeatureStatus"
	UpiOnboarding_CheckEligibilityForOneClickFlowPopUp_FullMethodName         = "/upi.onboarding.UpiOnboarding/CheckEligibilityForOneClickFlowPopUp"
	UpiOnboarding_IsAccountActionAllowed_FullMethodName                       = "/upi.onboarding.UpiOnboarding/IsAccountActionAllowed"
	UpiOnboarding_GetConsolidatedUpiNumberDetails_FullMethodName              = "/upi.onboarding.UpiOnboarding/GetConsolidatedUpiNumberDetails"
	UpiOnboarding_GetAccountByIdentifier_FullMethodName                       = "/upi.onboarding.UpiOnboarding/GetAccountByIdentifier"
	UpiOnboarding_GetUpiNumberPiMapping_FullMethodName                        = "/upi.onboarding.UpiOnboarding/GetUpiNumberPiMapping"
)

// UpiOnboardingClient is the client API for UpiOnboarding service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UpiOnboardingClient interface {
	// CreateInternalPiForVpa creates internal user pi for the given vpa
	// It fetches required details from upi onboarding details and upi account table (like account ref number, account type etc.)
	// and use that to creat the pi. The rpc also creates account pi relation for the pi
	// The rpc is idempotent i.e if the pi and account pi is already present will return success
	CreateInternalPiForVpa(ctx context.Context, in *CreateInternalPiForVpaRequest, opts ...grpc.CallOption) (*CreateInternalPiForVpaResponse, error)
	// ListAccountProviders calls the vendor to get the list of all account providers from vendornthat supports upi tpap integration
	// This will be required to check for registered account providers before registering a user account
	// NOTE: The rpc will cache the vendor response with a ttl as the list of bank accounts doesn't change very frequently
	ListAccountProviders(ctx context.Context, in *ListAccountProvidersRequest, opts ...grpc.CallOption) (*ListAccountProvidersResponse, error)
	// LinkUpiAccounts triggers the account linking flow for given account ids
	// It takes list of account ids and actor id as request, validates the same
	// it creates upi onboarding detail entities and initiates the celestial workflow
	// it doesn't returns error in case one/some of the accounts from given account list fails to be linked
	LinkUpiAccounts(ctx context.Context, in *LinkUpiAccountsRequest, opts ...grpc.CallOption) (*LinkUpiAccountsResponse, error)
	// GetUpiAccountsActionStatus fetches the account linking/delinking status for given client req ids
	// It takes list of celestial client id as request
	// it fetches status of triggered workflows using client request id
	// it doesn't returns error in case one/some of the accounts from given account list fails to be linked
	GetUpiAccountsActionStatus(ctx context.Context, in *GetUpiAccountsActionStatusRequest, opts ...grpc.CallOption) (*GetUpiAccountsActionStatusResponse, error)
	// ListAccount returns list of accounts  which are not linked through tpap for given phone number and ifsc
	ListAccount(ctx context.Context, in *ListAccountRequest, opts ...grpc.CallOption) (*ListAccountResponse, error)
	// GetAccounts gets the list of upi accounts for the user based on the list of status specified in the request
	// Note: If the list of account status is empty, we will return all the accounts
	GetAccounts(ctx context.Context, in *GetAccountsRequest, opts ...grpc.CallOption) (*GetAccountsResponse, error)
	// GetVpaNameForActor returns vpa name for the given actor id
	// For a vpa abcd@fifederal abcd is considered as vpa name
	// If a vpa name is already assigned to the actor returns the same else creates a new vpa
	// name for the actor
	GetVpaNameForActor(ctx context.Context, in *GetVpaNameForActorRequest, opts ...grpc.CallOption) (*GetVpaNameForActorResponse, error)
	// CreateVpaRequest creates vpa with vendor for the account corresponding to given client req id
	CreateVpa(ctx context.Context, in *CreateVpaRequest, opts ...grpc.CallOption) (*CreateVpaResponse, error)
	// InitiateDelinkUpiAccount triggers the account delinking flow for given account id with the given list of vendors
	// It takes accountId and actor id as request, validates the same
	// it creates upi onboarding detail entity and initiates the celestial workflow for delinking
	InitiateDelinkUpiAccount(ctx context.Context, in *InitiateDelinkUpiAccountRequest, opts ...grpc.CallOption) (*InitiateDelinkUpiAccountResponse, error)
	// LinkInternalAccount triggers the upi account linking flow for internal FI account
	// It call either of new linking flow or old create vpa flow based on flag.
	LinkInternalAccount(ctx context.Context, in *LinkInternalAccountRequest, opts ...grpc.CallOption) (*LinkInternalAccountResponse, error)
	// UpdateAccountPreference marks the account with the given actorId and accountId, as Primary
	UpdateAccountPreference(ctx context.Context, in *UpdateAccountPreferenceRequest, opts ...grpc.CallOption) (*UpdateAccountPreferenceResponse, error)
	// ActivateUpiAccount marks the given account as active
	ActivateUpiAccount(ctx context.Context, in *ActivateUpiAccountRequest, opts ...grpc.CallOption) (*ActivateUpiAccountResponse, error)
	// DelinkUpiAccountWithVendor delinks the upi account with the vendor
	DelinkUpiAccountWithVendor(ctx context.Context, in *DelinkUpiAccountWithVendorRequest, opts ...grpc.CallOption) (*DelinkUpiAccountWithVendorResponse, error)
	// GetAccount fetches the upiAccount from given accountId.
	GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*GetAccountResponse, error)
	// DeActivatePi deactivates the given vpa.
	// If it success to deactivates Pi, return status ok. Otherwise returns status Internal.
	DeActivatePi(ctx context.Context, in *DeActivatePiRequest, opts ...grpc.CallOption) (*DeActivatePiResponse, error)
	// CheckIfVpaMigrationRequired :
	//  1. checks whether for the given user that vpa need to be migrated or not, if required it shows a nudge to user
	//     about vpa migration and asks for the consent to migrate
	//  2. if user's vpa is already migrated, we check if user should be shown pop up to link other bank accounts.
	CheckIfVpaMigrationRequired(ctx context.Context, in *CheckIfVpaMigrationRequiredRequest, opts ...grpc.CallOption) (*CheckIfVpaMigrationRequiredResponse, error)
	// BalanceEnquiry fetches the balance for the tpap accountId passed in the request
	BalanceEnquiry(ctx context.Context, in *BalanceEnquiryRequest, opts ...grpc.CallOption) (*BalanceEnquiryResponse, error)
	// GetLatestUpiRequestLogForAccount fetches the latest upi request log for given account id, api type and api status
	GetLatestUpiRequestLogForAccount(ctx context.Context, in *GetLatestUpiRequestLogForAccountRequest, opts ...grpc.CallOption) (*GetLatestUpiRequestLogForAccountResponse, error)
	// LinkUpiNumber call VG to link upi number with a vpa
	// Takes in the client req id for an ongoing celestial workflow for linking upi number
	LinkUpiNumber(ctx context.Context, in *LinkUpiNumberRequest, opts ...grpc.CallOption) (*LinkUpiNumberResponse, error)
	// InitiateLinkUpiNumber initiates a celestial workflow for linking upi number with given vpa
	// It takes actor id, vpa, upi number, linking type, upi number type and celestial client request id
	InitiateLinkUpiNumber(ctx context.Context, in *InitiateLinkUpiNumberRequest, opts ...grpc.CallOption) (*InitiateLinkUpiNumberResponse, error)
	// GetPinStatus fetches the latest pin set status for tpap accounts
	// If pin is found not set it makes a vg call to get latest info
	GetPinStatus(ctx context.Context, in *GetPinStatusRequest, opts ...grpc.CallOption) (*GetPinStatusResponse, error)
	// InitiateDelinkUpiNumber triggers the upi number delinking flow for given upi number
	// It takes upi number and actor id as request, validates the same
	// it creates upi onboarding detail entity and initiates the celestial workflow for delinking
	InitiateDelinkUpiNumber(ctx context.Context, in *InitiateDelinkUpiNumberRequest, opts ...grpc.CallOption) (*InitiateDelinkUpiNumberResponse, error)
	// GetUpiNumberActionStatus fetches the upi number linking/delinking status for given client req id
	// It takes celestial client id as request
	// it fetches status of triggered workflow using client request id and returns response based on that
	GetUpiNumberActionStatus(ctx context.Context, in *GetUpiNumberActionStatusRequest, opts ...grpc.CallOption) (*GetUpiNumberActionStatusResponse, error)
	// GetMapperInfo fetches upi mapper information for given vpa or upi number.
	// It calls GetMapperInfo VG rpc to fetch data.
	GetMapperInfo(ctx context.Context, in *GetMapperInfoRequest, opts ...grpc.CallOption) (*GetMapperInfoResponse, error)
	// IsTpapEnabledForActor checks if tpap is enabled for user or not
	IsTpapEnabledForActor(ctx context.Context, in *IsTpapEnabledForActorRequest, opts ...grpc.CallOption) (*IsTpapEnabledForActorResponse, error)
	// CheckUpiNumberStatusWithVendor - checks the upi number action status with vendor
	CheckUpiNumberStatusWithVendor(ctx context.Context, in *CheckUpiNumberStatusWithVendorRequest, opts ...grpc.CallOption) (*CheckUpiNumberStatusWithVendorResponse, error)
	// DelinkUpiNumber call VG to delink upi number with a vpa
	// Takes in the client req id for an ongoing celestial workflow for delinking upi number
	DelinkUpiNumber(ctx context.Context, in *DelinkUpiNumberRequest, opts ...grpc.CallOption) (*DelinkUpiNumberResponse, error)
	// GetUpiNumberDetails is used to get all the upiNumbers for the given account_id and vpa
	GetUpiNumberDetails(ctx context.Context, in *GetUpiNumberDetailsRequest, opts ...grpc.CallOption) (*GetUpiNumberDetailsResponse, error)
	// DisableOrEnableUpiNumber disables or enables the upi number based on the request type
	DisableOrEnableUpiNumber(ctx context.Context, in *DisableOrEnableUpiNumberRequest, opts ...grpc.CallOption) (*DisableOrEnableUpiNumberResponse, error)
	// IsTpapEnabledForActor checks if mapper is enabled for user or not
	IsMapperEnabledForActor(ctx context.Context, in *IsMapperEnabledForActorRequest, opts ...grpc.CallOption) (*IsMapperEnabledForActorResponse, error)
	// ActivateInternationalPaymentsWithVendor is used to activate international payments for a user
	// Customer has to activate for international transaction unlike domestic transaction.
	// ActivateInternationalPaymentsWithVendor is used to initiate the activation of international payments with vendor
	// Steps:
	// > fetches onboarding details and checks the workflow status
	// > calls the ActivateInternationalPayments(NPCI rpc name: Req Activation) vg rpc
	//
	//	to authorise and activate international payments.
	//
	// > sends signal to ActivateInternationalPayments workflow to inform that activation with vendor has been triggered
	ActivateInternationalPaymentsWithVendor(ctx context.Context, in *ActivateInternationalPaymentsWithVendorRequest, opts ...grpc.CallOption) (*ActivateInternationalPaymentsWithVendorResponse, error)
	// InitiateInternationalPaymentsActivation triggers the workflow for activation of international payments for the given account
	InitiateInternationalPaymentsActivation(ctx context.Context, in *InitiateInternationalPaymentsActivationRequest, opts ...grpc.CallOption) (*InitiateInternationalPaymentsActivationResponse, error)
	// InternationalPaymentsActionEnquiryWithVendor checks the account activation status for international payment
	InternationalPaymentsActionEnquiryWithVendor(ctx context.Context, in *InternationalPaymentsActionEnquiryWithVendorRequest, opts ...grpc.CallOption) (*InternationalPaymentsActionEnquiryWithVendorResponse, error)
	// GetInternationalPaymentActionStatus checks the fetches the status of the International Payments activation/deactivation
	// status of the client account after request to activate or deactivate international payments has been raised with the vendor.
	GetInternationalPaymentActionStatus(ctx context.Context, in *GetInternationalPaymentActionStatusRequest, opts ...grpc.CallOption) (*GetInternationalPaymentActionStatusResponse, error)
	// ValidateAadhaarNoForUpiPinSet is used to validate the Aadhaar no during upi pin set flow
	// >  We get the last 4 digits of user's Aadhaar no from vendor using ListAccount Api
	// >  We'll store the same in redis for some amount of time (TTL).
	// > If client request for validation comes post that, then call will fail and user will have
	//
	//	to start the process again
	ValidateAadhaarNoForUpiPinSet(ctx context.Context, in *ValidateAadhaarNoForUpiPinSetRequest, opts ...grpc.CallOption) (*ValidateAadhaarNoForUpiPinSetResponse, error)
	// GetUpiPinSetOptionsForAccountId fetches the available options to set / reset upi pin for a given account Id
	// If user's bank account is Aadhaar enabled then user should get an option to set/reset upi pin using Aadhaar number
	// NOTE - Default method of setting pin using debit card details would be always available
	GetUpiPinSetOptionsForAccountId(ctx context.Context, in *GetUpiPinSetOptionsForAccountIdRequest, opts ...grpc.CallOption) (*GetUpiPinSetOptionsForAccountIdResponse, error)
	// DeactivateInternationalPaymentsWithVendor is used initiate deactivation of international payments for an account with vendor
	//   - Customer has the option to activate and deactivate internation payments.
	//   - International payments will also be deactivated automatically once user has
	//     crossed the expiry for international payments, set during activation of the same (max 3 months)
	//   - calls the ActivateInternationalPayments vg rpc with action type `ACTION_TYPE_DEACTIVATION` (NPCI rpc name: Req Activation)
	//     to authorise and deactivate international payments.
	//     sends signal to DeactivateInternationalPayments workflow to inform that deactivation with vendor has been triggered
	DeactivateInternationalPaymentsWithVendor(ctx context.Context, in *DeactivateInternationalPaymentsWithVendorRequest, opts ...grpc.CallOption) (*DeactivateInternationalPaymentsWithVendorResponse, error)
	// InitiateInternationalPaymentsDeactivation triggers the workflow for Deactivation of international payments for the given account
	InitiateInternationalPaymentsDeactivation(ctx context.Context, in *InitiateInternationalPaymentsDeactivationRequest, opts ...grpc.CallOption) (*InitiateInternationalPaymentsDeactivationResponse, error)
	// GetInternationPaymentDetailsForAccount gives the latest upi onboarding detail for the given account id
	GetInternationPaymentDetailsForAccount(ctx context.Context, in *GetInternationPaymentDetailsForAccountRequest, opts ...grpc.CallOption) (*GetInternationPaymentDetailsForAccountResponse, error)
	// ValidateUpiInternationalQr -
	//   - validates the international Qr with vendor using InternationalQRValidation vg rpc
	//   - vendor validates the Qr and gives us the details present in QR like forex charges, expiryTime of qr etc.
	//   - backend persists the qr details (received in vg response) in redis with Qr expiry time as TTL,
	//   - Once the user moves forward to make the payment, we will use the data in redis to verify the conversion and
	//     send all this data in ReqPay for payments. If data is not present in redis, it means the QR request has expired.
	ValidateUpiInternationalQr(ctx context.Context, in *ValidateUpiInternationalQrRequest, opts ...grpc.CallOption) (*ValidateUpiInternationalQrResponse, error)
	// IsUpiPinSetUsingAadhaarEnabledForActor checks if user is allowed to set/reset upi pin using aadhaar number
	IsUpiPinSetUsingAadhaarEnabledForActor(ctx context.Context, in *IsUpiPinSetUsingAadhaarEnabledForActorRequest, opts ...grpc.CallOption) (*IsUpiPinSetUsingAadhaarEnabledForActorResponse, error)
	// IsUpiInternationalPaymentEnabledForActor checks if user is allowed to activate or deactivate international payments
	IsUpiInternationalPaymentEnabledForActor(ctx context.Context, in *IsUpiInternationalPaymentEnabledForActorRequest, opts ...grpc.CallOption) (*IsUpiInternationalPaymentEnabledForActorResponse, error)
	// UpdateDefaultMerchantPaymentPreference updates the default merchant payment preference for a credit account
	UpdateDefaultMerchantPaymentPreference(ctx context.Context, in *UpdateDefaultMerchantPaymentPreferenceRequest, opts ...grpc.CallOption) (*UpdateDefaultMerchantPaymentPreferenceResponse, error)
	// IsCcLinkingEnabledForActor checks if user is allowed to link credit cards for upi payments
	IsCcLinkingEnabledForActor(ctx context.Context, in *IsCcLinkingEnabledForActorRequest, opts ...grpc.CallOption) (*IsCcLinkingEnabledForActorResponse, error)
	// InitiateUpiLiteActivation - initiates upi lite activation for an upi account
	//  1. calls GetUpiLite() vg rpc to get Lite Reference Number (LRN). LRN will be used as a unique
	//     identifier b/w NPCI and psp for all the future actions on upi lite account
	//  2. creates upi lite account in DB.
	//  3. triggers workflow for activation of upi lite account.
	InitiateUpiLiteActivation(ctx context.Context, in *InitiateUpiLiteActivationRequest, opts ...grpc.CallOption) (*InitiateUpiLiteActivationResponse, error)
	// InitiateUpiLiteDeactivation -
	// is used to initiate the upi lite deactivation for an upi account
	// triggers workflow for deactivation based on the balance of upi
	// lite account.
	InitiateUpiLiteDeactivation(ctx context.Context, in *InitiateUpiLiteDeactivationRequest, opts ...grpc.CallOption) (*InitiateUpiLiteDeactivationResponse, error)
	// CheckUpiLiteActionStatus -
	//  1. checks the status of upi lite action workflow.
	//  2. decides the next action to be performed by the client.
	//     Based on the workflow status, client would need to perform one of the following 3 actions:
	//     2.1 InitiatePayment - initiate upi lite top (or withdraw during deactivation) by calling InitiatePayment FE rpc
	//     2.2 SyncUpiLite - call SyncUpiLite Fe rpc to fetch the latest info regarding
	//     2.3 PollWorkflow - If upi lite action workflow is still in progress, then client will be given
	//     a retry timer using which client shall make the next polling call (if required).
	//  3. generates the terminal_screen to be shown to the user. Client shall show the screen to the user if
	//     populated (irrespective of the next action, screen should be shown to user and next action can go on
	//     in background).
	CheckUpiLiteActionStatus(ctx context.Context, in *CheckUpiLiteActionStatusRequest, opts ...grpc.CallOption) (*CheckUpiLiteActionStatusResponse, error)
	// SyncUpiLiteInfo-
	// 1. Delete upi lite account which have 0 balance
	// 2. Fetch account status and detail for upi lite account
	SyncUpiLiteInfo(ctx context.Context, in *SyncUpiLiteInfoRequest, opts ...grpc.CallOption) (*SyncUpiLiteInfoResponse, error)
	// GetUpiLiteInfo - fetches the upi lite info for an upi account
	GetUpiLiteInfo(ctx context.Context, in *GetUpiLiteInfoRequest, opts ...grpc.CallOption) (*GetUpiLiteInfoResponse, error)
	// IsUpiLiteEnabledForActor - checks if user is allowed to use upi lite
	IsUpiLiteEnabledForActor(ctx context.Context, in *IsUpiLiteEnabledForActorRequest, opts ...grpc.CallOption) (*IsUpiLiteEnabledForActorResponse, error)
	// GetAccountsForUpiLiteActivation - fetches the list of all the bank accounts that are eligible for upi lite activation
	// Eligibility of the bank account is determined by the response received from list account provider RPC
	GetAccountsForUpiLiteActivation(ctx context.Context, in *GetAccountsForUpiLiteActivationRequest, opts ...grpc.CallOption) (*GetAccountsForUpiLiteActivationResponse, error)
	// IsFeatureEnabledForActor - checks if the passed feature is enabled for given actor or not
	IsFeatureEnabledForActor(ctx context.Context, in *IsFeatureEnabledForActorRequest, opts ...grpc.CallOption) (*IsFeatureEnabledForActorResponse, error)
	// GetLatestUpiOnboardingDetailForAccount - It is used to fetch the latest upi onboarding detail for a account id and
	// given action type
	GetLatestUpiOnboardingDetailForAccount(ctx context.Context, in *GetLatestUpiOnboardingDetailForAccountRequest, opts ...grpc.CallOption) (*GetLatestUpiOnboardingDetailForAccountResponse, error)
	// GetTpapFeatureStatus : checks the TPAP feature status for an actor
	// e.g. Active, Inactive, InProgress etc.
	GetTpapFeatureStatus(ctx context.Context, in *GetTpapFeatureStatusRequest, opts ...grpc.CallOption) (*GetTpapFeatureStatusResponse, error)
	// CheckEligibilityForOneClickFlowPopUp : checks the eligibility for the user for the one click tpap flow
	// pop up.
	// Note - This pop up is used to prompt users to link their connected accounts via TPAP.
	CheckEligibilityForOneClickFlowPopUp(ctx context.Context, in *CheckEligibilityForOneClickFlowPopUpRequest, opts ...grpc.CallOption) (*CheckEligibilityForOneClickFlowPopUpResponse, error)
	// IsAccountActionAllowed: used to check if the given action is allowed for an account.
	// E.g. In case, user has an active Mandate present delink upi accounts and disable vpa actions
	// are not allowed.
	IsAccountActionAllowed(ctx context.Context, in *IsAccountActionAllowedRequest, opts ...grpc.CallOption) (*IsAccountActionAllowedResponse, error)
	// GetConsolidatedUpiNumberDetails: fetches the consolidated mapper details for the actor
	// It returns all upi accounts along with upi number details linked to those accounts
	GetConsolidatedUpiNumberDetails(ctx context.Context, in *GetConsolidatedUpiNumberDetailsRequest, opts ...grpc.CallOption) (*GetConsolidatedUpiNumberDetailsResponse, error)
	// GetAccountByIdentifier: fetches the upi account for the given account ref id
	GetAccountByIdentifier(ctx context.Context, in *GetAccountByIdentifierRequest, opts ...grpc.CallOption) (*GetAccountByIdentifierResponse, error)
	// GetUpiNumberPiMapping: fetches the upi number to pi mapping for the given upi number
	GetUpiNumberPiMapping(ctx context.Context, in *GetUpiNumberPiMappingRequest, opts ...grpc.CallOption) (*GetUpiNumberPiMappingResponse, error)
}

type upiOnboardingClient struct {
	cc grpc.ClientConnInterface
}

func NewUpiOnboardingClient(cc grpc.ClientConnInterface) UpiOnboardingClient {
	return &upiOnboardingClient{cc}
}

func (c *upiOnboardingClient) CreateInternalPiForVpa(ctx context.Context, in *CreateInternalPiForVpaRequest, opts ...grpc.CallOption) (*CreateInternalPiForVpaResponse, error) {
	out := new(CreateInternalPiForVpaResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_CreateInternalPiForVpa_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) ListAccountProviders(ctx context.Context, in *ListAccountProvidersRequest, opts ...grpc.CallOption) (*ListAccountProvidersResponse, error) {
	out := new(ListAccountProvidersResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_ListAccountProviders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) LinkUpiAccounts(ctx context.Context, in *LinkUpiAccountsRequest, opts ...grpc.CallOption) (*LinkUpiAccountsResponse, error) {
	out := new(LinkUpiAccountsResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_LinkUpiAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetUpiAccountsActionStatus(ctx context.Context, in *GetUpiAccountsActionStatusRequest, opts ...grpc.CallOption) (*GetUpiAccountsActionStatusResponse, error) {
	out := new(GetUpiAccountsActionStatusResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetUpiAccountsActionStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) ListAccount(ctx context.Context, in *ListAccountRequest, opts ...grpc.CallOption) (*ListAccountResponse, error) {
	out := new(ListAccountResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_ListAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetAccounts(ctx context.Context, in *GetAccountsRequest, opts ...grpc.CallOption) (*GetAccountsResponse, error) {
	out := new(GetAccountsResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetVpaNameForActor(ctx context.Context, in *GetVpaNameForActorRequest, opts ...grpc.CallOption) (*GetVpaNameForActorResponse, error) {
	out := new(GetVpaNameForActorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetVpaNameForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) CreateVpa(ctx context.Context, in *CreateVpaRequest, opts ...grpc.CallOption) (*CreateVpaResponse, error) {
	out := new(CreateVpaResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_CreateVpa_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) InitiateDelinkUpiAccount(ctx context.Context, in *InitiateDelinkUpiAccountRequest, opts ...grpc.CallOption) (*InitiateDelinkUpiAccountResponse, error) {
	out := new(InitiateDelinkUpiAccountResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_InitiateDelinkUpiAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) LinkInternalAccount(ctx context.Context, in *LinkInternalAccountRequest, opts ...grpc.CallOption) (*LinkInternalAccountResponse, error) {
	out := new(LinkInternalAccountResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_LinkInternalAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) UpdateAccountPreference(ctx context.Context, in *UpdateAccountPreferenceRequest, opts ...grpc.CallOption) (*UpdateAccountPreferenceResponse, error) {
	out := new(UpdateAccountPreferenceResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_UpdateAccountPreference_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) ActivateUpiAccount(ctx context.Context, in *ActivateUpiAccountRequest, opts ...grpc.CallOption) (*ActivateUpiAccountResponse, error) {
	out := new(ActivateUpiAccountResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_ActivateUpiAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) DelinkUpiAccountWithVendor(ctx context.Context, in *DelinkUpiAccountWithVendorRequest, opts ...grpc.CallOption) (*DelinkUpiAccountWithVendorResponse, error) {
	out := new(DelinkUpiAccountWithVendorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_DelinkUpiAccountWithVendor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*GetAccountResponse, error) {
	out := new(GetAccountResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) DeActivatePi(ctx context.Context, in *DeActivatePiRequest, opts ...grpc.CallOption) (*DeActivatePiResponse, error) {
	out := new(DeActivatePiResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_DeActivatePi_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) CheckIfVpaMigrationRequired(ctx context.Context, in *CheckIfVpaMigrationRequiredRequest, opts ...grpc.CallOption) (*CheckIfVpaMigrationRequiredResponse, error) {
	out := new(CheckIfVpaMigrationRequiredResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_CheckIfVpaMigrationRequired_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) BalanceEnquiry(ctx context.Context, in *BalanceEnquiryRequest, opts ...grpc.CallOption) (*BalanceEnquiryResponse, error) {
	out := new(BalanceEnquiryResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_BalanceEnquiry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetLatestUpiRequestLogForAccount(ctx context.Context, in *GetLatestUpiRequestLogForAccountRequest, opts ...grpc.CallOption) (*GetLatestUpiRequestLogForAccountResponse, error) {
	out := new(GetLatestUpiRequestLogForAccountResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetLatestUpiRequestLogForAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) LinkUpiNumber(ctx context.Context, in *LinkUpiNumberRequest, opts ...grpc.CallOption) (*LinkUpiNumberResponse, error) {
	out := new(LinkUpiNumberResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_LinkUpiNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) InitiateLinkUpiNumber(ctx context.Context, in *InitiateLinkUpiNumberRequest, opts ...grpc.CallOption) (*InitiateLinkUpiNumberResponse, error) {
	out := new(InitiateLinkUpiNumberResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_InitiateLinkUpiNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetPinStatus(ctx context.Context, in *GetPinStatusRequest, opts ...grpc.CallOption) (*GetPinStatusResponse, error) {
	out := new(GetPinStatusResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetPinStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) InitiateDelinkUpiNumber(ctx context.Context, in *InitiateDelinkUpiNumberRequest, opts ...grpc.CallOption) (*InitiateDelinkUpiNumberResponse, error) {
	out := new(InitiateDelinkUpiNumberResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_InitiateDelinkUpiNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetUpiNumberActionStatus(ctx context.Context, in *GetUpiNumberActionStatusRequest, opts ...grpc.CallOption) (*GetUpiNumberActionStatusResponse, error) {
	out := new(GetUpiNumberActionStatusResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetUpiNumberActionStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetMapperInfo(ctx context.Context, in *GetMapperInfoRequest, opts ...grpc.CallOption) (*GetMapperInfoResponse, error) {
	out := new(GetMapperInfoResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetMapperInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) IsTpapEnabledForActor(ctx context.Context, in *IsTpapEnabledForActorRequest, opts ...grpc.CallOption) (*IsTpapEnabledForActorResponse, error) {
	out := new(IsTpapEnabledForActorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_IsTpapEnabledForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) CheckUpiNumberStatusWithVendor(ctx context.Context, in *CheckUpiNumberStatusWithVendorRequest, opts ...grpc.CallOption) (*CheckUpiNumberStatusWithVendorResponse, error) {
	out := new(CheckUpiNumberStatusWithVendorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_CheckUpiNumberStatusWithVendor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) DelinkUpiNumber(ctx context.Context, in *DelinkUpiNumberRequest, opts ...grpc.CallOption) (*DelinkUpiNumberResponse, error) {
	out := new(DelinkUpiNumberResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_DelinkUpiNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetUpiNumberDetails(ctx context.Context, in *GetUpiNumberDetailsRequest, opts ...grpc.CallOption) (*GetUpiNumberDetailsResponse, error) {
	out := new(GetUpiNumberDetailsResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetUpiNumberDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) DisableOrEnableUpiNumber(ctx context.Context, in *DisableOrEnableUpiNumberRequest, opts ...grpc.CallOption) (*DisableOrEnableUpiNumberResponse, error) {
	out := new(DisableOrEnableUpiNumberResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_DisableOrEnableUpiNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) IsMapperEnabledForActor(ctx context.Context, in *IsMapperEnabledForActorRequest, opts ...grpc.CallOption) (*IsMapperEnabledForActorResponse, error) {
	out := new(IsMapperEnabledForActorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_IsMapperEnabledForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) ActivateInternationalPaymentsWithVendor(ctx context.Context, in *ActivateInternationalPaymentsWithVendorRequest, opts ...grpc.CallOption) (*ActivateInternationalPaymentsWithVendorResponse, error) {
	out := new(ActivateInternationalPaymentsWithVendorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_ActivateInternationalPaymentsWithVendor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) InitiateInternationalPaymentsActivation(ctx context.Context, in *InitiateInternationalPaymentsActivationRequest, opts ...grpc.CallOption) (*InitiateInternationalPaymentsActivationResponse, error) {
	out := new(InitiateInternationalPaymentsActivationResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_InitiateInternationalPaymentsActivation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) InternationalPaymentsActionEnquiryWithVendor(ctx context.Context, in *InternationalPaymentsActionEnquiryWithVendorRequest, opts ...grpc.CallOption) (*InternationalPaymentsActionEnquiryWithVendorResponse, error) {
	out := new(InternationalPaymentsActionEnquiryWithVendorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_InternationalPaymentsActionEnquiryWithVendor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetInternationalPaymentActionStatus(ctx context.Context, in *GetInternationalPaymentActionStatusRequest, opts ...grpc.CallOption) (*GetInternationalPaymentActionStatusResponse, error) {
	out := new(GetInternationalPaymentActionStatusResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetInternationalPaymentActionStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) ValidateAadhaarNoForUpiPinSet(ctx context.Context, in *ValidateAadhaarNoForUpiPinSetRequest, opts ...grpc.CallOption) (*ValidateAadhaarNoForUpiPinSetResponse, error) {
	out := new(ValidateAadhaarNoForUpiPinSetResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_ValidateAadhaarNoForUpiPinSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetUpiPinSetOptionsForAccountId(ctx context.Context, in *GetUpiPinSetOptionsForAccountIdRequest, opts ...grpc.CallOption) (*GetUpiPinSetOptionsForAccountIdResponse, error) {
	out := new(GetUpiPinSetOptionsForAccountIdResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetUpiPinSetOptionsForAccountId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) DeactivateInternationalPaymentsWithVendor(ctx context.Context, in *DeactivateInternationalPaymentsWithVendorRequest, opts ...grpc.CallOption) (*DeactivateInternationalPaymentsWithVendorResponse, error) {
	out := new(DeactivateInternationalPaymentsWithVendorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_DeactivateInternationalPaymentsWithVendor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) InitiateInternationalPaymentsDeactivation(ctx context.Context, in *InitiateInternationalPaymentsDeactivationRequest, opts ...grpc.CallOption) (*InitiateInternationalPaymentsDeactivationResponse, error) {
	out := new(InitiateInternationalPaymentsDeactivationResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_InitiateInternationalPaymentsDeactivation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetInternationPaymentDetailsForAccount(ctx context.Context, in *GetInternationPaymentDetailsForAccountRequest, opts ...grpc.CallOption) (*GetInternationPaymentDetailsForAccountResponse, error) {
	out := new(GetInternationPaymentDetailsForAccountResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetInternationPaymentDetailsForAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) ValidateUpiInternationalQr(ctx context.Context, in *ValidateUpiInternationalQrRequest, opts ...grpc.CallOption) (*ValidateUpiInternationalQrResponse, error) {
	out := new(ValidateUpiInternationalQrResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_ValidateUpiInternationalQr_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) IsUpiPinSetUsingAadhaarEnabledForActor(ctx context.Context, in *IsUpiPinSetUsingAadhaarEnabledForActorRequest, opts ...grpc.CallOption) (*IsUpiPinSetUsingAadhaarEnabledForActorResponse, error) {
	out := new(IsUpiPinSetUsingAadhaarEnabledForActorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_IsUpiPinSetUsingAadhaarEnabledForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) IsUpiInternationalPaymentEnabledForActor(ctx context.Context, in *IsUpiInternationalPaymentEnabledForActorRequest, opts ...grpc.CallOption) (*IsUpiInternationalPaymentEnabledForActorResponse, error) {
	out := new(IsUpiInternationalPaymentEnabledForActorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_IsUpiInternationalPaymentEnabledForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) UpdateDefaultMerchantPaymentPreference(ctx context.Context, in *UpdateDefaultMerchantPaymentPreferenceRequest, opts ...grpc.CallOption) (*UpdateDefaultMerchantPaymentPreferenceResponse, error) {
	out := new(UpdateDefaultMerchantPaymentPreferenceResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_UpdateDefaultMerchantPaymentPreference_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) IsCcLinkingEnabledForActor(ctx context.Context, in *IsCcLinkingEnabledForActorRequest, opts ...grpc.CallOption) (*IsCcLinkingEnabledForActorResponse, error) {
	out := new(IsCcLinkingEnabledForActorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_IsCcLinkingEnabledForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) InitiateUpiLiteActivation(ctx context.Context, in *InitiateUpiLiteActivationRequest, opts ...grpc.CallOption) (*InitiateUpiLiteActivationResponse, error) {
	out := new(InitiateUpiLiteActivationResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_InitiateUpiLiteActivation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) InitiateUpiLiteDeactivation(ctx context.Context, in *InitiateUpiLiteDeactivationRequest, opts ...grpc.CallOption) (*InitiateUpiLiteDeactivationResponse, error) {
	out := new(InitiateUpiLiteDeactivationResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_InitiateUpiLiteDeactivation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) CheckUpiLiteActionStatus(ctx context.Context, in *CheckUpiLiteActionStatusRequest, opts ...grpc.CallOption) (*CheckUpiLiteActionStatusResponse, error) {
	out := new(CheckUpiLiteActionStatusResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_CheckUpiLiteActionStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) SyncUpiLiteInfo(ctx context.Context, in *SyncUpiLiteInfoRequest, opts ...grpc.CallOption) (*SyncUpiLiteInfoResponse, error) {
	out := new(SyncUpiLiteInfoResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_SyncUpiLiteInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetUpiLiteInfo(ctx context.Context, in *GetUpiLiteInfoRequest, opts ...grpc.CallOption) (*GetUpiLiteInfoResponse, error) {
	out := new(GetUpiLiteInfoResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetUpiLiteInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) IsUpiLiteEnabledForActor(ctx context.Context, in *IsUpiLiteEnabledForActorRequest, opts ...grpc.CallOption) (*IsUpiLiteEnabledForActorResponse, error) {
	out := new(IsUpiLiteEnabledForActorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_IsUpiLiteEnabledForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetAccountsForUpiLiteActivation(ctx context.Context, in *GetAccountsForUpiLiteActivationRequest, opts ...grpc.CallOption) (*GetAccountsForUpiLiteActivationResponse, error) {
	out := new(GetAccountsForUpiLiteActivationResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetAccountsForUpiLiteActivation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) IsFeatureEnabledForActor(ctx context.Context, in *IsFeatureEnabledForActorRequest, opts ...grpc.CallOption) (*IsFeatureEnabledForActorResponse, error) {
	out := new(IsFeatureEnabledForActorResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_IsFeatureEnabledForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetLatestUpiOnboardingDetailForAccount(ctx context.Context, in *GetLatestUpiOnboardingDetailForAccountRequest, opts ...grpc.CallOption) (*GetLatestUpiOnboardingDetailForAccountResponse, error) {
	out := new(GetLatestUpiOnboardingDetailForAccountResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetLatestUpiOnboardingDetailForAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetTpapFeatureStatus(ctx context.Context, in *GetTpapFeatureStatusRequest, opts ...grpc.CallOption) (*GetTpapFeatureStatusResponse, error) {
	out := new(GetTpapFeatureStatusResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetTpapFeatureStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) CheckEligibilityForOneClickFlowPopUp(ctx context.Context, in *CheckEligibilityForOneClickFlowPopUpRequest, opts ...grpc.CallOption) (*CheckEligibilityForOneClickFlowPopUpResponse, error) {
	out := new(CheckEligibilityForOneClickFlowPopUpResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_CheckEligibilityForOneClickFlowPopUp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) IsAccountActionAllowed(ctx context.Context, in *IsAccountActionAllowedRequest, opts ...grpc.CallOption) (*IsAccountActionAllowedResponse, error) {
	out := new(IsAccountActionAllowedResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_IsAccountActionAllowed_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetConsolidatedUpiNumberDetails(ctx context.Context, in *GetConsolidatedUpiNumberDetailsRequest, opts ...grpc.CallOption) (*GetConsolidatedUpiNumberDetailsResponse, error) {
	out := new(GetConsolidatedUpiNumberDetailsResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetConsolidatedUpiNumberDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetAccountByIdentifier(ctx context.Context, in *GetAccountByIdentifierRequest, opts ...grpc.CallOption) (*GetAccountByIdentifierResponse, error) {
	out := new(GetAccountByIdentifierResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetAccountByIdentifier_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *upiOnboardingClient) GetUpiNumberPiMapping(ctx context.Context, in *GetUpiNumberPiMappingRequest, opts ...grpc.CallOption) (*GetUpiNumberPiMappingResponse, error) {
	out := new(GetUpiNumberPiMappingResponse)
	err := c.cc.Invoke(ctx, UpiOnboarding_GetUpiNumberPiMapping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UpiOnboardingServer is the server API for UpiOnboarding service.
// All implementations should embed UnimplementedUpiOnboardingServer
// for forward compatibility
type UpiOnboardingServer interface {
	// CreateInternalPiForVpa creates internal user pi for the given vpa
	// It fetches required details from upi onboarding details and upi account table (like account ref number, account type etc.)
	// and use that to creat the pi. The rpc also creates account pi relation for the pi
	// The rpc is idempotent i.e if the pi and account pi is already present will return success
	CreateInternalPiForVpa(context.Context, *CreateInternalPiForVpaRequest) (*CreateInternalPiForVpaResponse, error)
	// ListAccountProviders calls the vendor to get the list of all account providers from vendornthat supports upi tpap integration
	// This will be required to check for registered account providers before registering a user account
	// NOTE: The rpc will cache the vendor response with a ttl as the list of bank accounts doesn't change very frequently
	ListAccountProviders(context.Context, *ListAccountProvidersRequest) (*ListAccountProvidersResponse, error)
	// LinkUpiAccounts triggers the account linking flow for given account ids
	// It takes list of account ids and actor id as request, validates the same
	// it creates upi onboarding detail entities and initiates the celestial workflow
	// it doesn't returns error in case one/some of the accounts from given account list fails to be linked
	LinkUpiAccounts(context.Context, *LinkUpiAccountsRequest) (*LinkUpiAccountsResponse, error)
	// GetUpiAccountsActionStatus fetches the account linking/delinking status for given client req ids
	// It takes list of celestial client id as request
	// it fetches status of triggered workflows using client request id
	// it doesn't returns error in case one/some of the accounts from given account list fails to be linked
	GetUpiAccountsActionStatus(context.Context, *GetUpiAccountsActionStatusRequest) (*GetUpiAccountsActionStatusResponse, error)
	// ListAccount returns list of accounts  which are not linked through tpap for given phone number and ifsc
	ListAccount(context.Context, *ListAccountRequest) (*ListAccountResponse, error)
	// GetAccounts gets the list of upi accounts for the user based on the list of status specified in the request
	// Note: If the list of account status is empty, we will return all the accounts
	GetAccounts(context.Context, *GetAccountsRequest) (*GetAccountsResponse, error)
	// GetVpaNameForActor returns vpa name for the given actor id
	// For a vpa abcd@fifederal abcd is considered as vpa name
	// If a vpa name is already assigned to the actor returns the same else creates a new vpa
	// name for the actor
	GetVpaNameForActor(context.Context, *GetVpaNameForActorRequest) (*GetVpaNameForActorResponse, error)
	// CreateVpaRequest creates vpa with vendor for the account corresponding to given client req id
	CreateVpa(context.Context, *CreateVpaRequest) (*CreateVpaResponse, error)
	// InitiateDelinkUpiAccount triggers the account delinking flow for given account id with the given list of vendors
	// It takes accountId and actor id as request, validates the same
	// it creates upi onboarding detail entity and initiates the celestial workflow for delinking
	InitiateDelinkUpiAccount(context.Context, *InitiateDelinkUpiAccountRequest) (*InitiateDelinkUpiAccountResponse, error)
	// LinkInternalAccount triggers the upi account linking flow for internal FI account
	// It call either of new linking flow or old create vpa flow based on flag.
	LinkInternalAccount(context.Context, *LinkInternalAccountRequest) (*LinkInternalAccountResponse, error)
	// UpdateAccountPreference marks the account with the given actorId and accountId, as Primary
	UpdateAccountPreference(context.Context, *UpdateAccountPreferenceRequest) (*UpdateAccountPreferenceResponse, error)
	// ActivateUpiAccount marks the given account as active
	ActivateUpiAccount(context.Context, *ActivateUpiAccountRequest) (*ActivateUpiAccountResponse, error)
	// DelinkUpiAccountWithVendor delinks the upi account with the vendor
	DelinkUpiAccountWithVendor(context.Context, *DelinkUpiAccountWithVendorRequest) (*DelinkUpiAccountWithVendorResponse, error)
	// GetAccount fetches the upiAccount from given accountId.
	GetAccount(context.Context, *GetAccountRequest) (*GetAccountResponse, error)
	// DeActivatePi deactivates the given vpa.
	// If it success to deactivates Pi, return status ok. Otherwise returns status Internal.
	DeActivatePi(context.Context, *DeActivatePiRequest) (*DeActivatePiResponse, error)
	// CheckIfVpaMigrationRequired :
	//  1. checks whether for the given user that vpa need to be migrated or not, if required it shows a nudge to user
	//     about vpa migration and asks for the consent to migrate
	//  2. if user's vpa is already migrated, we check if user should be shown pop up to link other bank accounts.
	CheckIfVpaMigrationRequired(context.Context, *CheckIfVpaMigrationRequiredRequest) (*CheckIfVpaMigrationRequiredResponse, error)
	// BalanceEnquiry fetches the balance for the tpap accountId passed in the request
	BalanceEnquiry(context.Context, *BalanceEnquiryRequest) (*BalanceEnquiryResponse, error)
	// GetLatestUpiRequestLogForAccount fetches the latest upi request log for given account id, api type and api status
	GetLatestUpiRequestLogForAccount(context.Context, *GetLatestUpiRequestLogForAccountRequest) (*GetLatestUpiRequestLogForAccountResponse, error)
	// LinkUpiNumber call VG to link upi number with a vpa
	// Takes in the client req id for an ongoing celestial workflow for linking upi number
	LinkUpiNumber(context.Context, *LinkUpiNumberRequest) (*LinkUpiNumberResponse, error)
	// InitiateLinkUpiNumber initiates a celestial workflow for linking upi number with given vpa
	// It takes actor id, vpa, upi number, linking type, upi number type and celestial client request id
	InitiateLinkUpiNumber(context.Context, *InitiateLinkUpiNumberRequest) (*InitiateLinkUpiNumberResponse, error)
	// GetPinStatus fetches the latest pin set status for tpap accounts
	// If pin is found not set it makes a vg call to get latest info
	GetPinStatus(context.Context, *GetPinStatusRequest) (*GetPinStatusResponse, error)
	// InitiateDelinkUpiNumber triggers the upi number delinking flow for given upi number
	// It takes upi number and actor id as request, validates the same
	// it creates upi onboarding detail entity and initiates the celestial workflow for delinking
	InitiateDelinkUpiNumber(context.Context, *InitiateDelinkUpiNumberRequest) (*InitiateDelinkUpiNumberResponse, error)
	// GetUpiNumberActionStatus fetches the upi number linking/delinking status for given client req id
	// It takes celestial client id as request
	// it fetches status of triggered workflow using client request id and returns response based on that
	GetUpiNumberActionStatus(context.Context, *GetUpiNumberActionStatusRequest) (*GetUpiNumberActionStatusResponse, error)
	// GetMapperInfo fetches upi mapper information for given vpa or upi number.
	// It calls GetMapperInfo VG rpc to fetch data.
	GetMapperInfo(context.Context, *GetMapperInfoRequest) (*GetMapperInfoResponse, error)
	// IsTpapEnabledForActor checks if tpap is enabled for user or not
	IsTpapEnabledForActor(context.Context, *IsTpapEnabledForActorRequest) (*IsTpapEnabledForActorResponse, error)
	// CheckUpiNumberStatusWithVendor - checks the upi number action status with vendor
	CheckUpiNumberStatusWithVendor(context.Context, *CheckUpiNumberStatusWithVendorRequest) (*CheckUpiNumberStatusWithVendorResponse, error)
	// DelinkUpiNumber call VG to delink upi number with a vpa
	// Takes in the client req id for an ongoing celestial workflow for delinking upi number
	DelinkUpiNumber(context.Context, *DelinkUpiNumberRequest) (*DelinkUpiNumberResponse, error)
	// GetUpiNumberDetails is used to get all the upiNumbers for the given account_id and vpa
	GetUpiNumberDetails(context.Context, *GetUpiNumberDetailsRequest) (*GetUpiNumberDetailsResponse, error)
	// DisableOrEnableUpiNumber disables or enables the upi number based on the request type
	DisableOrEnableUpiNumber(context.Context, *DisableOrEnableUpiNumberRequest) (*DisableOrEnableUpiNumberResponse, error)
	// IsTpapEnabledForActor checks if mapper is enabled for user or not
	IsMapperEnabledForActor(context.Context, *IsMapperEnabledForActorRequest) (*IsMapperEnabledForActorResponse, error)
	// ActivateInternationalPaymentsWithVendor is used to activate international payments for a user
	// Customer has to activate for international transaction unlike domestic transaction.
	// ActivateInternationalPaymentsWithVendor is used to initiate the activation of international payments with vendor
	// Steps:
	// > fetches onboarding details and checks the workflow status
	// > calls the ActivateInternationalPayments(NPCI rpc name: Req Activation) vg rpc
	//
	//	to authorise and activate international payments.
	//
	// > sends signal to ActivateInternationalPayments workflow to inform that activation with vendor has been triggered
	ActivateInternationalPaymentsWithVendor(context.Context, *ActivateInternationalPaymentsWithVendorRequest) (*ActivateInternationalPaymentsWithVendorResponse, error)
	// InitiateInternationalPaymentsActivation triggers the workflow for activation of international payments for the given account
	InitiateInternationalPaymentsActivation(context.Context, *InitiateInternationalPaymentsActivationRequest) (*InitiateInternationalPaymentsActivationResponse, error)
	// InternationalPaymentsActionEnquiryWithVendor checks the account activation status for international payment
	InternationalPaymentsActionEnquiryWithVendor(context.Context, *InternationalPaymentsActionEnquiryWithVendorRequest) (*InternationalPaymentsActionEnquiryWithVendorResponse, error)
	// GetInternationalPaymentActionStatus checks the fetches the status of the International Payments activation/deactivation
	// status of the client account after request to activate or deactivate international payments has been raised with the vendor.
	GetInternationalPaymentActionStatus(context.Context, *GetInternationalPaymentActionStatusRequest) (*GetInternationalPaymentActionStatusResponse, error)
	// ValidateAadhaarNoForUpiPinSet is used to validate the Aadhaar no during upi pin set flow
	// >  We get the last 4 digits of user's Aadhaar no from vendor using ListAccount Api
	// >  We'll store the same in redis for some amount of time (TTL).
	// > If client request for validation comes post that, then call will fail and user will have
	//
	//	to start the process again
	ValidateAadhaarNoForUpiPinSet(context.Context, *ValidateAadhaarNoForUpiPinSetRequest) (*ValidateAadhaarNoForUpiPinSetResponse, error)
	// GetUpiPinSetOptionsForAccountId fetches the available options to set / reset upi pin for a given account Id
	// If user's bank account is Aadhaar enabled then user should get an option to set/reset upi pin using Aadhaar number
	// NOTE - Default method of setting pin using debit card details would be always available
	GetUpiPinSetOptionsForAccountId(context.Context, *GetUpiPinSetOptionsForAccountIdRequest) (*GetUpiPinSetOptionsForAccountIdResponse, error)
	// DeactivateInternationalPaymentsWithVendor is used initiate deactivation of international payments for an account with vendor
	//   - Customer has the option to activate and deactivate internation payments.
	//   - International payments will also be deactivated automatically once user has
	//     crossed the expiry for international payments, set during activation of the same (max 3 months)
	//   - calls the ActivateInternationalPayments vg rpc with action type `ACTION_TYPE_DEACTIVATION` (NPCI rpc name: Req Activation)
	//     to authorise and deactivate international payments.
	//     sends signal to DeactivateInternationalPayments workflow to inform that deactivation with vendor has been triggered
	DeactivateInternationalPaymentsWithVendor(context.Context, *DeactivateInternationalPaymentsWithVendorRequest) (*DeactivateInternationalPaymentsWithVendorResponse, error)
	// InitiateInternationalPaymentsDeactivation triggers the workflow for Deactivation of international payments for the given account
	InitiateInternationalPaymentsDeactivation(context.Context, *InitiateInternationalPaymentsDeactivationRequest) (*InitiateInternationalPaymentsDeactivationResponse, error)
	// GetInternationPaymentDetailsForAccount gives the latest upi onboarding detail for the given account id
	GetInternationPaymentDetailsForAccount(context.Context, *GetInternationPaymentDetailsForAccountRequest) (*GetInternationPaymentDetailsForAccountResponse, error)
	// ValidateUpiInternationalQr -
	//   - validates the international Qr with vendor using InternationalQRValidation vg rpc
	//   - vendor validates the Qr and gives us the details present in QR like forex charges, expiryTime of qr etc.
	//   - backend persists the qr details (received in vg response) in redis with Qr expiry time as TTL,
	//   - Once the user moves forward to make the payment, we will use the data in redis to verify the conversion and
	//     send all this data in ReqPay for payments. If data is not present in redis, it means the QR request has expired.
	ValidateUpiInternationalQr(context.Context, *ValidateUpiInternationalQrRequest) (*ValidateUpiInternationalQrResponse, error)
	// IsUpiPinSetUsingAadhaarEnabledForActor checks if user is allowed to set/reset upi pin using aadhaar number
	IsUpiPinSetUsingAadhaarEnabledForActor(context.Context, *IsUpiPinSetUsingAadhaarEnabledForActorRequest) (*IsUpiPinSetUsingAadhaarEnabledForActorResponse, error)
	// IsUpiInternationalPaymentEnabledForActor checks if user is allowed to activate or deactivate international payments
	IsUpiInternationalPaymentEnabledForActor(context.Context, *IsUpiInternationalPaymentEnabledForActorRequest) (*IsUpiInternationalPaymentEnabledForActorResponse, error)
	// UpdateDefaultMerchantPaymentPreference updates the default merchant payment preference for a credit account
	UpdateDefaultMerchantPaymentPreference(context.Context, *UpdateDefaultMerchantPaymentPreferenceRequest) (*UpdateDefaultMerchantPaymentPreferenceResponse, error)
	// IsCcLinkingEnabledForActor checks if user is allowed to link credit cards for upi payments
	IsCcLinkingEnabledForActor(context.Context, *IsCcLinkingEnabledForActorRequest) (*IsCcLinkingEnabledForActorResponse, error)
	// InitiateUpiLiteActivation - initiates upi lite activation for an upi account
	//  1. calls GetUpiLite() vg rpc to get Lite Reference Number (LRN). LRN will be used as a unique
	//     identifier b/w NPCI and psp for all the future actions on upi lite account
	//  2. creates upi lite account in DB.
	//  3. triggers workflow for activation of upi lite account.
	InitiateUpiLiteActivation(context.Context, *InitiateUpiLiteActivationRequest) (*InitiateUpiLiteActivationResponse, error)
	// InitiateUpiLiteDeactivation -
	// is used to initiate the upi lite deactivation for an upi account
	// triggers workflow for deactivation based on the balance of upi
	// lite account.
	InitiateUpiLiteDeactivation(context.Context, *InitiateUpiLiteDeactivationRequest) (*InitiateUpiLiteDeactivationResponse, error)
	// CheckUpiLiteActionStatus -
	//  1. checks the status of upi lite action workflow.
	//  2. decides the next action to be performed by the client.
	//     Based on the workflow status, client would need to perform one of the following 3 actions:
	//     2.1 InitiatePayment - initiate upi lite top (or withdraw during deactivation) by calling InitiatePayment FE rpc
	//     2.2 SyncUpiLite - call SyncUpiLite Fe rpc to fetch the latest info regarding
	//     2.3 PollWorkflow - If upi lite action workflow is still in progress, then client will be given
	//     a retry timer using which client shall make the next polling call (if required).
	//  3. generates the terminal_screen to be shown to the user. Client shall show the screen to the user if
	//     populated (irrespective of the next action, screen should be shown to user and next action can go on
	//     in background).
	CheckUpiLiteActionStatus(context.Context, *CheckUpiLiteActionStatusRequest) (*CheckUpiLiteActionStatusResponse, error)
	// SyncUpiLiteInfo-
	// 1. Delete upi lite account which have 0 balance
	// 2. Fetch account status and detail for upi lite account
	SyncUpiLiteInfo(context.Context, *SyncUpiLiteInfoRequest) (*SyncUpiLiteInfoResponse, error)
	// GetUpiLiteInfo - fetches the upi lite info for an upi account
	GetUpiLiteInfo(context.Context, *GetUpiLiteInfoRequest) (*GetUpiLiteInfoResponse, error)
	// IsUpiLiteEnabledForActor - checks if user is allowed to use upi lite
	IsUpiLiteEnabledForActor(context.Context, *IsUpiLiteEnabledForActorRequest) (*IsUpiLiteEnabledForActorResponse, error)
	// GetAccountsForUpiLiteActivation - fetches the list of all the bank accounts that are eligible for upi lite activation
	// Eligibility of the bank account is determined by the response received from list account provider RPC
	GetAccountsForUpiLiteActivation(context.Context, *GetAccountsForUpiLiteActivationRequest) (*GetAccountsForUpiLiteActivationResponse, error)
	// IsFeatureEnabledForActor - checks if the passed feature is enabled for given actor or not
	IsFeatureEnabledForActor(context.Context, *IsFeatureEnabledForActorRequest) (*IsFeatureEnabledForActorResponse, error)
	// GetLatestUpiOnboardingDetailForAccount - It is used to fetch the latest upi onboarding detail for a account id and
	// given action type
	GetLatestUpiOnboardingDetailForAccount(context.Context, *GetLatestUpiOnboardingDetailForAccountRequest) (*GetLatestUpiOnboardingDetailForAccountResponse, error)
	// GetTpapFeatureStatus : checks the TPAP feature status for an actor
	// e.g. Active, Inactive, InProgress etc.
	GetTpapFeatureStatus(context.Context, *GetTpapFeatureStatusRequest) (*GetTpapFeatureStatusResponse, error)
	// CheckEligibilityForOneClickFlowPopUp : checks the eligibility for the user for the one click tpap flow
	// pop up.
	// Note - This pop up is used to prompt users to link their connected accounts via TPAP.
	CheckEligibilityForOneClickFlowPopUp(context.Context, *CheckEligibilityForOneClickFlowPopUpRequest) (*CheckEligibilityForOneClickFlowPopUpResponse, error)
	// IsAccountActionAllowed: used to check if the given action is allowed for an account.
	// E.g. In case, user has an active Mandate present delink upi accounts and disable vpa actions
	// are not allowed.
	IsAccountActionAllowed(context.Context, *IsAccountActionAllowedRequest) (*IsAccountActionAllowedResponse, error)
	// GetConsolidatedUpiNumberDetails: fetches the consolidated mapper details for the actor
	// It returns all upi accounts along with upi number details linked to those accounts
	GetConsolidatedUpiNumberDetails(context.Context, *GetConsolidatedUpiNumberDetailsRequest) (*GetConsolidatedUpiNumberDetailsResponse, error)
	// GetAccountByIdentifier: fetches the upi account for the given account ref id
	GetAccountByIdentifier(context.Context, *GetAccountByIdentifierRequest) (*GetAccountByIdentifierResponse, error)
	// GetUpiNumberPiMapping: fetches the upi number to pi mapping for the given upi number
	GetUpiNumberPiMapping(context.Context, *GetUpiNumberPiMappingRequest) (*GetUpiNumberPiMappingResponse, error)
}

// UnimplementedUpiOnboardingServer should be embedded to have forward compatible implementations.
type UnimplementedUpiOnboardingServer struct {
}

func (UnimplementedUpiOnboardingServer) CreateInternalPiForVpa(context.Context, *CreateInternalPiForVpaRequest) (*CreateInternalPiForVpaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateInternalPiForVpa not implemented")
}
func (UnimplementedUpiOnboardingServer) ListAccountProviders(context.Context, *ListAccountProvidersRequest) (*ListAccountProvidersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAccountProviders not implemented")
}
func (UnimplementedUpiOnboardingServer) LinkUpiAccounts(context.Context, *LinkUpiAccountsRequest) (*LinkUpiAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkUpiAccounts not implemented")
}
func (UnimplementedUpiOnboardingServer) GetUpiAccountsActionStatus(context.Context, *GetUpiAccountsActionStatusRequest) (*GetUpiAccountsActionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpiAccountsActionStatus not implemented")
}
func (UnimplementedUpiOnboardingServer) ListAccount(context.Context, *ListAccountRequest) (*ListAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAccount not implemented")
}
func (UnimplementedUpiOnboardingServer) GetAccounts(context.Context, *GetAccountsRequest) (*GetAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccounts not implemented")
}
func (UnimplementedUpiOnboardingServer) GetVpaNameForActor(context.Context, *GetVpaNameForActorRequest) (*GetVpaNameForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVpaNameForActor not implemented")
}
func (UnimplementedUpiOnboardingServer) CreateVpa(context.Context, *CreateVpaRequest) (*CreateVpaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVpa not implemented")
}
func (UnimplementedUpiOnboardingServer) InitiateDelinkUpiAccount(context.Context, *InitiateDelinkUpiAccountRequest) (*InitiateDelinkUpiAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateDelinkUpiAccount not implemented")
}
func (UnimplementedUpiOnboardingServer) LinkInternalAccount(context.Context, *LinkInternalAccountRequest) (*LinkInternalAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkInternalAccount not implemented")
}
func (UnimplementedUpiOnboardingServer) UpdateAccountPreference(context.Context, *UpdateAccountPreferenceRequest) (*UpdateAccountPreferenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAccountPreference not implemented")
}
func (UnimplementedUpiOnboardingServer) ActivateUpiAccount(context.Context, *ActivateUpiAccountRequest) (*ActivateUpiAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivateUpiAccount not implemented")
}
func (UnimplementedUpiOnboardingServer) DelinkUpiAccountWithVendor(context.Context, *DelinkUpiAccountWithVendorRequest) (*DelinkUpiAccountWithVendorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelinkUpiAccountWithVendor not implemented")
}
func (UnimplementedUpiOnboardingServer) GetAccount(context.Context, *GetAccountRequest) (*GetAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedUpiOnboardingServer) DeActivatePi(context.Context, *DeActivatePiRequest) (*DeActivatePiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeActivatePi not implemented")
}
func (UnimplementedUpiOnboardingServer) CheckIfVpaMigrationRequired(context.Context, *CheckIfVpaMigrationRequiredRequest) (*CheckIfVpaMigrationRequiredResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckIfVpaMigrationRequired not implemented")
}
func (UnimplementedUpiOnboardingServer) BalanceEnquiry(context.Context, *BalanceEnquiryRequest) (*BalanceEnquiryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BalanceEnquiry not implemented")
}
func (UnimplementedUpiOnboardingServer) GetLatestUpiRequestLogForAccount(context.Context, *GetLatestUpiRequestLogForAccountRequest) (*GetLatestUpiRequestLogForAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestUpiRequestLogForAccount not implemented")
}
func (UnimplementedUpiOnboardingServer) LinkUpiNumber(context.Context, *LinkUpiNumberRequest) (*LinkUpiNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkUpiNumber not implemented")
}
func (UnimplementedUpiOnboardingServer) InitiateLinkUpiNumber(context.Context, *InitiateLinkUpiNumberRequest) (*InitiateLinkUpiNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateLinkUpiNumber not implemented")
}
func (UnimplementedUpiOnboardingServer) GetPinStatus(context.Context, *GetPinStatusRequest) (*GetPinStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPinStatus not implemented")
}
func (UnimplementedUpiOnboardingServer) InitiateDelinkUpiNumber(context.Context, *InitiateDelinkUpiNumberRequest) (*InitiateDelinkUpiNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateDelinkUpiNumber not implemented")
}
func (UnimplementedUpiOnboardingServer) GetUpiNumberActionStatus(context.Context, *GetUpiNumberActionStatusRequest) (*GetUpiNumberActionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpiNumberActionStatus not implemented")
}
func (UnimplementedUpiOnboardingServer) GetMapperInfo(context.Context, *GetMapperInfoRequest) (*GetMapperInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMapperInfo not implemented")
}
func (UnimplementedUpiOnboardingServer) IsTpapEnabledForActor(context.Context, *IsTpapEnabledForActorRequest) (*IsTpapEnabledForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsTpapEnabledForActor not implemented")
}
func (UnimplementedUpiOnboardingServer) CheckUpiNumberStatusWithVendor(context.Context, *CheckUpiNumberStatusWithVendorRequest) (*CheckUpiNumberStatusWithVendorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUpiNumberStatusWithVendor not implemented")
}
func (UnimplementedUpiOnboardingServer) DelinkUpiNumber(context.Context, *DelinkUpiNumberRequest) (*DelinkUpiNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelinkUpiNumber not implemented")
}
func (UnimplementedUpiOnboardingServer) GetUpiNumberDetails(context.Context, *GetUpiNumberDetailsRequest) (*GetUpiNumberDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpiNumberDetails not implemented")
}
func (UnimplementedUpiOnboardingServer) DisableOrEnableUpiNumber(context.Context, *DisableOrEnableUpiNumberRequest) (*DisableOrEnableUpiNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableOrEnableUpiNumber not implemented")
}
func (UnimplementedUpiOnboardingServer) IsMapperEnabledForActor(context.Context, *IsMapperEnabledForActorRequest) (*IsMapperEnabledForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsMapperEnabledForActor not implemented")
}
func (UnimplementedUpiOnboardingServer) ActivateInternationalPaymentsWithVendor(context.Context, *ActivateInternationalPaymentsWithVendorRequest) (*ActivateInternationalPaymentsWithVendorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivateInternationalPaymentsWithVendor not implemented")
}
func (UnimplementedUpiOnboardingServer) InitiateInternationalPaymentsActivation(context.Context, *InitiateInternationalPaymentsActivationRequest) (*InitiateInternationalPaymentsActivationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateInternationalPaymentsActivation not implemented")
}
func (UnimplementedUpiOnboardingServer) InternationalPaymentsActionEnquiryWithVendor(context.Context, *InternationalPaymentsActionEnquiryWithVendorRequest) (*InternationalPaymentsActionEnquiryWithVendorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InternationalPaymentsActionEnquiryWithVendor not implemented")
}
func (UnimplementedUpiOnboardingServer) GetInternationalPaymentActionStatus(context.Context, *GetInternationalPaymentActionStatusRequest) (*GetInternationalPaymentActionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInternationalPaymentActionStatus not implemented")
}
func (UnimplementedUpiOnboardingServer) ValidateAadhaarNoForUpiPinSet(context.Context, *ValidateAadhaarNoForUpiPinSetRequest) (*ValidateAadhaarNoForUpiPinSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateAadhaarNoForUpiPinSet not implemented")
}
func (UnimplementedUpiOnboardingServer) GetUpiPinSetOptionsForAccountId(context.Context, *GetUpiPinSetOptionsForAccountIdRequest) (*GetUpiPinSetOptionsForAccountIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpiPinSetOptionsForAccountId not implemented")
}
func (UnimplementedUpiOnboardingServer) DeactivateInternationalPaymentsWithVendor(context.Context, *DeactivateInternationalPaymentsWithVendorRequest) (*DeactivateInternationalPaymentsWithVendorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeactivateInternationalPaymentsWithVendor not implemented")
}
func (UnimplementedUpiOnboardingServer) InitiateInternationalPaymentsDeactivation(context.Context, *InitiateInternationalPaymentsDeactivationRequest) (*InitiateInternationalPaymentsDeactivationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateInternationalPaymentsDeactivation not implemented")
}
func (UnimplementedUpiOnboardingServer) GetInternationPaymentDetailsForAccount(context.Context, *GetInternationPaymentDetailsForAccountRequest) (*GetInternationPaymentDetailsForAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInternationPaymentDetailsForAccount not implemented")
}
func (UnimplementedUpiOnboardingServer) ValidateUpiInternationalQr(context.Context, *ValidateUpiInternationalQrRequest) (*ValidateUpiInternationalQrResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateUpiInternationalQr not implemented")
}
func (UnimplementedUpiOnboardingServer) IsUpiPinSetUsingAadhaarEnabledForActor(context.Context, *IsUpiPinSetUsingAadhaarEnabledForActorRequest) (*IsUpiPinSetUsingAadhaarEnabledForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsUpiPinSetUsingAadhaarEnabledForActor not implemented")
}
func (UnimplementedUpiOnboardingServer) IsUpiInternationalPaymentEnabledForActor(context.Context, *IsUpiInternationalPaymentEnabledForActorRequest) (*IsUpiInternationalPaymentEnabledForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsUpiInternationalPaymentEnabledForActor not implemented")
}
func (UnimplementedUpiOnboardingServer) UpdateDefaultMerchantPaymentPreference(context.Context, *UpdateDefaultMerchantPaymentPreferenceRequest) (*UpdateDefaultMerchantPaymentPreferenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDefaultMerchantPaymentPreference not implemented")
}
func (UnimplementedUpiOnboardingServer) IsCcLinkingEnabledForActor(context.Context, *IsCcLinkingEnabledForActorRequest) (*IsCcLinkingEnabledForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsCcLinkingEnabledForActor not implemented")
}
func (UnimplementedUpiOnboardingServer) InitiateUpiLiteActivation(context.Context, *InitiateUpiLiteActivationRequest) (*InitiateUpiLiteActivationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateUpiLiteActivation not implemented")
}
func (UnimplementedUpiOnboardingServer) InitiateUpiLiteDeactivation(context.Context, *InitiateUpiLiteDeactivationRequest) (*InitiateUpiLiteDeactivationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateUpiLiteDeactivation not implemented")
}
func (UnimplementedUpiOnboardingServer) CheckUpiLiteActionStatus(context.Context, *CheckUpiLiteActionStatusRequest) (*CheckUpiLiteActionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUpiLiteActionStatus not implemented")
}
func (UnimplementedUpiOnboardingServer) SyncUpiLiteInfo(context.Context, *SyncUpiLiteInfoRequest) (*SyncUpiLiteInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncUpiLiteInfo not implemented")
}
func (UnimplementedUpiOnboardingServer) GetUpiLiteInfo(context.Context, *GetUpiLiteInfoRequest) (*GetUpiLiteInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpiLiteInfo not implemented")
}
func (UnimplementedUpiOnboardingServer) IsUpiLiteEnabledForActor(context.Context, *IsUpiLiteEnabledForActorRequest) (*IsUpiLiteEnabledForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsUpiLiteEnabledForActor not implemented")
}
func (UnimplementedUpiOnboardingServer) GetAccountsForUpiLiteActivation(context.Context, *GetAccountsForUpiLiteActivationRequest) (*GetAccountsForUpiLiteActivationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountsForUpiLiteActivation not implemented")
}
func (UnimplementedUpiOnboardingServer) IsFeatureEnabledForActor(context.Context, *IsFeatureEnabledForActorRequest) (*IsFeatureEnabledForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsFeatureEnabledForActor not implemented")
}
func (UnimplementedUpiOnboardingServer) GetLatestUpiOnboardingDetailForAccount(context.Context, *GetLatestUpiOnboardingDetailForAccountRequest) (*GetLatestUpiOnboardingDetailForAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestUpiOnboardingDetailForAccount not implemented")
}
func (UnimplementedUpiOnboardingServer) GetTpapFeatureStatus(context.Context, *GetTpapFeatureStatusRequest) (*GetTpapFeatureStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTpapFeatureStatus not implemented")
}
func (UnimplementedUpiOnboardingServer) CheckEligibilityForOneClickFlowPopUp(context.Context, *CheckEligibilityForOneClickFlowPopUpRequest) (*CheckEligibilityForOneClickFlowPopUpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckEligibilityForOneClickFlowPopUp not implemented")
}
func (UnimplementedUpiOnboardingServer) IsAccountActionAllowed(context.Context, *IsAccountActionAllowedRequest) (*IsAccountActionAllowedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsAccountActionAllowed not implemented")
}
func (UnimplementedUpiOnboardingServer) GetConsolidatedUpiNumberDetails(context.Context, *GetConsolidatedUpiNumberDetailsRequest) (*GetConsolidatedUpiNumberDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConsolidatedUpiNumberDetails not implemented")
}
func (UnimplementedUpiOnboardingServer) GetAccountByIdentifier(context.Context, *GetAccountByIdentifierRequest) (*GetAccountByIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountByIdentifier not implemented")
}
func (UnimplementedUpiOnboardingServer) GetUpiNumberPiMapping(context.Context, *GetUpiNumberPiMappingRequest) (*GetUpiNumberPiMappingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpiNumberPiMapping not implemented")
}

// UnsafeUpiOnboardingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UpiOnboardingServer will
// result in compilation errors.
type UnsafeUpiOnboardingServer interface {
	mustEmbedUnimplementedUpiOnboardingServer()
}

func RegisterUpiOnboardingServer(s grpc.ServiceRegistrar, srv UpiOnboardingServer) {
	s.RegisterService(&UpiOnboarding_ServiceDesc, srv)
}

func _UpiOnboarding_CreateInternalPiForVpa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateInternalPiForVpaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).CreateInternalPiForVpa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_CreateInternalPiForVpa_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).CreateInternalPiForVpa(ctx, req.(*CreateInternalPiForVpaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_ListAccountProviders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAccountProvidersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).ListAccountProviders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_ListAccountProviders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).ListAccountProviders(ctx, req.(*ListAccountProvidersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_LinkUpiAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkUpiAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).LinkUpiAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_LinkUpiAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).LinkUpiAccounts(ctx, req.(*LinkUpiAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetUpiAccountsActionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpiAccountsActionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetUpiAccountsActionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetUpiAccountsActionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetUpiAccountsActionStatus(ctx, req.(*GetUpiAccountsActionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_ListAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).ListAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_ListAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).ListAccount(ctx, req.(*ListAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetAccounts(ctx, req.(*GetAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetVpaNameForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVpaNameForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetVpaNameForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetVpaNameForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetVpaNameForActor(ctx, req.(*GetVpaNameForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_CreateVpa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVpaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).CreateVpa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_CreateVpa_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).CreateVpa(ctx, req.(*CreateVpaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_InitiateDelinkUpiAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateDelinkUpiAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).InitiateDelinkUpiAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_InitiateDelinkUpiAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).InitiateDelinkUpiAccount(ctx, req.(*InitiateDelinkUpiAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_LinkInternalAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkInternalAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).LinkInternalAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_LinkInternalAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).LinkInternalAccount(ctx, req.(*LinkInternalAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_UpdateAccountPreference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAccountPreferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).UpdateAccountPreference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_UpdateAccountPreference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).UpdateAccountPreference(ctx, req.(*UpdateAccountPreferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_ActivateUpiAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateUpiAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).ActivateUpiAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_ActivateUpiAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).ActivateUpiAccount(ctx, req.(*ActivateUpiAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_DelinkUpiAccountWithVendor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelinkUpiAccountWithVendorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).DelinkUpiAccountWithVendor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_DelinkUpiAccountWithVendor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).DelinkUpiAccountWithVendor(ctx, req.(*DelinkUpiAccountWithVendorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetAccount(ctx, req.(*GetAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_DeActivatePi_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeActivatePiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).DeActivatePi(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_DeActivatePi_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).DeActivatePi(ctx, req.(*DeActivatePiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_CheckIfVpaMigrationRequired_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIfVpaMigrationRequiredRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).CheckIfVpaMigrationRequired(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_CheckIfVpaMigrationRequired_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).CheckIfVpaMigrationRequired(ctx, req.(*CheckIfVpaMigrationRequiredRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_BalanceEnquiry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BalanceEnquiryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).BalanceEnquiry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_BalanceEnquiry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).BalanceEnquiry(ctx, req.(*BalanceEnquiryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetLatestUpiRequestLogForAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestUpiRequestLogForAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetLatestUpiRequestLogForAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetLatestUpiRequestLogForAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetLatestUpiRequestLogForAccount(ctx, req.(*GetLatestUpiRequestLogForAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_LinkUpiNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkUpiNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).LinkUpiNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_LinkUpiNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).LinkUpiNumber(ctx, req.(*LinkUpiNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_InitiateLinkUpiNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateLinkUpiNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).InitiateLinkUpiNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_InitiateLinkUpiNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).InitiateLinkUpiNumber(ctx, req.(*InitiateLinkUpiNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetPinStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPinStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetPinStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetPinStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetPinStatus(ctx, req.(*GetPinStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_InitiateDelinkUpiNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateDelinkUpiNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).InitiateDelinkUpiNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_InitiateDelinkUpiNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).InitiateDelinkUpiNumber(ctx, req.(*InitiateDelinkUpiNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetUpiNumberActionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpiNumberActionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetUpiNumberActionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetUpiNumberActionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetUpiNumberActionStatus(ctx, req.(*GetUpiNumberActionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetMapperInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMapperInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetMapperInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetMapperInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetMapperInfo(ctx, req.(*GetMapperInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_IsTpapEnabledForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsTpapEnabledForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).IsTpapEnabledForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_IsTpapEnabledForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).IsTpapEnabledForActor(ctx, req.(*IsTpapEnabledForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_CheckUpiNumberStatusWithVendor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUpiNumberStatusWithVendorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).CheckUpiNumberStatusWithVendor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_CheckUpiNumberStatusWithVendor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).CheckUpiNumberStatusWithVendor(ctx, req.(*CheckUpiNumberStatusWithVendorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_DelinkUpiNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelinkUpiNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).DelinkUpiNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_DelinkUpiNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).DelinkUpiNumber(ctx, req.(*DelinkUpiNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetUpiNumberDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpiNumberDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetUpiNumberDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetUpiNumberDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetUpiNumberDetails(ctx, req.(*GetUpiNumberDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_DisableOrEnableUpiNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableOrEnableUpiNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).DisableOrEnableUpiNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_DisableOrEnableUpiNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).DisableOrEnableUpiNumber(ctx, req.(*DisableOrEnableUpiNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_IsMapperEnabledForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMapperEnabledForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).IsMapperEnabledForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_IsMapperEnabledForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).IsMapperEnabledForActor(ctx, req.(*IsMapperEnabledForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_ActivateInternationalPaymentsWithVendor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateInternationalPaymentsWithVendorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).ActivateInternationalPaymentsWithVendor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_ActivateInternationalPaymentsWithVendor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).ActivateInternationalPaymentsWithVendor(ctx, req.(*ActivateInternationalPaymentsWithVendorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_InitiateInternationalPaymentsActivation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateInternationalPaymentsActivationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).InitiateInternationalPaymentsActivation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_InitiateInternationalPaymentsActivation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).InitiateInternationalPaymentsActivation(ctx, req.(*InitiateInternationalPaymentsActivationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_InternationalPaymentsActionEnquiryWithVendor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternationalPaymentsActionEnquiryWithVendorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).InternationalPaymentsActionEnquiryWithVendor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_InternationalPaymentsActionEnquiryWithVendor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).InternationalPaymentsActionEnquiryWithVendor(ctx, req.(*InternationalPaymentsActionEnquiryWithVendorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetInternationalPaymentActionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInternationalPaymentActionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetInternationalPaymentActionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetInternationalPaymentActionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetInternationalPaymentActionStatus(ctx, req.(*GetInternationalPaymentActionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_ValidateAadhaarNoForUpiPinSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateAadhaarNoForUpiPinSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).ValidateAadhaarNoForUpiPinSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_ValidateAadhaarNoForUpiPinSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).ValidateAadhaarNoForUpiPinSet(ctx, req.(*ValidateAadhaarNoForUpiPinSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetUpiPinSetOptionsForAccountId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpiPinSetOptionsForAccountIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetUpiPinSetOptionsForAccountId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetUpiPinSetOptionsForAccountId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetUpiPinSetOptionsForAccountId(ctx, req.(*GetUpiPinSetOptionsForAccountIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_DeactivateInternationalPaymentsWithVendor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeactivateInternationalPaymentsWithVendorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).DeactivateInternationalPaymentsWithVendor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_DeactivateInternationalPaymentsWithVendor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).DeactivateInternationalPaymentsWithVendor(ctx, req.(*DeactivateInternationalPaymentsWithVendorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_InitiateInternationalPaymentsDeactivation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateInternationalPaymentsDeactivationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).InitiateInternationalPaymentsDeactivation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_InitiateInternationalPaymentsDeactivation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).InitiateInternationalPaymentsDeactivation(ctx, req.(*InitiateInternationalPaymentsDeactivationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetInternationPaymentDetailsForAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInternationPaymentDetailsForAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetInternationPaymentDetailsForAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetInternationPaymentDetailsForAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetInternationPaymentDetailsForAccount(ctx, req.(*GetInternationPaymentDetailsForAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_ValidateUpiInternationalQr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateUpiInternationalQrRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).ValidateUpiInternationalQr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_ValidateUpiInternationalQr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).ValidateUpiInternationalQr(ctx, req.(*ValidateUpiInternationalQrRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_IsUpiPinSetUsingAadhaarEnabledForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUpiPinSetUsingAadhaarEnabledForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).IsUpiPinSetUsingAadhaarEnabledForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_IsUpiPinSetUsingAadhaarEnabledForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).IsUpiPinSetUsingAadhaarEnabledForActor(ctx, req.(*IsUpiPinSetUsingAadhaarEnabledForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_IsUpiInternationalPaymentEnabledForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUpiInternationalPaymentEnabledForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).IsUpiInternationalPaymentEnabledForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_IsUpiInternationalPaymentEnabledForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).IsUpiInternationalPaymentEnabledForActor(ctx, req.(*IsUpiInternationalPaymentEnabledForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_UpdateDefaultMerchantPaymentPreference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDefaultMerchantPaymentPreferenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).UpdateDefaultMerchantPaymentPreference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_UpdateDefaultMerchantPaymentPreference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).UpdateDefaultMerchantPaymentPreference(ctx, req.(*UpdateDefaultMerchantPaymentPreferenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_IsCcLinkingEnabledForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsCcLinkingEnabledForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).IsCcLinkingEnabledForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_IsCcLinkingEnabledForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).IsCcLinkingEnabledForActor(ctx, req.(*IsCcLinkingEnabledForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_InitiateUpiLiteActivation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateUpiLiteActivationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).InitiateUpiLiteActivation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_InitiateUpiLiteActivation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).InitiateUpiLiteActivation(ctx, req.(*InitiateUpiLiteActivationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_InitiateUpiLiteDeactivation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateUpiLiteDeactivationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).InitiateUpiLiteDeactivation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_InitiateUpiLiteDeactivation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).InitiateUpiLiteDeactivation(ctx, req.(*InitiateUpiLiteDeactivationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_CheckUpiLiteActionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUpiLiteActionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).CheckUpiLiteActionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_CheckUpiLiteActionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).CheckUpiLiteActionStatus(ctx, req.(*CheckUpiLiteActionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_SyncUpiLiteInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncUpiLiteInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).SyncUpiLiteInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_SyncUpiLiteInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).SyncUpiLiteInfo(ctx, req.(*SyncUpiLiteInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetUpiLiteInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpiLiteInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetUpiLiteInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetUpiLiteInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetUpiLiteInfo(ctx, req.(*GetUpiLiteInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_IsUpiLiteEnabledForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUpiLiteEnabledForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).IsUpiLiteEnabledForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_IsUpiLiteEnabledForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).IsUpiLiteEnabledForActor(ctx, req.(*IsUpiLiteEnabledForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetAccountsForUpiLiteActivation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountsForUpiLiteActivationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetAccountsForUpiLiteActivation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetAccountsForUpiLiteActivation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetAccountsForUpiLiteActivation(ctx, req.(*GetAccountsForUpiLiteActivationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_IsFeatureEnabledForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsFeatureEnabledForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).IsFeatureEnabledForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_IsFeatureEnabledForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).IsFeatureEnabledForActor(ctx, req.(*IsFeatureEnabledForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetLatestUpiOnboardingDetailForAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestUpiOnboardingDetailForAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetLatestUpiOnboardingDetailForAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetLatestUpiOnboardingDetailForAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetLatestUpiOnboardingDetailForAccount(ctx, req.(*GetLatestUpiOnboardingDetailForAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetTpapFeatureStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTpapFeatureStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetTpapFeatureStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetTpapFeatureStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetTpapFeatureStatus(ctx, req.(*GetTpapFeatureStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_CheckEligibilityForOneClickFlowPopUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckEligibilityForOneClickFlowPopUpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).CheckEligibilityForOneClickFlowPopUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_CheckEligibilityForOneClickFlowPopUp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).CheckEligibilityForOneClickFlowPopUp(ctx, req.(*CheckEligibilityForOneClickFlowPopUpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_IsAccountActionAllowed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsAccountActionAllowedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).IsAccountActionAllowed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_IsAccountActionAllowed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).IsAccountActionAllowed(ctx, req.(*IsAccountActionAllowedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetConsolidatedUpiNumberDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConsolidatedUpiNumberDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetConsolidatedUpiNumberDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetConsolidatedUpiNumberDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetConsolidatedUpiNumberDetails(ctx, req.(*GetConsolidatedUpiNumberDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetAccountByIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountByIdentifierRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetAccountByIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetAccountByIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetAccountByIdentifier(ctx, req.(*GetAccountByIdentifierRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UpiOnboarding_GetUpiNumberPiMapping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpiNumberPiMappingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UpiOnboardingServer).GetUpiNumberPiMapping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UpiOnboarding_GetUpiNumberPiMapping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UpiOnboardingServer).GetUpiNumberPiMapping(ctx, req.(*GetUpiNumberPiMappingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UpiOnboarding_ServiceDesc is the grpc.ServiceDesc for UpiOnboarding service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UpiOnboarding_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "upi.onboarding.UpiOnboarding",
	HandlerType: (*UpiOnboardingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateInternalPiForVpa",
			Handler:    _UpiOnboarding_CreateInternalPiForVpa_Handler,
		},
		{
			MethodName: "ListAccountProviders",
			Handler:    _UpiOnboarding_ListAccountProviders_Handler,
		},
		{
			MethodName: "LinkUpiAccounts",
			Handler:    _UpiOnboarding_LinkUpiAccounts_Handler,
		},
		{
			MethodName: "GetUpiAccountsActionStatus",
			Handler:    _UpiOnboarding_GetUpiAccountsActionStatus_Handler,
		},
		{
			MethodName: "ListAccount",
			Handler:    _UpiOnboarding_ListAccount_Handler,
		},
		{
			MethodName: "GetAccounts",
			Handler:    _UpiOnboarding_GetAccounts_Handler,
		},
		{
			MethodName: "GetVpaNameForActor",
			Handler:    _UpiOnboarding_GetVpaNameForActor_Handler,
		},
		{
			MethodName: "CreateVpa",
			Handler:    _UpiOnboarding_CreateVpa_Handler,
		},
		{
			MethodName: "InitiateDelinkUpiAccount",
			Handler:    _UpiOnboarding_InitiateDelinkUpiAccount_Handler,
		},
		{
			MethodName: "LinkInternalAccount",
			Handler:    _UpiOnboarding_LinkInternalAccount_Handler,
		},
		{
			MethodName: "UpdateAccountPreference",
			Handler:    _UpiOnboarding_UpdateAccountPreference_Handler,
		},
		{
			MethodName: "ActivateUpiAccount",
			Handler:    _UpiOnboarding_ActivateUpiAccount_Handler,
		},
		{
			MethodName: "DelinkUpiAccountWithVendor",
			Handler:    _UpiOnboarding_DelinkUpiAccountWithVendor_Handler,
		},
		{
			MethodName: "GetAccount",
			Handler:    _UpiOnboarding_GetAccount_Handler,
		},
		{
			MethodName: "DeActivatePi",
			Handler:    _UpiOnboarding_DeActivatePi_Handler,
		},
		{
			MethodName: "CheckIfVpaMigrationRequired",
			Handler:    _UpiOnboarding_CheckIfVpaMigrationRequired_Handler,
		},
		{
			MethodName: "BalanceEnquiry",
			Handler:    _UpiOnboarding_BalanceEnquiry_Handler,
		},
		{
			MethodName: "GetLatestUpiRequestLogForAccount",
			Handler:    _UpiOnboarding_GetLatestUpiRequestLogForAccount_Handler,
		},
		{
			MethodName: "LinkUpiNumber",
			Handler:    _UpiOnboarding_LinkUpiNumber_Handler,
		},
		{
			MethodName: "InitiateLinkUpiNumber",
			Handler:    _UpiOnboarding_InitiateLinkUpiNumber_Handler,
		},
		{
			MethodName: "GetPinStatus",
			Handler:    _UpiOnboarding_GetPinStatus_Handler,
		},
		{
			MethodName: "InitiateDelinkUpiNumber",
			Handler:    _UpiOnboarding_InitiateDelinkUpiNumber_Handler,
		},
		{
			MethodName: "GetUpiNumberActionStatus",
			Handler:    _UpiOnboarding_GetUpiNumberActionStatus_Handler,
		},
		{
			MethodName: "GetMapperInfo",
			Handler:    _UpiOnboarding_GetMapperInfo_Handler,
		},
		{
			MethodName: "IsTpapEnabledForActor",
			Handler:    _UpiOnboarding_IsTpapEnabledForActor_Handler,
		},
		{
			MethodName: "CheckUpiNumberStatusWithVendor",
			Handler:    _UpiOnboarding_CheckUpiNumberStatusWithVendor_Handler,
		},
		{
			MethodName: "DelinkUpiNumber",
			Handler:    _UpiOnboarding_DelinkUpiNumber_Handler,
		},
		{
			MethodName: "GetUpiNumberDetails",
			Handler:    _UpiOnboarding_GetUpiNumberDetails_Handler,
		},
		{
			MethodName: "DisableOrEnableUpiNumber",
			Handler:    _UpiOnboarding_DisableOrEnableUpiNumber_Handler,
		},
		{
			MethodName: "IsMapperEnabledForActor",
			Handler:    _UpiOnboarding_IsMapperEnabledForActor_Handler,
		},
		{
			MethodName: "ActivateInternationalPaymentsWithVendor",
			Handler:    _UpiOnboarding_ActivateInternationalPaymentsWithVendor_Handler,
		},
		{
			MethodName: "InitiateInternationalPaymentsActivation",
			Handler:    _UpiOnboarding_InitiateInternationalPaymentsActivation_Handler,
		},
		{
			MethodName: "InternationalPaymentsActionEnquiryWithVendor",
			Handler:    _UpiOnboarding_InternationalPaymentsActionEnquiryWithVendor_Handler,
		},
		{
			MethodName: "GetInternationalPaymentActionStatus",
			Handler:    _UpiOnboarding_GetInternationalPaymentActionStatus_Handler,
		},
		{
			MethodName: "ValidateAadhaarNoForUpiPinSet",
			Handler:    _UpiOnboarding_ValidateAadhaarNoForUpiPinSet_Handler,
		},
		{
			MethodName: "GetUpiPinSetOptionsForAccountId",
			Handler:    _UpiOnboarding_GetUpiPinSetOptionsForAccountId_Handler,
		},
		{
			MethodName: "DeactivateInternationalPaymentsWithVendor",
			Handler:    _UpiOnboarding_DeactivateInternationalPaymentsWithVendor_Handler,
		},
		{
			MethodName: "InitiateInternationalPaymentsDeactivation",
			Handler:    _UpiOnboarding_InitiateInternationalPaymentsDeactivation_Handler,
		},
		{
			MethodName: "GetInternationPaymentDetailsForAccount",
			Handler:    _UpiOnboarding_GetInternationPaymentDetailsForAccount_Handler,
		},
		{
			MethodName: "ValidateUpiInternationalQr",
			Handler:    _UpiOnboarding_ValidateUpiInternationalQr_Handler,
		},
		{
			MethodName: "IsUpiPinSetUsingAadhaarEnabledForActor",
			Handler:    _UpiOnboarding_IsUpiPinSetUsingAadhaarEnabledForActor_Handler,
		},
		{
			MethodName: "IsUpiInternationalPaymentEnabledForActor",
			Handler:    _UpiOnboarding_IsUpiInternationalPaymentEnabledForActor_Handler,
		},
		{
			MethodName: "UpdateDefaultMerchantPaymentPreference",
			Handler:    _UpiOnboarding_UpdateDefaultMerchantPaymentPreference_Handler,
		},
		{
			MethodName: "IsCcLinkingEnabledForActor",
			Handler:    _UpiOnboarding_IsCcLinkingEnabledForActor_Handler,
		},
		{
			MethodName: "InitiateUpiLiteActivation",
			Handler:    _UpiOnboarding_InitiateUpiLiteActivation_Handler,
		},
		{
			MethodName: "InitiateUpiLiteDeactivation",
			Handler:    _UpiOnboarding_InitiateUpiLiteDeactivation_Handler,
		},
		{
			MethodName: "CheckUpiLiteActionStatus",
			Handler:    _UpiOnboarding_CheckUpiLiteActionStatus_Handler,
		},
		{
			MethodName: "SyncUpiLiteInfo",
			Handler:    _UpiOnboarding_SyncUpiLiteInfo_Handler,
		},
		{
			MethodName: "GetUpiLiteInfo",
			Handler:    _UpiOnboarding_GetUpiLiteInfo_Handler,
		},
		{
			MethodName: "IsUpiLiteEnabledForActor",
			Handler:    _UpiOnboarding_IsUpiLiteEnabledForActor_Handler,
		},
		{
			MethodName: "GetAccountsForUpiLiteActivation",
			Handler:    _UpiOnboarding_GetAccountsForUpiLiteActivation_Handler,
		},
		{
			MethodName: "IsFeatureEnabledForActor",
			Handler:    _UpiOnboarding_IsFeatureEnabledForActor_Handler,
		},
		{
			MethodName: "GetLatestUpiOnboardingDetailForAccount",
			Handler:    _UpiOnboarding_GetLatestUpiOnboardingDetailForAccount_Handler,
		},
		{
			MethodName: "GetTpapFeatureStatus",
			Handler:    _UpiOnboarding_GetTpapFeatureStatus_Handler,
		},
		{
			MethodName: "CheckEligibilityForOneClickFlowPopUp",
			Handler:    _UpiOnboarding_CheckEligibilityForOneClickFlowPopUp_Handler,
		},
		{
			MethodName: "IsAccountActionAllowed",
			Handler:    _UpiOnboarding_IsAccountActionAllowed_Handler,
		},
		{
			MethodName: "GetConsolidatedUpiNumberDetails",
			Handler:    _UpiOnboarding_GetConsolidatedUpiNumberDetails_Handler,
		},
		{
			MethodName: "GetAccountByIdentifier",
			Handler:    _UpiOnboarding_GetAccountByIdentifier_Handler,
		},
		{
			MethodName: "GetUpiNumberPiMapping",
			Handler:    _UpiOnboarding_GetUpiNumberPiMapping_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/upi/onboarding/service.proto",
}
