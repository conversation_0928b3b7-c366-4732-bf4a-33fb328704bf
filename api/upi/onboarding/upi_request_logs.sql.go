package onboarding

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// UnmarshalJSON implements the Unmarshaler interface
func (x *DetailedStatus) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, x)
}

// MarshalJSON implements the Marshaler interface
func (x *DetailedStatus) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(x)
}

// Value : Valuer interface implementation for storing the data in string format in DB
func (x *DetailedStatus) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return protojson.Marshal(x)
}

// Scan : Scanner interface implementation for parsing data while reading from DB
func (x *DetailedStatus) Scan(input interface{}) error {
	if val, ok := input.([]byte); ok {
		return backport.SafeUnmarshal(protojson.Unmarshal, val, x)
	}
	err := fmt.Errorf("expected []byte, got %T", input)
	return err
}
