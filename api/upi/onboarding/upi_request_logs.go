package onboarding

import "google.golang.org/protobuf/proto"

// AddDetailedStatusMetadata adds detailed status metadata to upi request logs detailed status list
// if the list is nil, will create a new list and add the detailed status metadata to that list
// if the list already contains metadata -
// will check if the latest entry is equal the passed metadata, if yes then update the latest entry's
// updated time with current metadata created time else append the metadata to the list
func (u *UpiRequestLog) AddDetailedStatusMetadata(detailedStatusMetadata *DetailedStatusMetadata) {
	if u == nil {
		return
	}
	if detailedStatusMetadata == nil {
		return
	}

	if u.GetDetailedStatus() != nil {
		existingDetailedStatusMetaData := u.GetDetailedStatus().GetDetailedStatusMetadata()[len(u.GetDetailedStatus().GetDetailedStatusMetadata())-1]
		newCreatedAt := detailedStatusMetadata.GetCreatedAt()
		// changing created at for proto.equal to work. We want to check
		// if all the fields are equal except created at
		detailedStatusMetadata.CreatedAt = existingDetailedStatusMetaData.GetCreatedAt()
		detailedStatusMetadata.UpdatedAt = existingDetailedStatusMetaData.GetUpdatedAt()
		if !proto.Equal(existingDetailedStatusMetaData, detailedStatusMetadata) {
			detailedStatusMetadata.CreatedAt = newCreatedAt
			// for new entry created at and updated at will be equal
			detailedStatusMetadata.UpdatedAt = newCreatedAt
			u.GetDetailedStatus().DetailedStatusMetadata = append(u.GetDetailedStatus().GetDetailedStatusMetadata(), detailedStatusMetadata)
		} else {
			existingDetailedStatusMetaData.UpdatedAt = newCreatedAt
		}
	} else {
		u.DetailedStatus = &DetailedStatus{}
		u.DetailedStatus.DetailedStatusMetadata = []*DetailedStatusMetadata{
			detailedStatusMetadata,
		}
	}
}
