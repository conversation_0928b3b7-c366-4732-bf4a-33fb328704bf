// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/upi_request_logs.proto

package onboarding

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UpiRequestLogFieldMask is the enum representation of some of the UpiRequestLog fields that requires updates.
// Meant to be used as field mask to help with database updates
type UpiRequestLogFieldMask int32

const (
	UpiRequestLogFieldMask_UPI_REQUEST_LOG_FIELD_MASK_UNSPECIFIED     UpiRequestLogFieldMask = 0
	UpiRequestLogFieldMask_UPI_REQUEST_LOG_FIELD_MASK_STATUS          UpiRequestLogFieldMask = 1
	UpiRequestLogFieldMask_UPI_REQUEST_LOG_FIELD_MASK_DETAILED_STATUS UpiRequestLogFieldMask = 2
)

// Enum value maps for UpiRequestLogFieldMask.
var (
	UpiRequestLogFieldMask_name = map[int32]string{
		0: "UPI_REQUEST_LOG_FIELD_MASK_UNSPECIFIED",
		1: "UPI_REQUEST_LOG_FIELD_MASK_STATUS",
		2: "UPI_REQUEST_LOG_FIELD_MASK_DETAILED_STATUS",
	}
	UpiRequestLogFieldMask_value = map[string]int32{
		"UPI_REQUEST_LOG_FIELD_MASK_UNSPECIFIED":     0,
		"UPI_REQUEST_LOG_FIELD_MASK_STATUS":          1,
		"UPI_REQUEST_LOG_FIELD_MASK_DETAILED_STATUS": 2,
	}
)

func (x UpiRequestLogFieldMask) Enum() *UpiRequestLogFieldMask {
	p := new(UpiRequestLogFieldMask)
	*p = x
	return p
}

func (x UpiRequestLogFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiRequestLogFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_upi_request_logs_proto_enumTypes[0].Descriptor()
}

func (UpiRequestLogFieldMask) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_upi_request_logs_proto_enumTypes[0]
}

func (x UpiRequestLogFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiRequestLogFieldMask.Descriptor instead.
func (UpiRequestLogFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_request_logs_proto_rawDescGZIP(), []int{0}
}

type UpiRequestLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for each row
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// actor id corresponding to the user
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// account id of the upi account corresponding to which the request/response are logged
	AccountId string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Upi onboarding partner bank
	Vendor vendorgateway.Vendor `protobuf:"varint,4,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// Status of the request initiated with vendor. E.g. SUCCESS, FAILURE etc.
	Status enums.UpiRequestLogApiStatus `protobuf:"varint,5,opt,name=status,proto3,enum=upi.onboarding.enums.UpiRequestLogApiStatus" json:"status,omitempty"`
	// contains the request codes and descriptions
	DetailedStatus *DetailedStatus `protobuf:"bytes,6,opt,name=detailed_status,json=detailedStatus,proto3" json:"detailed_status,omitempty"`
	// Api corresponding to the request initiated with vendor. Eg. GenerateUpiOtp, RegisterMobile etc.
	ApiType enums.UpiRequestLogApiType `protobuf:"varint,7,opt,name=api_type,json=apiType,proto3,enum=upi.onboarding.enums.UpiRequestLogApiType" json:"api_type,omitempty"`
	// Request id for the request initiated with vendor
	VendorReqId string `protobuf:"bytes,8,opt,name=vendor_req_id,json=vendorReqId,proto3" json:"vendor_req_id,omitempty"`
	// time of creation of upi request log
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time of upi request log
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// time of deletion of upi request log
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// ApiSubType specifies the subtype of API being called to serve the UPI Request
	// Sub type is used to represent api type on more granular level
	ApiSubType enums.UpiRequestLogApiSubType `protobuf:"varint,12,opt,name=api_sub_type,json=apiSubType,proto3,enum=upi.onboarding.enums.UpiRequestLogApiSubType" json:"api_sub_type,omitempty"`
}

func (x *UpiRequestLog) Reset() {
	*x = UpiRequestLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_request_logs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiRequestLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiRequestLog) ProtoMessage() {}

func (x *UpiRequestLog) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_request_logs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiRequestLog.ProtoReflect.Descriptor instead.
func (*UpiRequestLog) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_request_logs_proto_rawDescGZIP(), []int{0}
}

func (x *UpiRequestLog) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpiRequestLog) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpiRequestLog) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *UpiRequestLog) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *UpiRequestLog) GetStatus() enums.UpiRequestLogApiStatus {
	if x != nil {
		return x.Status
	}
	return enums.UpiRequestLogApiStatus(0)
}

func (x *UpiRequestLog) GetDetailedStatus() *DetailedStatus {
	if x != nil {
		return x.DetailedStatus
	}
	return nil
}

func (x *UpiRequestLog) GetApiType() enums.UpiRequestLogApiType {
	if x != nil {
		return x.ApiType
	}
	return enums.UpiRequestLogApiType(0)
}

func (x *UpiRequestLog) GetVendorReqId() string {
	if x != nil {
		return x.VendorReqId
	}
	return ""
}

func (x *UpiRequestLog) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UpiRequestLog) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UpiRequestLog) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *UpiRequestLog) GetApiSubType() enums.UpiRequestLogApiSubType {
	if x != nil {
		return x.ApiSubType
	}
	return enums.UpiRequestLogApiSubType(0)
}

// DetailedStatus contains the list of status for this entry updated over the time
type DetailedStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetailedStatusMetadata []*DetailedStatusMetadata `protobuf:"bytes,1,rep,name=detailed_status_metadata,json=detailedStatusMetadata,proto3" json:"detailed_status_metadata,omitempty"`
}

func (x *DetailedStatus) Reset() {
	*x = DetailedStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_request_logs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailedStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailedStatus) ProtoMessage() {}

func (x *DetailedStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_request_logs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailedStatus.ProtoReflect.Descriptor instead.
func (*DetailedStatus) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_request_logs_proto_rawDescGZIP(), []int{1}
}

func (x *DetailedStatus) GetDetailedStatusMetadata() []*DetailedStatusMetadata {
	if x != nil {
		return x.DetailedStatusMetadata
	}
	return nil
}

// DetailedStatusMetadata contains the request codes and descriptions
type DetailedStatusMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status code received from vendor
	RawStatusCode string `protobuf:"bytes,1,opt,name=raw_status_code,json=rawStatusCode,proto3" json:"raw_status_code,omitempty"`
	// description of the status code as sent by the vendor bank
	RawStatusDescription string `protobuf:"bytes,2,opt,name=raw_status_description,json=rawStatusDescription,proto3" json:"raw_status_description,omitempty"`
	// TD - Technical Decline / BD - Business Decline
	Category enums.DetailedStatusCategory `protobuf:"varint,3,opt,name=category,proto3,enum=upi.onboarding.enums.DetailedStatusCategory" json:"category,omitempty"`
	// timestamp when this entry in detailed status is created.
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// timestamp when this entry in detailed status is updated.
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *DetailedStatusMetadata) Reset() {
	*x = DetailedStatusMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_request_logs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailedStatusMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailedStatusMetadata) ProtoMessage() {}

func (x *DetailedStatusMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_request_logs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailedStatusMetadata.ProtoReflect.Descriptor instead.
func (*DetailedStatusMetadata) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_request_logs_proto_rawDescGZIP(), []int{2}
}

func (x *DetailedStatusMetadata) GetRawStatusCode() string {
	if x != nil {
		return x.RawStatusCode
	}
	return ""
}

func (x *DetailedStatusMetadata) GetRawStatusDescription() string {
	if x != nil {
		return x.RawStatusDescription
	}
	return ""
}

func (x *DetailedStatusMetadata) GetCategory() enums.DetailedStatusCategory {
	if x != nil {
		return x.Category
	}
	return enums.DetailedStatusCategory(0)
}

func (x *DetailedStatusMetadata) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DetailedStatusMetadata) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_api_upi_onboarding_upi_request_logs_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_upi_request_logs_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x6c, 0x6f, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x75, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x1e, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x61, 0x70, 0x69,
	0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x73, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x84, 0x05, 0x0a, 0x0d, 0x55, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x4c, 0x6f, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x44,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x4c, 0x6f, 0x67, 0x41, 0x70, 0x69, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x0f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x45, 0x0a,
	0x08, 0x61, 0x70, 0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x4c, 0x6f, 0x67, 0x41, 0x70, 0x69, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x61, 0x70, 0x69,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72,
	0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4f, 0x0a, 0x0c, 0x61, 0x70, 0x69,
	0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x4c, 0x6f, 0x67, 0x41, 0x70, 0x69, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a,
	0x61, 0x70, 0x69, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x22, 0x72, 0x0a, 0x0e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x60, 0x0a, 0x18,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x16, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb6,
	0x02, 0x0a, 0x16, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x61, 0x77,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x72, 0x61, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x72, 0x61, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0x9b, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x69, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x25,
	0x0a, 0x21, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f,
	0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0x02, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_upi_request_logs_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_upi_request_logs_proto_rawDescData = file_api_upi_onboarding_upi_request_logs_proto_rawDesc
)

func file_api_upi_onboarding_upi_request_logs_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_upi_request_logs_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_upi_request_logs_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_upi_request_logs_proto_rawDescData)
	})
	return file_api_upi_onboarding_upi_request_logs_proto_rawDescData
}

var file_api_upi_onboarding_upi_request_logs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_upi_request_logs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_upi_onboarding_upi_request_logs_proto_goTypes = []interface{}{
	(UpiRequestLogFieldMask)(0),        // 0: upi.onboarding.UpiRequestLogFieldMask
	(*UpiRequestLog)(nil),              // 1: upi.onboarding.UpiRequestLog
	(*DetailedStatus)(nil),             // 2: upi.onboarding.DetailedStatus
	(*DetailedStatusMetadata)(nil),     // 3: upi.onboarding.DetailedStatusMetadata
	(vendorgateway.Vendor)(0),          // 4: vendorgateway.Vendor
	(enums.UpiRequestLogApiStatus)(0),  // 5: upi.onboarding.enums.UpiRequestLogApiStatus
	(enums.UpiRequestLogApiType)(0),    // 6: upi.onboarding.enums.UpiRequestLogApiType
	(*timestamppb.Timestamp)(nil),      // 7: google.protobuf.Timestamp
	(enums.UpiRequestLogApiSubType)(0), // 8: upi.onboarding.enums.UpiRequestLogApiSubType
	(enums.DetailedStatusCategory)(0),  // 9: upi.onboarding.enums.DetailedStatusCategory
}
var file_api_upi_onboarding_upi_request_logs_proto_depIdxs = []int32{
	4,  // 0: upi.onboarding.UpiRequestLog.vendor:type_name -> vendorgateway.Vendor
	5,  // 1: upi.onboarding.UpiRequestLog.status:type_name -> upi.onboarding.enums.UpiRequestLogApiStatus
	2,  // 2: upi.onboarding.UpiRequestLog.detailed_status:type_name -> upi.onboarding.DetailedStatus
	6,  // 3: upi.onboarding.UpiRequestLog.api_type:type_name -> upi.onboarding.enums.UpiRequestLogApiType
	7,  // 4: upi.onboarding.UpiRequestLog.created_at:type_name -> google.protobuf.Timestamp
	7,  // 5: upi.onboarding.UpiRequestLog.updated_at:type_name -> google.protobuf.Timestamp
	7,  // 6: upi.onboarding.UpiRequestLog.deleted_at:type_name -> google.protobuf.Timestamp
	8,  // 7: upi.onboarding.UpiRequestLog.api_sub_type:type_name -> upi.onboarding.enums.UpiRequestLogApiSubType
	3,  // 8: upi.onboarding.DetailedStatus.detailed_status_metadata:type_name -> upi.onboarding.DetailedStatusMetadata
	9,  // 9: upi.onboarding.DetailedStatusMetadata.category:type_name -> upi.onboarding.enums.DetailedStatusCategory
	7,  // 10: upi.onboarding.DetailedStatusMetadata.created_at:type_name -> google.protobuf.Timestamp
	7,  // 11: upi.onboarding.DetailedStatusMetadata.updated_at:type_name -> google.protobuf.Timestamp
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_upi_request_logs_proto_init() }
func file_api_upi_onboarding_upi_request_logs_proto_init() {
	if File_api_upi_onboarding_upi_request_logs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_upi_request_logs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiRequestLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_request_logs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailedStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_request_logs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailedStatusMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_upi_request_logs_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_upi_request_logs_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_upi_request_logs_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_upi_request_logs_proto_enumTypes,
		MessageInfos:      file_api_upi_onboarding_upi_request_logs_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_upi_request_logs_proto = out.File
	file_api_upi_onboarding_upi_request_logs_proto_rawDesc = nil
	file_api_upi_onboarding_upi_request_logs_proto_goTypes = nil
	file_api_upi_onboarding_upi_request_logs_proto_depIdxs = nil
}
