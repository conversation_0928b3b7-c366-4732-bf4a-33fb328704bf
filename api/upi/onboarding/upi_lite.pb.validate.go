// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/onboarding/upi_lite.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on InitiatePayment with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InitiatePayment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiatePayment with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiatePaymentMultiError, or nil if none found.
func (m *InitiatePayment) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiatePayment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderId

	if all {
		switch v := interface{}(m.GetPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentValidationError{
				field:  "PaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayerPi()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentValidationError{
					field:  "PayerPi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentValidationError{
					field:  "PayerPi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayerPi()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentValidationError{
				field:  "PayerPi",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayeePi()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiatePaymentValidationError{
					field:  "PayeePi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiatePaymentValidationError{
					field:  "PayeePi",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayeePi()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiatePaymentValidationError{
				field:  "PayeePi",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayerAccountId

	if len(errors) > 0 {
		return InitiatePaymentMultiError(errors)
	}

	return nil
}

// InitiatePaymentMultiError is an error wrapping multiple validation errors
// returned by InitiatePayment.ValidateAll() if the designated constraints
// aren't met.
type InitiatePaymentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiatePaymentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiatePaymentMultiError) AllErrors() []error { return m }

// InitiatePaymentValidationError is the validation error returned by
// InitiatePayment.Validate if the designated constraints aren't met.
type InitiatePaymentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiatePaymentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiatePaymentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiatePaymentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiatePaymentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiatePaymentValidationError) ErrorName() string { return "InitiatePaymentValidationError" }

// Error satisfies the builtin error interface
func (e InitiatePaymentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiatePayment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiatePaymentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiatePaymentValidationError{}

// Validate checks the field values on SyncUpiLite with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SyncUpiLite) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncUpiLite with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SyncUpiLiteMultiError, or
// nil if none found.
func (m *SyncUpiLite) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncUpiLite) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if len(errors) > 0 {
		return SyncUpiLiteMultiError(errors)
	}

	return nil
}

// SyncUpiLiteMultiError is an error wrapping multiple validation errors
// returned by SyncUpiLite.ValidateAll() if the designated constraints aren't met.
type SyncUpiLiteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncUpiLiteMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncUpiLiteMultiError) AllErrors() []error { return m }

// SyncUpiLiteValidationError is the validation error returned by
// SyncUpiLite.Validate if the designated constraints aren't met.
type SyncUpiLiteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncUpiLiteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncUpiLiteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncUpiLiteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncUpiLiteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncUpiLiteValidationError) ErrorName() string { return "SyncUpiLiteValidationError" }

// Error satisfies the builtin error interface
func (e SyncUpiLiteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncUpiLite.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncUpiLiteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncUpiLiteValidationError{}

// Validate checks the field values on PollWorkflow with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PollWorkflow) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PollWorkflow with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PollWorkflowMultiError, or
// nil if none found.
func (m *PollWorkflow) ValidateAll() error {
	return m.validate(true)
}

func (m *PollWorkflow) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRetryTimer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PollWorkflowValidationError{
					field:  "RetryTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PollWorkflowValidationError{
					field:  "RetryTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRetryTimer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PollWorkflowValidationError{
				field:  "RetryTimer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PollWorkflowMultiError(errors)
	}

	return nil
}

// PollWorkflowMultiError is an error wrapping multiple validation errors
// returned by PollWorkflow.ValidateAll() if the designated constraints aren't met.
type PollWorkflowMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PollWorkflowMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PollWorkflowMultiError) AllErrors() []error { return m }

// PollWorkflowValidationError is the validation error returned by
// PollWorkflow.Validate if the designated constraints aren't met.
type PollWorkflowValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PollWorkflowValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PollWorkflowValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PollWorkflowValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PollWorkflowValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PollWorkflowValidationError) ErrorName() string { return "PollWorkflowValidationError" }

// Error satisfies the builtin error interface
func (e PollWorkflowValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPollWorkflow.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PollWorkflowValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PollWorkflowValidationError{}
