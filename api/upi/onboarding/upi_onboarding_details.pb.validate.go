// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/onboarding/upi_onboarding_details.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/upi/onboarding/enums"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.UpiOnboardingAction(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on UpiOnboardingDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiOnboardingDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiOnboardingDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiOnboardingDetailMultiError, or nil if none found.
func (m *UpiOnboardingDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiOnboardingDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AccountId

	// no validation rules for Vendor

	// no validation rules for Vpa

	// no validation rules for ClientReqId

	// no validation rules for Action

	// no validation rules for Status

	// no validation rules for VendorReqId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailValidationError{
				field:  "Payload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiOnboardingDetailMultiError(errors)
	}

	return nil
}

// UpiOnboardingDetailMultiError is an error wrapping multiple validation
// errors returned by UpiOnboardingDetail.ValidateAll() if the designated
// constraints aren't met.
type UpiOnboardingDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiOnboardingDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiOnboardingDetailMultiError) AllErrors() []error { return m }

// UpiOnboardingDetailValidationError is the validation error returned by
// UpiOnboardingDetail.Validate if the designated constraints aren't met.
type UpiOnboardingDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiOnboardingDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiOnboardingDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiOnboardingDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiOnboardingDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiOnboardingDetailValidationError) ErrorName() string {
	return "UpiOnboardingDetailValidationError"
}

// Error satisfies the builtin error interface
func (e UpiOnboardingDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiOnboardingDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiOnboardingDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiOnboardingDetailValidationError{}

// Validate checks the field values on UpiOnboardingDetailsPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiOnboardingDetailsPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiOnboardingDetailsPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiOnboardingDetailsPayloadMultiError, or nil if none found.
func (m *UpiOnboardingDetailsPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiOnboardingDetailsPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpiNumberLinkingDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "UpiNumberLinkingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "UpiNumberLinkingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpiNumberLinkingDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailsPayloadValidationError{
				field:  "UpiNumberLinkingDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpiNumberDelinkingDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "UpiNumberDelinkingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "UpiNumberDelinkingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpiNumberDelinkingDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailsPayloadValidationError{
				field:  "UpiNumberDelinkingDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActivateInternationalPaymentsPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "ActivateInternationalPaymentsPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "ActivateInternationalPaymentsPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivateInternationalPaymentsPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailsPayloadValidationError{
				field:  "ActivateInternationalPaymentsPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeactivateInternationalPaymentsPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "DeactivateInternationalPaymentsPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "DeactivateInternationalPaymentsPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeactivateInternationalPaymentsPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailsPayloadValidationError{
				field:  "DeactivateInternationalPaymentsPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActivateUpiLitePayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "ActivateUpiLitePayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "ActivateUpiLitePayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActivateUpiLitePayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailsPayloadValidationError{
				field:  "ActivateUpiLitePayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeactivateUpiLitePayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "DeactivateUpiLitePayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "DeactivateUpiLitePayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeactivateUpiLitePayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailsPayloadValidationError{
				field:  "DeactivateUpiLitePayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeactivateZeroBalanceUpiLitePayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "DeactivateZeroBalanceUpiLitePayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiOnboardingDetailsPayloadValidationError{
					field:  "DeactivateZeroBalanceUpiLitePayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeactivateZeroBalanceUpiLitePayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiOnboardingDetailsPayloadValidationError{
				field:  "DeactivateZeroBalanceUpiLitePayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiOnboardingDetailsPayloadMultiError(errors)
	}

	return nil
}

// UpiOnboardingDetailsPayloadMultiError is an error wrapping multiple
// validation errors returned by UpiOnboardingDetailsPayload.ValidateAll() if
// the designated constraints aren't met.
type UpiOnboardingDetailsPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiOnboardingDetailsPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiOnboardingDetailsPayloadMultiError) AllErrors() []error { return m }

// UpiOnboardingDetailsPayloadValidationError is the validation error returned
// by UpiOnboardingDetailsPayload.Validate if the designated constraints
// aren't met.
type UpiOnboardingDetailsPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiOnboardingDetailsPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiOnboardingDetailsPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiOnboardingDetailsPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiOnboardingDetailsPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiOnboardingDetailsPayloadValidationError) ErrorName() string {
	return "UpiOnboardingDetailsPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e UpiOnboardingDetailsPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiOnboardingDetailsPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiOnboardingDetailsPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiOnboardingDetailsPayloadValidationError{}

// Validate checks the field values on UpiNumberLinkingDetailsPayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiNumberLinkingDetailsPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiNumberLinkingDetailsPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpiNumberLinkingDetailsPayloadMultiError, or nil if none found.
func (m *UpiNumberLinkingDetailsPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberLinkingDetailsPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vpa

	// no validation rules for UpiNumberType

	// no validation rules for LinkingType

	// no validation rules for PreVpa

	if len(errors) > 0 {
		return UpiNumberLinkingDetailsPayloadMultiError(errors)
	}

	return nil
}

// UpiNumberLinkingDetailsPayloadMultiError is an error wrapping multiple
// validation errors returned by UpiNumberLinkingDetailsPayload.ValidateAll()
// if the designated constraints aren't met.
type UpiNumberLinkingDetailsPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberLinkingDetailsPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberLinkingDetailsPayloadMultiError) AllErrors() []error { return m }

// UpiNumberLinkingDetailsPayloadValidationError is the validation error
// returned by UpiNumberLinkingDetailsPayload.Validate if the designated
// constraints aren't met.
type UpiNumberLinkingDetailsPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberLinkingDetailsPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiNumberLinkingDetailsPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiNumberLinkingDetailsPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiNumberLinkingDetailsPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiNumberLinkingDetailsPayloadValidationError) ErrorName() string {
	return "UpiNumberLinkingDetailsPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e UpiNumberLinkingDetailsPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberLinkingDetailsPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberLinkingDetailsPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberLinkingDetailsPayloadValidationError{}

// Validate checks the field values on UpiNumberDelinkingDetailsPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpiNumberDelinkingDetailsPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiNumberDelinkingDetailsPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpiNumberDelinkingDetailsPayloadMultiError, or nil if none found.
func (m *UpiNumberDelinkingDetailsPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberDelinkingDetailsPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Vpa

	// no validation rules for UpiNumberType

	if len(errors) > 0 {
		return UpiNumberDelinkingDetailsPayloadMultiError(errors)
	}

	return nil
}

// UpiNumberDelinkingDetailsPayloadMultiError is an error wrapping multiple
// validation errors returned by
// UpiNumberDelinkingDetailsPayload.ValidateAll() if the designated
// constraints aren't met.
type UpiNumberDelinkingDetailsPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberDelinkingDetailsPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberDelinkingDetailsPayloadMultiError) AllErrors() []error { return m }

// UpiNumberDelinkingDetailsPayloadValidationError is the validation error
// returned by UpiNumberDelinkingDetailsPayload.Validate if the designated
// constraints aren't met.
type UpiNumberDelinkingDetailsPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberDelinkingDetailsPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiNumberDelinkingDetailsPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiNumberDelinkingDetailsPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiNumberDelinkingDetailsPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiNumberDelinkingDetailsPayloadValidationError) ErrorName() string {
	return "UpiNumberDelinkingDetailsPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e UpiNumberDelinkingDetailsPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberDelinkingDetailsPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberDelinkingDetailsPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberDelinkingDetailsPayloadValidationError{}

// Validate checks the field values on ActivateInternationalPaymentsPayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ActivateInternationalPaymentsPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivateInternationalPaymentsPayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ActivateInternationalPaymentsPayloadMultiError, or nil if none found.
func (m *ActivateInternationalPaymentsPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivateInternationalPaymentsPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInternationalPaymentsActivatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivateInternationalPaymentsPayloadValidationError{
					field:  "InternationalPaymentsActivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivateInternationalPaymentsPayloadValidationError{
					field:  "InternationalPaymentsActivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInternationalPaymentsActivatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivateInternationalPaymentsPayloadValidationError{
				field:  "InternationalPaymentsActivatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInternationalPaymentsExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivateInternationalPaymentsPayloadValidationError{
					field:  "InternationalPaymentsExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivateInternationalPaymentsPayloadValidationError{
					field:  "InternationalPaymentsExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInternationalPaymentsExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivateInternationalPaymentsPayloadValidationError{
				field:  "InternationalPaymentsExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActivateInternationalPaymentsPayloadMultiError(errors)
	}

	return nil
}

// ActivateInternationalPaymentsPayloadMultiError is an error wrapping multiple
// validation errors returned by
// ActivateInternationalPaymentsPayload.ValidateAll() if the designated
// constraints aren't met.
type ActivateInternationalPaymentsPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivateInternationalPaymentsPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivateInternationalPaymentsPayloadMultiError) AllErrors() []error { return m }

// ActivateInternationalPaymentsPayloadValidationError is the validation error
// returned by ActivateInternationalPaymentsPayload.Validate if the designated
// constraints aren't met.
type ActivateInternationalPaymentsPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivateInternationalPaymentsPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivateInternationalPaymentsPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivateInternationalPaymentsPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivateInternationalPaymentsPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivateInternationalPaymentsPayloadValidationError) ErrorName() string {
	return "ActivateInternationalPaymentsPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e ActivateInternationalPaymentsPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivateInternationalPaymentsPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivateInternationalPaymentsPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivateInternationalPaymentsPayloadValidationError{}

// Validate checks the field values on DeactivateInternationalPaymentsPayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DeactivateInternationalPaymentsPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DeactivateInternationalPaymentsPayload with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DeactivateInternationalPaymentsPayloadMultiError, or nil if none found.
func (m *DeactivateInternationalPaymentsPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateInternationalPaymentsPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeactivateInternationalPaymentsPayloadMultiError(errors)
	}

	return nil
}

// DeactivateInternationalPaymentsPayloadMultiError is an error wrapping
// multiple validation errors returned by
// DeactivateInternationalPaymentsPayload.ValidateAll() if the designated
// constraints aren't met.
type DeactivateInternationalPaymentsPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateInternationalPaymentsPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateInternationalPaymentsPayloadMultiError) AllErrors() []error { return m }

// DeactivateInternationalPaymentsPayloadValidationError is the validation
// error returned by DeactivateInternationalPaymentsPayload.Validate if the
// designated constraints aren't met.
type DeactivateInternationalPaymentsPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateInternationalPaymentsPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateInternationalPaymentsPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateInternationalPaymentsPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateInternationalPaymentsPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateInternationalPaymentsPayloadValidationError) ErrorName() string {
	return "DeactivateInternationalPaymentsPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateInternationalPaymentsPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateInternationalPaymentsPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateInternationalPaymentsPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateInternationalPaymentsPayloadValidationError{}

// Validate checks the field values on ActivateUpiLitePayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActivateUpiLitePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivateUpiLitePayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActivateUpiLitePayloadMultiError, or nil if none found.
func (m *ActivateUpiLitePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivateUpiLitePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Lrn

	if all {
		switch v := interface{}(m.GetInitialTopUpAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivateUpiLitePayloadValidationError{
					field:  "InitialTopUpAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivateUpiLitePayloadValidationError{
					field:  "InitialTopUpAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInitialTopUpAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivateUpiLitePayloadValidationError{
				field:  "InitialTopUpAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LocationToken

	// no validation rules for AccountRefId

	// no validation rules for OrderId

	if len(errors) > 0 {
		return ActivateUpiLitePayloadMultiError(errors)
	}

	return nil
}

// ActivateUpiLitePayloadMultiError is an error wrapping multiple validation
// errors returned by ActivateUpiLitePayload.ValidateAll() if the designated
// constraints aren't met.
type ActivateUpiLitePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivateUpiLitePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivateUpiLitePayloadMultiError) AllErrors() []error { return m }

// ActivateUpiLitePayloadValidationError is the validation error returned by
// ActivateUpiLitePayload.Validate if the designated constraints aren't met.
type ActivateUpiLitePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivateUpiLitePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivateUpiLitePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivateUpiLitePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivateUpiLitePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivateUpiLitePayloadValidationError) ErrorName() string {
	return "ActivateUpiLitePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e ActivateUpiLitePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivateUpiLitePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivateUpiLitePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivateUpiLitePayloadValidationError{}

// Validate checks the field values on DeactivateZeroBalanceUpiLitePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeactivateZeroBalanceUpiLitePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateZeroBalanceUpiLitePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeactivateZeroBalanceUpiLitePayloadMultiError, or nil if none found.
func (m *DeactivateZeroBalanceUpiLitePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateZeroBalanceUpiLitePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Lrn

	// no validation rules for AccountRefId

	if len(errors) > 0 {
		return DeactivateZeroBalanceUpiLitePayloadMultiError(errors)
	}

	return nil
}

// DeactivateZeroBalanceUpiLitePayloadMultiError is an error wrapping multiple
// validation errors returned by
// DeactivateZeroBalanceUpiLitePayload.ValidateAll() if the designated
// constraints aren't met.
type DeactivateZeroBalanceUpiLitePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateZeroBalanceUpiLitePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateZeroBalanceUpiLitePayloadMultiError) AllErrors() []error { return m }

// DeactivateZeroBalanceUpiLitePayloadValidationError is the validation error
// returned by DeactivateZeroBalanceUpiLitePayload.Validate if the designated
// constraints aren't met.
type DeactivateZeroBalanceUpiLitePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateZeroBalanceUpiLitePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateZeroBalanceUpiLitePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateZeroBalanceUpiLitePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateZeroBalanceUpiLitePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateZeroBalanceUpiLitePayloadValidationError) ErrorName() string {
	return "DeactivateZeroBalanceUpiLitePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateZeroBalanceUpiLitePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateZeroBalanceUpiLitePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateZeroBalanceUpiLitePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateZeroBalanceUpiLitePayloadValidationError{}

// Validate checks the field values on DeactivateUpiLitePayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivateUpiLitePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivateUpiLitePayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivateUpiLitePayloadMultiError, or nil if none found.
func (m *DeactivateUpiLitePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivateUpiLitePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Lrn

	if all {
		switch v := interface{}(m.GetUpiLiteBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivateUpiLitePayloadValidationError{
					field:  "UpiLiteBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivateUpiLitePayloadValidationError{
					field:  "UpiLiteBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpiLiteBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivateUpiLitePayloadValidationError{
				field:  "UpiLiteBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountRefId

	// no validation rules for LocationToken

	// no validation rules for OrderId

	if len(errors) > 0 {
		return DeactivateUpiLitePayloadMultiError(errors)
	}

	return nil
}

// DeactivateUpiLitePayloadMultiError is an error wrapping multiple validation
// errors returned by DeactivateUpiLitePayload.ValidateAll() if the designated
// constraints aren't met.
type DeactivateUpiLitePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivateUpiLitePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivateUpiLitePayloadMultiError) AllErrors() []error { return m }

// DeactivateUpiLitePayloadValidationError is the validation error returned by
// DeactivateUpiLitePayload.Validate if the designated constraints aren't met.
type DeactivateUpiLitePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivateUpiLitePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivateUpiLitePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivateUpiLitePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivateUpiLitePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivateUpiLitePayloadValidationError) ErrorName() string {
	return "DeactivateUpiLitePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivateUpiLitePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivateUpiLitePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivateUpiLitePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivateUpiLitePayloadValidationError{}
