package onboarding

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (o *UpiOnboardingDetailsPayload) Value() (driver.Value, error) {
	return json.Marshal(o)
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (o *UpiOnboardingDetailsPayload) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	err := json.Unmarshal(marshalledData, &o)
	if err != nil {
		return err
	}
	return nil
}
