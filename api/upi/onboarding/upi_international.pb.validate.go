// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/onboarding/upi_international.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.UpiPinSetStatus(0)
)

// Validate checks the field values on
// ActivateAccountsForInternationalUpiPaymentsInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActivateAccountsForInternationalUpiPaymentsInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ActivateAccountsForInternationalUpiPaymentsInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ActivateAccountsForInternationalUpiPaymentsInfoMultiError, or nil if none found.
func (m *ActivateAccountsForInternationalUpiPaymentsInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivateAccountsForInternationalUpiPaymentsInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEligibleAccountInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivateAccountsForInternationalUpiPaymentsInfoValidationError{
						field:  fmt.Sprintf("EligibleAccountInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivateAccountsForInternationalUpiPaymentsInfoValidationError{
						field:  fmt.Sprintf("EligibleAccountInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivateAccountsForInternationalUpiPaymentsInfoValidationError{
					field:  fmt.Sprintf("EligibleAccountInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ActivateAccountsForInternationalUpiPaymentsInfoMultiError(errors)
	}

	return nil
}

// ActivateAccountsForInternationalUpiPaymentsInfoMultiError is an error
// wrapping multiple validation errors returned by
// ActivateAccountsForInternationalUpiPaymentsInfo.ValidateAll() if the
// designated constraints aren't met.
type ActivateAccountsForInternationalUpiPaymentsInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivateAccountsForInternationalUpiPaymentsInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivateAccountsForInternationalUpiPaymentsInfoMultiError) AllErrors() []error { return m }

// ActivateAccountsForInternationalUpiPaymentsInfoValidationError is the
// validation error returned by
// ActivateAccountsForInternationalUpiPaymentsInfo.Validate if the designated
// constraints aren't met.
type ActivateAccountsForInternationalUpiPaymentsInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivateAccountsForInternationalUpiPaymentsInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ActivateAccountsForInternationalUpiPaymentsInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ActivateAccountsForInternationalUpiPaymentsInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivateAccountsForInternationalUpiPaymentsInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivateAccountsForInternationalUpiPaymentsInfoValidationError) ErrorName() string {
	return "ActivateAccountsForInternationalUpiPaymentsInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ActivateAccountsForInternationalUpiPaymentsInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivateAccountsForInternationalUpiPaymentsInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivateAccountsForInternationalUpiPaymentsInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivateAccountsForInternationalUpiPaymentsInfoValidationError{}

// Validate checks the field values on EligibleAccountInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EligibleAccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EligibleAccountInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EligibleAccountInfoMultiError, or nil if none found.
func (m *EligibleAccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EligibleAccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaskedAccountNumber

	if all {
		switch v := interface{}(m.GetBankInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EligibleAccountInfoValidationError{
					field:  "BankInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EligibleAccountInfoValidationError{
					field:  "BankInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EligibleAccountInfoValidationError{
				field:  "BankInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TpapAccountId

	// no validation rules for UpiPinSetStatus

	if len(errors) > 0 {
		return EligibleAccountInfoMultiError(errors)
	}

	return nil
}

// EligibleAccountInfoMultiError is an error wrapping multiple validation
// errors returned by EligibleAccountInfo.ValidateAll() if the designated
// constraints aren't met.
type EligibleAccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EligibleAccountInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EligibleAccountInfoMultiError) AllErrors() []error { return m }

// EligibleAccountInfoValidationError is the validation error returned by
// EligibleAccountInfo.Validate if the designated constraints aren't met.
type EligibleAccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EligibleAccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EligibleAccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EligibleAccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EligibleAccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EligibleAccountInfoValidationError) ErrorName() string {
	return "EligibleAccountInfoValidationError"
}

// Error satisfies the builtin error interface
func (e EligibleAccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEligibleAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EligibleAccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EligibleAccountInfoValidationError{}

// Validate checks the field values on AmountScreenForInternationalPaymentsInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AmountScreenForInternationalPaymentsInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AmountScreenForInternationalPaymentsInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// AmountScreenForInternationalPaymentsInfoMultiError, or nil if none found.
func (m *AmountScreenForInternationalPaymentsInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AmountScreenForInternationalPaymentsInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetForexInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AmountScreenForInternationalPaymentsInfoValidationError{
						field:  fmt.Sprintf("ForexInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AmountScreenForInternationalPaymentsInfoValidationError{
						field:  fmt.Sprintf("ForexInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AmountScreenForInternationalPaymentsInfoValidationError{
					field:  fmt.Sprintf("ForexInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AmountScreenForInternationalPaymentsInfoMultiError(errors)
	}

	return nil
}

// AmountScreenForInternationalPaymentsInfoMultiError is an error wrapping
// multiple validation errors returned by
// AmountScreenForInternationalPaymentsInfo.ValidateAll() if the designated
// constraints aren't met.
type AmountScreenForInternationalPaymentsInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AmountScreenForInternationalPaymentsInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AmountScreenForInternationalPaymentsInfoMultiError) AllErrors() []error { return m }

// AmountScreenForInternationalPaymentsInfoValidationError is the validation
// error returned by AmountScreenForInternationalPaymentsInfo.Validate if the
// designated constraints aren't met.
type AmountScreenForInternationalPaymentsInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AmountScreenForInternationalPaymentsInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AmountScreenForInternationalPaymentsInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AmountScreenForInternationalPaymentsInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AmountScreenForInternationalPaymentsInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AmountScreenForInternationalPaymentsInfoValidationError) ErrorName() string {
	return "AmountScreenForInternationalPaymentsInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AmountScreenForInternationalPaymentsInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAmountScreenForInternationalPaymentsInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AmountScreenForInternationalPaymentsInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AmountScreenForInternationalPaymentsInfoValidationError{}

// Validate checks the field values on ForexInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ForexInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForexInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ForexInfoMultiError, or nil
// if none found.
func (m *ForexInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ForexInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConversionRate

	// no validation rules for MarkUp

	// no validation rules for CurrencyCode

	if len(errors) > 0 {
		return ForexInfoMultiError(errors)
	}

	return nil
}

// ForexInfoMultiError is an error wrapping multiple validation errors returned
// by ForexInfo.ValidateAll() if the designated constraints aren't met.
type ForexInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForexInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForexInfoMultiError) AllErrors() []error { return m }

// ForexInfoValidationError is the validation error returned by
// ForexInfo.Validate if the designated constraints aren't met.
type ForexInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForexInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForexInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForexInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForexInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForexInfoValidationError) ErrorName() string { return "ForexInfoValidationError" }

// Error satisfies the builtin error interface
func (e ForexInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForexInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForexInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForexInfoValidationError{}
