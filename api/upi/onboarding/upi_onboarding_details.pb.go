// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/upi_onboarding_details.proto

package onboarding

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UpiOnboardingDetailFieldMask is the enum representation of all the UpiOnboardingDetail fields.
// Meant to be used as field mask to help with database updates
type UpiOnboardingDetailFieldMask int32

const (
	UpiOnboardingDetailFieldMask_UPI_ONBOARDING_DETAIL_FIELD_MASK_UNSPECIFIED       UpiOnboardingDetailFieldMask = 0
	UpiOnboardingDetailFieldMask_UPI_ONBOARDING_DETAIL_FIELD_MASK_VPA               UpiOnboardingDetailFieldMask = 1
	UpiOnboardingDetailFieldMask_UPI_ONBOARDING_DETAIL_FIELD_MASK_ACTION            UpiOnboardingDetailFieldMask = 2
	UpiOnboardingDetailFieldMask_UPI_ONBOARDING_DETAIL_FIELD_MASK_STATUS            UpiOnboardingDetailFieldMask = 3
	UpiOnboardingDetailFieldMask_UPI_ONBOARDING_DETAIL_FIELD_MASK_VENDOR_REQUEST_ID UpiOnboardingDetailFieldMask = 4
	UpiOnboardingDetailFieldMask_UPI_ONBOARDING_DETAIL_FIELD_MASK_PAYLOAD           UpiOnboardingDetailFieldMask = 5
)

// Enum value maps for UpiOnboardingDetailFieldMask.
var (
	UpiOnboardingDetailFieldMask_name = map[int32]string{
		0: "UPI_ONBOARDING_DETAIL_FIELD_MASK_UNSPECIFIED",
		1: "UPI_ONBOARDING_DETAIL_FIELD_MASK_VPA",
		2: "UPI_ONBOARDING_DETAIL_FIELD_MASK_ACTION",
		3: "UPI_ONBOARDING_DETAIL_FIELD_MASK_STATUS",
		4: "UPI_ONBOARDING_DETAIL_FIELD_MASK_VENDOR_REQUEST_ID",
		5: "UPI_ONBOARDING_DETAIL_FIELD_MASK_PAYLOAD",
	}
	UpiOnboardingDetailFieldMask_value = map[string]int32{
		"UPI_ONBOARDING_DETAIL_FIELD_MASK_UNSPECIFIED":       0,
		"UPI_ONBOARDING_DETAIL_FIELD_MASK_VPA":               1,
		"UPI_ONBOARDING_DETAIL_FIELD_MASK_ACTION":            2,
		"UPI_ONBOARDING_DETAIL_FIELD_MASK_STATUS":            3,
		"UPI_ONBOARDING_DETAIL_FIELD_MASK_VENDOR_REQUEST_ID": 4,
		"UPI_ONBOARDING_DETAIL_FIELD_MASK_PAYLOAD":           5,
	}
)

func (x UpiOnboardingDetailFieldMask) Enum() *UpiOnboardingDetailFieldMask {
	p := new(UpiOnboardingDetailFieldMask)
	*p = x
	return p
}

func (x UpiOnboardingDetailFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiOnboardingDetailFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_upi_onboarding_details_proto_enumTypes[0].Descriptor()
}

func (UpiOnboardingDetailFieldMask) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_upi_onboarding_details_proto_enumTypes[0]
}

func (x UpiOnboardingDetailFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiOnboardingDetailFieldMask.Descriptor instead.
func (UpiOnboardingDetailFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{0}
}

// details related to onboarding an upi account
type UpiOnboardingDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for each row
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// account id of the upi account
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Upi onboarding partner bank
	Vendor vendorgateway.Vendor `protobuf:"varint,3,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// Vpa to be linked with account
	Vpa string `protobuf:"bytes,4,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// request id to be sent by caller in case idempotency around
	ClientReqId string `protobuf:"bytes,5,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// type of the action taken - LINKING/DELINKING
	Action enums.UpiOnboardingAction `protobuf:"varint,6,opt,name=action,proto3,enum=upi.onboarding.enums.UpiOnboardingAction" json:"action,omitempty"`
	// Current status of the account onboarding.
	// CREATED -> INITIATED -> LINKED/FAILED -> DELINKED_INITIATED -> DELINKED/MANUAL_INTERVNETION
	// If the user drops in between entry will be marked as INVALID
	Status enums.UpiOnboardingStatus `protobuf:"varint,7,opt,name=status,proto3,enum=upi.onboarding.enums.UpiOnboardingStatus" json:"status,omitempty"`
	// Req id for the request initiated with vendor
	VendorReqId string `protobuf:"bytes,8,opt,name=vendor_req_id,json=vendorReqId,proto3" json:"vendor_req_id,omitempty"`
	// time of creation
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// time of deletion
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// payload contains details for upi onboarding action
	Payload *UpiOnboardingDetailsPayload `protobuf:"bytes,12,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *UpiOnboardingDetail) Reset() {
	*x = UpiOnboardingDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiOnboardingDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiOnboardingDetail) ProtoMessage() {}

func (x *UpiOnboardingDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiOnboardingDetail.ProtoReflect.Descriptor instead.
func (*UpiOnboardingDetail) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{0}
}

func (x *UpiOnboardingDetail) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpiOnboardingDetail) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *UpiOnboardingDetail) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *UpiOnboardingDetail) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *UpiOnboardingDetail) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *UpiOnboardingDetail) GetAction() enums.UpiOnboardingAction {
	if x != nil {
		return x.Action
	}
	return enums.UpiOnboardingAction(0)
}

func (x *UpiOnboardingDetail) GetStatus() enums.UpiOnboardingStatus {
	if x != nil {
		return x.Status
	}
	return enums.UpiOnboardingStatus(0)
}

func (x *UpiOnboardingDetail) GetVendorReqId() string {
	if x != nil {
		return x.VendorReqId
	}
	return ""
}

func (x *UpiOnboardingDetail) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UpiOnboardingDetail) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UpiOnboardingDetail) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *UpiOnboardingDetail) GetPayload() *UpiOnboardingDetailsPayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type UpiOnboardingDetailsPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// details related to upi number linking
	UpiNumberLinkingDetails *UpiNumberLinkingDetailsPayload `protobuf:"bytes,1,opt,name=upi_number_linking_details,json=upiNumberLinkingDetails,proto3" json:"upi_number_linking_details,omitempty"`
	// details related to upi number delinking
	UpiNumberDelinkingDetails *UpiNumberDelinkingDetailsPayload `protobuf:"bytes,2,opt,name=upi_number_delinking_details,json=upiNumberDelinkingDetails,proto3" json:"upi_number_delinking_details,omitempty"`
	// details related to international upi payments activation
	ActivateInternationalPaymentsPayload *ActivateInternationalPaymentsPayload `protobuf:"bytes,3,opt,name=activate_international_payments_payload,json=activateInternationalPaymentsPayload,proto3" json:"activate_international_payments_payload,omitempty"`
	// details related to international upi payments deactivation
	DeactivateInternationalPaymentsPayload *DeactivateInternationalPaymentsPayload `protobuf:"bytes,4,opt,name=deactivate_international_payments_payload,json=deactivateInternationalPaymentsPayload,proto3" json:"deactivate_international_payments_payload,omitempty"`
	// details related to activation of upi lite account
	ActivateUpiLitePayload *ActivateUpiLitePayload `protobuf:"bytes,5,opt,name=activate_upi_lite_payload,json=activateUpiLitePayload,proto3" json:"activate_upi_lite_payload,omitempty"`
	// details related to deactivation of upi lite account (with balance > 0)
	DeactivateUpiLitePayload *DeactivateUpiLitePayload `protobuf:"bytes,6,opt,name=deactivate_upi_lite_payload,json=deactivateUpiLitePayload,proto3" json:"deactivate_upi_lite_payload,omitempty"`
	// details related to deactivation of upi lite account  (with balance = 0)
	DeactivateZeroBalanceUpiLitePayload *DeactivateZeroBalanceUpiLitePayload `protobuf:"bytes,7,opt,name=deactivate_zero_balance_upi_lite_payload,json=deactivateZeroBalanceUpiLitePayload,proto3" json:"deactivate_zero_balance_upi_lite_payload,omitempty"`
}

func (x *UpiOnboardingDetailsPayload) Reset() {
	*x = UpiOnboardingDetailsPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiOnboardingDetailsPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiOnboardingDetailsPayload) ProtoMessage() {}

func (x *UpiOnboardingDetailsPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiOnboardingDetailsPayload.ProtoReflect.Descriptor instead.
func (*UpiOnboardingDetailsPayload) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{1}
}

func (x *UpiOnboardingDetailsPayload) GetUpiNumberLinkingDetails() *UpiNumberLinkingDetailsPayload {
	if x != nil {
		return x.UpiNumberLinkingDetails
	}
	return nil
}

func (x *UpiOnboardingDetailsPayload) GetUpiNumberDelinkingDetails() *UpiNumberDelinkingDetailsPayload {
	if x != nil {
		return x.UpiNumberDelinkingDetails
	}
	return nil
}

func (x *UpiOnboardingDetailsPayload) GetActivateInternationalPaymentsPayload() *ActivateInternationalPaymentsPayload {
	if x != nil {
		return x.ActivateInternationalPaymentsPayload
	}
	return nil
}

func (x *UpiOnboardingDetailsPayload) GetDeactivateInternationalPaymentsPayload() *DeactivateInternationalPaymentsPayload {
	if x != nil {
		return x.DeactivateInternationalPaymentsPayload
	}
	return nil
}

func (x *UpiOnboardingDetailsPayload) GetActivateUpiLitePayload() *ActivateUpiLitePayload {
	if x != nil {
		return x.ActivateUpiLitePayload
	}
	return nil
}

func (x *UpiOnboardingDetailsPayload) GetDeactivateUpiLitePayload() *DeactivateUpiLitePayload {
	if x != nil {
		return x.DeactivateUpiLitePayload
	}
	return nil
}

func (x *UpiOnboardingDetailsPayload) GetDeactivateZeroBalanceUpiLitePayload() *DeactivateZeroBalanceUpiLitePayload {
	if x != nil {
		return x.DeactivateZeroBalanceUpiLitePayload
	}
	return nil
}

// UpiNumberLinkingDetailsPayload contains upi number linking related details
type UpiNumberLinkingDetailsPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vpa to which the upi number needs to be linked
	Vpa string `protobuf:"bytes,1,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// denotes if the upi number is mobile number or numeric id
	UpiNumberType enums.UpiNumberType `protobuf:"varint,2,opt,name=upi_number_type,json=upiNumberType,proto3,enum=upi.onboarding.enums.UpiNumberType" json:"upi_number_type,omitempty"`
	// denotes if it is a new linking or porting from other psps
	LinkingType enums.UpiNumberLinkingType `protobuf:"varint,3,opt,name=linking_type,json=linkingType,proto3,enum=upi.onboarding.enums.UpiNumberLinkingType" json:"linking_type,omitempty"`
	// in case the linking is of type port, this field will store the previous vpa associated with the upi number
	PreVpa string `protobuf:"bytes,4,opt,name=pre_vpa,json=preVpa,proto3" json:"pre_vpa,omitempty"`
}

func (x *UpiNumberLinkingDetailsPayload) Reset() {
	*x = UpiNumberLinkingDetailsPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiNumberLinkingDetailsPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiNumberLinkingDetailsPayload) ProtoMessage() {}

func (x *UpiNumberLinkingDetailsPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiNumberLinkingDetailsPayload.ProtoReflect.Descriptor instead.
func (*UpiNumberLinkingDetailsPayload) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{2}
}

func (x *UpiNumberLinkingDetailsPayload) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *UpiNumberLinkingDetailsPayload) GetUpiNumberType() enums.UpiNumberType {
	if x != nil {
		return x.UpiNumberType
	}
	return enums.UpiNumberType(0)
}

func (x *UpiNumberLinkingDetailsPayload) GetLinkingType() enums.UpiNumberLinkingType {
	if x != nil {
		return x.LinkingType
	}
	return enums.UpiNumberLinkingType(0)
}

func (x *UpiNumberLinkingDetailsPayload) GetPreVpa() string {
	if x != nil {
		return x.PreVpa
	}
	return ""
}

// UpiNumberLinkingDetailsPayload contains upi number delinking related details
type UpiNumberDelinkingDetailsPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vpa to which the upi number needs to be delinked
	Vpa string `protobuf:"bytes,1,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// denotes if the upi number is mobile number or numeric id
	UpiNumberType enums.UpiNumberType `protobuf:"varint,2,opt,name=upi_number_type,json=upiNumberType,proto3,enum=upi.onboarding.enums.UpiNumberType" json:"upi_number_type,omitempty"`
}

func (x *UpiNumberDelinkingDetailsPayload) Reset() {
	*x = UpiNumberDelinkingDetailsPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiNumberDelinkingDetailsPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiNumberDelinkingDetailsPayload) ProtoMessage() {}

func (x *UpiNumberDelinkingDetailsPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiNumberDelinkingDetailsPayload.ProtoReflect.Descriptor instead.
func (*UpiNumberDelinkingDetailsPayload) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{3}
}

func (x *UpiNumberDelinkingDetailsPayload) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *UpiNumberDelinkingDetailsPayload) GetUpiNumberType() enums.UpiNumberType {
	if x != nil {
		return x.UpiNumberType
	}
	return enums.UpiNumberType(0)
}

// ActivateInternationalPaymentsPayload - to used to pass all required parameters
// to initiate international payments activation workflow
type ActivateInternationalPaymentsPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// represents activation time for international payments
	InternationalPaymentsActivatedAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=international_payments_activated_at,json=internationalPaymentsActivatedAt,proto3" json:"international_payments_activated_at,omitempty"`
	// represents deactivation time for international payments
	InternationalPaymentsExpiresAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=international_payments_expires_at,json=internationalPaymentsExpiresAt,proto3" json:"international_payments_expires_at,omitempty"`
}

func (x *ActivateInternationalPaymentsPayload) Reset() {
	*x = ActivateInternationalPaymentsPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivateInternationalPaymentsPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateInternationalPaymentsPayload) ProtoMessage() {}

func (x *ActivateInternationalPaymentsPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateInternationalPaymentsPayload.ProtoReflect.Descriptor instead.
func (*ActivateInternationalPaymentsPayload) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{4}
}

func (x *ActivateInternationalPaymentsPayload) GetInternationalPaymentsActivatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InternationalPaymentsActivatedAt
	}
	return nil
}

func (x *ActivateInternationalPaymentsPayload) GetInternationalPaymentsExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.InternationalPaymentsExpiresAt
	}
	return nil
}

// DeactivateInternationalPaymentsPayload - to used to pass all required parameters
// to initiate international payments deactivation workflow
type DeactivateInternationalPaymentsPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeactivateInternationalPaymentsPayload) Reset() {
	*x = DeactivateInternationalPaymentsPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeactivateInternationalPaymentsPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateInternationalPaymentsPayload) ProtoMessage() {}

func (x *DeactivateInternationalPaymentsPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateInternationalPaymentsPayload.ProtoReflect.Descriptor instead.
func (*DeactivateInternationalPaymentsPayload) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{5}
}

// ActivateUpiLitePayload - used to pass all the
// required data in activation of upi lite
// for an upi account
type ActivateUpiLitePayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lrn (Lite Reference Number) - used to identify
	// a upi lite account and PI uniquely
	Lrn string `protobuf:"bytes,1,opt,name=lrn,proto3" json:"lrn,omitempty"`
	// initial_top_up_amount - amount that user wants to add as an initial top up
	InitialTopUpAmount *money.Money `protobuf:"bytes,2,opt,name=initial_top_up_amount,json=initialTopUpAmount,proto3" json:"initial_top_up_amount,omitempty"`
	// location token of the user (required while creation order for amount transfer)
	LocationToken string `protobuf:"bytes,3,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
	// account id of the source upi account
	AccountRefId string `protobuf:"bytes,4,opt,name=account_ref_id,json=accountRefId,proto3" json:"account_ref_id,omitempty"`
	// order id of the payment transfer to be done, populated after order creation
	OrderId string `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *ActivateUpiLitePayload) Reset() {
	*x = ActivateUpiLitePayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivateUpiLitePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateUpiLitePayload) ProtoMessage() {}

func (x *ActivateUpiLitePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateUpiLitePayload.ProtoReflect.Descriptor instead.
func (*ActivateUpiLitePayload) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{6}
}

func (x *ActivateUpiLitePayload) GetLrn() string {
	if x != nil {
		return x.Lrn
	}
	return ""
}

func (x *ActivateUpiLitePayload) GetInitialTopUpAmount() *money.Money {
	if x != nil {
		return x.InitialTopUpAmount
	}
	return nil
}

func (x *ActivateUpiLitePayload) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

func (x *ActivateUpiLitePayload) GetAccountRefId() string {
	if x != nil {
		return x.AccountRefId
	}
	return ""
}

func (x *ActivateUpiLitePayload) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

// DeactivateZeroBalanceUpiLitePayload - used to pass all the
// required data in deactivation of zero balance upi lite account
type DeactivateZeroBalanceUpiLitePayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lrn (Lite Reference Number) - used to identify
	// a upi lite account and PI uniquely
	Lrn string `protobuf:"bytes,1,opt,name=lrn,proto3" json:"lrn,omitempty"`
	// account id of the source upi account
	AccountRefId string `protobuf:"bytes,4,opt,name=account_ref_id,json=accountRefId,proto3" json:"account_ref_id,omitempty"`
}

func (x *DeactivateZeroBalanceUpiLitePayload) Reset() {
	*x = DeactivateZeroBalanceUpiLitePayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeactivateZeroBalanceUpiLitePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateZeroBalanceUpiLitePayload) ProtoMessage() {}

func (x *DeactivateZeroBalanceUpiLitePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateZeroBalanceUpiLitePayload.ProtoReflect.Descriptor instead.
func (*DeactivateZeroBalanceUpiLitePayload) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{7}
}

func (x *DeactivateZeroBalanceUpiLitePayload) GetLrn() string {
	if x != nil {
		return x.Lrn
	}
	return ""
}

func (x *DeactivateZeroBalanceUpiLitePayload) GetAccountRefId() string {
	if x != nil {
		return x.AccountRefId
	}
	return ""
}

// DeactivateUpiLitePayload - used to pass all the
// required data in deactivation of upi lite account
// (with balance > 0)
type DeactivateUpiLitePayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lrn (Lite Reference Number) - used to identify
	// a upi lite account and PI uniquely
	Lrn string `protobuf:"bytes,1,opt,name=lrn,proto3" json:"lrn,omitempty"`
	// upi lite balance of user
	UpiLiteBalance *money.Money `protobuf:"bytes,2,opt,name=upi_lite_balance,json=upiLiteBalance,proto3" json:"upi_lite_balance,omitempty"`
	// account id of the source upi account
	AccountRefId string `protobuf:"bytes,3,opt,name=account_ref_id,json=accountRefId,proto3" json:"account_ref_id,omitempty"`
	// location token of the user (required while creation order for amount transfer)
	LocationToken string `protobuf:"bytes,4,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
	// order id of the payment transfer to be done, populated after order creation
	OrderId string `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *DeactivateUpiLitePayload) Reset() {
	*x = DeactivateUpiLitePayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeactivateUpiLitePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateUpiLitePayload) ProtoMessage() {}

func (x *DeactivateUpiLitePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateUpiLitePayload.ProtoReflect.Descriptor instead.
func (*DeactivateUpiLitePayload) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP(), []int{8}
}

func (x *DeactivateUpiLitePayload) GetLrn() string {
	if x != nil {
		return x.Lrn
	}
	return ""
}

func (x *DeactivateUpiLitePayload) GetUpiLiteBalance() *money.Money {
	if x != nil {
		return x.UpiLiteBalance
	}
	return nil
}

func (x *DeactivateUpiLitePayload) GetAccountRefId() string {
	if x != nil {
		return x.AccountRefId
	}
	return ""
}

func (x *DeactivateUpiLitePayload) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

func (x *DeactivateUpiLitePayload) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

var File_api_upi_onboarding_upi_onboarding_details_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_upi_onboarding_details_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f,
	0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xcb, 0x04, 0x0a, 0x13, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x22, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x45, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0xf8, 0x06,
	0x0a, 0x1b, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x6b, 0x0a,
	0x1a, 0x75, 0x70, 0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x17, 0x75, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x71, 0x0a, 0x1c, 0x75, 0x70,
	0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x52, 0x19, 0x75, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x65, 0x6c,
	0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x8b, 0x01,
	0x0a, 0x27, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x24, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x91, 0x01, 0x0a, 0x29,
	0x64, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x26, 0x64, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x61, 0x0a, 0x19, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x70, 0x69, 0x5f,
	0x6c, 0x69, 0x74, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4c,
	0x69, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x16, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x12, 0x67, 0x0a, 0x1b, 0x64, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x5f, 0x75, 0x70, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x18, 0x64, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69,
	0x4c, 0x69, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x8a, 0x01, 0x0a, 0x28,
	0x64, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x7a, 0x65, 0x72, 0x6f, 0x5f,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x75, 0x70, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65,
	0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5a, 0x65, 0x72, 0x6f, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x23, 0x64, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5a,
	0x65, 0x72, 0x6f, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74,
	0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0xe7, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x69,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76,
	0x70, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x4b, 0x0a,
	0x0f, 0x75, 0x70, 0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70,
	0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x75, 0x70, 0x69,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x6c, 0x69,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6c, 0x69,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x72, 0x65,
	0x5f, 0x76, 0x70, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x72, 0x65, 0x56,
	0x70, 0x61, 0x22, 0x81, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x4b, 0x0a, 0x0f, 0x75, 0x70, 0x69,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x75, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0xf8, 0x01, 0x0a, 0x24, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x69, 0x0a, 0x23, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x20, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x65, 0x0a, 0x21, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x1e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41,
	0x74, 0x22, 0x28, 0x0a, 0x26, 0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0xd9, 0x01, 0x0a, 0x16,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x72, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x72, 0x6e, 0x12, 0x45, 0x0a, 0x15, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x5f, 0x74, 0x6f, 0x70, 0x5f, 0x75, 0x70, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x12, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x6c, 0x54, 0x6f, 0x70, 0x55, 0x70, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x23, 0x44, 0x65, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x5a, 0x65, 0x72, 0x6f, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x72, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x72, 0x6e,
	0x12, 0x24, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x22, 0xd2, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x72, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6c, 0x72, 0x6e, 0x12, 0x3c, 0x0a, 0x10, 0x75, 0x70, 0x69, 0x5f, 0x6c, 0x69, 0x74,
	0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0e, 0x75, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x2a, 0xba, 0x02, 0x0a, 0x1c,
	0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x30, 0x0a, 0x2c,
	0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x28,
	0x0a, 0x24, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x56, 0x50, 0x41, 0x10, 0x01, 0x12, 0x2b, 0x0a, 0x27, 0x55, 0x50, 0x49, 0x5f,
	0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x2b, 0x0a, 0x27, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x10, 0x03, 0x12, 0x36, 0x0a, 0x32, 0x55, 0x50, 0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28, 0x55, 0x50,
	0x49, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50,
	0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x05, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_upi_onboarding_details_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_upi_onboarding_details_proto_rawDescData = file_api_upi_onboarding_upi_onboarding_details_proto_rawDesc
)

func file_api_upi_onboarding_upi_onboarding_details_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_upi_onboarding_details_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_upi_onboarding_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_upi_onboarding_details_proto_rawDescData)
	})
	return file_api_upi_onboarding_upi_onboarding_details_proto_rawDescData
}

var file_api_upi_onboarding_upi_onboarding_details_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_upi_onboarding_upi_onboarding_details_proto_goTypes = []interface{}{
	(UpiOnboardingDetailFieldMask)(0),              // 0: upi.onboarding.UpiOnboardingDetailFieldMask
	(*UpiOnboardingDetail)(nil),                    // 1: upi.onboarding.UpiOnboardingDetail
	(*UpiOnboardingDetailsPayload)(nil),            // 2: upi.onboarding.UpiOnboardingDetailsPayload
	(*UpiNumberLinkingDetailsPayload)(nil),         // 3: upi.onboarding.UpiNumberLinkingDetailsPayload
	(*UpiNumberDelinkingDetailsPayload)(nil),       // 4: upi.onboarding.UpiNumberDelinkingDetailsPayload
	(*ActivateInternationalPaymentsPayload)(nil),   // 5: upi.onboarding.ActivateInternationalPaymentsPayload
	(*DeactivateInternationalPaymentsPayload)(nil), // 6: upi.onboarding.DeactivateInternationalPaymentsPayload
	(*ActivateUpiLitePayload)(nil),                 // 7: upi.onboarding.ActivateUpiLitePayload
	(*DeactivateZeroBalanceUpiLitePayload)(nil),    // 8: upi.onboarding.DeactivateZeroBalanceUpiLitePayload
	(*DeactivateUpiLitePayload)(nil),               // 9: upi.onboarding.DeactivateUpiLitePayload
	(vendorgateway.Vendor)(0),                      // 10: vendorgateway.Vendor
	(enums.UpiOnboardingAction)(0),                 // 11: upi.onboarding.enums.UpiOnboardingAction
	(enums.UpiOnboardingStatus)(0),                 // 12: upi.onboarding.enums.UpiOnboardingStatus
	(*timestamppb.Timestamp)(nil),                  // 13: google.protobuf.Timestamp
	(enums.UpiNumberType)(0),                       // 14: upi.onboarding.enums.UpiNumberType
	(enums.UpiNumberLinkingType)(0),                // 15: upi.onboarding.enums.UpiNumberLinkingType
	(*money.Money)(nil),                            // 16: google.type.Money
}
var file_api_upi_onboarding_upi_onboarding_details_proto_depIdxs = []int32{
	10, // 0: upi.onboarding.UpiOnboardingDetail.vendor:type_name -> vendorgateway.Vendor
	11, // 1: upi.onboarding.UpiOnboardingDetail.action:type_name -> upi.onboarding.enums.UpiOnboardingAction
	12, // 2: upi.onboarding.UpiOnboardingDetail.status:type_name -> upi.onboarding.enums.UpiOnboardingStatus
	13, // 3: upi.onboarding.UpiOnboardingDetail.created_at:type_name -> google.protobuf.Timestamp
	13, // 4: upi.onboarding.UpiOnboardingDetail.updated_at:type_name -> google.protobuf.Timestamp
	13, // 5: upi.onboarding.UpiOnboardingDetail.deleted_at:type_name -> google.protobuf.Timestamp
	2,  // 6: upi.onboarding.UpiOnboardingDetail.payload:type_name -> upi.onboarding.UpiOnboardingDetailsPayload
	3,  // 7: upi.onboarding.UpiOnboardingDetailsPayload.upi_number_linking_details:type_name -> upi.onboarding.UpiNumberLinkingDetailsPayload
	4,  // 8: upi.onboarding.UpiOnboardingDetailsPayload.upi_number_delinking_details:type_name -> upi.onboarding.UpiNumberDelinkingDetailsPayload
	5,  // 9: upi.onboarding.UpiOnboardingDetailsPayload.activate_international_payments_payload:type_name -> upi.onboarding.ActivateInternationalPaymentsPayload
	6,  // 10: upi.onboarding.UpiOnboardingDetailsPayload.deactivate_international_payments_payload:type_name -> upi.onboarding.DeactivateInternationalPaymentsPayload
	7,  // 11: upi.onboarding.UpiOnboardingDetailsPayload.activate_upi_lite_payload:type_name -> upi.onboarding.ActivateUpiLitePayload
	9,  // 12: upi.onboarding.UpiOnboardingDetailsPayload.deactivate_upi_lite_payload:type_name -> upi.onboarding.DeactivateUpiLitePayload
	8,  // 13: upi.onboarding.UpiOnboardingDetailsPayload.deactivate_zero_balance_upi_lite_payload:type_name -> upi.onboarding.DeactivateZeroBalanceUpiLitePayload
	14, // 14: upi.onboarding.UpiNumberLinkingDetailsPayload.upi_number_type:type_name -> upi.onboarding.enums.UpiNumberType
	15, // 15: upi.onboarding.UpiNumberLinkingDetailsPayload.linking_type:type_name -> upi.onboarding.enums.UpiNumberLinkingType
	14, // 16: upi.onboarding.UpiNumberDelinkingDetailsPayload.upi_number_type:type_name -> upi.onboarding.enums.UpiNumberType
	13, // 17: upi.onboarding.ActivateInternationalPaymentsPayload.international_payments_activated_at:type_name -> google.protobuf.Timestamp
	13, // 18: upi.onboarding.ActivateInternationalPaymentsPayload.international_payments_expires_at:type_name -> google.protobuf.Timestamp
	16, // 19: upi.onboarding.ActivateUpiLitePayload.initial_top_up_amount:type_name -> google.type.Money
	16, // 20: upi.onboarding.DeactivateUpiLitePayload.upi_lite_balance:type_name -> google.type.Money
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_upi_onboarding_details_proto_init() }
func file_api_upi_onboarding_upi_onboarding_details_proto_init() {
	if File_api_upi_onboarding_upi_onboarding_details_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiOnboardingDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiOnboardingDetailsPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiNumberLinkingDetailsPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiNumberDelinkingDetailsPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivateInternationalPaymentsPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeactivateInternationalPaymentsPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivateUpiLitePayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeactivateZeroBalanceUpiLitePayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeactivateUpiLitePayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_upi_onboarding_details_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_upi_onboarding_details_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_upi_onboarding_details_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_upi_onboarding_details_proto_enumTypes,
		MessageInfos:      file_api_upi_onboarding_upi_onboarding_details_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_upi_onboarding_details_proto = out.File
	file_api_upi_onboarding_upi_onboarding_details_proto_rawDesc = nil
	file_api_upi_onboarding_upi_onboarding_details_proto_goTypes = nil
	file_api_upi_onboarding_upi_onboarding_details_proto_depIdxs = nil
}
