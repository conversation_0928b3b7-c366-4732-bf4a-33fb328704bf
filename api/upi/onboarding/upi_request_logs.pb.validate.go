// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/onboarding/upi_request_logs.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/upi/onboarding/enums"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.UpiRequestLogApiStatus(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on UpiRequestLog with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpiRequestLog) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiRequestLog with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpiRequestLogMultiError, or
// nil if none found.
func (m *UpiRequestLog) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiRequestLog) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for AccountId

	// no validation rules for Vendor

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetDetailedStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiRequestLogValidationError{
					field:  "DetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiRequestLogValidationError{
					field:  "DetailedStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailedStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiRequestLogValidationError{
				field:  "DetailedStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApiType

	// no validation rules for VendorReqId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiRequestLogValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiRequestLogValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiRequestLogValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiRequestLogValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiRequestLogValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiRequestLogValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiRequestLogValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiRequestLogValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiRequestLogValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApiSubType

	if len(errors) > 0 {
		return UpiRequestLogMultiError(errors)
	}

	return nil
}

// UpiRequestLogMultiError is an error wrapping multiple validation errors
// returned by UpiRequestLog.ValidateAll() if the designated constraints
// aren't met.
type UpiRequestLogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiRequestLogMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiRequestLogMultiError) AllErrors() []error { return m }

// UpiRequestLogValidationError is the validation error returned by
// UpiRequestLog.Validate if the designated constraints aren't met.
type UpiRequestLogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiRequestLogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiRequestLogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiRequestLogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiRequestLogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiRequestLogValidationError) ErrorName() string { return "UpiRequestLogValidationError" }

// Error satisfies the builtin error interface
func (e UpiRequestLogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiRequestLog.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiRequestLogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiRequestLogValidationError{}

// Validate checks the field values on DetailedStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DetailedStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailedStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DetailedStatusMultiError,
// or nil if none found.
func (m *DetailedStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailedStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDetailedStatusMetadata() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetailedStatusValidationError{
						field:  fmt.Sprintf("DetailedStatusMetadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetailedStatusValidationError{
						field:  fmt.Sprintf("DetailedStatusMetadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetailedStatusValidationError{
					field:  fmt.Sprintf("DetailedStatusMetadata[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DetailedStatusMultiError(errors)
	}

	return nil
}

// DetailedStatusMultiError is an error wrapping multiple validation errors
// returned by DetailedStatus.ValidateAll() if the designated constraints
// aren't met.
type DetailedStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailedStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailedStatusMultiError) AllErrors() []error { return m }

// DetailedStatusValidationError is the validation error returned by
// DetailedStatus.Validate if the designated constraints aren't met.
type DetailedStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailedStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailedStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailedStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailedStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailedStatusValidationError) ErrorName() string { return "DetailedStatusValidationError" }

// Error satisfies the builtin error interface
func (e DetailedStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailedStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailedStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailedStatusValidationError{}

// Validate checks the field values on DetailedStatusMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetailedStatusMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailedStatusMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetailedStatusMetadataMultiError, or nil if none found.
func (m *DetailedStatusMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailedStatusMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RawStatusCode

	// no validation rules for RawStatusDescription

	// no validation rules for Category

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetailedStatusMetadataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetailedStatusMetadataValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetailedStatusMetadataValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetailedStatusMetadataValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetailedStatusMetadataValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetailedStatusMetadataValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DetailedStatusMetadataMultiError(errors)
	}

	return nil
}

// DetailedStatusMetadataMultiError is an error wrapping multiple validation
// errors returned by DetailedStatusMetadata.ValidateAll() if the designated
// constraints aren't met.
type DetailedStatusMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailedStatusMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailedStatusMetadataMultiError) AllErrors() []error { return m }

// DetailedStatusMetadataValidationError is the validation error returned by
// DetailedStatusMetadata.Validate if the designated constraints aren't met.
type DetailedStatusMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailedStatusMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailedStatusMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailedStatusMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailedStatusMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailedStatusMetadataValidationError) ErrorName() string {
	return "DetailedStatusMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e DetailedStatusMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailedStatusMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailedStatusMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailedStatusMetadataValidationError{}
