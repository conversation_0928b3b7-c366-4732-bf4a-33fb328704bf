// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/upi_account.proto

package onboarding

import (
	accounts "github.com/epifi/gamma/api/accounts"
	account "github.com/epifi/gamma/api/typesv2/account"
	upi "github.com/epifi/gamma/api/upi"
	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpiAccountFieldMask int32

const (
	UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_UNSPECIFIED        UpiAccountFieldMask = 0
	UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_STATUS     UpiAccountFieldMask = 1
	UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PREFERENCE UpiAccountFieldMask = 2
	UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PIN_STATUS UpiAccountFieldMask = 3
	UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_UPI_CONTROLS_INFO  UpiAccountFieldMask = 4
	UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_META_INFO  UpiAccountFieldMask = 5
)

// Enum value maps for UpiAccountFieldMask.
var (
	UpiAccountFieldMask_name = map[int32]string{
		0: "UPI_ACCOUNT_FIELD_MASK_UNSPECIFIED",
		1: "UPI_ACCOUNT_FIELD_MASK_ACCOUNT_STATUS",
		2: "UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PREFERENCE",
		3: "UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PIN_STATUS",
		4: "UPI_ACCOUNT_FIELD_MASK_UPI_CONTROLS_INFO",
		5: "UPI_ACCOUNT_FIELD_MASK_ACCOUNT_META_INFO",
	}
	UpiAccountFieldMask_value = map[string]int32{
		"UPI_ACCOUNT_FIELD_MASK_UNSPECIFIED":        0,
		"UPI_ACCOUNT_FIELD_MASK_ACCOUNT_STATUS":     1,
		"UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PREFERENCE": 2,
		"UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PIN_STATUS": 3,
		"UPI_ACCOUNT_FIELD_MASK_UPI_CONTROLS_INFO":  4,
		"UPI_ACCOUNT_FIELD_MASK_ACCOUNT_META_INFO":  5,
	}
)

func (x UpiAccountFieldMask) Enum() *UpiAccountFieldMask {
	p := new(UpiAccountFieldMask)
	*p = x
	return p
}

func (x UpiAccountFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiAccountFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_onboarding_upi_account_proto_enumTypes[0].Descriptor()
}

func (UpiAccountFieldMask) Type() protoreflect.EnumType {
	return &file_api_upi_onboarding_upi_account_proto_enumTypes[0]
}

func (x UpiAccountFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiAccountFieldMask.Descriptor instead.
func (UpiAccountFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_account_proto_rawDescGZIP(), []int{0}
}

type UpiAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for upi accounts
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// id corresponding to the actor
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// encrypted account number returned from NPCI
	AccountRefNumber string `protobuf:"bytes,3,opt,name=account_ref_number,json=accountRefNumber,proto3" json:"account_ref_number,omitempty"`
	// masked account number
	MaskedAccountNumber string `protobuf:"bytes,4,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	// ifsc code for the account
	IfscCode string `protobuf:"bytes,5,opt,name=ifsc_code,json=ifscCode,proto3" json:"ifsc_code,omitempty"`
	// id corresponding to an account if it is referenced from an existing account. Eg- Fi Savings account
	AccountRefId string `protobuf:"bytes,6,opt,name=account_ref_id,json=accountRefId,proto3" json:"account_ref_id,omitempty"`
	// status of the upi account
	Status enums.UpiAccountStatus `protobuf:"varint,7,opt,name=status,proto3,enum=upi.onboarding.enums.UpiAccountStatus" json:"status,omitempty"`
	// account type whether savings/tpap
	AccountType accounts.Type `protobuf:"varint,8,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// pin set status of the upi account
	PinSetStatus enums.UpiPinSetStatus `protobuf:"varint,9,opt,name=pin_set_status,json=pinSetStatus,proto3,enum=upi.onboarding.enums.UpiPinSetStatus" json:"pin_set_status,omitempty"`
	// account preference, eg- primary
	// if primary,this will be the default account for all the payments
	AccountPreference enums.UpiAccountPreference `protobuf:"varint,10,opt,name=account_preference,json=accountPreference,proto3,enum=upi.onboarding.enums.UpiAccountPreference" json:"account_preference,omitempty"`
	// time of creation of upi account
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// last updated time of upi account
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// time of deletion of upi account
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// account_meta_info stores the details regarding the account received from NPCI
	// like the cred allowed type and length of the cred allowed
	AccountMetaInfo *upi.ControlJson `protobuf:"bytes,14,opt,name=account_meta_info,json=accountMetaInfo,proto3" json:"account_meta_info,omitempty"`
	// Bank Name corresponding to the upi account
	BankName string `protobuf:"bytes,15,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
	// upi_controls - represents the list of allowed payments types for a upi account
	UpiControls []enums.UpiControl `protobuf:"varint,16,rep,packed,name=upi_controls,json=upiControls,proto3,enum=upi.onboarding.enums.UpiControl" json:"upi_controls,omitempty"`
	// Account Product Offering associated with the AccountType.
	// This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
	//
	// For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
	Apo account.AccountProductOffering `protobuf:"varint,17,opt,name=apo,proto3,enum=api.typesv2.account.AccountProductOffering" json:"apo,omitempty"`
}

func (x *UpiAccount) Reset() {
	*x = UpiAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_account_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiAccount) ProtoMessage() {}

func (x *UpiAccount) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_account_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiAccount.ProtoReflect.Descriptor instead.
func (*UpiAccount) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_account_proto_rawDescGZIP(), []int{0}
}

func (x *UpiAccount) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpiAccount) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpiAccount) GetAccountRefNumber() string {
	if x != nil {
		return x.AccountRefNumber
	}
	return ""
}

func (x *UpiAccount) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

func (x *UpiAccount) GetIfscCode() string {
	if x != nil {
		return x.IfscCode
	}
	return ""
}

func (x *UpiAccount) GetAccountRefId() string {
	if x != nil {
		return x.AccountRefId
	}
	return ""
}

func (x *UpiAccount) GetStatus() enums.UpiAccountStatus {
	if x != nil {
		return x.Status
	}
	return enums.UpiAccountStatus(0)
}

func (x *UpiAccount) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *UpiAccount) GetPinSetStatus() enums.UpiPinSetStatus {
	if x != nil {
		return x.PinSetStatus
	}
	return enums.UpiPinSetStatus(0)
}

func (x *UpiAccount) GetAccountPreference() enums.UpiAccountPreference {
	if x != nil {
		return x.AccountPreference
	}
	return enums.UpiAccountPreference(0)
}

func (x *UpiAccount) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UpiAccount) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UpiAccount) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *UpiAccount) GetAccountMetaInfo() *upi.ControlJson {
	if x != nil {
		return x.AccountMetaInfo
	}
	return nil
}

func (x *UpiAccount) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *UpiAccount) GetUpiControls() []enums.UpiControl {
	if x != nil {
		return x.UpiControls
	}
	return nil
}

func (x *UpiAccount) GetApo() account.AccountProductOffering {
	if x != nil {
		return x.Apo
	}
	return account.AccountProductOffering(0)
}

var File_api_upi_onboarding_upi_account_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_upi_account_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70,
	0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x61, 0x70, 0x69, 0x2f,
	0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75,
	0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69,
	0x2f, 0x75, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x07, 0x0a, 0x0a,
	0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x66, 0x73, 0x63, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x66, 0x73, 0x63,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a,
	0x0e, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69,
	0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x70, 0x69,
	0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x12, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70,
	0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4a,
	0x73, 0x6f, 0x6e, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x43, 0x0a, 0x0c, 0x75, 0x70, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55,
	0x70, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x0b, 0x75, 0x70, 0x69, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x12, 0x3d, 0x0a, 0x03, 0x61, 0x70, 0x6f, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x52, 0x03, 0x61, 0x70, 0x6f, 0x2a, 0xa2, 0x02, 0x0a, 0x13, 0x55, 0x70, 0x69, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x26, 0x0a,
	0x22, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x01,
	0x12, 0x2d, 0x0a, 0x29, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x10, 0x02, 0x12,
	0x2d, 0x0a, 0x29, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x03, 0x12, 0x2c,
	0x0a, 0x28, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x52, 0x4f, 0x4c, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28,
	0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4d,
	0x45, 0x54, 0x41, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x05, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_upi_account_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_upi_account_proto_rawDescData = file_api_upi_onboarding_upi_account_proto_rawDesc
)

func file_api_upi_onboarding_upi_account_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_upi_account_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_upi_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_upi_account_proto_rawDescData)
	})
	return file_api_upi_onboarding_upi_account_proto_rawDescData
}

var file_api_upi_onboarding_upi_account_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_onboarding_upi_account_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_upi_onboarding_upi_account_proto_goTypes = []interface{}{
	(UpiAccountFieldMask)(0),            // 0: upi.onboarding.UpiAccountFieldMask
	(*UpiAccount)(nil),                  // 1: upi.onboarding.UpiAccount
	(enums.UpiAccountStatus)(0),         // 2: upi.onboarding.enums.UpiAccountStatus
	(accounts.Type)(0),                  // 3: accounts.Type
	(enums.UpiPinSetStatus)(0),          // 4: upi.onboarding.enums.UpiPinSetStatus
	(enums.UpiAccountPreference)(0),     // 5: upi.onboarding.enums.UpiAccountPreference
	(*timestamppb.Timestamp)(nil),       // 6: google.protobuf.Timestamp
	(*upi.ControlJson)(nil),             // 7: upi.ControlJson
	(enums.UpiControl)(0),               // 8: upi.onboarding.enums.UpiControl
	(account.AccountProductOffering)(0), // 9: api.typesv2.account.AccountProductOffering
}
var file_api_upi_onboarding_upi_account_proto_depIdxs = []int32{
	2,  // 0: upi.onboarding.UpiAccount.status:type_name -> upi.onboarding.enums.UpiAccountStatus
	3,  // 1: upi.onboarding.UpiAccount.account_type:type_name -> accounts.Type
	4,  // 2: upi.onboarding.UpiAccount.pin_set_status:type_name -> upi.onboarding.enums.UpiPinSetStatus
	5,  // 3: upi.onboarding.UpiAccount.account_preference:type_name -> upi.onboarding.enums.UpiAccountPreference
	6,  // 4: upi.onboarding.UpiAccount.created_at:type_name -> google.protobuf.Timestamp
	6,  // 5: upi.onboarding.UpiAccount.updated_at:type_name -> google.protobuf.Timestamp
	6,  // 6: upi.onboarding.UpiAccount.deleted_at:type_name -> google.protobuf.Timestamp
	7,  // 7: upi.onboarding.UpiAccount.account_meta_info:type_name -> upi.ControlJson
	8,  // 8: upi.onboarding.UpiAccount.upi_controls:type_name -> upi.onboarding.enums.UpiControl
	9,  // 9: upi.onboarding.UpiAccount.apo:type_name -> api.typesv2.account.AccountProductOffering
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_upi_account_proto_init() }
func file_api_upi_onboarding_upi_account_proto_init() {
	if File_api_upi_onboarding_upi_account_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_upi_account_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_upi_account_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_upi_account_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_upi_account_proto_depIdxs,
		EnumInfos:         file_api_upi_onboarding_upi_account_proto_enumTypes,
		MessageInfos:      file_api_upi_onboarding_upi_account_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_upi_account_proto = out.File
	file_api_upi_onboarding_upi_account_proto_rawDesc = nil
	file_api_upi_onboarding_upi_account_proto_goTypes = nil
	file_api_upi_onboarding_upi_account_proto_depIdxs = nil
}
