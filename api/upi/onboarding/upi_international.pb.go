// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/upi_international.proto

package onboarding

import (
	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ActivateAccountsForInternationalPaymentsInfo - represents
// the info required to give user account options
// which are eligible for international upi payments activation
type ActivateAccountsForInternationalUpiPaymentsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EligibleAccountInfos []*EligibleAccountInfo `protobuf:"bytes,1,rep,name=eligible_account_infos,json=eligibleAccountInfos,proto3" json:"eligible_account_infos,omitempty"`
}

func (x *ActivateAccountsForInternationalUpiPaymentsInfo) Reset() {
	*x = ActivateAccountsForInternationalUpiPaymentsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_international_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivateAccountsForInternationalUpiPaymentsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateAccountsForInternationalUpiPaymentsInfo) ProtoMessage() {}

func (x *ActivateAccountsForInternationalUpiPaymentsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_international_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateAccountsForInternationalUpiPaymentsInfo.ProtoReflect.Descriptor instead.
func (*ActivateAccountsForInternationalUpiPaymentsInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_international_proto_rawDescGZIP(), []int{0}
}

func (x *ActivateAccountsForInternationalUpiPaymentsInfo) GetEligibleAccountInfos() []*EligibleAccountInfo {
	if x != nil {
		return x.EligibleAccountInfos
	}
	return nil
}

// EligibleAccountInfo - required info to initiate international
// payments activation for an account
type EligibleAccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaskedAccountNumber string                `protobuf:"bytes,1,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	BankInfo            *BankInfo             `protobuf:"bytes,2,opt,name=bank_info,json=bankInfo,proto3" json:"bank_info,omitempty"`
	TpapAccountId       string                `protobuf:"bytes,3,opt,name=tpap_account_id,json=tpapAccountId,proto3" json:"tpap_account_id,omitempty"`
	UpiPinSetStatus     enums.UpiPinSetStatus `protobuf:"varint,4,opt,name=upi_pin_set_status,json=upiPinSetStatus,proto3,enum=upi.onboarding.enums.UpiPinSetStatus" json:"upi_pin_set_status,omitempty"`
}

func (x *EligibleAccountInfo) Reset() {
	*x = EligibleAccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_international_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EligibleAccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EligibleAccountInfo) ProtoMessage() {}

func (x *EligibleAccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_international_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EligibleAccountInfo.ProtoReflect.Descriptor instead.
func (*EligibleAccountInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_international_proto_rawDescGZIP(), []int{1}
}

func (x *EligibleAccountInfo) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

func (x *EligibleAccountInfo) GetBankInfo() *BankInfo {
	if x != nil {
		return x.BankInfo
	}
	return nil
}

func (x *EligibleAccountInfo) GetTpapAccountId() string {
	if x != nil {
		return x.TpapAccountId
	}
	return ""
}

func (x *EligibleAccountInfo) GetUpiPinSetStatus() enums.UpiPinSetStatus {
	if x != nil {
		return x.UpiPinSetStatus
	}
	return enums.UpiPinSetStatus(0)
}

// AmountScreenForInternationalPaymentsInfo - represents
// the forex info which client will use for converting
// amount to a foreign currency ̰
type AmountScreenForInternationalPaymentsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ForexInfoList []*ForexInfo `protobuf:"bytes,1,rep,name=forex_info_list,json=forexInfoList,proto3" json:"forex_info_list,omitempty"`
}

func (x *AmountScreenForInternationalPaymentsInfo) Reset() {
	*x = AmountScreenForInternationalPaymentsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_international_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmountScreenForInternationalPaymentsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmountScreenForInternationalPaymentsInfo) ProtoMessage() {}

func (x *AmountScreenForInternationalPaymentsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_international_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmountScreenForInternationalPaymentsInfo.ProtoReflect.Descriptor instead.
func (*AmountScreenForInternationalPaymentsInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_international_proto_rawDescGZIP(), []int{2}
}

func (x *AmountScreenForInternationalPaymentsInfo) GetForexInfoList() []*ForexInfo {
	if x != nil {
		return x.ForexInfoList
	}
	return nil
}

// ForexInfo - info required to convert given amount to
// a particular foreign currency
type ForexInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversion rate of a foreign currency to INR
	// 1 USD = 80 INR
	ConversionRate float64 `protobuf:"fixed64,1,opt,name=conversion_rate,json=conversionRate,proto3" json:"conversion_rate,omitempty"`
	// markup = x % (means x % of the base amount has to be paid extra)
	MarkUp float64 `protobuf:"fixed64,2,opt,name=mark_up,json=markUp,proto3" json:"mark_up,omitempty"`
	// E.g. USD
	CurrencyCode string `protobuf:"bytes,3,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
}

func (x *ForexInfo) Reset() {
	*x = ForexInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_international_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForexInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForexInfo) ProtoMessage() {}

func (x *ForexInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_international_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForexInfo.ProtoReflect.Descriptor instead.
func (*ForexInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_international_proto_rawDescGZIP(), []int{3}
}

func (x *ForexInfo) GetConversionRate() float64 {
	if x != nil {
		return x.ConversionRate
	}
	return 0
}

func (x *ForexInfo) GetMarkUp() float64 {
	if x != nil {
		return x.MarkUp
	}
	return 0
}

func (x *ForexInfo) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

var File_api_upi_onboarding_upi_international_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_upi_international_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x75, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x22, 0x61, 0x70,
	0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x70,
	0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x01, 0x0a, 0x2f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x70, 0x69, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x59, 0x0a, 0x16, 0x65, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x6c, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x14, 0x65, 0x6c,
	0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x73, 0x22, 0xfc, 0x01, 0x0a, 0x13, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x61,
	0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d, 0x61, 0x73, 0x6b, 0x65,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x35,
	0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x62, 0x61, 0x6e,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x70, 0x61, 0x70, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x74, 0x70, 0x61, 0x70, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x52, 0x0a,
	0x12, 0x75, 0x70, 0x69, 0x5f, 0x70, 0x69, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x55, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0f, 0x75, 0x70, 0x69, 0x50, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x6d, 0x0a, 0x28, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x46, 0x6f, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a,
	0x0f, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0d, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x72, 0x0a, 0x09, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x75,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x6d, 0x61, 0x72, 0x6b, 0x55, 0x70, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70,
	0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_upi_international_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_upi_international_proto_rawDescData = file_api_upi_onboarding_upi_international_proto_rawDesc
)

func file_api_upi_onboarding_upi_international_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_upi_international_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_upi_international_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_upi_international_proto_rawDescData)
	})
	return file_api_upi_onboarding_upi_international_proto_rawDescData
}

var file_api_upi_onboarding_upi_international_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_upi_onboarding_upi_international_proto_goTypes = []interface{}{
	(*ActivateAccountsForInternationalUpiPaymentsInfo)(nil), // 0: upi.onboarding.ActivateAccountsForInternationalUpiPaymentsInfo
	(*EligibleAccountInfo)(nil),                             // 1: upi.onboarding.EligibleAccountInfo
	(*AmountScreenForInternationalPaymentsInfo)(nil),        // 2: upi.onboarding.AmountScreenForInternationalPaymentsInfo
	(*ForexInfo)(nil),                                       // 3: upi.onboarding.ForexInfo
	(*BankInfo)(nil),                                        // 4: upi.onboarding.BankInfo
	(enums.UpiPinSetStatus)(0),                              // 5: upi.onboarding.enums.UpiPinSetStatus
}
var file_api_upi_onboarding_upi_international_proto_depIdxs = []int32{
	1, // 0: upi.onboarding.ActivateAccountsForInternationalUpiPaymentsInfo.eligible_account_infos:type_name -> upi.onboarding.EligibleAccountInfo
	4, // 1: upi.onboarding.EligibleAccountInfo.bank_info:type_name -> upi.onboarding.BankInfo
	5, // 2: upi.onboarding.EligibleAccountInfo.upi_pin_set_status:type_name -> upi.onboarding.enums.UpiPinSetStatus
	3, // 3: upi.onboarding.AmountScreenForInternationalPaymentsInfo.forex_info_list:type_name -> upi.onboarding.ForexInfo
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_upi_international_proto_init() }
func file_api_upi_onboarding_upi_international_proto_init() {
	if File_api_upi_onboarding_upi_international_proto != nil {
		return
	}
	file_api_upi_onboarding_bank_info_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_upi_international_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivateAccountsForInternationalUpiPaymentsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_international_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EligibleAccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_international_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmountScreenForInternationalPaymentsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_international_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForexInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_upi_international_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_upi_international_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_upi_international_proto_depIdxs,
		MessageInfos:      file_api_upi_onboarding_upi_international_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_upi_international_proto = out.File
	file_api_upi_onboarding_upi_international_proto_rawDesc = nil
	file_api_upi_onboarding_upi_international_proto_goTypes = nil
	file_api_upi_onboarding_upi_international_proto_depIdxs = nil
}
