// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/onboarding/upi_mapper.proto

package onboarding

import (
	accounts "github.com/epifi/gamma/api/accounts"
	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VpaInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vpa of the user
	Vpa string `protobuf:"bytes,1,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// logo url of the psp to which the vpa belongs
	PspLogoUrl string `protobuf:"bytes,2,opt,name=psp_logo_url,json=pspLogoUrl,proto3" json:"psp_logo_url,omitempty"`
	// complete psp_name to which the vpa belongs e.g. In ykwatra@fbl Fi is the psp name
	PspName string `protobuf:"bytes,3,opt,name=psp_name,json=pspName,proto3" json:"psp_name,omitempty"`
}

func (x *VpaInfo) Reset() {
	*x = VpaInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_mapper_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VpaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VpaInfo) ProtoMessage() {}

func (x *VpaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_mapper_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VpaInfo.ProtoReflect.Descriptor instead.
func (*VpaInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_mapper_proto_rawDescGZIP(), []int{0}
}

func (x *VpaInfo) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *VpaInfo) GetPspLogoUrl() string {
	if x != nil {
		return x.PspLogoUrl
	}
	return ""
}

func (x *VpaInfo) GetPspName() string {
	if x != nil {
		return x.PspName
	}
	return ""
}

// AccountInfoForUpiMapper stores the info related to account to which the vpa belongs
type AccountInfoForUpiMapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// bank name to which the account belongs
	BankName string `protobuf:"bytes,1,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
	// ifsc code of the corresponding bank
	BankIfsc string `protobuf:"bytes,2,opt,name=bank_ifsc,json=bankIfsc,proto3" json:"bank_ifsc,omitempty"`
	// account type
	AccountType accounts.Type `protobuf:"varint,3,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// masked account number. Client needs to show the last 4 digits of the account number
	MaskedAccountNumber string `protobuf:"bytes,4,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	// client needs to show explicily if account is Primary
	AccountPreference enums.UpiAccountPreference `protobuf:"varint,5,opt,name=account_preference,json=accountPreference,proto3,enum=upi.onboarding.enums.UpiAccountPreference" json:"account_preference,omitempty"`
	// derived account id for the account
	DerivedAccountId string `protobuf:"bytes,6,opt,name=derived_account_id,json=derivedAccountId,proto3" json:"derived_account_id,omitempty"`
	// vpa
	Vpa string `protobuf:"bytes,7,opt,name=vpa,proto3" json:"vpa,omitempty"`
}

func (x *AccountInfoForUpiMapper) Reset() {
	*x = AccountInfoForUpiMapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_mapper_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountInfoForUpiMapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInfoForUpiMapper) ProtoMessage() {}

func (x *AccountInfoForUpiMapper) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_mapper_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInfoForUpiMapper.ProtoReflect.Descriptor instead.
func (*AccountInfoForUpiMapper) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_mapper_proto_rawDescGZIP(), []int{1}
}

func (x *AccountInfoForUpiMapper) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *AccountInfoForUpiMapper) GetBankIfsc() string {
	if x != nil {
		return x.BankIfsc
	}
	return ""
}

func (x *AccountInfoForUpiMapper) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *AccountInfoForUpiMapper) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

func (x *AccountInfoForUpiMapper) GetAccountPreference() enums.UpiAccountPreference {
	if x != nil {
		return x.AccountPreference
	}
	return enums.UpiAccountPreference(0)
}

func (x *AccountInfoForUpiMapper) GetDerivedAccountId() string {
	if x != nil {
		return x.DerivedAccountId
	}
	return ""
}

func (x *AccountInfoForUpiMapper) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

type UpiNumberDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type of upi number
	UpiNumberType enums.UpiNumberType `protobuf:"varint,1,opt,name=upi_number_type,json=upiNumberType,proto3,enum=upi.onboarding.enums.UpiNumberType" json:"upi_number_type,omitempty"`
	// linked upi number
	UpiNumber string `protobuf:"bytes,2,opt,name=upi_number,json=upiNumber,proto3" json:"upi_number,omitempty"`
	// state of upi number e.g. new, active, inactive, blocked
	UpiNumberState enums.UpiNumberState `protobuf:"varint,3,opt,name=upi_number_state,json=upiNumberState,proto3,enum=upi.onboarding.enums.UpiNumberState" json:"upi_number_state,omitempty"`
	// upi onboarding status to know if there is an ongoing action on a upiNumber
	UpiOnboardingStatus enums.UpiOnboardingStatus `protobuf:"varint,4,opt,name=upi_onboarding_status,json=upiOnboardingStatus,proto3,enum=upi.onboarding.enums.UpiOnboardingStatus" json:"upi_onboarding_status,omitempty"`
	// Deregisterd upi numbers have an expiry timestamp after which they cannot be reclaimed.
	UpiNumberExpiresAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=upi_number_expires_at,json=upiNumberExpiresAt,proto3" json:"upi_number_expires_at,omitempty"`
}

func (x *UpiNumberDetail) Reset() {
	*x = UpiNumberDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_mapper_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiNumberDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiNumberDetail) ProtoMessage() {}

func (x *UpiNumberDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_mapper_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiNumberDetail.ProtoReflect.Descriptor instead.
func (*UpiNumberDetail) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_mapper_proto_rawDescGZIP(), []int{2}
}

func (x *UpiNumberDetail) GetUpiNumberType() enums.UpiNumberType {
	if x != nil {
		return x.UpiNumberType
	}
	return enums.UpiNumberType(0)
}

func (x *UpiNumberDetail) GetUpiNumber() string {
	if x != nil {
		return x.UpiNumber
	}
	return ""
}

func (x *UpiNumberDetail) GetUpiNumberState() enums.UpiNumberState {
	if x != nil {
		return x.UpiNumberState
	}
	return enums.UpiNumberState(0)
}

func (x *UpiNumberDetail) GetUpiOnboardingStatus() enums.UpiOnboardingStatus {
	if x != nil {
		return x.UpiOnboardingStatus
	}
	return enums.UpiOnboardingStatus(0)
}

func (x *UpiNumberDetail) GetUpiNumberExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpiNumberExpiresAt
	}
	return nil
}

// AccountMapperDetails encapsulates information about an account and its associated UPI numbers.
type AccountMapperDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountInfo *AccountInfoForUpiMapper `protobuf:"bytes,1,opt,name=account_info,json=accountInfo,proto3" json:"account_info,omitempty"`
	// List of linked numbers with the account
	UpiNumberDetails []*UpiNumberDetail `protobuf:"bytes,2,rep,name=upi_number_details,json=upiNumberDetails,proto3" json:"upi_number_details,omitempty"`
}

func (x *AccountMapperDetails) Reset() {
	*x = AccountMapperDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_onboarding_upi_mapper_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountMapperDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountMapperDetails) ProtoMessage() {}

func (x *AccountMapperDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_onboarding_upi_mapper_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountMapperDetails.ProtoReflect.Descriptor instead.
func (*AccountMapperDetails) Descriptor() ([]byte, []int) {
	return file_api_upi_onboarding_upi_mapper_proto_rawDescGZIP(), []int{3}
}

func (x *AccountMapperDetails) GetAccountInfo() *AccountInfoForUpiMapper {
	if x != nil {
		return x.AccountInfo
	}
	return nil
}

func (x *AccountMapperDetails) GetUpiNumberDetails() []*UpiNumberDetail {
	if x != nil {
		return x.UpiNumberDetails
	}
	return nil
}

var File_api_upi_onboarding_upi_mapper_proto protoreflect.FileDescriptor

var file_api_upi_onboarding_upi_mapper_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x75, 0x70, 0x69, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34,
	0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x58, 0x0a, 0x07, 0x56, 0x70, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76,
	0x70, 0x61, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x73, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x73, 0x70, 0x4c, 0x6f, 0x67,
	0x6f, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xd5, 0x02, 0x0a, 0x17, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x46,
	0x6f, 0x72, 0x55, 0x70, 0x69, 0x4d, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x69, 0x66, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e,
	0x6b, 0x49, 0x66, 0x73, 0x63, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x6d, 0x61, 0x73, 0x6b,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x12,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65, 0x72, 0x69, 0x76,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65, 0x72, 0x69, 0x76, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x22, 0xfb, 0x02, 0x0a, 0x0f, 0x55, 0x70, 0x69, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4b, 0x0a, 0x0f, 0x75,
	0x70, 0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x75, 0x70, 0x69, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x69, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70,
	0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x10, 0x75, 0x70, 0x69, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x24, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x75, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x5d, 0x0a, 0x15, 0x75, 0x70, 0x69, 0x5f, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70,
	0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x13, 0x75, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x0a, 0x15, 0x75, 0x70, 0x69, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x12, 0x75, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x41, 0x74, 0x22, 0xb1, 0x01, 0x0a, 0x14, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4d, 0x61, 0x70, 0x70, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4a,
	0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x46, 0x6f, 0x72, 0x55, 0x70, 0x69, 0x4d, 0x61, 0x70, 0x70, 0x65, 0x72, 0x52, 0x0b, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x12, 0x75, 0x70,
	0x69, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x10, 0x75, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_onboarding_upi_mapper_proto_rawDescOnce sync.Once
	file_api_upi_onboarding_upi_mapper_proto_rawDescData = file_api_upi_onboarding_upi_mapper_proto_rawDesc
)

func file_api_upi_onboarding_upi_mapper_proto_rawDescGZIP() []byte {
	file_api_upi_onboarding_upi_mapper_proto_rawDescOnce.Do(func() {
		file_api_upi_onboarding_upi_mapper_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_onboarding_upi_mapper_proto_rawDescData)
	})
	return file_api_upi_onboarding_upi_mapper_proto_rawDescData
}

var file_api_upi_onboarding_upi_mapper_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_upi_onboarding_upi_mapper_proto_goTypes = []interface{}{
	(*VpaInfo)(nil),                 // 0: upi.onboarding.VpaInfo
	(*AccountInfoForUpiMapper)(nil), // 1: upi.onboarding.AccountInfoForUpiMapper
	(*UpiNumberDetail)(nil),         // 2: upi.onboarding.UpiNumberDetail
	(*AccountMapperDetails)(nil),    // 3: upi.onboarding.AccountMapperDetails
	(accounts.Type)(0),              // 4: accounts.Type
	(enums.UpiAccountPreference)(0), // 5: upi.onboarding.enums.UpiAccountPreference
	(enums.UpiNumberType)(0),        // 6: upi.onboarding.enums.UpiNumberType
	(enums.UpiNumberState)(0),       // 7: upi.onboarding.enums.UpiNumberState
	(enums.UpiOnboardingStatus)(0),  // 8: upi.onboarding.enums.UpiOnboardingStatus
	(*timestamppb.Timestamp)(nil),   // 9: google.protobuf.Timestamp
}
var file_api_upi_onboarding_upi_mapper_proto_depIdxs = []int32{
	4, // 0: upi.onboarding.AccountInfoForUpiMapper.account_type:type_name -> accounts.Type
	5, // 1: upi.onboarding.AccountInfoForUpiMapper.account_preference:type_name -> upi.onboarding.enums.UpiAccountPreference
	6, // 2: upi.onboarding.UpiNumberDetail.upi_number_type:type_name -> upi.onboarding.enums.UpiNumberType
	7, // 3: upi.onboarding.UpiNumberDetail.upi_number_state:type_name -> upi.onboarding.enums.UpiNumberState
	8, // 4: upi.onboarding.UpiNumberDetail.upi_onboarding_status:type_name -> upi.onboarding.enums.UpiOnboardingStatus
	9, // 5: upi.onboarding.UpiNumberDetail.upi_number_expires_at:type_name -> google.protobuf.Timestamp
	1, // 6: upi.onboarding.AccountMapperDetails.account_info:type_name -> upi.onboarding.AccountInfoForUpiMapper
	2, // 7: upi.onboarding.AccountMapperDetails.upi_number_details:type_name -> upi.onboarding.UpiNumberDetail
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_upi_onboarding_upi_mapper_proto_init() }
func file_api_upi_onboarding_upi_mapper_proto_init() {
	if File_api_upi_onboarding_upi_mapper_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_onboarding_upi_mapper_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VpaInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_mapper_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountInfoForUpiMapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_mapper_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiNumberDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_onboarding_upi_mapper_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountMapperDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_onboarding_upi_mapper_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_onboarding_upi_mapper_proto_goTypes,
		DependencyIndexes: file_api_upi_onboarding_upi_mapper_proto_depIdxs,
		MessageInfos:      file_api_upi_onboarding_upi_mapper_proto_msgTypes,
	}.Build()
	File_api_upi_onboarding_upi_mapper_proto = out.File
	file_api_upi_onboarding_upi_mapper_proto_rawDesc = nil
	file_api_upi_onboarding_upi_mapper_proto_goTypes = nil
	file_api_upi_onboarding_upi_mapper_proto_depIdxs = nil
}
