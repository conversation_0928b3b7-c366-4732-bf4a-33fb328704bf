// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/upi.proto

package upi

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.KeyCode(0)
)

// Validate checks the field values on UpiAccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpiAccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiAccountInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpiAccountInfoMultiError,
// or nil if none found.
func (m *UpiAccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiAccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if all {
		switch v := interface{}(m.GetControlJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "ControlJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "ControlJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetControlJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiAccountInfoValidationError{
				field:  "ControlJson",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PinSetState

	// no validation rules for IsAadhaarBankingEnabled

	// no validation rules for Vpa

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiAccountInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiAccountInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiAccountInfoValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IfscCode

	// no validation rules for AccountRef

	// no validation rules for MaskedAccountNumber

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetListAccountCalledAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "ListAccountCalledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiAccountInfoValidationError{
					field:  "ListAccountCalledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListAccountCalledAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiAccountInfoValidationError{
				field:  "ListAccountCalledAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiAccountInfoMultiError(errors)
	}

	return nil
}

// UpiAccountInfoMultiError is an error wrapping multiple validation errors
// returned by UpiAccountInfo.ValidateAll() if the designated constraints
// aren't met.
type UpiAccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiAccountInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiAccountInfoMultiError) AllErrors() []error { return m }

// UpiAccountInfoValidationError is the validation error returned by
// UpiAccountInfo.Validate if the designated constraints aren't met.
type UpiAccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiAccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiAccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiAccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiAccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiAccountInfoValidationError) ErrorName() string { return "UpiAccountInfoValidationError" }

// Error satisfies the builtin error interface
func (e UpiAccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiAccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiAccountInfoValidationError{}

// Validate checks the field values on ControlJson with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ControlJson) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ControlJson with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ControlJsonMultiError, or
// nil if none found.
func (m *ControlJson) ValidateAll() error {
	return m.validate(true)
}

func (m *ControlJson) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCredAlloweds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ControlJsonValidationError{
						field:  fmt.Sprintf("CredAlloweds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ControlJsonValidationError{
						field:  fmt.Sprintf("CredAlloweds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ControlJsonValidationError{
					field:  fmt.Sprintf("CredAlloweds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInternationalPaymentsExpiresAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ControlJsonValidationError{
					field:  "InternationalPaymentsExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ControlJsonValidationError{
					field:  "InternationalPaymentsExpiresAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInternationalPaymentsExpiresAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ControlJsonValidationError{
				field:  "InternationalPaymentsExpiresAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsAadhaarEnabledBankAccount

	if all {
		switch v := interface{}(m.GetInternationalPaymentsActivatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ControlJsonValidationError{
					field:  "InternationalPaymentsActivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ControlJsonValidationError{
					field:  "InternationalPaymentsActivatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInternationalPaymentsActivatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ControlJsonValidationError{
				field:  "InternationalPaymentsActivatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ControlJsonMultiError(errors)
	}

	return nil
}

// ControlJsonMultiError is an error wrapping multiple validation errors
// returned by ControlJson.ValidateAll() if the designated constraints aren't met.
type ControlJsonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ControlJsonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ControlJsonMultiError) AllErrors() []error { return m }

// ControlJsonValidationError is the validation error returned by
// ControlJson.Validate if the designated constraints aren't met.
type ControlJsonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ControlJsonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ControlJsonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ControlJsonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ControlJsonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ControlJsonValidationError) ErrorName() string { return "ControlJsonValidationError" }

// Error satisfies the builtin error interface
func (e ControlJsonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sControlJson.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ControlJsonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ControlJsonValidationError{}

// Validate checks the field values on CredAllowed with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CredAllowed) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CredAllowed with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CredAllowedMultiError, or
// nil if none found.
func (m *CredAllowed) ValidateAll() error {
	return m.validate(true)
}

func (m *CredAllowed) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DLength

	// no validation rules for DType

	// no validation rules for SubType

	// no validation rules for Type

	if len(errors) > 0 {
		return CredAllowedMultiError(errors)
	}

	return nil
}

// CredAllowedMultiError is an error wrapping multiple validation errors
// returned by CredAllowed.ValidateAll() if the designated constraints aren't met.
type CredAllowedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CredAllowedMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CredAllowedMultiError) AllErrors() []error { return m }

// CredAllowedValidationError is the validation error returned by
// CredAllowed.Validate if the designated constraints aren't met.
type CredAllowedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CredAllowedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CredAllowedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CredAllowedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CredAllowedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CredAllowedValidationError) ErrorName() string { return "CredAllowedValidationError" }

// Error satisfies the builtin error interface
func (e CredAllowedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCredAllowed.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CredAllowedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CredAllowedValidationError{}

// Validate checks the field values on PspKey with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PspKey) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PspKey with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PspKeyMultiError, or nil if none found.
func (m *PspKey) ValidateAll() error {
	return m.validate(true)
}

func (m *PspKey) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for PspOrgId

	// no validation rules for Type

	// no validation rules for Ki

	// no validation rules for KeyValue

	// no validation rules for Owner

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PspKeyValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PspKeyValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PspKeyValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PspKeyValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PspKeyValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PspKeyValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PspKeyMultiError(errors)
	}

	return nil
}

// PspKeyMultiError is an error wrapping multiple validation errors returned by
// PspKey.ValidateAll() if the designated constraints aren't met.
type PspKeyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PspKeyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PspKeyMultiError) AllErrors() []error { return m }

// PspKeyValidationError is the validation error returned by PspKey.Validate if
// the designated constraints aren't met.
type PspKeyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PspKeyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PspKeyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PspKeyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PspKeyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PspKeyValidationError) ErrorName() string { return "PspKeyValidationError" }

// Error satisfies the builtin error interface
func (e PspKeyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPspKey.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PspKeyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PspKeyValidationError{}

// Validate checks the field values on VerifiedAddressEntry with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifiedAddressEntry) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifiedAddressEntry with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifiedAddressEntryMultiError, or nil if none found.
func (m *VerifiedAddressEntry) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifiedAddressEntry) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MerchantVpa

	// no validation rules for Name

	// no validation rules for Url

	// no validation rules for KeyCode

	// no validation rules for Type

	// no validation rules for Ki

	// no validation rules for KeyValue

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifiedAddressEntryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifiedAddressEntryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifiedAddressEntryValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifiedAddressEntryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifiedAddressEntryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifiedAddressEntryValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifiedAddressEntryMultiError(errors)
	}

	return nil
}

// VerifiedAddressEntryMultiError is an error wrapping multiple validation
// errors returned by VerifiedAddressEntry.ValidateAll() if the designated
// constraints aren't met.
type VerifiedAddressEntryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifiedAddressEntryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifiedAddressEntryMultiError) AllErrors() []error { return m }

// VerifiedAddressEntryValidationError is the validation error returned by
// VerifiedAddressEntry.Validate if the designated constraints aren't met.
type VerifiedAddressEntryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifiedAddressEntryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifiedAddressEntryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifiedAddressEntryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifiedAddressEntryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifiedAddressEntryValidationError) ErrorName() string {
	return "VerifiedAddressEntryValidationError"
}

// Error satisfies the builtin error interface
func (e VerifiedAddressEntryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifiedAddressEntry.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifiedAddressEntryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifiedAddressEntryValidationError{}

// Validate checks the field values on AccountUpiPinInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AccountUpiPinInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountUpiPinInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountUpiPinInfoMultiError, or nil if none found.
func (m *AccountUpiPinInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountUpiPinInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AccountId

	// no validation rules for UserAction

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountUpiPinInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountUpiPinInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountUpiPinInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountUpiPinInfoValidationError{
					field:  "UpdateAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountUpiPinInfoValidationError{
					field:  "UpdateAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountUpiPinInfoValidationError{
				field:  "UpdateAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountUpiPinInfoValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountUpiPinInfoValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountUpiPinInfoValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetDetailedStatusList()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountUpiPinInfoValidationError{
					field:  "DetailedStatusList",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountUpiPinInfoValidationError{
					field:  "DetailedStatusList",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailedStatusList()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountUpiPinInfoValidationError{
				field:  "DetailedStatusList",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorRequestId

	if len(errors) > 0 {
		return AccountUpiPinInfoMultiError(errors)
	}

	return nil
}

// AccountUpiPinInfoMultiError is an error wrapping multiple validation errors
// returned by AccountUpiPinInfo.ValidateAll() if the designated constraints
// aren't met.
type AccountUpiPinInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountUpiPinInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountUpiPinInfoMultiError) AllErrors() []error { return m }

// AccountUpiPinInfoValidationError is the validation error returned by
// AccountUpiPinInfo.Validate if the designated constraints aren't met.
type AccountUpiPinInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountUpiPinInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountUpiPinInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountUpiPinInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountUpiPinInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountUpiPinInfoValidationError) ErrorName() string {
	return "AccountUpiPinInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AccountUpiPinInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountUpiPinInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountUpiPinInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountUpiPinInfoValidationError{}

// Validate checks the field values on DetailedStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DetailedStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailedStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DetailedStatusMultiError,
// or nil if none found.
func (m *DetailedStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailedStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RawStatusCode

	// no validation rules for RawStatusDescription

	if len(errors) > 0 {
		return DetailedStatusMultiError(errors)
	}

	return nil
}

// DetailedStatusMultiError is an error wrapping multiple validation errors
// returned by DetailedStatus.ValidateAll() if the designated constraints
// aren't met.
type DetailedStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailedStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailedStatusMultiError) AllErrors() []error { return m }

// DetailedStatusValidationError is the validation error returned by
// DetailedStatus.Validate if the designated constraints aren't met.
type DetailedStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailedStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailedStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailedStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailedStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailedStatusValidationError) ErrorName() string { return "DetailedStatusValidationError" }

// Error satisfies the builtin error interface
func (e DetailedStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailedStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailedStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailedStatusValidationError{}

// Validate checks the field values on UpiProcessedPhoneNumber with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiProcessedPhoneNumber) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiProcessedPhoneNumber with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiProcessedPhoneNumberMultiError, or nil if none found.
func (m *UpiProcessedPhoneNumber) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiProcessedPhoneNumber) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiProcessedPhoneNumberValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVerifiedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "VerifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "VerifiedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVerifiedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiProcessedPhoneNumberValidationError{
				field:  "VerifiedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiProcessedPhoneNumberValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiProcessedPhoneNumberValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiProcessedPhoneNumberValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiProcessedPhoneNumberValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Id

	if len(errors) > 0 {
		return UpiProcessedPhoneNumberMultiError(errors)
	}

	return nil
}

// UpiProcessedPhoneNumberMultiError is an error wrapping multiple validation
// errors returned by UpiProcessedPhoneNumber.ValidateAll() if the designated
// constraints aren't met.
type UpiProcessedPhoneNumberMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiProcessedPhoneNumberMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiProcessedPhoneNumberMultiError) AllErrors() []error { return m }

// UpiProcessedPhoneNumberValidationError is the validation error returned by
// UpiProcessedPhoneNumber.Validate if the designated constraints aren't met.
type UpiProcessedPhoneNumberValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiProcessedPhoneNumberValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiProcessedPhoneNumberValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiProcessedPhoneNumberValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiProcessedPhoneNumberValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiProcessedPhoneNumberValidationError) ErrorName() string {
	return "UpiProcessedPhoneNumberValidationError"
}

// Error satisfies the builtin error interface
func (e UpiProcessedPhoneNumberValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiProcessedPhoneNumber.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiProcessedPhoneNumberValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiProcessedPhoneNumberValidationError{}

// Validate checks the field values on VpaInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VpaInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VpaInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in VpaInfoMultiError, or nil if none found.
func (m *VpaInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *VpaInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayeeName

	if all {
		switch v := interface{}(m.GetPspBadgeIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpaInfoValidationError{
					field:  "PspBadgeIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpaInfoValidationError{
					field:  "PspBadgeIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPspBadgeIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpaInfoValidationError{
				field:  "PspBadgeIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vpa

	if len(errors) > 0 {
		return VpaInfoMultiError(errors)
	}

	return nil
}

// VpaInfoMultiError is an error wrapping multiple validation errors returned
// by VpaInfo.ValidateAll() if the designated constraints aren't met.
type VpaInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VpaInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VpaInfoMultiError) AllErrors() []error { return m }

// VpaInfoValidationError is the validation error returned by VpaInfo.Validate
// if the designated constraints aren't met.
type VpaInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VpaInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VpaInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VpaInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VpaInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VpaInfoValidationError) ErrorName() string { return "VpaInfoValidationError" }

// Error satisfies the builtin error interface
func (e VpaInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVpaInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VpaInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VpaInfoValidationError{}

// Validate checks the field values on RegIdDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RegIdDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegIdDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RegIdDetailsMultiError, or
// nil if none found.
func (m *RegIdDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RegIdDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	// no validation rules for SetStatus

	// no validation rules for Addr

	if all {
		switch v := interface{}(m.GetExpiryTs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegIdDetailsValidationError{
					field:  "ExpiryTs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegIdDetailsValidationError{
					field:  "ExpiryTs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryTs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegIdDetailsValidationError{
				field:  "ExpiryTs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastUpdatedTs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegIdDetailsValidationError{
					field:  "LastUpdatedTs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegIdDetailsValidationError{
					field:  "LastUpdatedTs",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastUpdatedTs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegIdDetailsValidationError{
				field:  "LastUpdatedTs",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RegIdDetailsMultiError(errors)
	}

	return nil
}

// RegIdDetailsMultiError is an error wrapping multiple validation errors
// returned by RegIdDetails.ValidateAll() if the designated constraints aren't met.
type RegIdDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegIdDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegIdDetailsMultiError) AllErrors() []error { return m }

// RegIdDetailsValidationError is the validation error returned by
// RegIdDetails.Validate if the designated constraints aren't met.
type RegIdDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegIdDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegIdDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegIdDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegIdDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegIdDetailsValidationError) ErrorName() string { return "RegIdDetailsValidationError" }

// Error satisfies the builtin error interface
func (e RegIdDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegIdDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegIdDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegIdDetailsValidationError{}

// Validate checks the field values on Consent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Consent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Consent with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ConsentMultiError, or nil if none found.
func (m *Consent) ValidateAll() error {
	return m.validate(true)
}

func (m *Consent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	// no validation rules for PreVpa

	if len(errors) > 0 {
		return ConsentMultiError(errors)
	}

	return nil
}

// ConsentMultiError is an error wrapping multiple validation errors returned
// by Consent.ValidateAll() if the designated constraints aren't met.
type ConsentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsentMultiError) AllErrors() []error { return m }

// ConsentValidationError is the validation error returned by Consent.Validate
// if the designated constraints aren't met.
type ConsentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsentValidationError) ErrorName() string { return "ConsentValidationError" }

// Error satisfies the builtin error interface
func (e ConsentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsentValidationError{}

// Validate checks the field values on UpiInternationalPaymentCharge with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiInternationalPaymentCharge) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiInternationalPaymentCharge with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpiInternationalPaymentChargeMultiError, or nil if none found.
func (m *UpiInternationalPaymentCharge) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiInternationalPaymentCharge) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiInternationalPaymentChargeValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiInternationalPaymentChargeValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiInternationalPaymentChargeValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConversionRate

	// no validation rules for OverheadChargePercentage

	// no validation rules for UpiInternationalPaymentChargeType

	if len(errors) > 0 {
		return UpiInternationalPaymentChargeMultiError(errors)
	}

	return nil
}

// UpiInternationalPaymentChargeMultiError is an error wrapping multiple
// validation errors returned by UpiInternationalPaymentCharge.ValidateAll()
// if the designated constraints aren't met.
type UpiInternationalPaymentChargeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiInternationalPaymentChargeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiInternationalPaymentChargeMultiError) AllErrors() []error { return m }

// UpiInternationalPaymentChargeValidationError is the validation error
// returned by UpiInternationalPaymentCharge.Validate if the designated
// constraints aren't met.
type UpiInternationalPaymentChargeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiInternationalPaymentChargeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiInternationalPaymentChargeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiInternationalPaymentChargeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiInternationalPaymentChargeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiInternationalPaymentChargeValidationError) ErrorName() string {
	return "UpiInternationalPaymentChargeValidationError"
}

// Error satisfies the builtin error interface
func (e UpiInternationalPaymentChargeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiInternationalPaymentCharge.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiInternationalPaymentChargeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiInternationalPaymentChargeValidationError{}

// Validate checks the field values on UpiLiteTransactionParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiLiteTransactionParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiLiteTransactionParams with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiLiteTransactionParamsMultiError, or nil if none found.
func (m *UpiLiteTransactionParams) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiLiteTransactionParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KeyCode

	// no validation rules for KeyXmlPayload

	if all {
		switch v := interface{}(m.GetControlJson()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiLiteTransactionParamsValidationError{
					field:  "ControlJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiLiteTransactionParamsValidationError{
					field:  "ControlJson",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetControlJson()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiLiteTransactionParamsValidationError{
				field:  "ControlJson",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BankConfigJson

	if all {
		switch v := interface{}(m.GetBankConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiLiteTransactionParamsValidationError{
					field:  "BankConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiLiteTransactionParamsValidationError{
					field:  "BankConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiLiteTransactionParamsValidationError{
				field:  "BankConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountRefNumber

	// no validation rules for Lrn

	if len(errors) > 0 {
		return UpiLiteTransactionParamsMultiError(errors)
	}

	return nil
}

// UpiLiteTransactionParamsMultiError is an error wrapping multiple validation
// errors returned by UpiLiteTransactionParams.ValidateAll() if the designated
// constraints aren't met.
type UpiLiteTransactionParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiLiteTransactionParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiLiteTransactionParamsMultiError) AllErrors() []error { return m }

// UpiLiteTransactionParamsValidationError is the validation error returned by
// UpiLiteTransactionParams.Validate if the designated constraints aren't met.
type UpiLiteTransactionParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiLiteTransactionParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiLiteTransactionParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiLiteTransactionParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiLiteTransactionParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiLiteTransactionParamsValidationError) ErrorName() string {
	return "UpiLiteTransactionParamsValidationError"
}

// Error satisfies the builtin error interface
func (e UpiLiteTransactionParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiLiteTransactionParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiLiteTransactionParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiLiteTransactionParamsValidationError{}

// Validate checks the field values on UpiLiteBalanceParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiLiteBalanceParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiLiteBalanceParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiLiteBalanceParamsMultiError, or nil if none found.
func (m *UpiLiteBalanceParams) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiLiteBalanceParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountRefNumber

	// no validation rules for Lrn

	if len(errors) > 0 {
		return UpiLiteBalanceParamsMultiError(errors)
	}

	return nil
}

// UpiLiteBalanceParamsMultiError is an error wrapping multiple validation
// errors returned by UpiLiteBalanceParams.ValidateAll() if the designated
// constraints aren't met.
type UpiLiteBalanceParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiLiteBalanceParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiLiteBalanceParamsMultiError) AllErrors() []error { return m }

// UpiLiteBalanceParamsValidationError is the validation error returned by
// UpiLiteBalanceParams.Validate if the designated constraints aren't met.
type UpiLiteBalanceParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiLiteBalanceParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiLiteBalanceParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiLiteBalanceParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiLiteBalanceParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiLiteBalanceParamsValidationError) ErrorName() string {
	return "UpiLiteBalanceParamsValidationError"
}

// Error satisfies the builtin error interface
func (e UpiLiteBalanceParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiLiteBalanceParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiLiteBalanceParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiLiteBalanceParamsValidationError{}

// Validate checks the field values on AccountUpiPinInfo_DetailedStatusList
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AccountUpiPinInfo_DetailedStatusList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountUpiPinInfo_DetailedStatusList
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AccountUpiPinInfo_DetailedStatusListMultiError, or nil if none found.
func (m *AccountUpiPinInfo_DetailedStatusList) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountUpiPinInfo_DetailedStatusList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDetailedStatus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountUpiPinInfo_DetailedStatusListValidationError{
						field:  fmt.Sprintf("DetailedStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountUpiPinInfo_DetailedStatusListValidationError{
						field:  fmt.Sprintf("DetailedStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountUpiPinInfo_DetailedStatusListValidationError{
					field:  fmt.Sprintf("DetailedStatus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountUpiPinInfo_DetailedStatusListMultiError(errors)
	}

	return nil
}

// AccountUpiPinInfo_DetailedStatusListMultiError is an error wrapping multiple
// validation errors returned by
// AccountUpiPinInfo_DetailedStatusList.ValidateAll() if the designated
// constraints aren't met.
type AccountUpiPinInfo_DetailedStatusListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountUpiPinInfo_DetailedStatusListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountUpiPinInfo_DetailedStatusListMultiError) AllErrors() []error { return m }

// AccountUpiPinInfo_DetailedStatusListValidationError is the validation error
// returned by AccountUpiPinInfo_DetailedStatusList.Validate if the designated
// constraints aren't met.
type AccountUpiPinInfo_DetailedStatusListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountUpiPinInfo_DetailedStatusListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountUpiPinInfo_DetailedStatusListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountUpiPinInfo_DetailedStatusListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountUpiPinInfo_DetailedStatusListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountUpiPinInfo_DetailedStatusListValidationError) ErrorName() string {
	return "AccountUpiPinInfo_DetailedStatusListValidationError"
}

// Error satisfies the builtin error interface
func (e AccountUpiPinInfo_DetailedStatusListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountUpiPinInfo_DetailedStatusList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountUpiPinInfo_DetailedStatusListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountUpiPinInfo_DetailedStatusListValidationError{}

// Validate checks the field values on UpiLiteTransactionParams_BankConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpiLiteTransactionParams_BankConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiLiteTransactionParams_BankConfig
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpiLiteTransactionParams_BankConfigMultiError, or nil if none found.
func (m *UpiLiteTransactionParams_BankConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiLiteTransactionParams_BankConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayerBankName

	// no validation rules for BackgroundColor

	// no validation rules for Color

	// no validation rules for ResendOtpFeature

	if len(errors) > 0 {
		return UpiLiteTransactionParams_BankConfigMultiError(errors)
	}

	return nil
}

// UpiLiteTransactionParams_BankConfigMultiError is an error wrapping multiple
// validation errors returned by
// UpiLiteTransactionParams_BankConfig.ValidateAll() if the designated
// constraints aren't met.
type UpiLiteTransactionParams_BankConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiLiteTransactionParams_BankConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiLiteTransactionParams_BankConfigMultiError) AllErrors() []error { return m }

// UpiLiteTransactionParams_BankConfigValidationError is the validation error
// returned by UpiLiteTransactionParams_BankConfig.Validate if the designated
// constraints aren't met.
type UpiLiteTransactionParams_BankConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiLiteTransactionParams_BankConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiLiteTransactionParams_BankConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiLiteTransactionParams_BankConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiLiteTransactionParams_BankConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiLiteTransactionParams_BankConfigValidationError) ErrorName() string {
	return "UpiLiteTransactionParams_BankConfigValidationError"
}

// Error satisfies the builtin error interface
func (e UpiLiteTransactionParams_BankConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiLiteTransactionParams_BankConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiLiteTransactionParams_BankConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiLiteTransactionParams_BankConfigValidationError{}
