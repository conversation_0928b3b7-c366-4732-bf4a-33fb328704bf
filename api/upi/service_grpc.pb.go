// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/upi/service.proto

package upi

import (
	context "context"
	domain "github.com/epifi/gamma/api/order/domain"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UPI_VerifyPayeeVPA_FullMethodName                             = "/upi.UPI/VerifyPayeeVPA"
	UPI_GetToken_FullMethodName                                   = "/upi.UPI/GetToken"
	UPI_GenerateUpiOtp_FullMethodName                             = "/upi.UPI/GenerateUpiOtp"
	UPI_ResolveAccount_FullMethodName                             = "/upi.UPI/ResolveAccount"
	UPI_CreateVPA_FullMethodName                                  = "/upi.UPI/CreateVPA"
	UPI_ListKeys_FullMethodName                                   = "/upi.UPI/ListKeys"
	UPI_RegisterMobile_FullMethodName                             = "/upi.UPI/RegisterMobile"
	UPI_GetPinFlowParameters_FullMethodName                       = "/upi.UPI/GetPinFlowParameters"
	UPI_GetTransactionParameters_FullMethodName                   = "/upi.UPI/GetTransactionParameters"
	UPI_VerifyURN_FullMethodName                                  = "/upi.UPI/VerifyURN"
	UPI_ChangePin_FullMethodName                                  = "/upi.UPI/ChangePin"
	UPI_GenerateURN_FullMethodName                                = "/upi.UPI/GenerateURN"
	UPI_GetUpiSetupStatus_FullMethodName                          = "/upi.UPI/GetUpiSetupStatus"
	UPI_GetOrCreateURNTransaction_FullMethodName                  = "/upi.UPI/GetOrCreateURNTransaction"
	UPI_DisableOrEnableVPA_FullMethodName                         = "/upi.UPI/DisableOrEnableVPA"
	UPI_GetPinStatus_FullMethodName                               = "/upi.UPI/GetPinStatus"
	UPI_GetAccountPinInfos_FullMethodName                         = "/upi.UPI/GetAccountPinInfos"
	UPI_GetLatestAccountPinInfos_FullMethodName                   = "/upi.UPI/GetLatestAccountPinInfos"
	UPI_CheckVerifiedMerchant_FullMethodName                      = "/upi.UPI/CheckVerifiedMerchant"
	UPI_GetOrCreateAddFundsTransaction_FullMethodName             = "/upi.UPI/GetOrCreateAddFundsTransaction"
	UPI_GetVpaMerchantInfo_FullMethodName                         = "/upi.UPI/GetVpaMerchantInfo"
	UPI_GetPinInfosByAccountId_FullMethodName                     = "/upi.UPI/GetPinInfosByAccountId"
	UPI_PostUserActivity_FullMethodName                           = "/upi.UPI/PostUserActivity"
	UPI_ValidateSecurePin_FullMethodName                          = "/upi.UPI/ValidateSecurePin"
	UPI_CheckAndUpdateAddFundsCollectPaymentStatus_FullMethodName = "/upi.UPI/CheckAndUpdateAddFundsCollectPaymentStatus"
	UPI_RaiseComplaint_FullMethodName                             = "/upi.UPI/RaiseComplaint"
	UPI_CheckTxnStatus_FullMethodName                             = "/upi.UPI/CheckTxnStatus"
	UPI_CheckComplaintStatus_FullMethodName                       = "/upi.UPI/CheckComplaintStatus"
	UPI_ChangeUpiPinSetState_FullMethodName                       = "/upi.UPI/ChangeUpiPinSetState"
	UPI_GetVerifiedVpasByPhoneNumber_FullMethodName               = "/upi.UPI/GetVerifiedVpasByPhoneNumber"
	UPI_ValidateAddressAndCreatePi_FullMethodName                 = "/upi.UPI/ValidateAddressAndCreatePi"
	UPI_ValidateUpiNumberAndCreatePi_FullMethodName               = "/upi.UPI/ValidateUpiNumberAndCreatePi"
	UPI_ValidateInternationalPayment_FullMethodName               = "/upi.UPI/ValidateInternationalPayment"
	UPI_GetUpiInternationalQrInfoFromCache_FullMethodName         = "/upi.UPI/GetUpiInternationalQrInfoFromCache"
	UPI_GetExternalVpas_FullMethodName                            = "/upi.UPI/GetExternalVpas"
	UPI_GetNPCICLParametersV1_FullMethodName                      = "/upi.UPI/GetNPCICLParametersV1"
	UPI_IsNewAddFundsVpaEnabledForActor_FullMethodName            = "/upi.UPI/IsNewAddFundsVpaEnabledForActor"
)

// UPIClient is the client API for UPI service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UPIClient interface {
	// Verify if the VPA is valid or not
	// Makes call to to vendorgateway which in turn will make call to federal to verify the VPA
	// This will be used when a customer wants to add a upi beneficiary for sending/collecting money
	VerifyPayeeVPA(ctx context.Context, in *VerifyPayeeVPARequest, opts ...grpc.CallOption) (*VerifyPayeeVPAResponse, error)
	// Returns the token to be used for registering the app to use NPCI common library
	// The registration flow is as follows
	//  1. Execute “Get Challenge” service to receive a challenge from common library.
	//  2. Use the generated challenge to get the token from PSP server
	//  3. Use the token to register the app with common library
	GetToken(ctx context.Context, in *GetTokenRequest, opts ...grpc.CallOption) (*GetTokenResponse, error)
	// Used to request OTP from issuer bank via UPI for ATM PIN validation
	// Returns secure_url for redirection to bank page for OTP validation
	GenerateUpiOtp(ctx context.Context, in *GenerateUpiOtpRequest, opts ...grpc.CallOption) (*GenerateUpiOtpResponse, error)
	// RPC to to resolve account for a given VPA.
	// returns obfuscated account details and ifsc belonging to the VPA.
	// This RPC is used as in multiple places
	// 1) to resolve account information for payer in case of PAY request
	// 2) to resolve account information for payee in case of COLLECT request
	// 3) to resolve account information when `ReqAuth` is received.
	ResolveAccount(ctx context.Context, in *ResolveAccountRequest, opts ...grpc.CallOption) (*ResolveAccountResponse, error)
	// RPC to create virtual Id for account
	// Below are the steps followed by this RPC:
	//  1. ListAccount to get the account info like masked account number, account reference number etc.
	//  2. Make call to CreateVirtualId on vendorgateway to create VPA
	//  3. Create PI for VPA
	//  4. Create account for PI for VPA
	//
	// This RPC is idempotent i.e. on each retry on create vpa for a account Id,
	// it will get previous executed steps and resume create vpa from there
	CreateVPA(ctx context.Context, in *CreateVPARequest, opts ...grpc.CallOption) (*CreateVPAResponse, error)
	// This RPC allows to fetch all encryption public keys from NPCI.
	// It will be cached in UPI service in first call (in memory).
	// Cached data will be returned in subsequent call.
	// These public keys will be used to capture sensitive data like card digits, expiry
	// These keys will be used by CL (Common Library) and
	// are to be passed by PSP app to CL as parameter to GetCredentials API
	ListKeys(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListKeysResponse, error)
	// RPC for Mobile Banking Registration
	// This API allows the customer to set new UPI PIN for the first time and also can be used to reset pin.
	// This rpc will forward this request to vendorgateway to NPCI/Federal.
	//
	// This rpc will fetch actor's debit card details and send those details to vendor for register mobile API.
	// Card details will be tokenized. Tokens will be replaced in Vendorgateway with actual card details.
	RegisterMobile(ctx context.Context, in *RegisterMobileRequest, opts ...grpc.CallOption) (*RegisterMobileResponse, error)
	// This RPC helps the client app to fetch the encryption keys,
	// allowed_cred json associated with the account
	//
	// This data will enable the client app to get the credentials that are required to
	// process a Mobile Banking registration (or) Set Credentials request with the
	// help of "Get Credential Service" of NPCI CL
	GetPinFlowParameters(ctx context.Context, in *GetPinFlowParametersRequest, opts ...grpc.CallOption) (*GetPinFlowParametersResponse, error)
	// This RPC helps the client app to fetch the encryption keys,
	// allowed_cred json required for transaction associated with the account, bank config and other info appear on
	// UPI PIN page.
	//
	// This data will enable the client app to get the credentials that are required to
	// process a UPI request with the help of "Get Credential Service" of NPCI CL
	GetTransactionParameters(ctx context.Context, in *GetTransactionParametersRequest, opts ...grpc.CallOption) (*GetTransactionParametersResponse, error)
	// This rpc verifies the urn signature and returns parsed values from URN
	// For QR/Intent based payments we need to verify the signature of the urn received from the merchant system
	//
	// Checks for the mandatory fields in the urn, if any of the mandatory fields are missing will return status INVALID_ARGUMENT
	//
	// Process for signature verification
	// 1) Extract the signature from the URN
	// 2) Search for the public key from ListVAE or ListKeys cache based on the OrgId and VPA.
	// 3) Verify the signature using the public key and remaining URN as digest
	//
	// If the signature is valid rpc will parse the urn for relevant fields like txnId, merchant_refId etc.
	// and will return those fields in the response
	VerifyURN(ctx context.Context, in *VerifyURNRequest, opts ...grpc.CallOption) (*VerifyURNResponse, error)
	// Secure PIN a.k.a UPI PIN will be used to authenticate all modes of transactions
	// i.e., IMPS, RTGS, NEFT, UPI.
	//
	// This would enhance the user’s experience by using the same PIN for all transactions.
	//
	// # This RPC helps to change Secure PIN for the given account
	//
	// Note that Secure PIN is against an account and not a UPI VPA
	ChangePin(ctx context.Context, in *ChangePinRequest, opts ...grpc.CallOption) (*ChangePinResponse, error)
	// GenerateURN returns the urn for outgoing intent/QR based payments
	//
	// It first creates an order with URN_TRANSFER workflow and then generate URN.
	// Generated urn contains parameter such as payeeVPA, amount, txnId, refId etc and
	// is be signed by the PSP's key.
	//
	// It returns corresponding orderId and URN in response.
	GenerateURN(ctx context.Context, in *GenerateURNRequest, opts ...grpc.CallOption) (*GenerateURNResponse, error)
	// RPC to get the upi creation status of account id.
	GetUpiSetupStatus(ctx context.Context, in *GetUpiSetupStatusRequest, opts ...grpc.CallOption) (*GetUpiSetupStatusResponse, error)
	// GetOrCreateURNTransaction RPC is called by centralized orchestrator in order service, for processing
	// order having URN_TRANSFER workflow.
	// The order service takes care of the retrying logic while UPI service acts a domain service
	// of taking care of business logic specific to URN transfer.
	// The API is made idempotent, using the clientRequestId passed by order.
	// If the transaction is in a terminal state, then the corresponding order domain status is returned.
	// Else, it checks the transaction status with the corresponding partner banks and creates entry in transaction
	// schema if entry doesnt exists already.
	GetOrCreateURNTransaction(ctx context.Context, in *domain.ProcessPaymentRequest, opts ...grpc.CallOption) (*domain.ProcessPaymentResponse, error)
	// rpc to enable/disable vpa based on the request type
	// request type enable - pi state will be marked as created
	// request type disable - pi state will be marked as suspend
	DisableOrEnableVPA(ctx context.Context, in *DisableOrEnableVPARequest, opts ...grpc.CallOption) (*DisableOrEnableVPAResponse, error)
	// Rpc to get the status of upi pin set state.
	// Returns PinSetState against accountId in map
	//
	// Queries the account upi_account_infos table and check pin set state. If pin set state is PIN_NOT_SET
	// or REOOBE_PIN_NOT_SET then it will fetch latest PIN_SET_STATE from vendor.
	//
	// NOTE - returns internal server error if there is error while fetching pin status for one or more accountId
	GetPinStatus(ctx context.Context, in *PinStatusRequest, opts ...grpc.CallOption) (*PinStatusResponse, error)
	// rpc to get the list of account upi pin infos for the given accountId
	// returns status record not found if no entry is present for the account id
	GetAccountPinInfos(ctx context.Context, in *GetAccountPinInfosRequest, opts ...grpc.CallOption) (*GetAccountPinInfosResponse, error)
	// rpc to get the list of most recent account upi pin infos for the given list of accountIds
	// returns status record not found if no entry is present for the account id
	GetLatestAccountPinInfos(ctx context.Context, in *GetLatestAccountPinInfosRequest, opts ...grpc.CallOption) (*GetLatestAccountPinInfosResponse, error)
	// RPC checks if a VPA is in NPCI verified merchant list.
	// Verified merchant list is fetched and kept in DB by polling NPCI.
	//
	// Response will be:
	//  1. status OK if merchant is in NPCI verified merchant list.
	//  2. status RECORD_NOT_FOUND if not found in verified merchant list.
	//  3. status INTERNAL if encountered error.
	CheckVerifiedMerchant(ctx context.Context, in *CheckVerifiedMerchantRequest, opts ...grpc.CallOption) (*CheckVerifiedMerchantResponse, error)
	// GetOrCreateAddFundsTransaction RPC is called by centralized orchestrator in order service, for processing
	// order having ADD_FUNDS workflow.
	// The order service takes care of the retrying logic while UPI service acts a domain service
	// of taking care of business logic specific to Add Funds.
	// The API is made idempotent, using the clientRequestId passed by order.
	// If the transaction is in a terminal state, then the corresponding order domain status is returned.
	// Else, it checks the transaction status with the corresponding partner banks and creates entry in transaction
	// schema if entry doesnt exists already.
	GetOrCreateAddFundsTransaction(ctx context.Context, in *domain.ProcessPaymentRequest, opts ...grpc.CallOption) (*domain.ProcessPaymentResponse, error)
	// RPC to get merchant info using VPA
	// Response will be:
	//  1. status OK if merchant info is present for input VPA
	//  2. status RECORD_NOT_FOUND if not found in vpa merchant infos records
	//  3. status INTERNAL if encountered error.
	GetVpaMerchantInfo(ctx context.Context, in *GetVpaMerchantInfoRequest, opts ...grpc.CallOption) (*GetVpaMerchantInfoResponse, error)
	// GetPinInfosByAccountId returns AccountUpiPinInfos belonging to the provided account id.
	// Response will be:
	//  1. status OK if AccountUpiPinInfo fetch was successful
	//  2. INVALID_ARGUMENT if incorrect arguments passed
	//  3. status INTERNAL if encountered error.
	GetPinInfosByAccountId(ctx context.Context, in *GetPinInfosByAccountIdRequest, opts ...grpc.CallOption) (*GetPinInfosByAccountIdResponse, error)
	// An user activity is an action perform on UI by user. Client will post any such activity that need to be tracked from BE
	// can post activity here.
	// For ex: For ETB(existing to bank) user, if user have once seen the message related to UPI pin, we do not want to show this message again.
	// currently message is driven from BE. We will track this activity posted on this RPC and will disable message for that user.
	PostUserActivity(ctx context.Context, in *PostUserActivityRequest, opts ...grpc.CallOption) (*PostUserActivityResponse, error)
	// RPC for Validating secure pin
	// This rpc will forward this request to vendorgateway to NPCI/Federal for validating secure pin and returning
	// the appropriate success/failure response to the client.
	ValidateSecurePin(ctx context.Context, in *ValidateSecurePinRequest, opts ...grpc.CallOption) (*ValidateSecurePinResponse, error)
	// CheckAndUpdateAddFundsCollectPaymentStatus RPC is called by centralized orchestrator in order service, for processing
	// order having ADD_FUNDS_COLLECT workflow.
	// The order service takes care of the retrying logic while UPI service acts a domain service
	// of taking care of business logic specific to Add Funds Collect.
	// The API is made idempotent, using the clientRequestId passed by order.
	// If the transaction is in a terminal state, then the corresponding order domain status is returned.
	// Else, it checks the transaction status with the corresponding partner banks and updates the txn schema in data base
	CheckAndUpdateAddFundsCollectPaymentStatus(ctx context.Context, in *domain.ProcessPaymentRequest, opts ...grpc.CallOption) (*domain.ProcessPaymentResponse, error)
	// RaiseComplaint rpc is initiated by payer to create a complaint
	// request of type COMPLAINT is only applicable to be raised by payer
	// RaiseComplaint rpc is initiated by payer/payee to raise a complaint/dispute on a transaction.
	// Below are the three major reason where a user can possibly raise a complain.
	// 1. Transaction is in failed state and amount deducted. If customer is debited and fund is with Remitter.
	// 2. Transaction is in pending/deemed state. Beneficiary could have received the fund or amount is lying with the Beneficiary Bank
	// 3. In case of success transaction. There is no scope of Customer complaints for P2P transaction.
	// For P2M transaction, complaints can be registered, if goods or
	// services are not delivered or for any other service related issue.
	// On raising complaint underlying vendor involve in transaction will check the issue with transaction and take // proper action to resolve the transaction status.
	// Currently request of type COMPLAINT is only applicable to be raised.
	RaiseComplaint(ctx context.Context, in *RaiseComplaintRequest, opts ...grpc.CallOption) (*RaiseComplaintResponse, error)
	// CheckUpiTxnStatus fetch transaction from payment and check the current status of payment if payment is already in terminal state,
	// it will build response with values from transactions and return.
	// If transaction state is not in terminal state then it will check the current status from vendor by building ReqCheckTxnStatusRequest
	// and return response.
	// Note: It will not do any update for the transaction. It will only get latest status from db or vendor.
	CheckTxnStatus(ctx context.Context, in *CheckTxnStatusRequest, opts ...grpc.CallOption) (*CheckTxnStatusResponse, error)
	// CheckComplaintStatus will fetch the transaction from payment and check forward request to vendor to get the latest status of complaint
	// on transaction (if any). It will return the latest status of complaint by calling vendor.
	// NOTE: It will not validate if any complaint/dispute is created on transaction or not. It is caller responsibility to call this RPC only
	// if complaint/dispute was actually created.
	CheckComplaintStatus(ctx context.Context, in *CheckComplaintStatusRequest, opts ...grpc.CallOption) (*CheckComplaintStatusResponse, error)
	// ChangeUpiPinSetStatus will change upi pin set state to input pin set state.
	// It will also send a background notification to refresh UserSession to fetch the latest status/state of Upi Pin.
	//
	// If a user tries an incorrect UPI PIN/Secure Pin more than thrice consecutively, across different neo-banking APIs
	// across our app (ex - payments, card controls, usage etc) then the UPI PIN is blocked by Federal.
	// It does not get automatically un-blocked after 24hrs and the user needs to Reset UPI PIN to unblock and transact again.
	// Post UPI PIN reset, UPI cool down will also get triggered.
	ChangeUpiPinSetState(ctx context.Context, in *ChangeUpiPinSetStateRequest, opts ...grpc.CallOption) (*ChangeUpiPinSetStateResponse, error)
	// GetVerifiedVpasByPhoneNumber finds all the validate VPA handles corresponding to a given phone number of the form phoneNumber@psp (eg.**********@paytm)
	// NOTE : It will not make call to validate address if the phone number is entered again and the time difference between now and last time it was entered is less than threshold  instead it will fetch vpa from PIs
	GetVerifiedVpasByPhoneNumber(ctx context.Context, in *GetVerifiedVpasByPhoneNumberRequest, opts ...grpc.CallOption) (*GetVerifiedVpasByPhoneNumberResponse, error)
	// Verify if the VPA is valid or not and create a pi
	// Makes call to to vendorgateway which in turn will make call to federal to verify the VPA
	// NOTE: pi will be created even if the vap is invalid
	ValidateAddressAndCreatePi(ctx context.Context, in *ValidateAddressAndCreatePiRequest, opts ...grpc.CallOption) (*ValidateAddressAndCreatePiResponse, error)
	// ValidateUpiNumberAndCreatePi validates the upi number and creates a pi if there is a valid vpa associated with the upi number
	// If the upi number is already validated within a specified time threshold, we will use the same result and not call vendor again
	ValidateUpiNumberAndCreatePi(ctx context.Context, in *ValidateUpiNumberAndCreatePiRequest, opts ...grpc.CallOption) (*ValidateUpiNumberAndCreatePiResponse, error)
	// ValidateInternationalPayment validates the converted INR amount from client with the
	// forex details for the international payment stored in redis cache
	ValidateInternationalPayment(ctx context.Context, in *ValidateInternationalPaymentRequest, opts ...grpc.CallOption) (*ValidateInternationalPaymentResponse, error)
	// GetUpiInternationalQrInfoFromCache -
	//   - is used to get qr info from cache
	//     while initiating international upi transaction
	//   - qr and forex details is cached in upi service and transaction
	//     is initiated in order service, so this rpc will be used by
	//     order service to fetch cached qr and forex details
	GetUpiInternationalQrInfoFromCache(ctx context.Context, in *GetUpiInternationalQrInfoFromCacheRequest, opts ...grpc.CallOption) (*GetUpiInternationalQrInfoFromCacheResponse, error)
	// REFRAIN FROM USING THIS RPC FOR ANY PURPOSE OTHER THAN RISK AS IT HAS
	// POTENTIAL COMPLIANCE IMPLICATIONS. For clarifications, please reach out to UPI team
	//
	// GetVerifiedVpas is a generic RPC which fetches all the valid external VPA handles
	// VPAs are returned on a best effort basis by guessing possible VPAs against identifiers such as mobile number, email
	// - use case is one of actorID, phone-number or email
	// - generic fields like actorID will fetch data according to field mask
	// - field mask is a mandatory field
	// - actor ID and device are mandatory fields and are of the primary user
	// - returns StatusRecordNotFound if no valid address found
	// NOTE : It will only make vendor call if VPA/ PI is absent in our systems
	//
	// If the requirement is to fetch based on both email and phone, pass the actor_id in target_identifier
	// If the requirement is to fetch based on email but email/actor value are not passed in the target_identifier,
	// RPC would fail with Invalid_argument
	GetExternalVpas(ctx context.Context, in *GetExternalVpasRequest, opts ...grpc.CallOption) (*GetExternalVpasResponse, error)
	// GetNPCICLParameters gets the necessary NPCI CL Parameter for the upi flow type in the request
	GetNPCICLParametersV1(ctx context.Context, in *GetNPCICLParametersV1Request, opts ...grpc.CallOption) (*GetNPCICLParametersV1Response, error)
	// IsNewAddFundsVpaEnabledForActor - check if the new add funds vpa is enabled for given actor or not
	IsNewAddFundsVpaEnabledForActor(ctx context.Context, in *IsNewAddFundsVpaEnabledForActorRequest, opts ...grpc.CallOption) (*IsNewAddFundsVpaEnabledForActorResponse, error)
}

type uPIClient struct {
	cc grpc.ClientConnInterface
}

func NewUPIClient(cc grpc.ClientConnInterface) UPIClient {
	return &uPIClient{cc}
}

func (c *uPIClient) VerifyPayeeVPA(ctx context.Context, in *VerifyPayeeVPARequest, opts ...grpc.CallOption) (*VerifyPayeeVPAResponse, error) {
	out := new(VerifyPayeeVPAResponse)
	err := c.cc.Invoke(ctx, UPI_VerifyPayeeVPA_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetToken(ctx context.Context, in *GetTokenRequest, opts ...grpc.CallOption) (*GetTokenResponse, error) {
	out := new(GetTokenResponse)
	err := c.cc.Invoke(ctx, UPI_GetToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GenerateUpiOtp(ctx context.Context, in *GenerateUpiOtpRequest, opts ...grpc.CallOption) (*GenerateUpiOtpResponse, error) {
	out := new(GenerateUpiOtpResponse)
	err := c.cc.Invoke(ctx, UPI_GenerateUpiOtp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) ResolveAccount(ctx context.Context, in *ResolveAccountRequest, opts ...grpc.CallOption) (*ResolveAccountResponse, error) {
	out := new(ResolveAccountResponse)
	err := c.cc.Invoke(ctx, UPI_ResolveAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) CreateVPA(ctx context.Context, in *CreateVPARequest, opts ...grpc.CallOption) (*CreateVPAResponse, error) {
	out := new(CreateVPAResponse)
	err := c.cc.Invoke(ctx, UPI_CreateVPA_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) ListKeys(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ListKeysResponse, error) {
	out := new(ListKeysResponse)
	err := c.cc.Invoke(ctx, UPI_ListKeys_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) RegisterMobile(ctx context.Context, in *RegisterMobileRequest, opts ...grpc.CallOption) (*RegisterMobileResponse, error) {
	out := new(RegisterMobileResponse)
	err := c.cc.Invoke(ctx, UPI_RegisterMobile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetPinFlowParameters(ctx context.Context, in *GetPinFlowParametersRequest, opts ...grpc.CallOption) (*GetPinFlowParametersResponse, error) {
	out := new(GetPinFlowParametersResponse)
	err := c.cc.Invoke(ctx, UPI_GetPinFlowParameters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetTransactionParameters(ctx context.Context, in *GetTransactionParametersRequest, opts ...grpc.CallOption) (*GetTransactionParametersResponse, error) {
	out := new(GetTransactionParametersResponse)
	err := c.cc.Invoke(ctx, UPI_GetTransactionParameters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) VerifyURN(ctx context.Context, in *VerifyURNRequest, opts ...grpc.CallOption) (*VerifyURNResponse, error) {
	out := new(VerifyURNResponse)
	err := c.cc.Invoke(ctx, UPI_VerifyURN_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) ChangePin(ctx context.Context, in *ChangePinRequest, opts ...grpc.CallOption) (*ChangePinResponse, error) {
	out := new(ChangePinResponse)
	err := c.cc.Invoke(ctx, UPI_ChangePin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GenerateURN(ctx context.Context, in *GenerateURNRequest, opts ...grpc.CallOption) (*GenerateURNResponse, error) {
	out := new(GenerateURNResponse)
	err := c.cc.Invoke(ctx, UPI_GenerateURN_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetUpiSetupStatus(ctx context.Context, in *GetUpiSetupStatusRequest, opts ...grpc.CallOption) (*GetUpiSetupStatusResponse, error) {
	out := new(GetUpiSetupStatusResponse)
	err := c.cc.Invoke(ctx, UPI_GetUpiSetupStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetOrCreateURNTransaction(ctx context.Context, in *domain.ProcessPaymentRequest, opts ...grpc.CallOption) (*domain.ProcessPaymentResponse, error) {
	out := new(domain.ProcessPaymentResponse)
	err := c.cc.Invoke(ctx, UPI_GetOrCreateURNTransaction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) DisableOrEnableVPA(ctx context.Context, in *DisableOrEnableVPARequest, opts ...grpc.CallOption) (*DisableOrEnableVPAResponse, error) {
	out := new(DisableOrEnableVPAResponse)
	err := c.cc.Invoke(ctx, UPI_DisableOrEnableVPA_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetPinStatus(ctx context.Context, in *PinStatusRequest, opts ...grpc.CallOption) (*PinStatusResponse, error) {
	out := new(PinStatusResponse)
	err := c.cc.Invoke(ctx, UPI_GetPinStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetAccountPinInfos(ctx context.Context, in *GetAccountPinInfosRequest, opts ...grpc.CallOption) (*GetAccountPinInfosResponse, error) {
	out := new(GetAccountPinInfosResponse)
	err := c.cc.Invoke(ctx, UPI_GetAccountPinInfos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetLatestAccountPinInfos(ctx context.Context, in *GetLatestAccountPinInfosRequest, opts ...grpc.CallOption) (*GetLatestAccountPinInfosResponse, error) {
	out := new(GetLatestAccountPinInfosResponse)
	err := c.cc.Invoke(ctx, UPI_GetLatestAccountPinInfos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) CheckVerifiedMerchant(ctx context.Context, in *CheckVerifiedMerchantRequest, opts ...grpc.CallOption) (*CheckVerifiedMerchantResponse, error) {
	out := new(CheckVerifiedMerchantResponse)
	err := c.cc.Invoke(ctx, UPI_CheckVerifiedMerchant_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetOrCreateAddFundsTransaction(ctx context.Context, in *domain.ProcessPaymentRequest, opts ...grpc.CallOption) (*domain.ProcessPaymentResponse, error) {
	out := new(domain.ProcessPaymentResponse)
	err := c.cc.Invoke(ctx, UPI_GetOrCreateAddFundsTransaction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetVpaMerchantInfo(ctx context.Context, in *GetVpaMerchantInfoRequest, opts ...grpc.CallOption) (*GetVpaMerchantInfoResponse, error) {
	out := new(GetVpaMerchantInfoResponse)
	err := c.cc.Invoke(ctx, UPI_GetVpaMerchantInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetPinInfosByAccountId(ctx context.Context, in *GetPinInfosByAccountIdRequest, opts ...grpc.CallOption) (*GetPinInfosByAccountIdResponse, error) {
	out := new(GetPinInfosByAccountIdResponse)
	err := c.cc.Invoke(ctx, UPI_GetPinInfosByAccountId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) PostUserActivity(ctx context.Context, in *PostUserActivityRequest, opts ...grpc.CallOption) (*PostUserActivityResponse, error) {
	out := new(PostUserActivityResponse)
	err := c.cc.Invoke(ctx, UPI_PostUserActivity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) ValidateSecurePin(ctx context.Context, in *ValidateSecurePinRequest, opts ...grpc.CallOption) (*ValidateSecurePinResponse, error) {
	out := new(ValidateSecurePinResponse)
	err := c.cc.Invoke(ctx, UPI_ValidateSecurePin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) CheckAndUpdateAddFundsCollectPaymentStatus(ctx context.Context, in *domain.ProcessPaymentRequest, opts ...grpc.CallOption) (*domain.ProcessPaymentResponse, error) {
	out := new(domain.ProcessPaymentResponse)
	err := c.cc.Invoke(ctx, UPI_CheckAndUpdateAddFundsCollectPaymentStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) RaiseComplaint(ctx context.Context, in *RaiseComplaintRequest, opts ...grpc.CallOption) (*RaiseComplaintResponse, error) {
	out := new(RaiseComplaintResponse)
	err := c.cc.Invoke(ctx, UPI_RaiseComplaint_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) CheckTxnStatus(ctx context.Context, in *CheckTxnStatusRequest, opts ...grpc.CallOption) (*CheckTxnStatusResponse, error) {
	out := new(CheckTxnStatusResponse)
	err := c.cc.Invoke(ctx, UPI_CheckTxnStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) CheckComplaintStatus(ctx context.Context, in *CheckComplaintStatusRequest, opts ...grpc.CallOption) (*CheckComplaintStatusResponse, error) {
	out := new(CheckComplaintStatusResponse)
	err := c.cc.Invoke(ctx, UPI_CheckComplaintStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) ChangeUpiPinSetState(ctx context.Context, in *ChangeUpiPinSetStateRequest, opts ...grpc.CallOption) (*ChangeUpiPinSetStateResponse, error) {
	out := new(ChangeUpiPinSetStateResponse)
	err := c.cc.Invoke(ctx, UPI_ChangeUpiPinSetState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetVerifiedVpasByPhoneNumber(ctx context.Context, in *GetVerifiedVpasByPhoneNumberRequest, opts ...grpc.CallOption) (*GetVerifiedVpasByPhoneNumberResponse, error) {
	out := new(GetVerifiedVpasByPhoneNumberResponse)
	err := c.cc.Invoke(ctx, UPI_GetVerifiedVpasByPhoneNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) ValidateAddressAndCreatePi(ctx context.Context, in *ValidateAddressAndCreatePiRequest, opts ...grpc.CallOption) (*ValidateAddressAndCreatePiResponse, error) {
	out := new(ValidateAddressAndCreatePiResponse)
	err := c.cc.Invoke(ctx, UPI_ValidateAddressAndCreatePi_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) ValidateUpiNumberAndCreatePi(ctx context.Context, in *ValidateUpiNumberAndCreatePiRequest, opts ...grpc.CallOption) (*ValidateUpiNumberAndCreatePiResponse, error) {
	out := new(ValidateUpiNumberAndCreatePiResponse)
	err := c.cc.Invoke(ctx, UPI_ValidateUpiNumberAndCreatePi_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) ValidateInternationalPayment(ctx context.Context, in *ValidateInternationalPaymentRequest, opts ...grpc.CallOption) (*ValidateInternationalPaymentResponse, error) {
	out := new(ValidateInternationalPaymentResponse)
	err := c.cc.Invoke(ctx, UPI_ValidateInternationalPayment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetUpiInternationalQrInfoFromCache(ctx context.Context, in *GetUpiInternationalQrInfoFromCacheRequest, opts ...grpc.CallOption) (*GetUpiInternationalQrInfoFromCacheResponse, error) {
	out := new(GetUpiInternationalQrInfoFromCacheResponse)
	err := c.cc.Invoke(ctx, UPI_GetUpiInternationalQrInfoFromCache_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetExternalVpas(ctx context.Context, in *GetExternalVpasRequest, opts ...grpc.CallOption) (*GetExternalVpasResponse, error) {
	out := new(GetExternalVpasResponse)
	err := c.cc.Invoke(ctx, UPI_GetExternalVpas_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) GetNPCICLParametersV1(ctx context.Context, in *GetNPCICLParametersV1Request, opts ...grpc.CallOption) (*GetNPCICLParametersV1Response, error) {
	out := new(GetNPCICLParametersV1Response)
	err := c.cc.Invoke(ctx, UPI_GetNPCICLParametersV1_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *uPIClient) IsNewAddFundsVpaEnabledForActor(ctx context.Context, in *IsNewAddFundsVpaEnabledForActorRequest, opts ...grpc.CallOption) (*IsNewAddFundsVpaEnabledForActorResponse, error) {
	out := new(IsNewAddFundsVpaEnabledForActorResponse)
	err := c.cc.Invoke(ctx, UPI_IsNewAddFundsVpaEnabledForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UPIServer is the server API for UPI service.
// All implementations should embed UnimplementedUPIServer
// for forward compatibility
type UPIServer interface {
	// Verify if the VPA is valid or not
	// Makes call to to vendorgateway which in turn will make call to federal to verify the VPA
	// This will be used when a customer wants to add a upi beneficiary for sending/collecting money
	VerifyPayeeVPA(context.Context, *VerifyPayeeVPARequest) (*VerifyPayeeVPAResponse, error)
	// Returns the token to be used for registering the app to use NPCI common library
	// The registration flow is as follows
	//  1. Execute “Get Challenge” service to receive a challenge from common library.
	//  2. Use the generated challenge to get the token from PSP server
	//  3. Use the token to register the app with common library
	GetToken(context.Context, *GetTokenRequest) (*GetTokenResponse, error)
	// Used to request OTP from issuer bank via UPI for ATM PIN validation
	// Returns secure_url for redirection to bank page for OTP validation
	GenerateUpiOtp(context.Context, *GenerateUpiOtpRequest) (*GenerateUpiOtpResponse, error)
	// RPC to to resolve account for a given VPA.
	// returns obfuscated account details and ifsc belonging to the VPA.
	// This RPC is used as in multiple places
	// 1) to resolve account information for payer in case of PAY request
	// 2) to resolve account information for payee in case of COLLECT request
	// 3) to resolve account information when `ReqAuth` is received.
	ResolveAccount(context.Context, *ResolveAccountRequest) (*ResolveAccountResponse, error)
	// RPC to create virtual Id for account
	// Below are the steps followed by this RPC:
	//  1. ListAccount to get the account info like masked account number, account reference number etc.
	//  2. Make call to CreateVirtualId on vendorgateway to create VPA
	//  3. Create PI for VPA
	//  4. Create account for PI for VPA
	//
	// This RPC is idempotent i.e. on each retry on create vpa for a account Id,
	// it will get previous executed steps and resume create vpa from there
	CreateVPA(context.Context, *CreateVPARequest) (*CreateVPAResponse, error)
	// This RPC allows to fetch all encryption public keys from NPCI.
	// It will be cached in UPI service in first call (in memory).
	// Cached data will be returned in subsequent call.
	// These public keys will be used to capture sensitive data like card digits, expiry
	// These keys will be used by CL (Common Library) and
	// are to be passed by PSP app to CL as parameter to GetCredentials API
	ListKeys(context.Context, *emptypb.Empty) (*ListKeysResponse, error)
	// RPC for Mobile Banking Registration
	// This API allows the customer to set new UPI PIN for the first time and also can be used to reset pin.
	// This rpc will forward this request to vendorgateway to NPCI/Federal.
	//
	// This rpc will fetch actor's debit card details and send those details to vendor for register mobile API.
	// Card details will be tokenized. Tokens will be replaced in Vendorgateway with actual card details.
	RegisterMobile(context.Context, *RegisterMobileRequest) (*RegisterMobileResponse, error)
	// This RPC helps the client app to fetch the encryption keys,
	// allowed_cred json associated with the account
	//
	// This data will enable the client app to get the credentials that are required to
	// process a Mobile Banking registration (or) Set Credentials request with the
	// help of "Get Credential Service" of NPCI CL
	GetPinFlowParameters(context.Context, *GetPinFlowParametersRequest) (*GetPinFlowParametersResponse, error)
	// This RPC helps the client app to fetch the encryption keys,
	// allowed_cred json required for transaction associated with the account, bank config and other info appear on
	// UPI PIN page.
	//
	// This data will enable the client app to get the credentials that are required to
	// process a UPI request with the help of "Get Credential Service" of NPCI CL
	GetTransactionParameters(context.Context, *GetTransactionParametersRequest) (*GetTransactionParametersResponse, error)
	// This rpc verifies the urn signature and returns parsed values from URN
	// For QR/Intent based payments we need to verify the signature of the urn received from the merchant system
	//
	// Checks for the mandatory fields in the urn, if any of the mandatory fields are missing will return status INVALID_ARGUMENT
	//
	// Process for signature verification
	// 1) Extract the signature from the URN
	// 2) Search for the public key from ListVAE or ListKeys cache based on the OrgId and VPA.
	// 3) Verify the signature using the public key and remaining URN as digest
	//
	// If the signature is valid rpc will parse the urn for relevant fields like txnId, merchant_refId etc.
	// and will return those fields in the response
	VerifyURN(context.Context, *VerifyURNRequest) (*VerifyURNResponse, error)
	// Secure PIN a.k.a UPI PIN will be used to authenticate all modes of transactions
	// i.e., IMPS, RTGS, NEFT, UPI.
	//
	// This would enhance the user’s experience by using the same PIN for all transactions.
	//
	// # This RPC helps to change Secure PIN for the given account
	//
	// Note that Secure PIN is against an account and not a UPI VPA
	ChangePin(context.Context, *ChangePinRequest) (*ChangePinResponse, error)
	// GenerateURN returns the urn for outgoing intent/QR based payments
	//
	// It first creates an order with URN_TRANSFER workflow and then generate URN.
	// Generated urn contains parameter such as payeeVPA, amount, txnId, refId etc and
	// is be signed by the PSP's key.
	//
	// It returns corresponding orderId and URN in response.
	GenerateURN(context.Context, *GenerateURNRequest) (*GenerateURNResponse, error)
	// RPC to get the upi creation status of account id.
	GetUpiSetupStatus(context.Context, *GetUpiSetupStatusRequest) (*GetUpiSetupStatusResponse, error)
	// GetOrCreateURNTransaction RPC is called by centralized orchestrator in order service, for processing
	// order having URN_TRANSFER workflow.
	// The order service takes care of the retrying logic while UPI service acts a domain service
	// of taking care of business logic specific to URN transfer.
	// The API is made idempotent, using the clientRequestId passed by order.
	// If the transaction is in a terminal state, then the corresponding order domain status is returned.
	// Else, it checks the transaction status with the corresponding partner banks and creates entry in transaction
	// schema if entry doesnt exists already.
	GetOrCreateURNTransaction(context.Context, *domain.ProcessPaymentRequest) (*domain.ProcessPaymentResponse, error)
	// rpc to enable/disable vpa based on the request type
	// request type enable - pi state will be marked as created
	// request type disable - pi state will be marked as suspend
	DisableOrEnableVPA(context.Context, *DisableOrEnableVPARequest) (*DisableOrEnableVPAResponse, error)
	// Rpc to get the status of upi pin set state.
	// Returns PinSetState against accountId in map
	//
	// Queries the account upi_account_infos table and check pin set state. If pin set state is PIN_NOT_SET
	// or REOOBE_PIN_NOT_SET then it will fetch latest PIN_SET_STATE from vendor.
	//
	// NOTE - returns internal server error if there is error while fetching pin status for one or more accountId
	GetPinStatus(context.Context, *PinStatusRequest) (*PinStatusResponse, error)
	// rpc to get the list of account upi pin infos for the given accountId
	// returns status record not found if no entry is present for the account id
	GetAccountPinInfos(context.Context, *GetAccountPinInfosRequest) (*GetAccountPinInfosResponse, error)
	// rpc to get the list of most recent account upi pin infos for the given list of accountIds
	// returns status record not found if no entry is present for the account id
	GetLatestAccountPinInfos(context.Context, *GetLatestAccountPinInfosRequest) (*GetLatestAccountPinInfosResponse, error)
	// RPC checks if a VPA is in NPCI verified merchant list.
	// Verified merchant list is fetched and kept in DB by polling NPCI.
	//
	// Response will be:
	//  1. status OK if merchant is in NPCI verified merchant list.
	//  2. status RECORD_NOT_FOUND if not found in verified merchant list.
	//  3. status INTERNAL if encountered error.
	CheckVerifiedMerchant(context.Context, *CheckVerifiedMerchantRequest) (*CheckVerifiedMerchantResponse, error)
	// GetOrCreateAddFundsTransaction RPC is called by centralized orchestrator in order service, for processing
	// order having ADD_FUNDS workflow.
	// The order service takes care of the retrying logic while UPI service acts a domain service
	// of taking care of business logic specific to Add Funds.
	// The API is made idempotent, using the clientRequestId passed by order.
	// If the transaction is in a terminal state, then the corresponding order domain status is returned.
	// Else, it checks the transaction status with the corresponding partner banks and creates entry in transaction
	// schema if entry doesnt exists already.
	GetOrCreateAddFundsTransaction(context.Context, *domain.ProcessPaymentRequest) (*domain.ProcessPaymentResponse, error)
	// RPC to get merchant info using VPA
	// Response will be:
	//  1. status OK if merchant info is present for input VPA
	//  2. status RECORD_NOT_FOUND if not found in vpa merchant infos records
	//  3. status INTERNAL if encountered error.
	GetVpaMerchantInfo(context.Context, *GetVpaMerchantInfoRequest) (*GetVpaMerchantInfoResponse, error)
	// GetPinInfosByAccountId returns AccountUpiPinInfos belonging to the provided account id.
	// Response will be:
	//  1. status OK if AccountUpiPinInfo fetch was successful
	//  2. INVALID_ARGUMENT if incorrect arguments passed
	//  3. status INTERNAL if encountered error.
	GetPinInfosByAccountId(context.Context, *GetPinInfosByAccountIdRequest) (*GetPinInfosByAccountIdResponse, error)
	// An user activity is an action perform on UI by user. Client will post any such activity that need to be tracked from BE
	// can post activity here.
	// For ex: For ETB(existing to bank) user, if user have once seen the message related to UPI pin, we do not want to show this message again.
	// currently message is driven from BE. We will track this activity posted on this RPC and will disable message for that user.
	PostUserActivity(context.Context, *PostUserActivityRequest) (*PostUserActivityResponse, error)
	// RPC for Validating secure pin
	// This rpc will forward this request to vendorgateway to NPCI/Federal for validating secure pin and returning
	// the appropriate success/failure response to the client.
	ValidateSecurePin(context.Context, *ValidateSecurePinRequest) (*ValidateSecurePinResponse, error)
	// CheckAndUpdateAddFundsCollectPaymentStatus RPC is called by centralized orchestrator in order service, for processing
	// order having ADD_FUNDS_COLLECT workflow.
	// The order service takes care of the retrying logic while UPI service acts a domain service
	// of taking care of business logic specific to Add Funds Collect.
	// The API is made idempotent, using the clientRequestId passed by order.
	// If the transaction is in a terminal state, then the corresponding order domain status is returned.
	// Else, it checks the transaction status with the corresponding partner banks and updates the txn schema in data base
	CheckAndUpdateAddFundsCollectPaymentStatus(context.Context, *domain.ProcessPaymentRequest) (*domain.ProcessPaymentResponse, error)
	// RaiseComplaint rpc is initiated by payer to create a complaint
	// request of type COMPLAINT is only applicable to be raised by payer
	// RaiseComplaint rpc is initiated by payer/payee to raise a complaint/dispute on a transaction.
	// Below are the three major reason where a user can possibly raise a complain.
	// 1. Transaction is in failed state and amount deducted. If customer is debited and fund is with Remitter.
	// 2. Transaction is in pending/deemed state. Beneficiary could have received the fund or amount is lying with the Beneficiary Bank
	// 3. In case of success transaction. There is no scope of Customer complaints for P2P transaction.
	// For P2M transaction, complaints can be registered, if goods or
	// services are not delivered or for any other service related issue.
	// On raising complaint underlying vendor involve in transaction will check the issue with transaction and take // proper action to resolve the transaction status.
	// Currently request of type COMPLAINT is only applicable to be raised.
	RaiseComplaint(context.Context, *RaiseComplaintRequest) (*RaiseComplaintResponse, error)
	// CheckUpiTxnStatus fetch transaction from payment and check the current status of payment if payment is already in terminal state,
	// it will build response with values from transactions and return.
	// If transaction state is not in terminal state then it will check the current status from vendor by building ReqCheckTxnStatusRequest
	// and return response.
	// Note: It will not do any update for the transaction. It will only get latest status from db or vendor.
	CheckTxnStatus(context.Context, *CheckTxnStatusRequest) (*CheckTxnStatusResponse, error)
	// CheckComplaintStatus will fetch the transaction from payment and check forward request to vendor to get the latest status of complaint
	// on transaction (if any). It will return the latest status of complaint by calling vendor.
	// NOTE: It will not validate if any complaint/dispute is created on transaction or not. It is caller responsibility to call this RPC only
	// if complaint/dispute was actually created.
	CheckComplaintStatus(context.Context, *CheckComplaintStatusRequest) (*CheckComplaintStatusResponse, error)
	// ChangeUpiPinSetStatus will change upi pin set state to input pin set state.
	// It will also send a background notification to refresh UserSession to fetch the latest status/state of Upi Pin.
	//
	// If a user tries an incorrect UPI PIN/Secure Pin more than thrice consecutively, across different neo-banking APIs
	// across our app (ex - payments, card controls, usage etc) then the UPI PIN is blocked by Federal.
	// It does not get automatically un-blocked after 24hrs and the user needs to Reset UPI PIN to unblock and transact again.
	// Post UPI PIN reset, UPI cool down will also get triggered.
	ChangeUpiPinSetState(context.Context, *ChangeUpiPinSetStateRequest) (*ChangeUpiPinSetStateResponse, error)
	// GetVerifiedVpasByPhoneNumber finds all the validate VPA handles corresponding to a given phone number of the form phoneNumber@psp (eg.**********@paytm)
	// NOTE : It will not make call to validate address if the phone number is entered again and the time difference between now and last time it was entered is less than threshold  instead it will fetch vpa from PIs
	GetVerifiedVpasByPhoneNumber(context.Context, *GetVerifiedVpasByPhoneNumberRequest) (*GetVerifiedVpasByPhoneNumberResponse, error)
	// Verify if the VPA is valid or not and create a pi
	// Makes call to to vendorgateway which in turn will make call to federal to verify the VPA
	// NOTE: pi will be created even if the vap is invalid
	ValidateAddressAndCreatePi(context.Context, *ValidateAddressAndCreatePiRequest) (*ValidateAddressAndCreatePiResponse, error)
	// ValidateUpiNumberAndCreatePi validates the upi number and creates a pi if there is a valid vpa associated with the upi number
	// If the upi number is already validated within a specified time threshold, we will use the same result and not call vendor again
	ValidateUpiNumberAndCreatePi(context.Context, *ValidateUpiNumberAndCreatePiRequest) (*ValidateUpiNumberAndCreatePiResponse, error)
	// ValidateInternationalPayment validates the converted INR amount from client with the
	// forex details for the international payment stored in redis cache
	ValidateInternationalPayment(context.Context, *ValidateInternationalPaymentRequest) (*ValidateInternationalPaymentResponse, error)
	// GetUpiInternationalQrInfoFromCache -
	//   - is used to get qr info from cache
	//     while initiating international upi transaction
	//   - qr and forex details is cached in upi service and transaction
	//     is initiated in order service, so this rpc will be used by
	//     order service to fetch cached qr and forex details
	GetUpiInternationalQrInfoFromCache(context.Context, *GetUpiInternationalQrInfoFromCacheRequest) (*GetUpiInternationalQrInfoFromCacheResponse, error)
	// REFRAIN FROM USING THIS RPC FOR ANY PURPOSE OTHER THAN RISK AS IT HAS
	// POTENTIAL COMPLIANCE IMPLICATIONS. For clarifications, please reach out to UPI team
	//
	// GetVerifiedVpas is a generic RPC which fetches all the valid external VPA handles
	// VPAs are returned on a best effort basis by guessing possible VPAs against identifiers such as mobile number, email
	// - use case is one of actorID, phone-number or email
	// - generic fields like actorID will fetch data according to field mask
	// - field mask is a mandatory field
	// - actor ID and device are mandatory fields and are of the primary user
	// - returns StatusRecordNotFound if no valid address found
	// NOTE : It will only make vendor call if VPA/ PI is absent in our systems
	//
	// If the requirement is to fetch based on both email and phone, pass the actor_id in target_identifier
	// If the requirement is to fetch based on email but email/actor value are not passed in the target_identifier,
	// RPC would fail with Invalid_argument
	GetExternalVpas(context.Context, *GetExternalVpasRequest) (*GetExternalVpasResponse, error)
	// GetNPCICLParameters gets the necessary NPCI CL Parameter for the upi flow type in the request
	GetNPCICLParametersV1(context.Context, *GetNPCICLParametersV1Request) (*GetNPCICLParametersV1Response, error)
	// IsNewAddFundsVpaEnabledForActor - check if the new add funds vpa is enabled for given actor or not
	IsNewAddFundsVpaEnabledForActor(context.Context, *IsNewAddFundsVpaEnabledForActorRequest) (*IsNewAddFundsVpaEnabledForActorResponse, error)
}

// UnimplementedUPIServer should be embedded to have forward compatible implementations.
type UnimplementedUPIServer struct {
}

func (UnimplementedUPIServer) VerifyPayeeVPA(context.Context, *VerifyPayeeVPARequest) (*VerifyPayeeVPAResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyPayeeVPA not implemented")
}
func (UnimplementedUPIServer) GetToken(context.Context, *GetTokenRequest) (*GetTokenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetToken not implemented")
}
func (UnimplementedUPIServer) GenerateUpiOtp(context.Context, *GenerateUpiOtpRequest) (*GenerateUpiOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateUpiOtp not implemented")
}
func (UnimplementedUPIServer) ResolveAccount(context.Context, *ResolveAccountRequest) (*ResolveAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResolveAccount not implemented")
}
func (UnimplementedUPIServer) CreateVPA(context.Context, *CreateVPARequest) (*CreateVPAResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVPA not implemented")
}
func (UnimplementedUPIServer) ListKeys(context.Context, *emptypb.Empty) (*ListKeysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKeys not implemented")
}
func (UnimplementedUPIServer) RegisterMobile(context.Context, *RegisterMobileRequest) (*RegisterMobileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterMobile not implemented")
}
func (UnimplementedUPIServer) GetPinFlowParameters(context.Context, *GetPinFlowParametersRequest) (*GetPinFlowParametersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPinFlowParameters not implemented")
}
func (UnimplementedUPIServer) GetTransactionParameters(context.Context, *GetTransactionParametersRequest) (*GetTransactionParametersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionParameters not implemented")
}
func (UnimplementedUPIServer) VerifyURN(context.Context, *VerifyURNRequest) (*VerifyURNResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyURN not implemented")
}
func (UnimplementedUPIServer) ChangePin(context.Context, *ChangePinRequest) (*ChangePinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangePin not implemented")
}
func (UnimplementedUPIServer) GenerateURN(context.Context, *GenerateURNRequest) (*GenerateURNResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateURN not implemented")
}
func (UnimplementedUPIServer) GetUpiSetupStatus(context.Context, *GetUpiSetupStatusRequest) (*GetUpiSetupStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpiSetupStatus not implemented")
}
func (UnimplementedUPIServer) GetOrCreateURNTransaction(context.Context, *domain.ProcessPaymentRequest) (*domain.ProcessPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrCreateURNTransaction not implemented")
}
func (UnimplementedUPIServer) DisableOrEnableVPA(context.Context, *DisableOrEnableVPARequest) (*DisableOrEnableVPAResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DisableOrEnableVPA not implemented")
}
func (UnimplementedUPIServer) GetPinStatus(context.Context, *PinStatusRequest) (*PinStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPinStatus not implemented")
}
func (UnimplementedUPIServer) GetAccountPinInfos(context.Context, *GetAccountPinInfosRequest) (*GetAccountPinInfosResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountPinInfos not implemented")
}
func (UnimplementedUPIServer) GetLatestAccountPinInfos(context.Context, *GetLatestAccountPinInfosRequest) (*GetLatestAccountPinInfosResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestAccountPinInfos not implemented")
}
func (UnimplementedUPIServer) CheckVerifiedMerchant(context.Context, *CheckVerifiedMerchantRequest) (*CheckVerifiedMerchantResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckVerifiedMerchant not implemented")
}
func (UnimplementedUPIServer) GetOrCreateAddFundsTransaction(context.Context, *domain.ProcessPaymentRequest) (*domain.ProcessPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrCreateAddFundsTransaction not implemented")
}
func (UnimplementedUPIServer) GetVpaMerchantInfo(context.Context, *GetVpaMerchantInfoRequest) (*GetVpaMerchantInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVpaMerchantInfo not implemented")
}
func (UnimplementedUPIServer) GetPinInfosByAccountId(context.Context, *GetPinInfosByAccountIdRequest) (*GetPinInfosByAccountIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPinInfosByAccountId not implemented")
}
func (UnimplementedUPIServer) PostUserActivity(context.Context, *PostUserActivityRequest) (*PostUserActivityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PostUserActivity not implemented")
}
func (UnimplementedUPIServer) ValidateSecurePin(context.Context, *ValidateSecurePinRequest) (*ValidateSecurePinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateSecurePin not implemented")
}
func (UnimplementedUPIServer) CheckAndUpdateAddFundsCollectPaymentStatus(context.Context, *domain.ProcessPaymentRequest) (*domain.ProcessPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAndUpdateAddFundsCollectPaymentStatus not implemented")
}
func (UnimplementedUPIServer) RaiseComplaint(context.Context, *RaiseComplaintRequest) (*RaiseComplaintResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RaiseComplaint not implemented")
}
func (UnimplementedUPIServer) CheckTxnStatus(context.Context, *CheckTxnStatusRequest) (*CheckTxnStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTxnStatus not implemented")
}
func (UnimplementedUPIServer) CheckComplaintStatus(context.Context, *CheckComplaintStatusRequest) (*CheckComplaintStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckComplaintStatus not implemented")
}
func (UnimplementedUPIServer) ChangeUpiPinSetState(context.Context, *ChangeUpiPinSetStateRequest) (*ChangeUpiPinSetStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeUpiPinSetState not implemented")
}
func (UnimplementedUPIServer) GetVerifiedVpasByPhoneNumber(context.Context, *GetVerifiedVpasByPhoneNumberRequest) (*GetVerifiedVpasByPhoneNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVerifiedVpasByPhoneNumber not implemented")
}
func (UnimplementedUPIServer) ValidateAddressAndCreatePi(context.Context, *ValidateAddressAndCreatePiRequest) (*ValidateAddressAndCreatePiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateAddressAndCreatePi not implemented")
}
func (UnimplementedUPIServer) ValidateUpiNumberAndCreatePi(context.Context, *ValidateUpiNumberAndCreatePiRequest) (*ValidateUpiNumberAndCreatePiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateUpiNumberAndCreatePi not implemented")
}
func (UnimplementedUPIServer) ValidateInternationalPayment(context.Context, *ValidateInternationalPaymentRequest) (*ValidateInternationalPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateInternationalPayment not implemented")
}
func (UnimplementedUPIServer) GetUpiInternationalQrInfoFromCache(context.Context, *GetUpiInternationalQrInfoFromCacheRequest) (*GetUpiInternationalQrInfoFromCacheResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpiInternationalQrInfoFromCache not implemented")
}
func (UnimplementedUPIServer) GetExternalVpas(context.Context, *GetExternalVpasRequest) (*GetExternalVpasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExternalVpas not implemented")
}
func (UnimplementedUPIServer) GetNPCICLParametersV1(context.Context, *GetNPCICLParametersV1Request) (*GetNPCICLParametersV1Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNPCICLParametersV1 not implemented")
}
func (UnimplementedUPIServer) IsNewAddFundsVpaEnabledForActor(context.Context, *IsNewAddFundsVpaEnabledForActorRequest) (*IsNewAddFundsVpaEnabledForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsNewAddFundsVpaEnabledForActor not implemented")
}

// UnsafeUPIServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UPIServer will
// result in compilation errors.
type UnsafeUPIServer interface {
	mustEmbedUnimplementedUPIServer()
}

func RegisterUPIServer(s grpc.ServiceRegistrar, srv UPIServer) {
	s.RegisterService(&UPI_ServiceDesc, srv)
}

func _UPI_VerifyPayeeVPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyPayeeVPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).VerifyPayeeVPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_VerifyPayeeVPA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).VerifyPayeeVPA(ctx, req.(*VerifyPayeeVPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetToken(ctx, req.(*GetTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GenerateUpiOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateUpiOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GenerateUpiOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GenerateUpiOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GenerateUpiOtp(ctx, req.(*GenerateUpiOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_ResolveAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResolveAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).ResolveAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_ResolveAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).ResolveAccount(ctx, req.(*ResolveAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_CreateVPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).CreateVPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_CreateVPA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).CreateVPA(ctx, req.(*CreateVPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_ListKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).ListKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_ListKeys_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).ListKeys(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_RegisterMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterMobileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).RegisterMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_RegisterMobile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).RegisterMobile(ctx, req.(*RegisterMobileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetPinFlowParameters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPinFlowParametersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetPinFlowParameters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetPinFlowParameters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetPinFlowParameters(ctx, req.(*GetPinFlowParametersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetTransactionParameters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTransactionParametersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetTransactionParameters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetTransactionParameters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetTransactionParameters(ctx, req.(*GetTransactionParametersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_VerifyURN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyURNRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).VerifyURN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_VerifyURN_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).VerifyURN(ctx, req.(*VerifyURNRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_ChangePin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangePinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).ChangePin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_ChangePin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).ChangePin(ctx, req.(*ChangePinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GenerateURN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateURNRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GenerateURN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GenerateURN_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GenerateURN(ctx, req.(*GenerateURNRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetUpiSetupStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpiSetupStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetUpiSetupStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetUpiSetupStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetUpiSetupStatus(ctx, req.(*GetUpiSetupStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetOrCreateURNTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(domain.ProcessPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetOrCreateURNTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetOrCreateURNTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetOrCreateURNTransaction(ctx, req.(*domain.ProcessPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_DisableOrEnableVPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DisableOrEnableVPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).DisableOrEnableVPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_DisableOrEnableVPA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).DisableOrEnableVPA(ctx, req.(*DisableOrEnableVPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetPinStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PinStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetPinStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetPinStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetPinStatus(ctx, req.(*PinStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetAccountPinInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountPinInfosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetAccountPinInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetAccountPinInfos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetAccountPinInfos(ctx, req.(*GetAccountPinInfosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetLatestAccountPinInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestAccountPinInfosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetLatestAccountPinInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetLatestAccountPinInfos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetLatestAccountPinInfos(ctx, req.(*GetLatestAccountPinInfosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_CheckVerifiedMerchant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVerifiedMerchantRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).CheckVerifiedMerchant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_CheckVerifiedMerchant_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).CheckVerifiedMerchant(ctx, req.(*CheckVerifiedMerchantRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetOrCreateAddFundsTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(domain.ProcessPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetOrCreateAddFundsTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetOrCreateAddFundsTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetOrCreateAddFundsTransaction(ctx, req.(*domain.ProcessPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetVpaMerchantInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVpaMerchantInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetVpaMerchantInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetVpaMerchantInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetVpaMerchantInfo(ctx, req.(*GetVpaMerchantInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetPinInfosByAccountId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPinInfosByAccountIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetPinInfosByAccountId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetPinInfosByAccountId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetPinInfosByAccountId(ctx, req.(*GetPinInfosByAccountIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_PostUserActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostUserActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).PostUserActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_PostUserActivity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).PostUserActivity(ctx, req.(*PostUserActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_ValidateSecurePin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateSecurePinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).ValidateSecurePin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_ValidateSecurePin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).ValidateSecurePin(ctx, req.(*ValidateSecurePinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_CheckAndUpdateAddFundsCollectPaymentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(domain.ProcessPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).CheckAndUpdateAddFundsCollectPaymentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_CheckAndUpdateAddFundsCollectPaymentStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).CheckAndUpdateAddFundsCollectPaymentStatus(ctx, req.(*domain.ProcessPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_RaiseComplaint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RaiseComplaintRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).RaiseComplaint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_RaiseComplaint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).RaiseComplaint(ctx, req.(*RaiseComplaintRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_CheckTxnStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTxnStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).CheckTxnStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_CheckTxnStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).CheckTxnStatus(ctx, req.(*CheckTxnStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_CheckComplaintStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckComplaintStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).CheckComplaintStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_CheckComplaintStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).CheckComplaintStatus(ctx, req.(*CheckComplaintStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_ChangeUpiPinSetState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeUpiPinSetStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).ChangeUpiPinSetState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_ChangeUpiPinSetState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).ChangeUpiPinSetState(ctx, req.(*ChangeUpiPinSetStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetVerifiedVpasByPhoneNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVerifiedVpasByPhoneNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetVerifiedVpasByPhoneNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetVerifiedVpasByPhoneNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetVerifiedVpasByPhoneNumber(ctx, req.(*GetVerifiedVpasByPhoneNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_ValidateAddressAndCreatePi_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateAddressAndCreatePiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).ValidateAddressAndCreatePi(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_ValidateAddressAndCreatePi_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).ValidateAddressAndCreatePi(ctx, req.(*ValidateAddressAndCreatePiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_ValidateUpiNumberAndCreatePi_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateUpiNumberAndCreatePiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).ValidateUpiNumberAndCreatePi(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_ValidateUpiNumberAndCreatePi_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).ValidateUpiNumberAndCreatePi(ctx, req.(*ValidateUpiNumberAndCreatePiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_ValidateInternationalPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateInternationalPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).ValidateInternationalPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_ValidateInternationalPayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).ValidateInternationalPayment(ctx, req.(*ValidateInternationalPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetUpiInternationalQrInfoFromCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpiInternationalQrInfoFromCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetUpiInternationalQrInfoFromCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetUpiInternationalQrInfoFromCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetUpiInternationalQrInfoFromCache(ctx, req.(*GetUpiInternationalQrInfoFromCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetExternalVpas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExternalVpasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetExternalVpas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetExternalVpas_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetExternalVpas(ctx, req.(*GetExternalVpasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_GetNPCICLParametersV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNPCICLParametersV1Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).GetNPCICLParametersV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_GetNPCICLParametersV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).GetNPCICLParametersV1(ctx, req.(*GetNPCICLParametersV1Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _UPI_IsNewAddFundsVpaEnabledForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsNewAddFundsVpaEnabledForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UPIServer).IsNewAddFundsVpaEnabledForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UPI_IsNewAddFundsVpaEnabledForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UPIServer).IsNewAddFundsVpaEnabledForActor(ctx, req.(*IsNewAddFundsVpaEnabledForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UPI_ServiceDesc is the grpc.ServiceDesc for UPI service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UPI_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "upi.UPI",
	HandlerType: (*UPIServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "VerifyPayeeVPA",
			Handler:    _UPI_VerifyPayeeVPA_Handler,
		},
		{
			MethodName: "GetToken",
			Handler:    _UPI_GetToken_Handler,
		},
		{
			MethodName: "GenerateUpiOtp",
			Handler:    _UPI_GenerateUpiOtp_Handler,
		},
		{
			MethodName: "ResolveAccount",
			Handler:    _UPI_ResolveAccount_Handler,
		},
		{
			MethodName: "CreateVPA",
			Handler:    _UPI_CreateVPA_Handler,
		},
		{
			MethodName: "ListKeys",
			Handler:    _UPI_ListKeys_Handler,
		},
		{
			MethodName: "RegisterMobile",
			Handler:    _UPI_RegisterMobile_Handler,
		},
		{
			MethodName: "GetPinFlowParameters",
			Handler:    _UPI_GetPinFlowParameters_Handler,
		},
		{
			MethodName: "GetTransactionParameters",
			Handler:    _UPI_GetTransactionParameters_Handler,
		},
		{
			MethodName: "VerifyURN",
			Handler:    _UPI_VerifyURN_Handler,
		},
		{
			MethodName: "ChangePin",
			Handler:    _UPI_ChangePin_Handler,
		},
		{
			MethodName: "GenerateURN",
			Handler:    _UPI_GenerateURN_Handler,
		},
		{
			MethodName: "GetUpiSetupStatus",
			Handler:    _UPI_GetUpiSetupStatus_Handler,
		},
		{
			MethodName: "GetOrCreateURNTransaction",
			Handler:    _UPI_GetOrCreateURNTransaction_Handler,
		},
		{
			MethodName: "DisableOrEnableVPA",
			Handler:    _UPI_DisableOrEnableVPA_Handler,
		},
		{
			MethodName: "GetPinStatus",
			Handler:    _UPI_GetPinStatus_Handler,
		},
		{
			MethodName: "GetAccountPinInfos",
			Handler:    _UPI_GetAccountPinInfos_Handler,
		},
		{
			MethodName: "GetLatestAccountPinInfos",
			Handler:    _UPI_GetLatestAccountPinInfos_Handler,
		},
		{
			MethodName: "CheckVerifiedMerchant",
			Handler:    _UPI_CheckVerifiedMerchant_Handler,
		},
		{
			MethodName: "GetOrCreateAddFundsTransaction",
			Handler:    _UPI_GetOrCreateAddFundsTransaction_Handler,
		},
		{
			MethodName: "GetVpaMerchantInfo",
			Handler:    _UPI_GetVpaMerchantInfo_Handler,
		},
		{
			MethodName: "GetPinInfosByAccountId",
			Handler:    _UPI_GetPinInfosByAccountId_Handler,
		},
		{
			MethodName: "PostUserActivity",
			Handler:    _UPI_PostUserActivity_Handler,
		},
		{
			MethodName: "ValidateSecurePin",
			Handler:    _UPI_ValidateSecurePin_Handler,
		},
		{
			MethodName: "CheckAndUpdateAddFundsCollectPaymentStatus",
			Handler:    _UPI_CheckAndUpdateAddFundsCollectPaymentStatus_Handler,
		},
		{
			MethodName: "RaiseComplaint",
			Handler:    _UPI_RaiseComplaint_Handler,
		},
		{
			MethodName: "CheckTxnStatus",
			Handler:    _UPI_CheckTxnStatus_Handler,
		},
		{
			MethodName: "CheckComplaintStatus",
			Handler:    _UPI_CheckComplaintStatus_Handler,
		},
		{
			MethodName: "ChangeUpiPinSetState",
			Handler:    _UPI_ChangeUpiPinSetState_Handler,
		},
		{
			MethodName: "GetVerifiedVpasByPhoneNumber",
			Handler:    _UPI_GetVerifiedVpasByPhoneNumber_Handler,
		},
		{
			MethodName: "ValidateAddressAndCreatePi",
			Handler:    _UPI_ValidateAddressAndCreatePi_Handler,
		},
		{
			MethodName: "ValidateUpiNumberAndCreatePi",
			Handler:    _UPI_ValidateUpiNumberAndCreatePi_Handler,
		},
		{
			MethodName: "ValidateInternationalPayment",
			Handler:    _UPI_ValidateInternationalPayment_Handler,
		},
		{
			MethodName: "GetUpiInternationalQrInfoFromCache",
			Handler:    _UPI_GetUpiInternationalQrInfoFromCache_Handler,
		},
		{
			MethodName: "GetExternalVpas",
			Handler:    _UPI_GetExternalVpas_Handler,
		},
		{
			MethodName: "GetNPCICLParametersV1",
			Handler:    _UPI_GetNPCICLParametersV1_Handler,
		},
		{
			MethodName: "IsNewAddFundsVpaEnabledForActor",
			Handler:    _UPI_IsNewAddFundsVpaEnabledForActor_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/upi/service.proto",
}
