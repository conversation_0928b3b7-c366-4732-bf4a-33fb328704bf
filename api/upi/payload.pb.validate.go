// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/payload.proto

package upi

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UrnTransferInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UrnTransferInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UrnTransferInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UrnTransferInfoMultiError, or nil if none found.
func (m *UrnTransferInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UrnTransferInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PiTo

	// no validation rules for ActorTo

	// no validation rules for TxnId

	if all {
		switch v := interface{}(m.GetNextOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UrnTransferInfoValidationError{
					field:  "NextOrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UrnTransferInfoValidationError{
					field:  "NextOrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UrnTransferInfoValidationError{
				field:  "NextOrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InitiationMode

	// no validation rules for Purpose

	if all {
		switch v := interface{}(m.GetTxnOriginTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UrnTransferInfoValidationError{
					field:  "TxnOriginTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UrnTransferInfoValidationError{
					field:  "TxnOriginTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnOriginTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UrnTransferInfoValidationError{
				field:  "TxnOriginTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UrnTransferInfoMultiError(errors)
	}

	return nil
}

// UrnTransferInfoMultiError is an error wrapping multiple validation errors
// returned by UrnTransferInfo.ValidateAll() if the designated constraints
// aren't met.
type UrnTransferInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UrnTransferInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UrnTransferInfoMultiError) AllErrors() []error { return m }

// UrnTransferInfoValidationError is the validation error returned by
// UrnTransferInfo.Validate if the designated constraints aren't met.
type UrnTransferInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UrnTransferInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UrnTransferInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UrnTransferInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UrnTransferInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UrnTransferInfoValidationError) ErrorName() string { return "UrnTransferInfoValidationError" }

// Error satisfies the builtin error interface
func (e UrnTransferInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUrnTransferInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UrnTransferInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UrnTransferInfoValidationError{}

// Validate checks the field values on NextOrderInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NextOrderInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NextOrderInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NextOrderInfoMultiError, or
// nil if none found.
func (m *NextOrderInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NextOrderInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorFrom

	if utf8.RuneCountInString(m.GetActorTo()) < 1 {
		err := NextOrderInfoValidationError{
			field:  "ActorTo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _NextOrderInfo_Workflow_NotInLookup[m.GetWorkflow()]; ok {
		err := NextOrderInfoValidationError{
			field:  "Workflow",
			reason: "value must not be in list [NEXT_ORDER_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetPayload()) < 1 {
		err := NextOrderInfoValidationError{
			field:  "Payload",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() == nil {
		err := NextOrderInfoValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NextOrderInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NextOrderInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NextOrderInfoValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NextOrderInfoMultiError(errors)
	}

	return nil
}

// NextOrderInfoMultiError is an error wrapping multiple validation errors
// returned by NextOrderInfo.ValidateAll() if the designated constraints
// aren't met.
type NextOrderInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NextOrderInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NextOrderInfoMultiError) AllErrors() []error { return m }

// NextOrderInfoValidationError is the validation error returned by
// NextOrderInfo.Validate if the designated constraints aren't met.
type NextOrderInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NextOrderInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NextOrderInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NextOrderInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NextOrderInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NextOrderInfoValidationError) ErrorName() string { return "NextOrderInfoValidationError" }

// Error satisfies the builtin error interface
func (e NextOrderInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNextOrderInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NextOrderInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NextOrderInfoValidationError{}

var _NextOrderInfo_Workflow_NotInLookup = map[NextOrderType]struct{}{
	0: {},
}
