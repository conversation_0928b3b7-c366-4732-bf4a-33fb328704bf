// protolint:disable MAX_LINE_LENGTH
// This file defines different messages that are passed along with order creation request as order payload.
// The order central retry orchestrator then sends the details to the upi service as it is for processing

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/payload.proto

package upi

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NextOrderType int32

const (
	NextOrderType_NEXT_ORDER_TYPE_UNSPECIFIED NextOrderType = 0
	// Next order is to be created with CREATE_DEPOSIT workflow
	NextOrderType_CREATE_DEPOSIT NextOrderType = 1
)

// Enum value maps for NextOrderType.
var (
	NextOrderType_name = map[int32]string{
		0: "NEXT_ORDER_TYPE_UNSPECIFIED",
		1: "CREATE_DEPOSIT",
	}
	NextOrderType_value = map[string]int32{
		"NEXT_ORDER_TYPE_UNSPECIFIED": 0,
		"CREATE_DEPOSIT":              1,
	}
)

func (x NextOrderType) Enum() *NextOrderType {
	p := new(NextOrderType)
	*p = x
	return p
}

func (x NextOrderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NextOrderType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_payload_proto_enumTypes[0].Descriptor()
}

func (NextOrderType) Type() protoreflect.EnumType {
	return &file_api_upi_payload_proto_enumTypes[0]
}

func (x NextOrderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NextOrderType.Descriptor instead.
func (NextOrderType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_payload_proto_rawDescGZIP(), []int{0}
}

// Contains all the meta data required to process URN transfer
type UrnTransferInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PiTo    string `protobuf:"bytes,1,opt,name=pi_to,json=piTo,proto3" json:"pi_to,omitempty"`
	ActorTo string `protobuf:"bytes,2,opt,name=actor_to,json=actorTo,proto3" json:"actor_to,omitempty"`
	TxnId   string `protobuf:"bytes,3,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	// order details for the next order to be created on successful processing of order with URN_TRANSFER workflow.
	NextOrderInfo *NextOrderInfo `protobuf:"bytes,4,opt,name=next_order_info,json=nextOrderInfo,proto3" json:"next_order_info,omitempty"`
	// initiation mode for the transfer
	// we need to pass this in CheckTxnStatus Api call.
	// we store this in transaction reqInfo, but for urn/add funds transfers
	// we dont have a transaction created before call CheckTxnStatus
	InitiationMode string `protobuf:"bytes,5,opt,name=initiation_mode,json=initiationMode,proto3" json:"initiation_mode,omitempty"`
	// purpose for the transfer
	// we need to pass this in CheckTxnStatus Api call.
	// we store this in transaction reqInfo, but for urn/add funds transfers
	// we dont have a transaction created before call CheckTxnStatus
	Purpose string `protobuf:"bytes,6,opt,name=purpose,proto3" json:"purpose,omitempty"`
	// Transaction origination timestamp represents the time when transaction was originated
	// we need to pass this in CheckTxnStatus Api call.
	// we store this in transaction reqInfo, but for urn/add funds transfers
	// we dont have a transaction created before call CheckTxnStatus
	TxnOriginTimestamp *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=txn_origin_timestamp,json=txnOriginTimestamp,proto3" json:"txn_origin_timestamp,omitempty"`
}

func (x *UrnTransferInfo) Reset() {
	*x = UrnTransferInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_payload_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UrnTransferInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UrnTransferInfo) ProtoMessage() {}

func (x *UrnTransferInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_payload_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UrnTransferInfo.ProtoReflect.Descriptor instead.
func (*UrnTransferInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_payload_proto_rawDescGZIP(), []int{0}
}

func (x *UrnTransferInfo) GetPiTo() string {
	if x != nil {
		return x.PiTo
	}
	return ""
}

func (x *UrnTransferInfo) GetActorTo() string {
	if x != nil {
		return x.ActorTo
	}
	return ""
}

func (x *UrnTransferInfo) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *UrnTransferInfo) GetNextOrderInfo() *NextOrderInfo {
	if x != nil {
		return x.NextOrderInfo
	}
	return nil
}

func (x *UrnTransferInfo) GetInitiationMode() string {
	if x != nil {
		return x.InitiationMode
	}
	return ""
}

func (x *UrnTransferInfo) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

func (x *UrnTransferInfo) GetTxnOriginTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.TxnOriginTimestamp
	}
	return nil
}

type NextOrderInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor who initiated the order
	ActorFrom string `protobuf:"bytes,1,opt,name=actor_from,json=actorFrom,proto3" json:"actor_from,omitempty"`
	// receiving entity of the order
	ActorTo string `protobuf:"bytes,2,opt,name=actor_to,json=actorTo,proto3" json:"actor_to,omitempty"`
	// To avoid circular dependency, a new enum (NextOrderType) is defined to help identify the workflow of the next order
	Workflow NextOrderType `protobuf:"varint,3,opt,name=workflow,proto3,enum=upi.NextOrderType" json:"workflow,omitempty"`
	// payload of the next order
	Payload []byte `protobuf:"bytes,4,opt,name=payload,proto3" json:"payload,omitempty"`
	// order amount
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *NextOrderInfo) Reset() {
	*x = NextOrderInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_payload_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NextOrderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NextOrderInfo) ProtoMessage() {}

func (x *NextOrderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_payload_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NextOrderInfo.ProtoReflect.Descriptor instead.
func (*NextOrderInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_payload_proto_rawDescGZIP(), []int{1}
}

func (x *NextOrderInfo) GetActorFrom() string {
	if x != nil {
		return x.ActorFrom
	}
	return ""
}

func (x *NextOrderInfo) GetActorTo() string {
	if x != nil {
		return x.ActorTo
	}
	return ""
}

func (x *NextOrderInfo) GetWorkflow() NextOrderType {
	if x != nil {
		return x.Workflow
	}
	return NextOrderType_NEXT_ORDER_TYPE_UNSPECIFIED
}

func (x *NextOrderInfo) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *NextOrderInfo) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

var File_api_upi_payload_proto protoreflect.FileDescriptor

var file_api_upi_payload_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x75, 0x70, 0x69, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xa5, 0x02, 0x0a, 0x0f, 0x55, 0x72, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x13, 0x0a, 0x05, 0x70, 0x69, 0x5f, 0x74, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x69, 0x54, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x54, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0f, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4e, 0x65, 0x78, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x14, 0x74, 0x78, 0x6e,
	0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x12, 0x74, 0x78, 0x6e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xe5, 0x01, 0x0a, 0x0d, 0x4e, 0x65, 0x78, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x6f, 0x12, 0x38, 0x0a, 0x08,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4e, 0x65, 0x78, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x08, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x21, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x7a, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x2a,
	0x44, 0x0a, 0x0d, 0x4e, 0x65, 0x78, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x1b, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x10, 0x01, 0x42, 0x40, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x5a, 0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_payload_proto_rawDescOnce sync.Once
	file_api_upi_payload_proto_rawDescData = file_api_upi_payload_proto_rawDesc
)

func file_api_upi_payload_proto_rawDescGZIP() []byte {
	file_api_upi_payload_proto_rawDescOnce.Do(func() {
		file_api_upi_payload_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_payload_proto_rawDescData)
	})
	return file_api_upi_payload_proto_rawDescData
}

var file_api_upi_payload_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_payload_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_upi_payload_proto_goTypes = []interface{}{
	(NextOrderType)(0),            // 0: upi.NextOrderType
	(*UrnTransferInfo)(nil),       // 1: upi.UrnTransferInfo
	(*NextOrderInfo)(nil),         // 2: upi.NextOrderInfo
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
	(*money.Money)(nil),           // 4: google.type.Money
}
var file_api_upi_payload_proto_depIdxs = []int32{
	2, // 0: upi.UrnTransferInfo.next_order_info:type_name -> upi.NextOrderInfo
	3, // 1: upi.UrnTransferInfo.txn_origin_timestamp:type_name -> google.protobuf.Timestamp
	0, // 2: upi.NextOrderInfo.workflow:type_name -> upi.NextOrderType
	4, // 3: upi.NextOrderInfo.amount:type_name -> google.type.Money
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_upi_payload_proto_init() }
func file_api_upi_payload_proto_init() {
	if File_api_upi_payload_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_payload_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UrnTransferInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_payload_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NextOrderInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_payload_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_payload_proto_goTypes,
		DependencyIndexes: file_api_upi_payload_proto_depIdxs,
		EnumInfos:         file_api_upi_payload_proto_enumTypes,
		MessageInfos:      file_api_upi_payload_proto_msgTypes,
	}.Build()
	File_api_upi_payload_proto = out.File
	file_api_upi_payload_proto_rawDesc = nil
	file_api_upi_payload_proto_goTypes = nil
	file_api_upi_payload_proto_depIdxs = nil
}
