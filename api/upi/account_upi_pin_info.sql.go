package upi

import (
	"bytes"
	"database/sql/driver"
	"fmt"

	"github.com/golang/protobuf/jsonpb"
)

// Valuer interface implementation for storing the data  in string format in DB
func (p AccountUpiPinInfo_Action) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (p *AccountUpiPinInfo_Action) Scan(input interface{}) error {
	if p == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := AccountUpiPinInfo_Action_value[val]
	*p = AccountUpiPinInfo_Action(valInt)
	return nil
}

// Valuer interface implementation for storing the data  in string format in DB
func (p AccountUpiPinInfo_Status) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (p *AccountUpiPinInfo_Status) Scan(input interface{}) error {
	if p == nil {
		return nil
	}
	val, ok := input.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := AccountUpiPinInfo_Status_value[val]
	*p = AccountUpiPinInfo_Status(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (d *AccountUpiPinInfo_DetailedStatusList) Value() (driver.Value, error) {
	if d == nil {
		return nil, nil
	}
	var detailedStatusBytes bytes.Buffer
	marshaller := jsonpb.Marshaler{}
	err := marshaller.Marshal(&detailedStatusBytes, d)
	if err != nil {
		return nil, err
	}
	return detailedStatusBytes.Bytes(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (d *AccountUpiPinInfo_DetailedStatusList) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte, got %T", input)
		return err
	}
	var detailedStatusBytes = bytes.NewBuffer(val)
	unMarshaller := jsonpb.Unmarshaler{}
	if err := unMarshaller.Unmarshal(detailedStatusBytes, d); err != nil {
		return err
	}
	return nil
}
