// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/mandate/service.proto

package mandate

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
	upi "github.com/epifi/gamma/api/upi"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActorRole int32

const (
	ActorRole_ACTOR_ROLE_UNSPECIFIED ActorRole = 0
	// actor is payer
	ActorRole_ACTOR_ROLE_PAYER ActorRole = 1
	// actor is payee
	ActorRole_ACTOR_ROLE_PAYEE ActorRole = 2
)

// Enum value maps for ActorRole.
var (
	ActorRole_name = map[int32]string{
		0: "ACTOR_ROLE_UNSPECIFIED",
		1: "ACTOR_ROLE_PAYER",
		2: "ACTOR_ROLE_PAYEE",
	}
	ActorRole_value = map[string]int32{
		"ACTOR_ROLE_UNSPECIFIED": 0,
		"ACTOR_ROLE_PAYER":       1,
		"ACTOR_ROLE_PAYEE":       2,
	}
)

func (x ActorRole) Enum() *ActorRole {
	p := new(ActorRole)
	*p = x
	return p
}

func (x ActorRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActorRole) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[0].Descriptor()
}

func (ActorRole) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[0]
}

func (x ActorRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActorRole.Descriptor instead.
func (ActorRole) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{0}
}

type InitiateMandateExecutionResponse_Status int32

const (
	InitiateMandateExecutionResponse_OK InitiateMandateExecutionResponse_Status = 0
	// transaction is in in-valid state
	// it can be mean either transaction is in terminal state or it has already been initiated.
	InitiateMandateExecutionResponse_FAILED_PRECONDITION InitiateMandateExecutionResponse_Status = 9
	// transaction failed to initiate.
	// this can happen due to various reasons e.g. in-sufficient funds, account frozen temporarily.
	InitiateMandateExecutionResponse_TRANSACTION_FAILED InitiateMandateExecutionResponse_Status = 100
)

// Enum value maps for InitiateMandateExecutionResponse_Status.
var (
	InitiateMandateExecutionResponse_Status_name = map[int32]string{
		0:   "OK",
		9:   "FAILED_PRECONDITION",
		100: "TRANSACTION_FAILED",
	}
	InitiateMandateExecutionResponse_Status_value = map[string]int32{
		"OK":                  0,
		"FAILED_PRECONDITION": 9,
		"TRANSACTION_FAILED":  100,
	}
)

func (x InitiateMandateExecutionResponse_Status) Enum() *InitiateMandateExecutionResponse_Status {
	p := new(InitiateMandateExecutionResponse_Status)
	*p = x
	return p
}

func (x InitiateMandateExecutionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitiateMandateExecutionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[1].Descriptor()
}

func (InitiateMandateExecutionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[1]
}

func (x InitiateMandateExecutionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitiateMandateExecutionResponse_Status.Descriptor instead.
func (InitiateMandateExecutionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{1, 0}
}

// List of status codes returned
type CreateMandateResponse_Status int32

const (
	// Returned an success
	CreateMandateResponse_OK CreateMandateResponse_Status = 0
	// Indicates that arguments are problematic
	CreateMandateResponse_INVALID_ARGUMENT CreateMandateResponse_Status = 3
	// modify for the given req id already exists
	CreateMandateResponse_ALREADY_EXISTS CreateMandateResponse_Status = 6
	// permission denied for creation of mandate
	CreateMandateResponse_PERMISSION_DENIED CreateMandateResponse_Status = 7
	// System faced internal errors while processing the request
	CreateMandateResponse_INTERNAL CreateMandateResponse_Status = 13
)

// Enum value maps for CreateMandateResponse_Status.
var (
	CreateMandateResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		6:  "ALREADY_EXISTS",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
	}
	CreateMandateResponse_Status_value = map[string]int32{
		"OK":                0,
		"INVALID_ARGUMENT":  3,
		"ALREADY_EXISTS":    6,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
	}
)

func (x CreateMandateResponse_Status) Enum() *CreateMandateResponse_Status {
	p := new(CreateMandateResponse_Status)
	*p = x
	return p
}

func (x CreateMandateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreateMandateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[2].Descriptor()
}

func (CreateMandateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[2]
}

func (x CreateMandateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreateMandateResponse_Status.Descriptor instead.
func (CreateMandateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{3, 0}
}

// List of status codes returned
type GetMandateResponse_Status int32

const (
	// Returned an success
	GetMandateResponse_OK GetMandateResponse_Status = 0
	// mandate not found
	GetMandateResponse_RECORD_NOT_FOUND  GetMandateResponse_Status = 5
	GetMandateResponse_PERMISSION_DENIED GetMandateResponse_Status = 7
	// System faced internal errors while processing the request
	GetMandateResponse_INTERNAL GetMandateResponse_Status = 13
)

// Enum value maps for GetMandateResponse_Status.
var (
	GetMandateResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
	}
	GetMandateResponse_Status_value = map[string]int32{
		"OK":                0,
		"RECORD_NOT_FOUND":  5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
	}
)

func (x GetMandateResponse_Status) Enum() *GetMandateResponse_Status {
	p := new(GetMandateResponse_Status)
	*p = x
	return p
}

func (x GetMandateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMandateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[3].Descriptor()
}

func (GetMandateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[3]
}

func (x GetMandateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMandateResponse_Status.Descriptor instead.
func (GetMandateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{5, 0}
}

// List of status codes returned
type AuthoriseMandateActionResponse_Status int32

const (
	// Returned an success
	AuthoriseMandateActionResponse_OK                AuthoriseMandateActionResponse_Status = 0
	AuthoriseMandateActionResponse_PERMISSION_DENIED AuthoriseMandateActionResponse_Status = 7
	// permanent failure
	AuthoriseMandateActionResponse_PERMANENT_FAILURE AuthoriseMandateActionResponse_Status = 100
	// transient failure
	AuthoriseMandateActionResponse_TRANSIENT_FAILURE AuthoriseMandateActionResponse_Status = 101
	// request has expired
	AuthoriseMandateActionResponse_EXPIRED AuthoriseMandateActionResponse_Status = 102
)

// Enum value maps for AuthoriseMandateActionResponse_Status.
var (
	AuthoriseMandateActionResponse_Status_name = map[int32]string{
		0:   "OK",
		7:   "PERMISSION_DENIED",
		100: "PERMANENT_FAILURE",
		101: "TRANSIENT_FAILURE",
		102: "EXPIRED",
	}
	AuthoriseMandateActionResponse_Status_value = map[string]int32{
		"OK":                0,
		"PERMISSION_DENIED": 7,
		"PERMANENT_FAILURE": 100,
		"TRANSIENT_FAILURE": 101,
		"EXPIRED":           102,
	}
)

func (x AuthoriseMandateActionResponse_Status) Enum() *AuthoriseMandateActionResponse_Status {
	p := new(AuthoriseMandateActionResponse_Status)
	*p = x
	return p
}

func (x AuthoriseMandateActionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthoriseMandateActionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[4].Descriptor()
}

func (AuthoriseMandateActionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[4]
}

func (x AuthoriseMandateActionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthoriseMandateActionResponse_Status.Descriptor instead.
func (AuthoriseMandateActionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{7, 0}
}

// List of status codes returned
type FetchAndUpdateRequestStatusResponse_Status int32

const (
	// Returned an success
	FetchAndUpdateRequestStatusResponse_OK FetchAndUpdateRequestStatusResponse_Status = 0
	// mandate request is in progress
	FetchAndUpdateRequestStatusResponse_IN_PROGRESS FetchAndUpdateRequestStatusResponse_Status = 51
	// transient failure
	FetchAndUpdateRequestStatusResponse_TRANSIENT_FAILURE FetchAndUpdateRequestStatusResponse_Status = 100
	// permanent failure
	FetchAndUpdateRequestStatusResponse_PERMANENT_FAILURE FetchAndUpdateRequestStatusResponse_Status = 101
	// As per NPCI guidelines, vendor status enquiry  for UPI mandate creation or modification
	// should not be made within a minimum delay period (e.g., 90 seconds) from the action's creation time.
	// If the action is too recent, this RPC returns a special status (TOO_EARLY_FOR_VENDOR_CALL) to signal the caller to retry later.
	FetchAndUpdateRequestStatusResponse_TOO_EARLY_FOR_VENDOR_CALL FetchAndUpdateRequestStatusResponse_Status = 102
)

// Enum value maps for FetchAndUpdateRequestStatusResponse_Status.
var (
	FetchAndUpdateRequestStatusResponse_Status_name = map[int32]string{
		0:   "OK",
		51:  "IN_PROGRESS",
		100: "TRANSIENT_FAILURE",
		101: "PERMANENT_FAILURE",
		102: "TOO_EARLY_FOR_VENDOR_CALL",
	}
	FetchAndUpdateRequestStatusResponse_Status_value = map[string]int32{
		"OK":                        0,
		"IN_PROGRESS":               51,
		"TRANSIENT_FAILURE":         100,
		"PERMANENT_FAILURE":         101,
		"TOO_EARLY_FOR_VENDOR_CALL": 102,
	}
)

func (x FetchAndUpdateRequestStatusResponse_Status) Enum() *FetchAndUpdateRequestStatusResponse_Status {
	p := new(FetchAndUpdateRequestStatusResponse_Status)
	*p = x
	return p
}

func (x FetchAndUpdateRequestStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FetchAndUpdateRequestStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[5].Descriptor()
}

func (FetchAndUpdateRequestStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[5]
}

func (x FetchAndUpdateRequestStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FetchAndUpdateRequestStatusResponse_Status.Descriptor instead.
func (FetchAndUpdateRequestStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{9, 0}
}

// List of status codes returned
type DeclineMandateActionResponse_Status int32

const (
	// Returned an success
	DeclineMandateActionResponse_OK DeclineMandateActionResponse_Status = 0
	// invalid argument passed
	DeclineMandateActionResponse_INVALID_ARGUMENT DeclineMandateActionResponse_Status = 3
	// internal error occurred
	DeclineMandateActionResponse_INTERNAL DeclineMandateActionResponse_Status = 13
)

// Enum value maps for DeclineMandateActionResponse_Status.
var (
	DeclineMandateActionResponse_Status_name = map[int32]string{
		0:  "OK",
		3:  "INVALID_ARGUMENT",
		13: "INTERNAL",
	}
	DeclineMandateActionResponse_Status_value = map[string]int32{
		"OK":               0,
		"INVALID_ARGUMENT": 3,
		"INTERNAL":         13,
	}
)

func (x DeclineMandateActionResponse_Status) Enum() *DeclineMandateActionResponse_Status {
	p := new(DeclineMandateActionResponse_Status)
	*p = x
	return p
}

func (x DeclineMandateActionResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeclineMandateActionResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[6].Descriptor()
}

func (DeclineMandateActionResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[6]
}

func (x DeclineMandateActionResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeclineMandateActionResponse_Status.Descriptor instead.
func (DeclineMandateActionResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{11, 0}
}

// List of status codes returned
type GetMandateRequestParametersResponse_Status int32

const (
	// Returned an success
	GetMandateRequestParametersResponse_OK GetMandateRequestParametersResponse_Status = 0
	// mandate not found
	GetMandateRequestParametersResponse_RECORD_NOT_FOUND  GetMandateRequestParametersResponse_Status = 5
	GetMandateRequestParametersResponse_PERMISSION_DENIED GetMandateRequestParametersResponse_Status = 7
	// System faced internal errors while processing the request
	GetMandateRequestParametersResponse_INTERNAL GetMandateRequestParametersResponse_Status = 13
)

// Enum value maps for GetMandateRequestParametersResponse_Status.
var (
	GetMandateRequestParametersResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
	}
	GetMandateRequestParametersResponse_Status_value = map[string]int32{
		"OK":                0,
		"RECORD_NOT_FOUND":  5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
	}
)

func (x GetMandateRequestParametersResponse_Status) Enum() *GetMandateRequestParametersResponse_Status {
	p := new(GetMandateRequestParametersResponse_Status)
	*p = x
	return p
}

func (x GetMandateRequestParametersResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMandateRequestParametersResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[7].Descriptor()
}

func (GetMandateRequestParametersResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[7]
}

func (x GetMandateRequestParametersResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMandateRequestParametersResponse_Status.Descriptor instead.
func (GetMandateRequestParametersResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{13, 0}
}

// List of status codes returned
type GetMandateDetailsResponse_Status int32

const (
	// Returned an success
	GetMandateDetailsResponse_OK GetMandateDetailsResponse_Status = 0
	// mandate not found
	GetMandateDetailsResponse_RECORD_NOT_FOUND  GetMandateDetailsResponse_Status = 5
	GetMandateDetailsResponse_PERMISSION_DENIED GetMandateDetailsResponse_Status = 7
	// System faced internal errors while processing the request
	GetMandateDetailsResponse_INTERNAL GetMandateDetailsResponse_Status = 13
)

// Enum value maps for GetMandateDetailsResponse_Status.
var (
	GetMandateDetailsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		7:  "PERMISSION_DENIED",
		13: "INTERNAL",
	}
	GetMandateDetailsResponse_Status_value = map[string]int32{
		"OK":                0,
		"RECORD_NOT_FOUND":  5,
		"PERMISSION_DENIED": 7,
		"INTERNAL":          13,
	}
)

func (x GetMandateDetailsResponse_Status) Enum() *GetMandateDetailsResponse_Status {
	p := new(GetMandateDetailsResponse_Status)
	*p = x
	return p
}

func (x GetMandateDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMandateDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[8].Descriptor()
}

func (GetMandateDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[8]
}

func (x GetMandateDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMandateDetailsResponse_Status.Descriptor instead.
func (GetMandateDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{15, 0}
}

// List of status codes returned
type ModifyMandateResponse_Status int32

const (
	// Returned an success
	ModifyMandateResponse_OK ModifyMandateResponse_Status = 0
	// mandate not found
	ModifyMandateResponse_RECORD_NOT_FOUND ModifyMandateResponse_Status = 5
	// modify for the given req id already exists
	ModifyMandateResponse_ALREADY_EXISTS ModifyMandateResponse_Status = 6
	// System faced internal errors while processing the request
	ModifyMandateResponse_INTERNAL ModifyMandateResponse_Status = 13
)

// Enum value maps for ModifyMandateResponse_Status.
var (
	ModifyMandateResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		6:  "ALREADY_EXISTS",
		13: "INTERNAL",
	}
	ModifyMandateResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"ALREADY_EXISTS":   6,
		"INTERNAL":         13,
	}
)

func (x ModifyMandateResponse_Status) Enum() *ModifyMandateResponse_Status {
	p := new(ModifyMandateResponse_Status)
	*p = x
	return p
}

func (x ModifyMandateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModifyMandateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[9].Descriptor()
}

func (ModifyMandateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[9]
}

func (x ModifyMandateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModifyMandateResponse_Status.Descriptor instead.
func (ModifyMandateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{17, 0}
}

// List of status codes returned
type RevokeMandateResponse_Status int32

const (
	// Returned an success
	RevokeMandateResponse_OK RevokeMandateResponse_Status = 0
	// mandate not found
	RevokeMandateResponse_RECORD_NOT_FOUND RevokeMandateResponse_Status = 5
	// modify for the given req id already exists
	RevokeMandateResponse_ALREADY_EXISTS RevokeMandateResponse_Status = 6
	// System faced internal errors while processing the request
	RevokeMandateResponse_INTERNAL RevokeMandateResponse_Status = 13
)

// Enum value maps for RevokeMandateResponse_Status.
var (
	RevokeMandateResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		6:  "ALREADY_EXISTS",
		13: "INTERNAL",
	}
	RevokeMandateResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"ALREADY_EXISTS":   6,
		"INTERNAL":         13,
	}
)

func (x RevokeMandateResponse_Status) Enum() *RevokeMandateResponse_Status {
	p := new(RevokeMandateResponse_Status)
	*p = x
	return p
}

func (x RevokeMandateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RevokeMandateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[10].Descriptor()
}

func (RevokeMandateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[10]
}

func (x RevokeMandateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RevokeMandateResponse_Status.Descriptor instead.
func (RevokeMandateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{19, 0}
}

// List of status codes returned
type PauseUnpauseMandateResponse_Status int32

const (
	// Returned an success
	PauseUnpauseMandateResponse_OK PauseUnpauseMandateResponse_Status = 0
	// mandate not found
	PauseUnpauseMandateResponse_RECORD_NOT_FOUND PauseUnpauseMandateResponse_Status = 5
	// modify for the given req id already exists
	PauseUnpauseMandateResponse_ALREADY_EXISTS PauseUnpauseMandateResponse_Status = 6
	// System faced internal errors while processing the request
	PauseUnpauseMandateResponse_INTERNAL PauseUnpauseMandateResponse_Status = 13
)

// Enum value maps for PauseUnpauseMandateResponse_Status.
var (
	PauseUnpauseMandateResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		6:  "ALREADY_EXISTS",
		13: "INTERNAL",
	}
	PauseUnpauseMandateResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"ALREADY_EXISTS":   6,
		"INTERNAL":         13,
	}
)

func (x PauseUnpauseMandateResponse_Status) Enum() *PauseUnpauseMandateResponse_Status {
	p := new(PauseUnpauseMandateResponse_Status)
	*p = x
	return p
}

func (x PauseUnpauseMandateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PauseUnpauseMandateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_mandate_service_proto_enumTypes[11].Descriptor()
}

func (PauseUnpauseMandateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_upi_mandate_service_proto_enumTypes[11]
}

func (x PauseUnpauseMandateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PauseUnpauseMandateResponse_Status.Descriptor instead.
func (PauseUnpauseMandateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{21, 0}
}

type InitiateMandateExecutionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// header containing details for authorisation of mandate initiation
	AuthHeader *InitiateMandateExecutionRequest_AuthHeader `protobuf:"bytes,1,opt,name=auth_header,json=authHeader,proto3" json:"auth_header,omitempty"`
	// order id for which the execution needs to be authorised
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// actor id who is initiating transaction
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *InitiateMandateExecutionRequest) Reset() {
	*x = InitiateMandateExecutionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateMandateExecutionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateMandateExecutionRequest) ProtoMessage() {}

func (x *InitiateMandateExecutionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateMandateExecutionRequest.ProtoReflect.Descriptor instead.
func (*InitiateMandateExecutionRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{0}
}

func (x *InitiateMandateExecutionRequest) GetAuthHeader() *InitiateMandateExecutionRequest_AuthHeader {
	if x != nil {
		return x.AuthHeader
	}
	return nil
}

func (x *InitiateMandateExecutionRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *InitiateMandateExecutionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type InitiateMandateExecutionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *InitiateMandateExecutionResponse) Reset() {
	*x = InitiateMandateExecutionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateMandateExecutionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateMandateExecutionResponse) ProtoMessage() {}

func (x *InitiateMandateExecutionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateMandateExecutionResponse.ProtoReflect.Descriptor instead.
func (*InitiateMandateExecutionResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{1}
}

func (x *InitiateMandateExecutionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// recurring payment id
	RecurringPaymentId string `protobuf:"bytes,1,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
	// req_id shared with the vendor
	ReqId string `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// unique mandate reference number
	Umn string `protobuf:"bytes,3,opt,name=umn,proto3" json:"umn,omitempty"`
	// denotes if the mandate is revokeable
	Revokeable bool `protobuf:"varint,4,opt,name=revokeable,proto3" json:"revokeable,omitempty"`
	// denotes if the mandate needs to be shared with payee
	ShareToPayee bool `protobuf:"varint,5,opt,name=share_to_payee,json=shareToPayee,proto3" json:"share_to_payee,omitempty"`
	// denotes if the funds will be blocked fo this mandate
	BlockFund bool `protobuf:"varint,6,opt,name=block_fund,json=blockFund,proto3" json:"block_fund,omitempty"`
	// initiator of the mandate - PAYER
	InitiatedBy MandateInitiatedBy `protobuf:"varint,7,opt,name=initiated_by,json=initiatedBy,proto3,enum=upi.mandate.MandateInitiatedBy" json:"initiated_by,omitempty"`
	// signed token used to authorise the mandate execution for payer
	SignedToken string `protobuf:"bytes,8,opt,name=signed_token,json=signedToken,proto3" json:"signed_token,omitempty"`
	// payload for the mandate request
	MandateRequestPayload *Payload `protobuf:"bytes,9,opt,name=mandate_request_payload,json=mandateRequestPayload,proto3" json:"mandate_request_payload,omitempty"`
	// current actor role- payer/payee
	CurrentActorRole ActorRole `protobuf:"varint,10,opt,name=current_actor_role,json=currentActorRole,proto3,enum=upi.mandate.ActorRole" json:"current_actor_role,omitempty"`
	// partner bank which the mandate is getting initialised with
	PartnerBank vendorgateway.Vendor `protobuf:"varint,11,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// current actor id who is initiating the request
	CurrentActorId string `protobuf:"bytes,12,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
}

func (x *CreateMandateRequest) Reset() {
	*x = CreateMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMandateRequest) ProtoMessage() {}

func (x *CreateMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMandateRequest.ProtoReflect.Descriptor instead.
func (*CreateMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateMandateRequest) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

func (x *CreateMandateRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *CreateMandateRequest) GetUmn() string {
	if x != nil {
		return x.Umn
	}
	return ""
}

func (x *CreateMandateRequest) GetRevokeable() bool {
	if x != nil {
		return x.Revokeable
	}
	return false
}

func (x *CreateMandateRequest) GetShareToPayee() bool {
	if x != nil {
		return x.ShareToPayee
	}
	return false
}

func (x *CreateMandateRequest) GetBlockFund() bool {
	if x != nil {
		return x.BlockFund
	}
	return false
}

func (x *CreateMandateRequest) GetInitiatedBy() MandateInitiatedBy {
	if x != nil {
		return x.InitiatedBy
	}
	return MandateInitiatedBy_MANDATE_INITIATED_BY_UNSPECIFIED
}

func (x *CreateMandateRequest) GetSignedToken() string {
	if x != nil {
		return x.SignedToken
	}
	return ""
}

func (x *CreateMandateRequest) GetMandateRequestPayload() *Payload {
	if x != nil {
		return x.MandateRequestPayload
	}
	return nil
}

func (x *CreateMandateRequest) GetCurrentActorRole() ActorRole {
	if x != nil {
		return x.CurrentActorRole
	}
	return ActorRole_ACTOR_ROLE_UNSPECIFIED
}

func (x *CreateMandateRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *CreateMandateRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

type CreateMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// created mandate
	Mandate *MandateEntity `protobuf:"bytes,2,opt,name=mandate,proto3" json:"mandate,omitempty"`
}

func (x *CreateMandateResponse) Reset() {
	*x = CreateMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMandateResponse) ProtoMessage() {}

func (x *CreateMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMandateResponse.ProtoReflect.Descriptor instead.
func (*CreateMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateMandateResponse) GetMandate() *MandateEntity {
	if x != nil {
		return x.Mandate
	}
	return nil
}

type GetMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetMandateRequest_ReqId
	//	*GetMandateRequest_RecurringPaymentId
	//	*GetMandateRequest_Umn
	Identifier isGetMandateRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetMandateRequest) Reset() {
	*x = GetMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateRequest) ProtoMessage() {}

func (x *GetMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateRequest.ProtoReflect.Descriptor instead.
func (*GetMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{4}
}

func (m *GetMandateRequest) GetIdentifier() isGetMandateRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetMandateRequest) GetReqId() string {
	if x, ok := x.GetIdentifier().(*GetMandateRequest_ReqId); ok {
		return x.ReqId
	}
	return ""
}

func (x *GetMandateRequest) GetRecurringPaymentId() string {
	if x, ok := x.GetIdentifier().(*GetMandateRequest_RecurringPaymentId); ok {
		return x.RecurringPaymentId
	}
	return ""
}

func (x *GetMandateRequest) GetUmn() string {
	if x, ok := x.GetIdentifier().(*GetMandateRequest_Umn); ok {
		return x.Umn
	}
	return ""
}

type isGetMandateRequest_Identifier interface {
	isGetMandateRequest_Identifier()
}

type GetMandateRequest_ReqId struct {
	ReqId string `protobuf:"bytes,1,opt,name=req_id,json=reqId,proto3,oneof"`
}

type GetMandateRequest_RecurringPaymentId struct {
	RecurringPaymentId string `protobuf:"bytes,2,opt,name=recurring_payment_id,json=recurringPaymentId,proto3,oneof"`
}

type GetMandateRequest_Umn struct {
	Umn string `protobuf:"bytes,3,opt,name=umn,proto3,oneof"`
}

func (*GetMandateRequest_ReqId) isGetMandateRequest_Identifier() {}

func (*GetMandateRequest_RecurringPaymentId) isGetMandateRequest_Identifier() {}

func (*GetMandateRequest_Umn) isGetMandateRequest_Identifier() {}

type GetMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status  *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Mandate *MandateEntity `protobuf:"bytes,2,opt,name=mandate,proto3" json:"mandate,omitempty"`
}

func (x *GetMandateResponse) Reset() {
	*x = GetMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateResponse) ProtoMessage() {}

func (x *GetMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateResponse.ProtoReflect.Descriptor instead.
func (*GetMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMandateResponse) GetMandate() *MandateEntity {
	if x != nil {
		return x.Mandate
	}
	return nil
}

type AuthoriseMandateActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// header containing details for authorisation of mandate initiation
	AuthHeader *AuthoriseMandateActionRequest_AuthHeader `protobuf:"bytes,1,opt,name=auth_header,json=authHeader,proto3" json:"auth_header,omitempty"`
	ReqId      string                                    `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// actor id of the one initialising the mandate
	CurrentActorId string `protobuf:"bytes,3,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	FromActorId    string `protobuf:"bytes,4,opt,name=from_actor_id,json=fromActorId,proto3" json:"from_actor_id,omitempty"`
	ToActorId      string `protobuf:"bytes,5,opt,name=to_actor_id,json=toActorId,proto3" json:"to_actor_id,omitempty"`
	FromPiId       string `protobuf:"bytes,6,opt,name=from_pi_id,json=fromPiId,proto3" json:"from_pi_id,omitempty"`
	ToPiId         string `protobuf:"bytes,7,opt,name=to_pi_id,json=toPiId,proto3" json:"to_pi_id,omitempty"`
	Remarks        string `protobuf:"bytes,8,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// validity of the mandate
	MandateValidity *Validity `protobuf:"bytes,9,opt,name=mandate_validity,json=mandateValidity,proto3" json:"mandate_validity,omitempty"`
	// partner bank which the mandate is getting initialised with
	PartnerBank vendorgateway.Vendor `protobuf:"varint,10,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// amount for which the mandate is created
	Amount *money.Money `protobuf:"bytes,11,opt,name=amount,proto3" json:"amount,omitempty"`
	// amount rule for the mandate eg. MAX, EXACT
	AmountRule AmountRule `protobuf:"varint,12,opt,name=amount_rule,json=amountRule,proto3,enum=upi.mandate.AmountRule" json:"amount_rule,omitempty"`
	// recurrence params for te mandate
	Recurrence *Recurrence `protobuf:"bytes,13,opt,name=recurrence,proto3" json:"recurrence,omitempty"`
}

func (x *AuthoriseMandateActionRequest) Reset() {
	*x = AuthoriseMandateActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthoriseMandateActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthoriseMandateActionRequest) ProtoMessage() {}

func (x *AuthoriseMandateActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthoriseMandateActionRequest.ProtoReflect.Descriptor instead.
func (*AuthoriseMandateActionRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{6}
}

func (x *AuthoriseMandateActionRequest) GetAuthHeader() *AuthoriseMandateActionRequest_AuthHeader {
	if x != nil {
		return x.AuthHeader
	}
	return nil
}

func (x *AuthoriseMandateActionRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *AuthoriseMandateActionRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *AuthoriseMandateActionRequest) GetFromActorId() string {
	if x != nil {
		return x.FromActorId
	}
	return ""
}

func (x *AuthoriseMandateActionRequest) GetToActorId() string {
	if x != nil {
		return x.ToActorId
	}
	return ""
}

func (x *AuthoriseMandateActionRequest) GetFromPiId() string {
	if x != nil {
		return x.FromPiId
	}
	return ""
}

func (x *AuthoriseMandateActionRequest) GetToPiId() string {
	if x != nil {
		return x.ToPiId
	}
	return ""
}

func (x *AuthoriseMandateActionRequest) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *AuthoriseMandateActionRequest) GetMandateValidity() *Validity {
	if x != nil {
		return x.MandateValidity
	}
	return nil
}

func (x *AuthoriseMandateActionRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *AuthoriseMandateActionRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *AuthoriseMandateActionRequest) GetAmountRule() AmountRule {
	if x != nil {
		return x.AmountRule
	}
	return AmountRule_AMOUNT_RULE_UNSPECIFIED
}

func (x *AuthoriseMandateActionRequest) GetRecurrence() *Recurrence {
	if x != nil {
		return x.Recurrence
	}
	return nil
}

type AuthoriseMandateActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status               *rpc.Status                            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ActionDetailedStatus *recurringpayment.ActionDetailedStatus `protobuf:"bytes,2,opt,name=action_detailed_status,json=actionDetailedStatus,proto3" json:"action_detailed_status,omitempty"`
}

func (x *AuthoriseMandateActionResponse) Reset() {
	*x = AuthoriseMandateActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthoriseMandateActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthoriseMandateActionResponse) ProtoMessage() {}

func (x *AuthoriseMandateActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthoriseMandateActionResponse.ProtoReflect.Descriptor instead.
func (*AuthoriseMandateActionResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{7}
}

func (x *AuthoriseMandateActionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *AuthoriseMandateActionResponse) GetActionDetailedStatus() *recurringpayment.ActionDetailedStatus {
	if x != nil {
		return x.ActionDetailedStatus
	}
	return nil
}

type FetchAndUpdateRequestStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// request if for which the status needs to fetched
	ReqId string `protobuf:"bytes,1,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// partner bank involved in the mandate
	PartnerBank vendorgateway.Vendor `protobuf:"varint,2,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// pi id of the payment instrument belonging to epifi user
	EpifiCustomerPi string `protobuf:"bytes,3,opt,name=epifi_customer_pi,json=epifiCustomerPi,proto3" json:"epifi_customer_pi,omitempty"`
	// Context: As per NPCI guidelines, vendor status enquiry (ReqChkTxnStatus) for UPI mandate actions (creation or modification)
	// should not be made within a minimum delay period (e.g., 90 seconds) from the time the action was initiated.
	// This flag allows bypassing the delay period in exceptional cases.
	// Default: false (recommended for production to comply with NPCI guidelines)
	// Note: Setting this to true should be used with caution as it may violate NPCI guidelines.
	ForceImmediateVendorEnquiry bool `protobuf:"varint,4,opt,name=force_immediate_vendor_enquiry,json=forceImmediateVendorEnquiry,proto3" json:"force_immediate_vendor_enquiry,omitempty"`
}

func (x *FetchAndUpdateRequestStatusRequest) Reset() {
	*x = FetchAndUpdateRequestStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAndUpdateRequestStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAndUpdateRequestStatusRequest) ProtoMessage() {}

func (x *FetchAndUpdateRequestStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAndUpdateRequestStatusRequest.ProtoReflect.Descriptor instead.
func (*FetchAndUpdateRequestStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{8}
}

func (x *FetchAndUpdateRequestStatusRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *FetchAndUpdateRequestStatusRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *FetchAndUpdateRequestStatusRequest) GetEpifiCustomerPi() string {
	if x != nil {
		return x.EpifiCustomerPi
	}
	return ""
}

func (x *FetchAndUpdateRequestStatusRequest) GetForceImmediateVendorEnquiry() bool {
	if x != nil {
		return x.ForceImmediateVendorEnquiry
	}
	return false
}

type FetchAndUpdateRequestStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status               *rpc.Status                            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ActionDetailedStatus *recurringpayment.ActionDetailedStatus `protobuf:"bytes,2,opt,name=action_detailed_status,json=actionDetailedStatus,proto3" json:"action_detailed_status,omitempty"`
}

func (x *FetchAndUpdateRequestStatusResponse) Reset() {
	*x = FetchAndUpdateRequestStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAndUpdateRequestStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAndUpdateRequestStatusResponse) ProtoMessage() {}

func (x *FetchAndUpdateRequestStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAndUpdateRequestStatusResponse.ProtoReflect.Descriptor instead.
func (*FetchAndUpdateRequestStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{9}
}

func (x *FetchAndUpdateRequestStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchAndUpdateRequestStatusResponse) GetActionDetailedStatus() *recurringpayment.ActionDetailedStatus {
	if x != nil {
		return x.ActionDetailedStatus
	}
	return nil
}

type DeclineMandateActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// string req id of action to be declined
	ReqId string `protobuf:"bytes,1,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
}

func (x *DeclineMandateActionRequest) Reset() {
	*x = DeclineMandateActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclineMandateActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineMandateActionRequest) ProtoMessage() {}

func (x *DeclineMandateActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineMandateActionRequest.ProtoReflect.Descriptor instead.
func (*DeclineMandateActionRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{10}
}

func (x *DeclineMandateActionRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

type DeclineMandateActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeclineMandateActionResponse) Reset() {
	*x = DeclineMandateActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeclineMandateActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeclineMandateActionResponse) ProtoMessage() {}

func (x *DeclineMandateActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeclineMandateActionResponse.ProtoReflect.Descriptor instead.
func (*DeclineMandateActionResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{11}
}

func (x *DeclineMandateActionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetMandateRequestParametersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqId string `protobuf:"bytes,1,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
}

func (x *GetMandateRequestParametersRequest) Reset() {
	*x = GetMandateRequestParametersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateRequestParametersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateRequestParametersRequest) ProtoMessage() {}

func (x *GetMandateRequestParametersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateRequestParametersRequest.ProtoReflect.Descriptor instead.
func (*GetMandateRequestParametersRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetMandateRequestParametersRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

type GetMandateRequestParametersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ReqId  string      `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// reference id corresponds to order-id in case of merchant payment. It has no significance in case of P2P
	// e.g. a merchant shows a dynamic QR and wants a reference id in the payment that ties it to their internal order system.
	MerchantRefId string `protobuf:"bytes,3,opt,name=merchant_ref_id,json=merchantRefId,proto3" json:"merchant_ref_id,omitempty"`
	// transaction reference url should be a URL when clicked provides customer with further transaction details
	// like complete bill details, bill copy, order copy, ticket details, etc.
	//
	// for dynamic QR and intent based payments.. merchant system can send this information
	// in other cases we will be using a default string which may redirect to epifi
	RefUrl string `protobuf:"bytes,4,opt,name=ref_url,json=refUrl,proto3" json:"ref_url,omitempty"`
	// vpa of the payer involved in the transaction
	// we are storing this as even though the vpa is case insensitive, we need to pass the exact
	// vpa which we received, for salt generation and while sending a call back to NPCI
	PayerVpa string `protobuf:"bytes,5,opt,name=payer_vpa,json=payerVpa,proto3" json:"payer_vpa,omitempty"`
	// vpa of the payee involved in the transaction
	// we are storing this as even though the vpa is case insensitive, we need to pass the exact
	// vpa which we received, for salt generation and while sending a call back to NPCI
	PayeeVpa string `protobuf:"bytes,6,opt,name=payee_vpa,json=payeeVpa,proto3" json:"payee_vpa,omitempty"`
}

func (x *GetMandateRequestParametersResponse) Reset() {
	*x = GetMandateRequestParametersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateRequestParametersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateRequestParametersResponse) ProtoMessage() {}

func (x *GetMandateRequestParametersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateRequestParametersResponse.ProtoReflect.Descriptor instead.
func (*GetMandateRequestParametersResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetMandateRequestParametersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMandateRequestParametersResponse) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *GetMandateRequestParametersResponse) GetMerchantRefId() string {
	if x != nil {
		return x.MerchantRefId
	}
	return ""
}

func (x *GetMandateRequestParametersResponse) GetRefUrl() string {
	if x != nil {
		return x.RefUrl
	}
	return ""
}

func (x *GetMandateRequestParametersResponse) GetPayerVpa() string {
	if x != nil {
		return x.PayerVpa
	}
	return ""
}

func (x *GetMandateRequestParametersResponse) GetPayeeVpa() string {
	if x != nil {
		return x.PayeeVpa
	}
	return ""
}

type GetMandateDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecurringPaymentId string `protobuf:"bytes,1,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
}

func (x *GetMandateDetailsRequest) Reset() {
	*x = GetMandateDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateDetailsRequest) ProtoMessage() {}

func (x *GetMandateDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetMandateDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{14}
}

func (x *GetMandateDetailsRequest) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

type GetMandateDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// umn of the mandate
	Umn string `protobuf:"bytes,2,opt,name=umn,proto3" json:"umn,omitempty"`
}

func (x *GetMandateDetailsResponse) Reset() {
	*x = GetMandateDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMandateDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMandateDetailsResponse) ProtoMessage() {}

func (x *GetMandateDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMandateDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetMandateDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetMandateDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMandateDetailsResponse) GetUmn() string {
	if x != nil {
		return x.Umn
	}
	return ""
}

type ModifyMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// recurring payment id
	RecurringPaymentId string `protobuf:"bytes,1,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
	// req_id shared with the vendor
	ReqId string `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// payload for the mandate request
	MandateRequestPayload *Payload `protobuf:"bytes,3,opt,name=mandate_request_payload,json=mandateRequestPayload,proto3" json:"mandate_request_payload,omitempty"`
	// current actor role- payer/payee
	CurrentActorRole ActorRole `protobuf:"varint,4,opt,name=current_actor_role,json=currentActorRole,proto3,enum=upi.mandate.ActorRole" json:"current_actor_role,omitempty"`
	// partner bank which the mandate is getting initialised with
	PartnerBank vendorgateway.Vendor `protobuf:"varint,5,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
}

func (x *ModifyMandateRequest) Reset() {
	*x = ModifyMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyMandateRequest) ProtoMessage() {}

func (x *ModifyMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyMandateRequest.ProtoReflect.Descriptor instead.
func (*ModifyMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{16}
}

func (x *ModifyMandateRequest) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

func (x *ModifyMandateRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *ModifyMandateRequest) GetMandateRequestPayload() *Payload {
	if x != nil {
		return x.MandateRequestPayload
	}
	return nil
}

func (x *ModifyMandateRequest) GetCurrentActorRole() ActorRole {
	if x != nil {
		return x.CurrentActorRole
	}
	return ActorRole_ACTOR_ROLE_UNSPECIFIED
}

func (x *ModifyMandateRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

type ModifyMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ModifyMandateResponse) Reset() {
	*x = ModifyMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyMandateResponse) ProtoMessage() {}

func (x *ModifyMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyMandateResponse.ProtoReflect.Descriptor instead.
func (*ModifyMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{17}
}

func (x *ModifyMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type RevokeMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// recurring payment id
	RecurringPaymentId string `protobuf:"bytes,1,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
	// req_id shared with the vendor
	ReqId string `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// payload for the mandate request
	MandateRequestPayload *Payload `protobuf:"bytes,3,opt,name=mandate_request_payload,json=mandateRequestPayload,proto3" json:"mandate_request_payload,omitempty"`
	// current actor role- payer/payee
	CurrentActorRole ActorRole `protobuf:"varint,4,opt,name=current_actor_role,json=currentActorRole,proto3,enum=upi.mandate.ActorRole" json:"current_actor_role,omitempty"`
	// partner bank which the mandate is getting initialised with
	PartnerBank vendorgateway.Vendor `protobuf:"varint,5,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// initiator of the mandate - PAYER
	InitiatedBy MandateInitiatedBy `protobuf:"varint,6,opt,name=initiated_by,json=initiatedBy,proto3,enum=upi.mandate.MandateInitiatedBy" json:"initiated_by,omitempty"`
}

func (x *RevokeMandateRequest) Reset() {
	*x = RevokeMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RevokeMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeMandateRequest) ProtoMessage() {}

func (x *RevokeMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeMandateRequest.ProtoReflect.Descriptor instead.
func (*RevokeMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{18}
}

func (x *RevokeMandateRequest) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

func (x *RevokeMandateRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *RevokeMandateRequest) GetMandateRequestPayload() *Payload {
	if x != nil {
		return x.MandateRequestPayload
	}
	return nil
}

func (x *RevokeMandateRequest) GetCurrentActorRole() ActorRole {
	if x != nil {
		return x.CurrentActorRole
	}
	return ActorRole_ACTOR_ROLE_UNSPECIFIED
}

func (x *RevokeMandateRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *RevokeMandateRequest) GetInitiatedBy() MandateInitiatedBy {
	if x != nil {
		return x.InitiatedBy
	}
	return MandateInitiatedBy_MANDATE_INITIATED_BY_UNSPECIFIED
}

type RevokeMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RevokeMandateResponse) Reset() {
	*x = RevokeMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RevokeMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeMandateResponse) ProtoMessage() {}

func (x *RevokeMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeMandateResponse.ProtoReflect.Descriptor instead.
func (*RevokeMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{19}
}

func (x *RevokeMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type PauseUnpauseMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// recurring payment id
	RecurringPaymentId string `protobuf:"bytes,1,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
	// req_id shared with the vendor
	ReqId string `protobuf:"bytes,2,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	// payload for the mandate request
	MandateRequestPayload *Payload `protobuf:"bytes,3,opt,name=mandate_request_payload,json=mandateRequestPayload,proto3" json:"mandate_request_payload,omitempty"`
	// current actor role- payer/payee
	CurrentActorRole ActorRole `protobuf:"varint,4,opt,name=current_actor_role,json=currentActorRole,proto3,enum=upi.mandate.ActorRole" json:"current_actor_role,omitempty"`
	// partner bank which the mandate is getting initialised with
	PartnerBank vendorgateway.Vendor `protobuf:"varint,5,opt,name=partner_bank,json=partnerBank,proto3,enum=vendorgateway.Vendor" json:"partner_bank,omitempty"`
	// request action that can either be pause or unpause
	ReqAction MandateType `protobuf:"varint,6,opt,name=req_action,json=reqAction,proto3,enum=upi.mandate.MandateType" json:"req_action,omitempty"`
}

func (x *PauseUnpauseMandateRequest) Reset() {
	*x = PauseUnpauseMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseUnpauseMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseUnpauseMandateRequest) ProtoMessage() {}

func (x *PauseUnpauseMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseUnpauseMandateRequest.ProtoReflect.Descriptor instead.
func (*PauseUnpauseMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{20}
}

func (x *PauseUnpauseMandateRequest) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

func (x *PauseUnpauseMandateRequest) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *PauseUnpauseMandateRequest) GetMandateRequestPayload() *Payload {
	if x != nil {
		return x.MandateRequestPayload
	}
	return nil
}

func (x *PauseUnpauseMandateRequest) GetCurrentActorRole() ActorRole {
	if x != nil {
		return x.CurrentActorRole
	}
	return ActorRole_ACTOR_ROLE_UNSPECIFIED
}

func (x *PauseUnpauseMandateRequest) GetPartnerBank() vendorgateway.Vendor {
	if x != nil {
		return x.PartnerBank
	}
	return vendorgateway.Vendor(0)
}

func (x *PauseUnpauseMandateRequest) GetReqAction() MandateType {
	if x != nil {
		return x.ReqAction
	}
	return MandateType_TYPE_UNSPECIFIED
}

type PauseUnpauseMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *PauseUnpauseMandateResponse) Reset() {
	*x = PauseUnpauseMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseUnpauseMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseUnpauseMandateResponse) ProtoMessage() {}

func (x *PauseUnpauseMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseUnpauseMandateResponse.ProtoReflect.Descriptor instead.
func (*PauseUnpauseMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{21}
}

func (x *PauseUnpauseMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type InitiateMandateExecutionRequest_AuthHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Device ID or fingerprint of the device that is registered with the partner bank
	Device *upi.Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// Credential contains a cred block that acts as second auth factor for the authorisation
	//
	// They typically contain Salt parameters in addition to PIN such as, but not limited to:
	// 1. Transaction ID
	// 2. Amount
	// 3. Timestamp
	NpciCredBlock *upi.CredBlock `protobuf:"bytes,2,opt,name=npci_cred_block,json=npciCredBlock,proto3" json:"npci_cred_block,omitempty"`
}

func (x *InitiateMandateExecutionRequest_AuthHeader) Reset() {
	*x = InitiateMandateExecutionRequest_AuthHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateMandateExecutionRequest_AuthHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateMandateExecutionRequest_AuthHeader) ProtoMessage() {}

func (x *InitiateMandateExecutionRequest_AuthHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateMandateExecutionRequest_AuthHeader.ProtoReflect.Descriptor instead.
func (*InitiateMandateExecutionRequest_AuthHeader) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *InitiateMandateExecutionRequest_AuthHeader) GetDevice() *upi.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *InitiateMandateExecutionRequest_AuthHeader) GetNpciCredBlock() *upi.CredBlock {
	if x != nil {
		return x.NpciCredBlock
	}
	return nil
}

type AuthoriseMandateActionRequest_AuthHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Device ID or fingerprint of the device that is registered with the partner bank
	Device *upi.Device `protobuf:"bytes,1,opt,name=device,proto3" json:"device,omitempty"`
	// Credential contains a cred block that acts as second auth factor for the authorisation
	//
	// They typically contain Salt parameters in addition to PIN such as, but not limited to:
	// 1. Transaction ID
	// 2. Amount
	// 3. Timestamp
	NpciCredBlock *upi.CredBlock `protobuf:"bytes,2,opt,name=npci_cred_block,json=npciCredBlock,proto3" json:"npci_cred_block,omitempty"`
}

func (x *AuthoriseMandateActionRequest_AuthHeader) Reset() {
	*x = AuthoriseMandateActionRequest_AuthHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_mandate_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthoriseMandateActionRequest_AuthHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthoriseMandateActionRequest_AuthHeader) ProtoMessage() {}

func (x *AuthoriseMandateActionRequest_AuthHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_mandate_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthoriseMandateActionRequest_AuthHeader.ProtoReflect.Descriptor instead.
func (*AuthoriseMandateActionRequest_AuthHeader) Descriptor() ([]byte, []int) {
	return file_api_upi_mandate_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *AuthoriseMandateActionRequest_AuthHeader) GetDevice() *upi.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *AuthoriseMandateActionRequest_AuthHeader) GetNpciCredBlock() *upi.CredBlock {
	if x != nil {
		return x.NpciCredBlock
	}
	return nil
}

var File_api_upi_mandate_service_proto protoreflect.FileDescriptor

var file_api_upi_mandate_service_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0b, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x1a, 0x34, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70,
	0x69, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70,
	0x69, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69,
	0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa6, 0x02, 0x0a, 0x1f, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x58, 0x0a, 0x0b, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x1a, 0x73, 0x0a, 0x0a, 0x41,
	0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x06, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x0f, 0x6e, 0x70, 0x63, 0x69,
	0x5f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x52, 0x0d, 0x6e, 0x70, 0x63, 0x69, 0x43, 0x72, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x22, 0x8a, 0x01, 0x0a, 0x20, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x41, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x64, 0x22, 0xbf, 0x04,
	0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x6d, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x6d,
	0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x70, 0x61,
	0x79, 0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x65,
	0x54, 0x6f, 0x50, 0x61, 0x79, 0x65, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x46, 0x75, 0x6e, 0x64, 0x12, 0x42, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x52, 0x0b, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x4c, 0x0a,
	0x17, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x50, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x15, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x44, 0x0a, 0x12, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x72, 0x6f, 0x6c,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x52,
	0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x6f, 0x6c,
	0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22,
	0xd3, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34,
	0x0a, 0x07, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x22, 0x5f, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e,
	0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x06,
	0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44,
	0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x82, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x06, 0x72,
	0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x72,
	0x65, 0x71, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x03, 0x75, 0x6d, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x75, 0x6d, 0x6e, 0x42, 0x0c, 0x0a, 0x0a,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xbc, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x22, 0x4b, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xe8, 0x05, 0x0a, 0x1d, 0x41, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x0b, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x35, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x75, 0x74,
	0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x72, 0x6f,
	0x6d, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x6f, 0x5f, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74,
	0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6d,
	0x5f, 0x70, 0x69, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x72,
	0x6f, 0x6d, 0x50, 0x69, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x08, 0x74, 0x6f, 0x5f, 0x70, 0x69, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x50, 0x69, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x40, 0x0a, 0x10, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x42, 0x0a, 0x0c,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x0b,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x17, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0a, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x1a,
	0x73, 0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2d, 0x0a,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x0f,
	0x6e, 0x70, 0x63, 0x69, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x0d, 0x6e, 0x70, 0x63, 0x69, 0x43, 0x72, 0x65, 0x64, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x87, 0x02, 0x0a, 0x1e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x73, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5c, 0x0a, 0x16,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x14, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x62, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11,
	0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45,
	0x44, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x66, 0x22, 0xe6,
	0x01, 0x0a, 0x22, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0c,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x70, 0x69, 0x66, 0x69, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x50, 0x69, 0x12, 0x43, 0x0a, 0x1e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6d, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x6e, 0x71,
	0x75, 0x69, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x49, 0x6d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x22, 0x98, 0x02, 0x0a, 0x23, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x41, 0x6e, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x5c, 0x0a, 0x16, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x14, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x6e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x33, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x45,
	0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x64, 0x12, 0x15, 0x0a, 0x11,
	0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x10, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x4f, 0x4f, 0x5f, 0x45, 0x41, 0x52, 0x4c, 0x59,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c,
	0x10, 0x66, 0x22, 0x34, 0x0a, 0x1b, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x1c, 0x44, 0x65, 0x63, 0x6c,
	0x69, 0x6e, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x34, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x41, 0x52, 0x47, 0x55, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0x3b, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64,
	0x22, 0xa9, 0x02, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a,
	0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72,
	0x65, 0x71, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x72, 0x65, 0x66, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x66, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x76,
	0x70, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x79, 0x65, 0x72, 0x56,
	0x70, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x65, 0x65, 0x5f, 0x76, 0x70, 0x61, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x79, 0x65, 0x65, 0x56, 0x70, 0x61, 0x22,
	0x4b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49,
	0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x4c, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x9f, 0x01, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x6d, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x6d, 0x6e, 0x22,
	0x4b, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x45, 0x52, 0x4d, 0x49,
	0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4e, 0x49, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xb7, 0x02, 0x0a,
	0x14, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x4c,
	0x0a, 0x17, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x15, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x44, 0x0a, 0x12,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x72, 0x6f,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x6f,
	0x6c, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x22, 0x86, 0x01, 0x0a, 0x15, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x48, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x12, 0x0a,
	0x0e, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10,
	0x06, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22,
	0xfb, 0x02, 0x0a, 0x14, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75,
	0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49,
	0x64, 0x12, 0x4c, 0x0a, 0x17, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x15, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x44, 0x0a, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x6f, 0x6c, 0x65, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72,
	0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x42, 0x0a, 0x0c, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0x86, 0x01,
	0x0a, 0x15, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x48, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x06, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x82, 0x03, 0x0a, 0x1a, 0x50, 0x61, 0x75, 0x73, 0x65,
	0x55, 0x6e, 0x70, 0x61, 0x75, 0x73, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x4c,
	0x0a, 0x17, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x15, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x44, 0x0a, 0x12,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x72, 0x6f,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x6f,
	0x6c, 0x65, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x62, 0x61,
	0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e,
	0x65, 0x72, 0x42, 0x61, 0x6e, 0x6b, 0x12, 0x43, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x18, 0x04, 0x18, 0x05,
	0x52, 0x09, 0x72, 0x65, 0x71, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8c, 0x01, 0x0a, 0x1b,
	0x50, 0x61, 0x75, 0x73, 0x65, 0x55, 0x6e, 0x70, 0x61, 0x75, 0x73, 0x65, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x48, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4c, 0x52, 0x45,
	0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x06, 0x12, 0x0c, 0x0a, 0x08,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x2a, 0x53, 0x0a, 0x09, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x43, 0x54, 0x4f, 0x52,
	0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x52, 0x4f, 0x4c,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x45, 0x52, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x45, 0x45, 0x10, 0x02, 0x32,
	0xaa, 0x09, 0x0a, 0x0e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x58, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4f, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x75, 0x70, 0x69,
	0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x73, 0x0a,
	0x16, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x1b, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x64, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x14, 0x44, 0x65, 0x63, 0x6c, 0x69,
	0x6e, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x28, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x44, 0x65,
	0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x2f, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x25, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x58, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x58, 0x0a, 0x0d, 0x52,
	0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x52, 0x65, 0x76, 0x6f, 0x6b,
	0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x52, 0x65,
	0x76, 0x6f, 0x6b, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6a, 0x0a, 0x13, 0x50, 0x61, 0x75, 0x73, 0x65, 0x55, 0x6e,
	0x70, 0x61, 0x75, 0x73, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x27, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x50, 0x61, 0x75, 0x73, 0x65,
	0x55, 0x6e, 0x70, 0x61, 0x75, 0x73, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x55, 0x6e, 0x70, 0x61, 0x75, 0x73, 0x65,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x79, 0x0a, 0x18, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x50, 0x0a, 0x26,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_mandate_service_proto_rawDescOnce sync.Once
	file_api_upi_mandate_service_proto_rawDescData = file_api_upi_mandate_service_proto_rawDesc
)

func file_api_upi_mandate_service_proto_rawDescGZIP() []byte {
	file_api_upi_mandate_service_proto_rawDescOnce.Do(func() {
		file_api_upi_mandate_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_mandate_service_proto_rawDescData)
	})
	return file_api_upi_mandate_service_proto_rawDescData
}

var file_api_upi_mandate_service_proto_enumTypes = make([]protoimpl.EnumInfo, 12)
var file_api_upi_mandate_service_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_api_upi_mandate_service_proto_goTypes = []interface{}{
	(ActorRole)(0), // 0: upi.mandate.ActorRole
	(InitiateMandateExecutionResponse_Status)(0),       // 1: upi.mandate.InitiateMandateExecutionResponse.Status
	(CreateMandateResponse_Status)(0),                  // 2: upi.mandate.CreateMandateResponse.Status
	(GetMandateResponse_Status)(0),                     // 3: upi.mandate.GetMandateResponse.Status
	(AuthoriseMandateActionResponse_Status)(0),         // 4: upi.mandate.AuthoriseMandateActionResponse.Status
	(FetchAndUpdateRequestStatusResponse_Status)(0),    // 5: upi.mandate.FetchAndUpdateRequestStatusResponse.Status
	(DeclineMandateActionResponse_Status)(0),           // 6: upi.mandate.DeclineMandateActionResponse.Status
	(GetMandateRequestParametersResponse_Status)(0),    // 7: upi.mandate.GetMandateRequestParametersResponse.Status
	(GetMandateDetailsResponse_Status)(0),              // 8: upi.mandate.GetMandateDetailsResponse.Status
	(ModifyMandateResponse_Status)(0),                  // 9: upi.mandate.ModifyMandateResponse.Status
	(RevokeMandateResponse_Status)(0),                  // 10: upi.mandate.RevokeMandateResponse.Status
	(PauseUnpauseMandateResponse_Status)(0),            // 11: upi.mandate.PauseUnpauseMandateResponse.Status
	(*InitiateMandateExecutionRequest)(nil),            // 12: upi.mandate.InitiateMandateExecutionRequest
	(*InitiateMandateExecutionResponse)(nil),           // 13: upi.mandate.InitiateMandateExecutionResponse
	(*CreateMandateRequest)(nil),                       // 14: upi.mandate.CreateMandateRequest
	(*CreateMandateResponse)(nil),                      // 15: upi.mandate.CreateMandateResponse
	(*GetMandateRequest)(nil),                          // 16: upi.mandate.GetMandateRequest
	(*GetMandateResponse)(nil),                         // 17: upi.mandate.GetMandateResponse
	(*AuthoriseMandateActionRequest)(nil),              // 18: upi.mandate.AuthoriseMandateActionRequest
	(*AuthoriseMandateActionResponse)(nil),             // 19: upi.mandate.AuthoriseMandateActionResponse
	(*FetchAndUpdateRequestStatusRequest)(nil),         // 20: upi.mandate.FetchAndUpdateRequestStatusRequest
	(*FetchAndUpdateRequestStatusResponse)(nil),        // 21: upi.mandate.FetchAndUpdateRequestStatusResponse
	(*DeclineMandateActionRequest)(nil),                // 22: upi.mandate.DeclineMandateActionRequest
	(*DeclineMandateActionResponse)(nil),               // 23: upi.mandate.DeclineMandateActionResponse
	(*GetMandateRequestParametersRequest)(nil),         // 24: upi.mandate.GetMandateRequestParametersRequest
	(*GetMandateRequestParametersResponse)(nil),        // 25: upi.mandate.GetMandateRequestParametersResponse
	(*GetMandateDetailsRequest)(nil),                   // 26: upi.mandate.GetMandateDetailsRequest
	(*GetMandateDetailsResponse)(nil),                  // 27: upi.mandate.GetMandateDetailsResponse
	(*ModifyMandateRequest)(nil),                       // 28: upi.mandate.ModifyMandateRequest
	(*ModifyMandateResponse)(nil),                      // 29: upi.mandate.ModifyMandateResponse
	(*RevokeMandateRequest)(nil),                       // 30: upi.mandate.RevokeMandateRequest
	(*RevokeMandateResponse)(nil),                      // 31: upi.mandate.RevokeMandateResponse
	(*PauseUnpauseMandateRequest)(nil),                 // 32: upi.mandate.PauseUnpauseMandateRequest
	(*PauseUnpauseMandateResponse)(nil),                // 33: upi.mandate.PauseUnpauseMandateResponse
	(*InitiateMandateExecutionRequest_AuthHeader)(nil), // 34: upi.mandate.InitiateMandateExecutionRequest.AuthHeader
	(*AuthoriseMandateActionRequest_AuthHeader)(nil),   // 35: upi.mandate.AuthoriseMandateActionRequest.AuthHeader
	(*rpc.Status)(nil),                                 // 36: rpc.Status
	(MandateInitiatedBy)(0),                            // 37: upi.mandate.MandateInitiatedBy
	(*Payload)(nil),                                    // 38: upi.mandate.Payload
	(vendorgateway.Vendor)(0),                          // 39: vendorgateway.Vendor
	(*MandateEntity)(nil),                              // 40: upi.mandate.MandateEntity
	(*Validity)(nil),                                   // 41: upi.mandate.Validity
	(*money.Money)(nil),                                // 42: google.type.Money
	(AmountRule)(0),                                    // 43: upi.mandate.AmountRule
	(*Recurrence)(nil),                                 // 44: upi.mandate.Recurrence
	(*recurringpayment.ActionDetailedStatus)(nil),      // 45: recurringpayment.ActionDetailedStatus
	(MandateType)(0),                                   // 46: upi.mandate.MandateType
	(*upi.Device)(nil),                                 // 47: upi.Device
	(*upi.CredBlock)(nil),                              // 48: upi.CredBlock
}
var file_api_upi_mandate_service_proto_depIdxs = []int32{
	34, // 0: upi.mandate.InitiateMandateExecutionRequest.auth_header:type_name -> upi.mandate.InitiateMandateExecutionRequest.AuthHeader
	36, // 1: upi.mandate.InitiateMandateExecutionResponse.status:type_name -> rpc.Status
	37, // 2: upi.mandate.CreateMandateRequest.initiated_by:type_name -> upi.mandate.MandateInitiatedBy
	38, // 3: upi.mandate.CreateMandateRequest.mandate_request_payload:type_name -> upi.mandate.Payload
	0,  // 4: upi.mandate.CreateMandateRequest.current_actor_role:type_name -> upi.mandate.ActorRole
	39, // 5: upi.mandate.CreateMandateRequest.partner_bank:type_name -> vendorgateway.Vendor
	36, // 6: upi.mandate.CreateMandateResponse.status:type_name -> rpc.Status
	40, // 7: upi.mandate.CreateMandateResponse.mandate:type_name -> upi.mandate.MandateEntity
	36, // 8: upi.mandate.GetMandateResponse.status:type_name -> rpc.Status
	40, // 9: upi.mandate.GetMandateResponse.mandate:type_name -> upi.mandate.MandateEntity
	35, // 10: upi.mandate.AuthoriseMandateActionRequest.auth_header:type_name -> upi.mandate.AuthoriseMandateActionRequest.AuthHeader
	41, // 11: upi.mandate.AuthoriseMandateActionRequest.mandate_validity:type_name -> upi.mandate.Validity
	39, // 12: upi.mandate.AuthoriseMandateActionRequest.partner_bank:type_name -> vendorgateway.Vendor
	42, // 13: upi.mandate.AuthoriseMandateActionRequest.amount:type_name -> google.type.Money
	43, // 14: upi.mandate.AuthoriseMandateActionRequest.amount_rule:type_name -> upi.mandate.AmountRule
	44, // 15: upi.mandate.AuthoriseMandateActionRequest.recurrence:type_name -> upi.mandate.Recurrence
	36, // 16: upi.mandate.AuthoriseMandateActionResponse.status:type_name -> rpc.Status
	45, // 17: upi.mandate.AuthoriseMandateActionResponse.action_detailed_status:type_name -> recurringpayment.ActionDetailedStatus
	39, // 18: upi.mandate.FetchAndUpdateRequestStatusRequest.partner_bank:type_name -> vendorgateway.Vendor
	36, // 19: upi.mandate.FetchAndUpdateRequestStatusResponse.status:type_name -> rpc.Status
	45, // 20: upi.mandate.FetchAndUpdateRequestStatusResponse.action_detailed_status:type_name -> recurringpayment.ActionDetailedStatus
	36, // 21: upi.mandate.DeclineMandateActionResponse.status:type_name -> rpc.Status
	36, // 22: upi.mandate.GetMandateRequestParametersResponse.status:type_name -> rpc.Status
	36, // 23: upi.mandate.GetMandateDetailsResponse.status:type_name -> rpc.Status
	38, // 24: upi.mandate.ModifyMandateRequest.mandate_request_payload:type_name -> upi.mandate.Payload
	0,  // 25: upi.mandate.ModifyMandateRequest.current_actor_role:type_name -> upi.mandate.ActorRole
	39, // 26: upi.mandate.ModifyMandateRequest.partner_bank:type_name -> vendorgateway.Vendor
	36, // 27: upi.mandate.ModifyMandateResponse.status:type_name -> rpc.Status
	38, // 28: upi.mandate.RevokeMandateRequest.mandate_request_payload:type_name -> upi.mandate.Payload
	0,  // 29: upi.mandate.RevokeMandateRequest.current_actor_role:type_name -> upi.mandate.ActorRole
	39, // 30: upi.mandate.RevokeMandateRequest.partner_bank:type_name -> vendorgateway.Vendor
	37, // 31: upi.mandate.RevokeMandateRequest.initiated_by:type_name -> upi.mandate.MandateInitiatedBy
	36, // 32: upi.mandate.RevokeMandateResponse.status:type_name -> rpc.Status
	38, // 33: upi.mandate.PauseUnpauseMandateRequest.mandate_request_payload:type_name -> upi.mandate.Payload
	0,  // 34: upi.mandate.PauseUnpauseMandateRequest.current_actor_role:type_name -> upi.mandate.ActorRole
	39, // 35: upi.mandate.PauseUnpauseMandateRequest.partner_bank:type_name -> vendorgateway.Vendor
	46, // 36: upi.mandate.PauseUnpauseMandateRequest.req_action:type_name -> upi.mandate.MandateType
	36, // 37: upi.mandate.PauseUnpauseMandateResponse.status:type_name -> rpc.Status
	47, // 38: upi.mandate.InitiateMandateExecutionRequest.AuthHeader.device:type_name -> upi.Device
	48, // 39: upi.mandate.InitiateMandateExecutionRequest.AuthHeader.npci_cred_block:type_name -> upi.CredBlock
	47, // 40: upi.mandate.AuthoriseMandateActionRequest.AuthHeader.device:type_name -> upi.Device
	48, // 41: upi.mandate.AuthoriseMandateActionRequest.AuthHeader.npci_cred_block:type_name -> upi.CredBlock
	14, // 42: upi.mandate.MandateService.CreateMandate:input_type -> upi.mandate.CreateMandateRequest
	16, // 43: upi.mandate.MandateService.GetMandate:input_type -> upi.mandate.GetMandateRequest
	18, // 44: upi.mandate.MandateService.AuthoriseMandateAction:input_type -> upi.mandate.AuthoriseMandateActionRequest
	20, // 45: upi.mandate.MandateService.FetchAndUpdateRequestStatus:input_type -> upi.mandate.FetchAndUpdateRequestStatusRequest
	22, // 46: upi.mandate.MandateService.DeclineMandateAction:input_type -> upi.mandate.DeclineMandateActionRequest
	24, // 47: upi.mandate.MandateService.GetMandateRequestParameters:input_type -> upi.mandate.GetMandateRequestParametersRequest
	26, // 48: upi.mandate.MandateService.GetMandateDetails:input_type -> upi.mandate.GetMandateDetailsRequest
	28, // 49: upi.mandate.MandateService.ModifyMandate:input_type -> upi.mandate.ModifyMandateRequest
	30, // 50: upi.mandate.MandateService.RevokeMandate:input_type -> upi.mandate.RevokeMandateRequest
	32, // 51: upi.mandate.MandateService.PauseUnpauseMandate:input_type -> upi.mandate.PauseUnpauseMandateRequest
	12, // 52: upi.mandate.MandateService.InitiateMandateExecution:input_type -> upi.mandate.InitiateMandateExecutionRequest
	15, // 53: upi.mandate.MandateService.CreateMandate:output_type -> upi.mandate.CreateMandateResponse
	17, // 54: upi.mandate.MandateService.GetMandate:output_type -> upi.mandate.GetMandateResponse
	19, // 55: upi.mandate.MandateService.AuthoriseMandateAction:output_type -> upi.mandate.AuthoriseMandateActionResponse
	21, // 56: upi.mandate.MandateService.FetchAndUpdateRequestStatus:output_type -> upi.mandate.FetchAndUpdateRequestStatusResponse
	23, // 57: upi.mandate.MandateService.DeclineMandateAction:output_type -> upi.mandate.DeclineMandateActionResponse
	25, // 58: upi.mandate.MandateService.GetMandateRequestParameters:output_type -> upi.mandate.GetMandateRequestParametersResponse
	27, // 59: upi.mandate.MandateService.GetMandateDetails:output_type -> upi.mandate.GetMandateDetailsResponse
	29, // 60: upi.mandate.MandateService.ModifyMandate:output_type -> upi.mandate.ModifyMandateResponse
	31, // 61: upi.mandate.MandateService.RevokeMandate:output_type -> upi.mandate.RevokeMandateResponse
	33, // 62: upi.mandate.MandateService.PauseUnpauseMandate:output_type -> upi.mandate.PauseUnpauseMandateResponse
	13, // 63: upi.mandate.MandateService.InitiateMandateExecution:output_type -> upi.mandate.InitiateMandateExecutionResponse
	53, // [53:64] is the sub-list for method output_type
	42, // [42:53] is the sub-list for method input_type
	42, // [42:42] is the sub-list for extension type_name
	42, // [42:42] is the sub-list for extension extendee
	0,  // [0:42] is the sub-list for field type_name
}

func init() { file_api_upi_mandate_service_proto_init() }
func file_api_upi_mandate_service_proto_init() {
	if File_api_upi_mandate_service_proto != nil {
		return
	}
	file_api_upi_mandate_mandate_proto_init()
	file_api_upi_mandate_mandate_entity_proto_init()
	file_api_upi_mandate_mandate_requests_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_upi_mandate_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateMandateExecutionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateMandateExecutionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthoriseMandateActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthoriseMandateActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAndUpdateRequestStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAndUpdateRequestStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclineMandateActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeclineMandateActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateRequestParametersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateRequestParametersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMandateDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RevokeMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RevokeMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseUnpauseMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseUnpauseMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateMandateExecutionRequest_AuthHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_mandate_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthoriseMandateActionRequest_AuthHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_upi_mandate_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*GetMandateRequest_ReqId)(nil),
		(*GetMandateRequest_RecurringPaymentId)(nil),
		(*GetMandateRequest_Umn)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_mandate_service_proto_rawDesc,
			NumEnums:      12,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_upi_mandate_service_proto_goTypes,
		DependencyIndexes: file_api_upi_mandate_service_proto_depIdxs,
		EnumInfos:         file_api_upi_mandate_service_proto_enumTypes,
		MessageInfos:      file_api_upi_mandate_service_proto_msgTypes,
	}.Build()
	File_api_upi_mandate_service_proto = out.File
	file_api_upi_mandate_service_proto_rawDesc = nil
	file_api_upi_mandate_service_proto_goTypes = nil
	file_api_upi_mandate_service_proto_depIdxs = nil
}
