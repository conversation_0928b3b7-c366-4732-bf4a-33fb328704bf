package upi

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"strings"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
)

const (
	AndroidAppName = "com.epifi.paisa"

	IosAppName = "com.epifi.fi"

	defaultDeviceType = "MOB"

	androidOsPrefix = "Android"

	iosOsPrefix = "iOS"
)

var (
	PlatformToDeviceAppMap = map[commontypes.Platform]Device_App{
		// treating default as ANDROID as default for backward compatibility reasons
		commontypes.Platform_PLATFORM_UNSPECIFIED: Device_ANDROID,
		commontypes.Platform_ANDROID:              Device_ANDROID,
		commontypes.Platform_IOS:                  Device_IOS,
	}
)

// FromFeDevice creates UPI device from types/device.proto
func FromFeDevice(ctx context.Context, d *commontypes.Device, ph *commontypes.PhoneNumber) *Device {
	ip := epificontext.UserIPAddrFromContext(ctx)
	app := PlatformToDeviceAppMap[d.GetPlatform()]
	device := &Device{
		PhoneNumber:   ph,
		Geocode:       d.GetLatLng(),
		Id:            d.GetDeviceId(),
		OsVersion:     GetDeviceOsVersion(app, d.GetSwVersion()),
		App:           app,
		Type:          defaultDeviceType,
		LocationToken: d.GetLocationToken(),
	}

	if ip != epificontext.UnknownIP {
		device.IpAddr = ip
	}

	return device
}

func DeviceAppFromString(app string) Device_App {
	return Device_App(Device_App_value[app])
}

func (da Device_App) ToString() string {
	// attempt to get the env name on the best effort basis
	// in case env name is missing we fallback to default name
	// defined in the constants
	env, _ := cfg.GetEnvironment()

	return getAppPkgNameForEnv(da, env)
}

func getAppPkgNameForEnv(app Device_App, env string) string {
	switch app {
	case Device_ANDROID:
		if env == "" || env == cfg.ProductionEnv {
			return AndroidAppName
		}

		return strings.Join([]string{AndroidAppName, env}, ".")
	case Device_IOS:
		if env == "" || env == cfg.ProductionEnv {
			return IosAppName
		}

		return strings.Join([]string{IosAppName, env}, ".")

	default:
		return app.String()
	}
}

func GetDeviceOsVersion(app Device_App, clientOsVersion string) string {
	switch app {
	case Device_ANDROID:
		return strings.Join([]string{androidOsPrefix, clientOsVersion}, " ")
	case Device_IOS:
		return strings.Join([]string{iosOsPrefix, clientOsVersion}, " ")
	default:
		return clientOsVersion
	}
}
