// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/developer/developer.proto

package developer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpiEntity int32

const (
	UpiEntity_UPI_ENTITY_UNSPECIFIED      UpiEntity = 0
	UpiEntity_UPI_ACCOUNT_INFO            UpiEntity = 1
	UpiEntity_ACCOUNT_UPI_PIN_INFO        UpiEntity = 2
	UpiEntity_PSP_KEY                     UpiEntity = 3
	UpiEntity_VERIFIED_ADDRESS_ENTRY      UpiEntity = 4
	UpiEntity_VPA_MERCHANT_INFO           UpiEntity = 5
	UpiEntity_UPI_MANDATE                 UpiEntity = 6
	UpiEntity_UPI_MANDATE_REQUEST         UpiEntity = 7
	UpiEntity_UPI_ACCOUNT                 UpiEntity = 8
	UpiEntity_UPI_ONBOARDING_DETAILS      UpiEntity = 9
	UpiEntity_UPI_REQUEST_LOGS            UpiEntity = 10
	UpiEntity_UPI_NUMBER_PI_MAPPING       UpiEntity = 11
	UpiEntity_ACTOR_UPI_NUMBER_RESOLUTION UpiEntity = 12
)

// Enum value maps for UpiEntity.
var (
	UpiEntity_name = map[int32]string{
		0:  "UPI_ENTITY_UNSPECIFIED",
		1:  "UPI_ACCOUNT_INFO",
		2:  "ACCOUNT_UPI_PIN_INFO",
		3:  "PSP_KEY",
		4:  "VERIFIED_ADDRESS_ENTRY",
		5:  "VPA_MERCHANT_INFO",
		6:  "UPI_MANDATE",
		7:  "UPI_MANDATE_REQUEST",
		8:  "UPI_ACCOUNT",
		9:  "UPI_ONBOARDING_DETAILS",
		10: "UPI_REQUEST_LOGS",
		11: "UPI_NUMBER_PI_MAPPING",
		12: "ACTOR_UPI_NUMBER_RESOLUTION",
	}
	UpiEntity_value = map[string]int32{
		"UPI_ENTITY_UNSPECIFIED":      0,
		"UPI_ACCOUNT_INFO":            1,
		"ACCOUNT_UPI_PIN_INFO":        2,
		"PSP_KEY":                     3,
		"VERIFIED_ADDRESS_ENTRY":      4,
		"VPA_MERCHANT_INFO":           5,
		"UPI_MANDATE":                 6,
		"UPI_MANDATE_REQUEST":         7,
		"UPI_ACCOUNT":                 8,
		"UPI_ONBOARDING_DETAILS":      9,
		"UPI_REQUEST_LOGS":            10,
		"UPI_NUMBER_PI_MAPPING":       11,
		"ACTOR_UPI_NUMBER_RESOLUTION": 12,
	}
)

func (x UpiEntity) Enum() *UpiEntity {
	p := new(UpiEntity)
	*p = x
	return p
}

func (x UpiEntity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpiEntity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_developer_developer_proto_enumTypes[0].Descriptor()
}

func (UpiEntity) Type() protoreflect.EnumType {
	return &file_api_upi_developer_developer_proto_enumTypes[0]
}

func (x UpiEntity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpiEntity.Descriptor instead.
func (UpiEntity) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_developer_developer_proto_rawDescGZIP(), []int{0}
}

var File_api_upi_developer_developer_proto protoreflect.FileDescriptor

var file_api_upi_developer_developer_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x2a, 0xc0, 0x02, 0x0a, 0x09, 0x55, 0x70, 0x69, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x1a, 0x0a, 0x16, 0x55, 0x50, 0x49, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x55, 0x50, 0x49, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x55, 0x50,
	0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07,
	0x50, 0x53, 0x50, 0x5f, 0x4b, 0x45, 0x59, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x45, 0x4e,
	0x54, 0x52, 0x59, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x50, 0x41, 0x5f, 0x4d, 0x45, 0x52,
	0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b,
	0x55, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x10, 0x06, 0x12, 0x17, 0x0a,
	0x13, 0x55, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x50, 0x49, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x50, 0x49, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x09, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x50, 0x49, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x53, 0x10, 0x0a, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x50, 0x49,
	0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49,
	0x4e, 0x47, 0x10, 0x0b, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x50,
	0x49, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x55, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x0c, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70,
	0x69, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_upi_developer_developer_proto_rawDescOnce sync.Once
	file_api_upi_developer_developer_proto_rawDescData = file_api_upi_developer_developer_proto_rawDesc
)

func file_api_upi_developer_developer_proto_rawDescGZIP() []byte {
	file_api_upi_developer_developer_proto_rawDescOnce.Do(func() {
		file_api_upi_developer_developer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_developer_developer_proto_rawDescData)
	})
	return file_api_upi_developer_developer_proto_rawDescData
}

var file_api_upi_developer_developer_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_developer_developer_proto_goTypes = []interface{}{
	(UpiEntity)(0), // 0: upi.developer.UpiEntity
}
var file_api_upi_developer_developer_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_upi_developer_developer_proto_init() }
func file_api_upi_developer_developer_proto_init() {
	if File_api_upi_developer_developer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_developer_developer_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_developer_developer_proto_goTypes,
		DependencyIndexes: file_api_upi_developer_developer_proto_depIdxs,
		EnumInfos:         file_api_upi_developer_developer_proto_enumTypes,
	}.Build()
	File_api_upi_developer_developer_proto = out.File
	file_api_upi_developer_developer_proto_rawDesc = nil
	file_api_upi_developer_developer_proto_goTypes = nil
	file_api_upi_developer_developer_proto_depIdxs = nil
}
