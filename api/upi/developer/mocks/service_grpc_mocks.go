// Code generated by MockGen. DO NOT EDIT.
// Source: api/upi/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDevClient is a mock of DevClient interface.
type MockDevClient struct {
	ctrl     *gomock.Controller
	recorder *MockDevClientMockRecorder
}

// MockDevClientMockRecorder is the mock recorder for MockDevClient.
type MockDevClientMockRecorder struct {
	mock *MockDevClient
}

// NewMockDevClient creates a new mock instance.
func NewMockDevClient(ctrl *gomock.Controller) *MockDevClient {
	mock := &MockDevClient{ctrl: ctrl}
	mock.recorder = &MockDevClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevClient) EXPECT() *MockDevClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockDevClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockDevClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevClient)(nil).GetParameterList), varargs...)
}

// MockDevServer is a mock of DevServer interface.
type MockDevServer struct {
	ctrl     *gomock.Controller
	recorder *MockDevServerMockRecorder
}

// MockDevServerMockRecorder is the mock recorder for MockDevServer.
type MockDevServerMockRecorder struct {
	mock *MockDevServer
}

// NewMockDevServer creates a new mock instance.
func NewMockDevServer(ctrl *gomock.Controller) *MockDevServer {
	mock := &MockDevServer{ctrl: ctrl}
	mock.recorder = &MockDevServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevServer) EXPECT() *MockDevServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockDevServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockDevServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeDevServer is a mock of UnsafeDevServer interface.
type MockUnsafeDevServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDevServerMockRecorder
}

// MockUnsafeDevServerMockRecorder is the mock recorder for MockUnsafeDevServer.
type MockUnsafeDevServerMockRecorder struct {
	mock *MockUnsafeDevServer
}

// NewMockUnsafeDevServer creates a new mock instance.
func NewMockUnsafeDevServer(ctrl *gomock.Controller) *MockUnsafeDevServer {
	mock := &MockUnsafeDevServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDevServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDevServer) EXPECT() *MockUnsafeDevServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDevServer mocks base method.
func (m *MockUnsafeDevServer) mustEmbedUnimplementedDevServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDevServer")
}

// mustEmbedUnimplementedDevServer indicates an expected call of mustEmbedUnimplementedDevServer.
func (mr *MockUnsafeDevServerMockRecorder) mustEmbedUnimplementedDevServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDevServer", reflect.TypeOf((*MockUnsafeDevServer)(nil).mustEmbedUnimplementedDevServer))
}
