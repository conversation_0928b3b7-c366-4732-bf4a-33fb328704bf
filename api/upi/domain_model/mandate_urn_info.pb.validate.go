// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/domain_model/mandate_urn_info.proto

package domain_model

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MandateUrnInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MandateUrnInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MandateUrnInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MandateUrnInfoMultiError,
// or nil if none found.
func (m *MandateUrnInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MandateUrnInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValidity()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateUrnInfoValidationError{
					field:  "Validity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateUrnInfoValidationError{
					field:  "Validity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidity()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateUrnInfoValidationError{
				field:  "Validity",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateUrnInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateUrnInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateUrnInfoValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AmountRule

	if all {
		switch v := interface{}(m.GetRecurrence()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateUrnInfoValidationError{
					field:  "Recurrence",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateUrnInfoValidationError{
					field:  "Recurrence",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurrence()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateUrnInfoValidationError{
				field:  "Recurrence",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrencyCode

	// no validation rules for IsRevocable

	// no validation rules for ShareToPayee

	// no validation rules for BlockFund

	// no validation rules for MandateName

	// no validation rules for Umn

	// no validation rules for MandateType

	if all {
		switch v := interface{}(m.GetOneTimeAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MandateUrnInfoValidationError{
					field:  "OneTimeAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MandateUrnInfoValidationError{
					field:  "OneTimeAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOneTimeAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MandateUrnInfoValidationError{
				field:  "OneTimeAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Consent

	for idx, item := range m.GetAmounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MandateUrnInfoValidationError{
						field:  fmt.Sprintf("Amounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MandateUrnInfoValidationError{
						field:  fmt.Sprintf("Amounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MandateUrnInfoValidationError{
					field:  fmt.Sprintf("Amounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MandateUrnInfoMultiError(errors)
	}

	return nil
}

// MandateUrnInfoMultiError is an error wrapping multiple validation errors
// returned by MandateUrnInfo.ValidateAll() if the designated constraints
// aren't met.
type MandateUrnInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MandateUrnInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MandateUrnInfoMultiError) AllErrors() []error { return m }

// MandateUrnInfoValidationError is the validation error returned by
// MandateUrnInfo.Validate if the designated constraints aren't met.
type MandateUrnInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MandateUrnInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MandateUrnInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MandateUrnInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MandateUrnInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MandateUrnInfoValidationError) ErrorName() string { return "MandateUrnInfoValidationError" }

// Error satisfies the builtin error interface
func (e MandateUrnInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMandateUrnInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MandateUrnInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MandateUrnInfoValidationError{}
