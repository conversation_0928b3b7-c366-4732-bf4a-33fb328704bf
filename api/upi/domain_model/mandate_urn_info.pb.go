// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/domain_model/mandate_urn_info.proto

package domain_model

import (
	typesv2 "github.com/epifi/gamma/api/typesv2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MandateUrnInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// interval with validity start and end for mandate urn
	Validity *typesv2.Interval `protobuf:"bytes,1,opt,name=validity,proto3" json:"validity,omitempty"`
	// mandate amount
	//
	// Deprecated: Marked as deprecated in api/upi/domain_model/mandate_urn_info.proto.
	Amount *money.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// amount rule applicable on amount.
	AmountRule AmountRule `protobuf:"varint,3,opt,name=amount_rule,json=amountRule,proto3,enum=upi.domain_model.AmountRule" json:"amount_rule,omitempty"`
	// mandate recurrence pattern details
	Recurrence *Recurrence `protobuf:"bytes,4,opt,name=recurrence,proto3" json:"recurrence,omitempty"`
	// currency code. currently only INR is supported
	CurrencyCode string `protobuf:"bytes,5,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// boolean flag to mark if mandate is revocable or not
	IsRevocable bool `protobuf:"varint,6,opt,name=is_revocable,json=isRevocable,proto3" json:"is_revocable,omitempty"`
	// flag to share with payee
	ShareToPayee bool `protobuf:"varint,7,opt,name=share_to_payee,json=shareToPayee,proto3" json:"share_to_payee,omitempty"`
	// When an authorized create mandate request comes to remitter bank where ‘blockfund’ tag is set,
	// then remitter bank needs to block the specified amount in customers account. This
	// functionality will only be allowed for one-time mandates.
	// relevant for scenarios like IPO and mutual funds.
	BlockFund bool `protobuf:"varint,8,opt,name=block_fund,json=blockFund,proto3" json:"block_fund,omitempty"`
	// mandate name
	MandateName string `protobuf:"bytes,9,opt,name=mandate_name,json=mandateName,proto3" json:"mandate_name,omitempty"`
	// umn will be present in case urn is of type
	// collect for one time mandate execution
	// modifying existing mandate
	Umn string `protobuf:"bytes,10,opt,name=umn,proto3" json:"umn,omitempty"`
	// this is present in case intent/qr is for mandate modification
	MandateType MandateType `protobuf:"varint,11,opt,name=mandate_type,json=mandateType,proto3,enum=upi.domain_model.MandateType" json:"mandate_type,omitempty"`
	// this is present when user is to be provided option of making one time payment instead of mandate
	OneTimeAmount *money.Money `protobuf:"bytes,12,opt,name=one_time_amount,json=oneTimeAmount,proto3" json:"one_time_amount,omitempty"`
	// this indicates whether consent should be taken from user
	Consent bool `protobuf:"varint,13,opt,name=consent,proto3" json:"consent,omitempty"`
	// multiple amounts can be provided for user to choose from
	Amounts []*money.Money `protobuf:"bytes,14,rep,name=amounts,proto3" json:"amounts,omitempty"`
}

func (x *MandateUrnInfo) Reset() {
	*x = MandateUrnInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_domain_model_mandate_urn_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MandateUrnInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MandateUrnInfo) ProtoMessage() {}

func (x *MandateUrnInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_domain_model_mandate_urn_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MandateUrnInfo.ProtoReflect.Descriptor instead.
func (*MandateUrnInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_domain_model_mandate_urn_info_proto_rawDescGZIP(), []int{0}
}

func (x *MandateUrnInfo) GetValidity() *typesv2.Interval {
	if x != nil {
		return x.Validity
	}
	return nil
}

// Deprecated: Marked as deprecated in api/upi/domain_model/mandate_urn_info.proto.
func (x *MandateUrnInfo) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *MandateUrnInfo) GetAmountRule() AmountRule {
	if x != nil {
		return x.AmountRule
	}
	return AmountRule_AMOUNT_RULE_UNSPECIFIED
}

func (x *MandateUrnInfo) GetRecurrence() *Recurrence {
	if x != nil {
		return x.Recurrence
	}
	return nil
}

func (x *MandateUrnInfo) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *MandateUrnInfo) GetIsRevocable() bool {
	if x != nil {
		return x.IsRevocable
	}
	return false
}

func (x *MandateUrnInfo) GetShareToPayee() bool {
	if x != nil {
		return x.ShareToPayee
	}
	return false
}

func (x *MandateUrnInfo) GetBlockFund() bool {
	if x != nil {
		return x.BlockFund
	}
	return false
}

func (x *MandateUrnInfo) GetMandateName() string {
	if x != nil {
		return x.MandateName
	}
	return ""
}

func (x *MandateUrnInfo) GetUmn() string {
	if x != nil {
		return x.Umn
	}
	return ""
}

func (x *MandateUrnInfo) GetMandateType() MandateType {
	if x != nil {
		return x.MandateType
	}
	return MandateType_TYPE_UNSPECIFIED
}

func (x *MandateUrnInfo) GetOneTimeAmount() *money.Money {
	if x != nil {
		return x.OneTimeAmount
	}
	return nil
}

func (x *MandateUrnInfo) GetConsent() bool {
	if x != nil {
		return x.Consent
	}
	return false
}

func (x *MandateUrnInfo) GetAmounts() []*money.Money {
	if x != nil {
		return x.Amounts
	}
	return nil
}

var File_api_upi_domain_model_mandate_urn_info_proto protoreflect.FileDescriptor

var file_api_upi_domain_model_mandate_urn_info_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75,
	0x72, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x75,
	0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a,
	0x1a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69,
	0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf8, 0x04, 0x0a, 0x0e, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x72, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x08, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x52, 0x08, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x12, 0x2e,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d,
	0x0a, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x3c, 0x0a,
	0x0a, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x0a, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x76, 0x6f, 0x63, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x52, 0x65, 0x76, 0x6f, 0x63, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x6f, 0x5f,
	0x70, 0x61, 0x79, 0x65, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x54, 0x6f, 0x50, 0x61, 0x79, 0x65, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x75, 0x6e, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x6d, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x6d, 0x6e, 0x12, 0x40, 0x0a,
	0x0c, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3a, 0x0a, 0x0f, 0x6f, 0x6e, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6f, 0x6e,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x07, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x07, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x70, 0x69, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_domain_model_mandate_urn_info_proto_rawDescOnce sync.Once
	file_api_upi_domain_model_mandate_urn_info_proto_rawDescData = file_api_upi_domain_model_mandate_urn_info_proto_rawDesc
)

func file_api_upi_domain_model_mandate_urn_info_proto_rawDescGZIP() []byte {
	file_api_upi_domain_model_mandate_urn_info_proto_rawDescOnce.Do(func() {
		file_api_upi_domain_model_mandate_urn_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_domain_model_mandate_urn_info_proto_rawDescData)
	})
	return file_api_upi_domain_model_mandate_urn_info_proto_rawDescData
}

var file_api_upi_domain_model_mandate_urn_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_upi_domain_model_mandate_urn_info_proto_goTypes = []interface{}{
	(*MandateUrnInfo)(nil),   // 0: upi.domain_model.MandateUrnInfo
	(*typesv2.Interval)(nil), // 1: api.typesv2.Interval
	(*money.Money)(nil),      // 2: google.type.Money
	(AmountRule)(0),          // 3: upi.domain_model.AmountRule
	(*Recurrence)(nil),       // 4: upi.domain_model.Recurrence
	(MandateType)(0),         // 5: upi.domain_model.MandateType
}
var file_api_upi_domain_model_mandate_urn_info_proto_depIdxs = []int32{
	1, // 0: upi.domain_model.MandateUrnInfo.validity:type_name -> api.typesv2.Interval
	2, // 1: upi.domain_model.MandateUrnInfo.amount:type_name -> google.type.Money
	3, // 2: upi.domain_model.MandateUrnInfo.amount_rule:type_name -> upi.domain_model.AmountRule
	4, // 3: upi.domain_model.MandateUrnInfo.recurrence:type_name -> upi.domain_model.Recurrence
	5, // 4: upi.domain_model.MandateUrnInfo.mandate_type:type_name -> upi.domain_model.MandateType
	2, // 5: upi.domain_model.MandateUrnInfo.one_time_amount:type_name -> google.type.Money
	2, // 6: upi.domain_model.MandateUrnInfo.amounts:type_name -> google.type.Money
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_upi_domain_model_mandate_urn_info_proto_init() }
func file_api_upi_domain_model_mandate_urn_info_proto_init() {
	if File_api_upi_domain_model_mandate_urn_info_proto != nil {
		return
	}
	file_api_upi_domain_model_mandate_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_upi_domain_model_mandate_urn_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MandateUrnInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_domain_model_mandate_urn_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_domain_model_mandate_urn_info_proto_goTypes,
		DependencyIndexes: file_api_upi_domain_model_mandate_urn_info_proto_depIdxs,
		MessageInfos:      file_api_upi_domain_model_mandate_urn_info_proto_msgTypes,
	}.Build()
	File_api_upi_domain_model_mandate_urn_info_proto = out.File
	file_api_upi_domain_model_mandate_urn_info_proto_rawDesc = nil
	file_api_upi_domain_model_mandate_urn_info_proto_goTypes = nil
	file_api_upi_domain_model_mandate_urn_info_proto_depIdxs = nil
}
