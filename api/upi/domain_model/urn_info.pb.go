// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/domain_model/urn_info.proto

package domain_model

import (
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// contains info that can be parsed from urn
type UrnInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// an optional field to be used in certain workflow where transaction id needs to be generated by external merchant
	// system e.g. dynamic QR scan and intent based payment
	//
	// This txn id is passed to all the external systems to uniquely identify an system transaction.
	//
	// Optional field, if present this id will be used to initiate payment with NPCI
	// for QR and intent based payments.. merchant system can send this information
	TxnId string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	// mandatory field if present in urn request to be used in certain workflow where merchant ref id needs to be generated by external merchant
	// merchant ref id passed to associate a transaction with merchant's order system. It is mandatory to pass this field in payment request.
	//
	// In certain flows, e.g UPI QR scans or UPI intent based payments,
	// a merchant_ref_id can be present in the QR or the intent and it is mandatory
	// for epiFi to consume this information while initiating the transaction as per NPCI guidelines to PSP.
	//
	// Typically, this could be order number, subscription number, Bill ID,
	// booking ID, insurance, renewal reference, etc. from the merchant's system.
	MerchantRefId string `protobuf:"bytes,2,opt,name=merchant_ref_id,json=merchantRefId,proto3" json:"merchant_ref_id,omitempty"`
	// transaction reference url should be a URL when clicked provides customer with further transaction details
	// like complete bill details, bill copy, order copy, ticket details, etc.
	//
	// for dynamic QR and intent based payments.. merchant system can send this information
	// Mandatory to send in pay request if available in URN request.
	ReferenceUrl string `protobuf:"bytes,3,opt,name=reference_url,json=referenceUrl,proto3" json:"reference_url,omitempty"`
	// initiation mode for the payment
	// 00=Default txn
	// 01=QR Code
	// 02=Secure QR Code
	// 03=BharatQR
	// 04=Intent
	// 05=Secure Intent
	// 06=NFC
	// 07=BLE (Bluetooth)
	// 08=UHF(Ultra High
	InitiationMode string `protobuf:"bytes,4,opt,name=initiation_mode,json=initiationMode,proto3" json:"initiation_mode,omitempty"`
	// purpose of the transaction
	// 00 - Default
	// 01 - SEBI
	// 02 - AMC
	// 03 - Travel
	// 04 - Hospitality
	// 05 – Hospital
	// 06 – Telecom
	// 07 – Insurance
	// 08 – Education
	// 09- Gifting
	// 10-Others
	Purpose string `protobuf:"bytes,5,opt,name=purpose,proto3" json:"purpose,omitempty"`
	// unique identifier for classification of a merchant - Merchant Classification Code
	// for QR and intent based payments.. merchant system can send this information
	// in other cases we will be using the default code -  "0000"
	Mcc string `protobuf:"bytes,6,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// unique identifier of the merchant registered with NPCI/merchant acquirer. It is not necessary that merchant id to be unique in all payment ecosystem.
	// for QR and intent based payments.. merchant system can send this information
	// for all other cases this field will be empty
	MerchantId string `protobuf:"bytes,7,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	// unique identifier of a registered merchant store with NPCI/merchant acquirer
	// for QR and intent based payments.. merchant system can send this information
	// for all other cases this field will be empty
	MerchantStoreId string `protobuf:"bytes,8,opt,name=merchant_store_id,json=merchantStoreId,proto3" json:"merchant_store_id,omitempty"`
	// terminal id of the merchant
	// for QR and intent based payments.. merchant system can send this information
	// for all other cases this field will be empty
	MerchantTerminalId string `protobuf:"bytes,9,opt,name=merchant_terminal_id,json=merchantTerminalId,proto3" json:"merchant_terminal_id,omitempty"`
	// If the transaction is initiated by any PSP app then the respective orgID passed by the PSP app
	// needs to be passed.
	// for merchant initiated intent ‘000000’ will be used.
	OrgId string `protobuf:"bytes,10,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	// denotes the payee vpa address in the URN
	PayeeAddress string `protobuf:"bytes,11,opt,name=payee_address,json=payeeAddress,proto3" json:"payee_address,omitempty"`
	// payee name present in urn(if any).
	PayeeName string `protobuf:"bytes,12,opt,name=payee_name,json=payeeName,proto3" json:"payee_name,omitempty"`
	// Amount of monies involved in the transaction
	Amount *money.Money `protobuf:"bytes,13,opt,name=amount,proto3" json:"amount,omitempty"`
	// minimum amount for the transaction
	MinAmount *money.Money `protobuf:"bytes,14,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
}

func (x *UrnInfo) Reset() {
	*x = UrnInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_domain_model_urn_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UrnInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UrnInfo) ProtoMessage() {}

func (x *UrnInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_domain_model_urn_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UrnInfo.ProtoReflect.Descriptor instead.
func (*UrnInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_domain_model_urn_info_proto_rawDescGZIP(), []int{0}
}

func (x *UrnInfo) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *UrnInfo) GetMerchantRefId() string {
	if x != nil {
		return x.MerchantRefId
	}
	return ""
}

func (x *UrnInfo) GetReferenceUrl() string {
	if x != nil {
		return x.ReferenceUrl
	}
	return ""
}

func (x *UrnInfo) GetInitiationMode() string {
	if x != nil {
		return x.InitiationMode
	}
	return ""
}

func (x *UrnInfo) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

func (x *UrnInfo) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *UrnInfo) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *UrnInfo) GetMerchantStoreId() string {
	if x != nil {
		return x.MerchantStoreId
	}
	return ""
}

func (x *UrnInfo) GetMerchantTerminalId() string {
	if x != nil {
		return x.MerchantTerminalId
	}
	return ""
}

func (x *UrnInfo) GetOrgId() string {
	if x != nil {
		return x.OrgId
	}
	return ""
}

func (x *UrnInfo) GetPayeeAddress() string {
	if x != nil {
		return x.PayeeAddress
	}
	return ""
}

func (x *UrnInfo) GetPayeeName() string {
	if x != nil {
		return x.PayeeName
	}
	return ""
}

func (x *UrnInfo) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *UrnInfo) GetMinAmount() *money.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

var File_api_upi_domain_model_urn_info_proto protoreflect.FileDescriptor

var file_api_upi_domain_model_urn_info_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x75, 0x72, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xfb, 0x03, 0x0a, 0x07, 0x55, 0x72, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06,
	0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78,
	0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x55, 0x72, 0x6c,
	0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70,
	0x6f, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x63, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x63, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x70,
	0x61, 0x79, 0x65, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x65, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x65, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x65, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6d,
	0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x5a,
	0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69,
	0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5a, 0x2b, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_upi_domain_model_urn_info_proto_rawDescOnce sync.Once
	file_api_upi_domain_model_urn_info_proto_rawDescData = file_api_upi_domain_model_urn_info_proto_rawDesc
)

func file_api_upi_domain_model_urn_info_proto_rawDescGZIP() []byte {
	file_api_upi_domain_model_urn_info_proto_rawDescOnce.Do(func() {
		file_api_upi_domain_model_urn_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_domain_model_urn_info_proto_rawDescData)
	})
	return file_api_upi_domain_model_urn_info_proto_rawDescData
}

var file_api_upi_domain_model_urn_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_upi_domain_model_urn_info_proto_goTypes = []interface{}{
	(*UrnInfo)(nil),     // 0: upi.domain_model.UrnInfo
	(*money.Money)(nil), // 1: google.type.Money
}
var file_api_upi_domain_model_urn_info_proto_depIdxs = []int32{
	1, // 0: upi.domain_model.UrnInfo.amount:type_name -> google.type.Money
	1, // 1: upi.domain_model.UrnInfo.min_amount:type_name -> google.type.Money
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_upi_domain_model_urn_info_proto_init() }
func file_api_upi_domain_model_urn_info_proto_init() {
	if File_api_upi_domain_model_urn_info_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_domain_model_urn_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UrnInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_domain_model_urn_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_domain_model_urn_info_proto_goTypes,
		DependencyIndexes: file_api_upi_domain_model_urn_info_proto_depIdxs,
		MessageInfos:      file_api_upi_domain_model_urn_info_proto_msgTypes,
	}.Build()
	File_api_upi_domain_model_urn_info_proto = out.File
	file_api_upi_domain_model_urn_info_proto_rawDesc = nil
	file_api_upi_domain_model_urn_info_proto_goTypes = nil
	file_api_upi_domain_model_urn_info_proto_depIdxs = nil
}
