// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/domain_model/urn_info.proto

package domain_model

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UrnInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UrnInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UrnInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in UrnInfoMultiError, or nil if none found.
func (m *UrnInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UrnInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	// no validation rules for MerchantRefId

	// no validation rules for ReferenceUrl

	// no validation rules for InitiationMode

	// no validation rules for Purpose

	// no validation rules for Mcc

	// no validation rules for MerchantId

	// no validation rules for MerchantStoreId

	// no validation rules for MerchantTerminalId

	// no validation rules for OrgId

	// no validation rules for PayeeAddress

	// no validation rules for PayeeName

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UrnInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UrnInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UrnInfoValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UrnInfoValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UrnInfoValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UrnInfoValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UrnInfoMultiError(errors)
	}

	return nil
}

// UrnInfoMultiError is an error wrapping multiple validation errors returned
// by UrnInfo.ValidateAll() if the designated constraints aren't met.
type UrnInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UrnInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UrnInfoMultiError) AllErrors() []error { return m }

// UrnInfoValidationError is the validation error returned by UrnInfo.Validate
// if the designated constraints aren't met.
type UrnInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UrnInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UrnInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UrnInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UrnInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UrnInfoValidationError) ErrorName() string { return "UrnInfoValidationError" }

// Error satisfies the builtin error interface
func (e UrnInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUrnInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UrnInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UrnInfoValidationError{}
