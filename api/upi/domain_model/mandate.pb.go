// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/domain_model/mandate.proto

package domain_model

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MandateType int32

const (
	MandateType_TYPE_UNSPECIFIED MandateType = 0
	// Allows a customer to create a mandate.
	MandateType_CREATE MandateType = 1
	// Allows a customer to revoke a mandate.
	MandateType_REVOKE MandateType = 2
	// Allows a customer to revoke a mandate.
	MandateType_UPDATE MandateType = 3
)

// Enum value maps for MandateType.
var (
	MandateType_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CREATE",
		2: "REVOKE",
		3: "UPDATE",
	}
	MandateType_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"CREATE":           1,
		"REVOKE":           2,
		"UPDATE":           3,
	}
)

func (x MandateType) Enum() *MandateType {
	p := new(MandateType)
	*p = x
	return p
}

func (x MandateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MandateType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_domain_model_mandate_proto_enumTypes[0].Descriptor()
}

func (MandateType) Type() protoreflect.EnumType {
	return &file_api_upi_domain_model_mandate_proto_enumTypes[0]
}

func (x MandateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MandateType.Descriptor instead.
func (MandateType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_domain_model_mandate_proto_rawDescGZIP(), []int{0}
}

type AmountRule int32

const (
	AmountRule_AMOUNT_RULE_UNSPECIFIED AmountRule = 0
	AmountRule_MAX                     AmountRule = 1
	AmountRule_EXACT                   AmountRule = 2
)

// Enum value maps for AmountRule.
var (
	AmountRule_name = map[int32]string{
		0: "AMOUNT_RULE_UNSPECIFIED",
		1: "MAX",
		2: "EXACT",
	}
	AmountRule_value = map[string]int32{
		"AMOUNT_RULE_UNSPECIFIED": 0,
		"MAX":                     1,
		"EXACT":                   2,
	}
)

func (x AmountRule) Enum() *AmountRule {
	p := new(AmountRule)
	*p = x
	return p
}

func (x AmountRule) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmountRule) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_domain_model_mandate_proto_enumTypes[1].Descriptor()
}

func (AmountRule) Type() protoreflect.EnumType {
	return &file_api_upi_domain_model_mandate_proto_enumTypes[1]
}

func (x AmountRule) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmountRule.Descriptor instead.
func (AmountRule) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_domain_model_mandate_proto_rawDescGZIP(), []int{1}
}

type RecurrencePattern int32

const (
	RecurrencePattern_RECURRENCE_PATTERN_UNSPECIFIED RecurrencePattern = 0
	RecurrencePattern_ONE_TIME                       RecurrencePattern = 1
	RecurrencePattern_DAILY                          RecurrencePattern = 2
	RecurrencePattern_WEEKLY                         RecurrencePattern = 3
	RecurrencePattern_FORTNIGHTLY                    RecurrencePattern = 4
	RecurrencePattern_MONTHLY                        RecurrencePattern = 5
	RecurrencePattern_BI_MONTHLY                     RecurrencePattern = 6
	RecurrencePattern_QUARTERLY                      RecurrencePattern = 7
	RecurrencePattern_HALF_YEARLY                    RecurrencePattern = 8
	RecurrencePattern_YEARLY                         RecurrencePattern = 9
	RecurrencePattern_AS_PRESENTED                   RecurrencePattern = 10
)

// Enum value maps for RecurrencePattern.
var (
	RecurrencePattern_name = map[int32]string{
		0:  "RECURRENCE_PATTERN_UNSPECIFIED",
		1:  "ONE_TIME",
		2:  "DAILY",
		3:  "WEEKLY",
		4:  "FORTNIGHTLY",
		5:  "MONTHLY",
		6:  "BI_MONTHLY",
		7:  "QUARTERLY",
		8:  "HALF_YEARLY",
		9:  "YEARLY",
		10: "AS_PRESENTED",
	}
	RecurrencePattern_value = map[string]int32{
		"RECURRENCE_PATTERN_UNSPECIFIED": 0,
		"ONE_TIME":                       1,
		"DAILY":                          2,
		"WEEKLY":                         3,
		"FORTNIGHTLY":                    4,
		"MONTHLY":                        5,
		"BI_MONTHLY":                     6,
		"QUARTERLY":                      7,
		"HALF_YEARLY":                    8,
		"YEARLY":                         9,
		"AS_PRESENTED":                   10,
	}
)

func (x RecurrencePattern) Enum() *RecurrencePattern {
	p := new(RecurrencePattern)
	*p = x
	return p
}

func (x RecurrencePattern) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecurrencePattern) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_domain_model_mandate_proto_enumTypes[2].Descriptor()
}

func (RecurrencePattern) Type() protoreflect.EnumType {
	return &file_api_upi_domain_model_mandate_proto_enumTypes[2]
}

func (x RecurrencePattern) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecurrencePattern.Descriptor instead.
func (RecurrencePattern) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_domain_model_mandate_proto_rawDescGZIP(), []int{2}
}

type Recurrence_RecurrenceRule_RecurrenceRuleType int32

const (
	Recurrence_RecurrenceRule_RECURRENCE_RULE_TYPE_UNSPECIFIED Recurrence_RecurrenceRule_RecurrenceRuleType = 0
	Recurrence_RecurrenceRule_BEFORE                           Recurrence_RecurrenceRule_RecurrenceRuleType = 1
	Recurrence_RecurrenceRule_ON                               Recurrence_RecurrenceRule_RecurrenceRuleType = 2
	Recurrence_RecurrenceRule_AFTER                            Recurrence_RecurrenceRule_RecurrenceRuleType = 3
)

// Enum value maps for Recurrence_RecurrenceRule_RecurrenceRuleType.
var (
	Recurrence_RecurrenceRule_RecurrenceRuleType_name = map[int32]string{
		0: "RECURRENCE_RULE_TYPE_UNSPECIFIED",
		1: "BEFORE",
		2: "ON",
		3: "AFTER",
	}
	Recurrence_RecurrenceRule_RecurrenceRuleType_value = map[string]int32{
		"RECURRENCE_RULE_TYPE_UNSPECIFIED": 0,
		"BEFORE":                           1,
		"ON":                               2,
		"AFTER":                            3,
	}
)

func (x Recurrence_RecurrenceRule_RecurrenceRuleType) Enum() *Recurrence_RecurrenceRule_RecurrenceRuleType {
	p := new(Recurrence_RecurrenceRule_RecurrenceRuleType)
	*p = x
	return p
}

func (x Recurrence_RecurrenceRule_RecurrenceRuleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Recurrence_RecurrenceRule_RecurrenceRuleType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_domain_model_mandate_proto_enumTypes[3].Descriptor()
}

func (Recurrence_RecurrenceRule_RecurrenceRuleType) Type() protoreflect.EnumType {
	return &file_api_upi_domain_model_mandate_proto_enumTypes[3]
}

func (x Recurrence_RecurrenceRule_RecurrenceRuleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Recurrence_RecurrenceRule_RecurrenceRuleType.Descriptor instead.
func (Recurrence_RecurrenceRule_RecurrenceRuleType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_domain_model_mandate_proto_rawDescGZIP(), []int{0, 0, 0}
}

type Recurrence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandate recurrence rule
	RecurrenceRule *Recurrence_RecurrenceRule `protobuf:"bytes,1,opt,name=recurrence_rule,json=recurrenceRule,proto3" json:"recurrence_rule,omitempty"`
	// mandate recurrence pattern
	RecurrencePattern RecurrencePattern `protobuf:"varint,2,opt,name=recurrence_pattern,json=recurrencePattern,proto3,enum=upi.domain_model.RecurrencePattern" json:"recurrence_pattern,omitempty"`
	Value             string            `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Recurrence) Reset() {
	*x = Recurrence{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_domain_model_mandate_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Recurrence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Recurrence) ProtoMessage() {}

func (x *Recurrence) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_domain_model_mandate_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Recurrence.ProtoReflect.Descriptor instead.
func (*Recurrence) Descriptor() ([]byte, []int) {
	return file_api_upi_domain_model_mandate_proto_rawDescGZIP(), []int{0}
}

func (x *Recurrence) GetRecurrenceRule() *Recurrence_RecurrenceRule {
	if x != nil {
		return x.RecurrenceRule
	}
	return nil
}

func (x *Recurrence) GetRecurrencePattern() RecurrencePattern {
	if x != nil {
		return x.RecurrencePattern
	}
	return RecurrencePattern_RECURRENCE_PATTERN_UNSPECIFIED
}

func (x *Recurrence) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Recurrence_RecurrenceRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandate recurrence rule value
	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// mandate recurrence rule type
	RecurrenceRuleType Recurrence_RecurrenceRule_RecurrenceRuleType `protobuf:"varint,2,opt,name=recurrence_rule_type,json=recurrenceRuleType,proto3,enum=upi.domain_model.Recurrence_RecurrenceRule_RecurrenceRuleType" json:"recurrence_rule_type,omitempty"`
}

func (x *Recurrence_RecurrenceRule) Reset() {
	*x = Recurrence_RecurrenceRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_domain_model_mandate_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Recurrence_RecurrenceRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Recurrence_RecurrenceRule) ProtoMessage() {}

func (x *Recurrence_RecurrenceRule) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_domain_model_mandate_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Recurrence_RecurrenceRule.ProtoReflect.Descriptor instead.
func (*Recurrence_RecurrenceRule) Descriptor() ([]byte, []int) {
	return file_api_upi_domain_model_mandate_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Recurrence_RecurrenceRule) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Recurrence_RecurrenceRule) GetRecurrenceRuleType() Recurrence_RecurrenceRule_RecurrenceRuleType {
	if x != nil {
		return x.RecurrenceRuleType
	}
	return Recurrence_RecurrenceRule_RECURRENCE_RULE_TYPE_UNSPECIFIED
}

var File_api_upi_domain_model_mandate_proto protoreflect.FileDescriptor

var file_api_upi_domain_model_mandate_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0xc2, 0x03, 0x0a, 0x0a, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x54, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0e, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x52, 0x0a, 0x12, 0x72,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x52, 0x11, 0x72, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0xf3, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x70,
	0x0a, 0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x72, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x59, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x20, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52,
	0x45, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x42, 0x45, 0x46, 0x4f, 0x52, 0x45, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4e, 0x10, 0x02,
	0x12, 0x09, 0x0a, 0x05, 0x41, 0x46, 0x54, 0x45, 0x52, 0x10, 0x03, 0x2a, 0x47, 0x0a, 0x0b, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x52, 0x45, 0x56, 0x4f, 0x4b, 0x45, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x10, 0x03, 0x2a, 0x3d, 0x0a, 0x0a, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52, 0x55, 0x4c,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x07, 0x0a, 0x03, 0x4d, 0x41, 0x58, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x58, 0x41, 0x43,
	0x54, 0x10, 0x02, 0x2a, 0xc8, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x12, 0x22, 0x0a, 0x1e, 0x52, 0x45, 0x43,
	0x55, 0x52, 0x52, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x41, 0x54, 0x54, 0x45, 0x52, 0x4e, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x4f, 0x4e, 0x45, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x44,
	0x41, 0x49, 0x4c, 0x59, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x57, 0x45, 0x45, 0x4b, 0x4c, 0x59,
	0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x54, 0x4e, 0x49, 0x47, 0x48, 0x54, 0x4c,
	0x59, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x10, 0x05,
	0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x49, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x10, 0x06,
	0x12, 0x0d, 0x0a, 0x09, 0x51, 0x55, 0x41, 0x52, 0x54, 0x45, 0x52, 0x4c, 0x59, 0x10, 0x07, 0x12,
	0x0f, 0x0a, 0x0b, 0x48, 0x41, 0x4c, 0x46, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x10, 0x08,
	0x12, 0x0a, 0x0a, 0x06, 0x59, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c,
	0x41, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x10, 0x0a, 0x42, 0x5a,
	0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69,
	0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5a, 0x2b, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_upi_domain_model_mandate_proto_rawDescOnce sync.Once
	file_api_upi_domain_model_mandate_proto_rawDescData = file_api_upi_domain_model_mandate_proto_rawDesc
)

func file_api_upi_domain_model_mandate_proto_rawDescGZIP() []byte {
	file_api_upi_domain_model_mandate_proto_rawDescOnce.Do(func() {
		file_api_upi_domain_model_mandate_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_domain_model_mandate_proto_rawDescData)
	})
	return file_api_upi_domain_model_mandate_proto_rawDescData
}

var file_api_upi_domain_model_mandate_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_upi_domain_model_mandate_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_upi_domain_model_mandate_proto_goTypes = []interface{}{
	(MandateType)(0),       // 0: upi.domain_model.MandateType
	(AmountRule)(0),        // 1: upi.domain_model.AmountRule
	(RecurrencePattern)(0), // 2: upi.domain_model.RecurrencePattern
	(Recurrence_RecurrenceRule_RecurrenceRuleType)(0), // 3: upi.domain_model.Recurrence.RecurrenceRule.RecurrenceRuleType
	(*Recurrence)(nil),                // 4: upi.domain_model.Recurrence
	(*Recurrence_RecurrenceRule)(nil), // 5: upi.domain_model.Recurrence.RecurrenceRule
}
var file_api_upi_domain_model_mandate_proto_depIdxs = []int32{
	5, // 0: upi.domain_model.Recurrence.recurrence_rule:type_name -> upi.domain_model.Recurrence.RecurrenceRule
	2, // 1: upi.domain_model.Recurrence.recurrence_pattern:type_name -> upi.domain_model.RecurrencePattern
	3, // 2: upi.domain_model.Recurrence.RecurrenceRule.recurrence_rule_type:type_name -> upi.domain_model.Recurrence.RecurrenceRule.RecurrenceRuleType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_upi_domain_model_mandate_proto_init() }
func file_api_upi_domain_model_mandate_proto_init() {
	if File_api_upi_domain_model_mandate_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_domain_model_mandate_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Recurrence); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_domain_model_mandate_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Recurrence_RecurrenceRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_domain_model_mandate_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_domain_model_mandate_proto_goTypes,
		DependencyIndexes: file_api_upi_domain_model_mandate_proto_depIdxs,
		EnumInfos:         file_api_upi_domain_model_mandate_proto_enumTypes,
		MessageInfos:      file_api_upi_domain_model_mandate_proto_msgTypes,
	}.Build()
	File_api_upi_domain_model_mandate_proto = out.File
	file_api_upi_domain_model_mandate_proto_rawDesc = nil
	file_api_upi_domain_model_mandate_proto_goTypes = nil
	file_api_upi_domain_model_mandate_proto_depIdxs = nil
}
