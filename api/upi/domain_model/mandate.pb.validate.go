// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/domain_model/mandate.proto

package domain_model

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Recurrence with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Recurrence) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Recurrence with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RecurrenceMultiError, or
// nil if none found.
func (m *Recurrence) ValidateAll() error {
	return m.validate(true)
}

func (m *Recurrence) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRecurrenceRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurrenceValidationError{
					field:  "RecurrenceRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurrenceValidationError{
					field:  "RecurrenceRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurrenceRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurrenceValidationError{
				field:  "RecurrenceRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RecurrencePattern

	// no validation rules for Value

	if len(errors) > 0 {
		return RecurrenceMultiError(errors)
	}

	return nil
}

// RecurrenceMultiError is an error wrapping multiple validation errors
// returned by Recurrence.ValidateAll() if the designated constraints aren't met.
type RecurrenceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurrenceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurrenceMultiError) AllErrors() []error { return m }

// RecurrenceValidationError is the validation error returned by
// Recurrence.Validate if the designated constraints aren't met.
type RecurrenceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurrenceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecurrenceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecurrenceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecurrenceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecurrenceValidationError) ErrorName() string { return "RecurrenceValidationError" }

// Error satisfies the builtin error interface
func (e RecurrenceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurrence.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurrenceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurrenceValidationError{}

// Validate checks the field values on Recurrence_RecurrenceRule with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *Recurrence_RecurrenceRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Recurrence_RecurrenceRule with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// Recurrence_RecurrenceRuleMultiError, or nil if none found.
func (m *Recurrence_RecurrenceRule) ValidateAll() error {
	return m.validate(true)
}

func (m *Recurrence_RecurrenceRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Value

	// no validation rules for RecurrenceRuleType

	if len(errors) > 0 {
		return Recurrence_RecurrenceRuleMultiError(errors)
	}

	return nil
}

// Recurrence_RecurrenceRuleMultiError is an error wrapping multiple validation
// errors returned by Recurrence_RecurrenceRule.ValidateAll() if the
// designated constraints aren't met.
type Recurrence_RecurrenceRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Recurrence_RecurrenceRuleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Recurrence_RecurrenceRuleMultiError) AllErrors() []error { return m }

// Recurrence_RecurrenceRuleValidationError is the validation error returned by
// Recurrence_RecurrenceRule.Validate if the designated constraints aren't met.
type Recurrence_RecurrenceRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Recurrence_RecurrenceRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Recurrence_RecurrenceRuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Recurrence_RecurrenceRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Recurrence_RecurrenceRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Recurrence_RecurrenceRuleValidationError) ErrorName() string {
	return "Recurrence_RecurrenceRuleValidationError"
}

// Error satisfies the builtin error interface
func (e Recurrence_RecurrenceRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurrence_RecurrenceRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Recurrence_RecurrenceRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Recurrence_RecurrenceRuleValidationError{}
