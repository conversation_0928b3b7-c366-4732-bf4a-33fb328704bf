// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/cred_block.proto

package upi

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CredBlock with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CredBlock) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CredBlock with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CredBlockMultiError, or nil
// if none found.
func (m *CredBlock) ValidateAll() error {
	return m.validate(true)
}

func (m *CredBlock) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for SubType

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CredBlockValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CredBlockValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CredBlockValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CredBlockMultiError(errors)
	}

	return nil
}

// CredBlockMultiError is an error wrapping multiple validation errors returned
// by CredBlock.ValidateAll() if the designated constraints aren't met.
type CredBlockMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CredBlockMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CredBlockMultiError) AllErrors() []error { return m }

// CredBlockValidationError is the validation error returned by
// CredBlock.Validate if the designated constraints aren't met.
type CredBlockValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CredBlockValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CredBlockValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CredBlockValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CredBlockValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CredBlockValidationError) ErrorName() string { return "CredBlockValidationError" }

// Error satisfies the builtin error interface
func (e CredBlockValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCredBlock.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CredBlockValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CredBlockValidationError{}

// Validate checks the field values on CredBlock_Data with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CredBlock_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CredBlock_Data with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CredBlock_DataMultiError,
// or nil if none found.
func (m *CredBlock_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CredBlock_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Ki

	// no validation rules for Text

	if len(errors) > 0 {
		return CredBlock_DataMultiError(errors)
	}

	return nil
}

// CredBlock_DataMultiError is an error wrapping multiple validation errors
// returned by CredBlock_Data.ValidateAll() if the designated constraints
// aren't met.
type CredBlock_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CredBlock_DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CredBlock_DataMultiError) AllErrors() []error { return m }

// CredBlock_DataValidationError is the validation error returned by
// CredBlock_Data.Validate if the designated constraints aren't met.
type CredBlock_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CredBlock_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CredBlock_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CredBlock_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CredBlock_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CredBlock_DataValidationError) ErrorName() string { return "CredBlock_DataValidationError" }

// Error satisfies the builtin error interface
func (e CredBlock_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCredBlock_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CredBlock_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CredBlock_DataValidationError{}
