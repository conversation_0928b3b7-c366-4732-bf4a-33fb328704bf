// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/customer.proto

package upi

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	account "github.com/epifi/gamma/api/typesv2/account"

	accounts "github.com/epifi/gamma/api/accounts"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = account.AccountProductOffering(0)

	_ = accounts.Type(0)
)

// Validate checks the field values on Customer with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Customer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Customer with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CustomerMultiError, or nil
// if none found.
func (m *Customer) ValidateAll() error {
	return m.validate(true)
}

func (m *Customer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PaymentAddress

	// no validation rules for Name

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCreds() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerValidationError{
						field:  fmt.Sprintf("Creds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerValidationError{
						field:  fmt.Sprintf("Creds[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerValidationError{
					field:  fmt.Sprintf("Creds[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "AccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "AccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MCC

	if all {
		switch v := interface{}(m.GetMerchant()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Merchant",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchant()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "Merchant",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRegIdDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "RegIdDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "RegIdDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRegIdDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "RegIdDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Consent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Consent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "Consent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CmId

	// no validation rules for AadhaarConsent

	if all {
		switch v := interface{}(m.GetInstitution()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Institution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "Institution",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstitution()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "Institution",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SeqNum

	if all {
		switch v := interface{}(m.GetReqIdDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "ReqIdDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerValidationError{
					field:  "ReqIdDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReqIdDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerValidationError{
				field:  "ReqIdDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFxList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomerValidationError{
						field:  fmt.Sprintf("FxList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomerValidationError{
						field:  fmt.Sprintf("FxList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomerValidationError{
					field:  fmt.Sprintf("FxList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CustomerMultiError(errors)
	}

	return nil
}

// CustomerMultiError is an error wrapping multiple validation errors returned
// by Customer.ValidateAll() if the designated constraints aren't met.
type CustomerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerMultiError) AllErrors() []error { return m }

// CustomerValidationError is the validation error returned by
// Customer.Validate if the designated constraints aren't met.
type CustomerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerValidationError) ErrorName() string { return "CustomerValidationError" }

// Error satisfies the builtin error interface
func (e CustomerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerValidationError{}

// Validate checks the field values on CustomerInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerInformationMultiError, or nil if none found.
func (m *CustomerInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIdentity()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerInformationValidationError{
					field:  "Identity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerInformationValidationError{
					field:  "Identity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentity()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerInformationValidationError{
				field:  "Identity",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RatingVerified

	if len(errors) > 0 {
		return CustomerInformationMultiError(errors)
	}

	return nil
}

// CustomerInformationMultiError is an error wrapping multiple validation
// errors returned by CustomerInformation.ValidateAll() if the designated
// constraints aren't met.
type CustomerInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerInformationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerInformationMultiError) AllErrors() []error { return m }

// CustomerInformationValidationError is the validation error returned by
// CustomerInformation.Validate if the designated constraints aren't met.
type CustomerInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerInformationValidationError) ErrorName() string {
	return "CustomerInformationValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerInformationValidationError{}

// Validate checks the field values on CustomerAccountDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerAccountDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerAccountDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerAccountDetailsMultiError, or nil if none found.
func (m *CustomerAccountDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerAccountDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for Ifsc

	// no validation rules for Type

	// no validation rules for Lrn

	// no validation rules for Apo

	if len(errors) > 0 {
		return CustomerAccountDetailsMultiError(errors)
	}

	return nil
}

// CustomerAccountDetailsMultiError is an error wrapping multiple validation
// errors returned by CustomerAccountDetails.ValidateAll() if the designated
// constraints aren't met.
type CustomerAccountDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerAccountDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerAccountDetailsMultiError) AllErrors() []error { return m }

// CustomerAccountDetailsValidationError is the validation error returned by
// CustomerAccountDetails.Validate if the designated constraints aren't met.
type CustomerAccountDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerAccountDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerAccountDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerAccountDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerAccountDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerAccountDetailsValidationError) ErrorName() string {
	return "CustomerAccountDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerAccountDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerAccountDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerAccountDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerAccountDetailsValidationError{}

// Validate checks the field values on Rules with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Rules) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Rules with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in RulesMultiError, or nil if none found.
func (m *Rules) ValidateAll() error {
	return m.validate(true)
}

func (m *Rules) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExpireAfter

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RulesValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RulesValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RulesValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RulesMultiError(errors)
	}

	return nil
}

// RulesMultiError is an error wrapping multiple validation errors returned by
// Rules.ValidateAll() if the designated constraints aren't met.
type RulesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RulesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RulesMultiError) AllErrors() []error { return m }

// RulesValidationError is the validation error returned by Rules.Validate if
// the designated constraints aren't met.
type RulesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RulesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RulesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RulesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RulesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RulesValidationError) ErrorName() string { return "RulesValidationError" }

// Error satisfies the builtin error interface
func (e RulesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRules.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RulesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RulesValidationError{}

// Validate checks the field values on Institution with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Institution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Institution with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InstitutionMultiError, or
// nil if none found.
func (m *Institution) ValidateAll() error {
	return m.validate(true)
}

func (m *Institution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QrPayload

	// no validation rules for ConCode

	// no validation rules for NetInstId

	if len(errors) > 0 {
		return InstitutionMultiError(errors)
	}

	return nil
}

// InstitutionMultiError is an error wrapping multiple validation errors
// returned by Institution.ValidateAll() if the designated constraints aren't met.
type InstitutionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InstitutionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InstitutionMultiError) AllErrors() []error { return m }

// InstitutionValidationError is the validation error returned by
// Institution.Validate if the designated constraints aren't met.
type InstitutionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InstitutionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InstitutionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InstitutionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InstitutionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InstitutionValidationError) ErrorName() string { return "InstitutionValidationError" }

// Error satisfies the builtin error interface
func (e InstitutionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInstitution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InstitutionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InstitutionValidationError{}

// Validate checks the field values on FxDetail with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FxDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FxDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FxDetailMultiError, or nil
// if none found.
func (m *FxDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *FxDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BaseAmount

	// no validation rules for BaseCurrency

	// no validation rules for ConversionRate

	// no validation rules for Markup

	// no validation rules for IsActive

	if len(errors) > 0 {
		return FxDetailMultiError(errors)
	}

	return nil
}

// FxDetailMultiError is an error wrapping multiple validation errors returned
// by FxDetail.ValidateAll() if the designated constraints aren't met.
type FxDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FxDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FxDetailMultiError) AllErrors() []error { return m }

// FxDetailValidationError is the validation error returned by
// FxDetail.Validate if the designated constraints aren't met.
type FxDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FxDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FxDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FxDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FxDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FxDetailValidationError) ErrorName() string { return "FxDetailValidationError" }

// Error satisfies the builtin error interface
func (e FxDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFxDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FxDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FxDetailValidationError{}

// Validate checks the field values on CustomerInformation_Identity with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerInformation_Identity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerInformation_Identity with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerInformation_IdentityMultiError, or nil if none found.
func (m *CustomerInformation_Identity) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerInformation_Identity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Type

	// no validation rules for VerifiedName

	if len(errors) > 0 {
		return CustomerInformation_IdentityMultiError(errors)
	}

	return nil
}

// CustomerInformation_IdentityMultiError is an error wrapping multiple
// validation errors returned by CustomerInformation_Identity.ValidateAll() if
// the designated constraints aren't met.
type CustomerInformation_IdentityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerInformation_IdentityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerInformation_IdentityMultiError) AllErrors() []error { return m }

// CustomerInformation_IdentityValidationError is the validation error returned
// by CustomerInformation_Identity.Validate if the designated constraints
// aren't met.
type CustomerInformation_IdentityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerInformation_IdentityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerInformation_IdentityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerInformation_IdentityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerInformation_IdentityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerInformation_IdentityValidationError) ErrorName() string {
	return "CustomerInformation_IdentityValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerInformation_IdentityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerInformation_Identity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerInformation_IdentityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerInformation_IdentityValidationError{}
