// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/merchant.proto

package upi

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	enums "github.com/epifi/gamma/api/upi/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = enums.UpiAccountRestrictionExemptionType(0)
)

// Validate checks the field values on MerchantDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MerchantDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MerchantDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MerchantDetailsMultiError, or nil if none found.
func (m *MerchantDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MerchantDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MerchantId

	// no validation rules for MerchantStoreId

	// no validation rules for MerchantTerminalId

	// no validation rules for Genre

	// no validation rules for BrandName

	// no validation rules for LegalName

	// no validation rules for FranchiseName

	// no validation rules for OwnershipType

	// no validation rules for OnboardingType

	// no validation rules for MerchantType

	// no validation rules for SubCode

	// no validation rules for Mcc

	// no validation rules for RegId

	// no validation rules for PinCode

	// no validation rules for Tier

	// no validation rules for MerchantLocation

	// no validation rules for MerchantInstituteCode

	if len(errors) > 0 {
		return MerchantDetailsMultiError(errors)
	}

	return nil
}

// MerchantDetailsMultiError is an error wrapping multiple validation errors
// returned by MerchantDetails.ValidateAll() if the designated constraints
// aren't met.
type MerchantDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MerchantDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MerchantDetailsMultiError) AllErrors() []error { return m }

// MerchantDetailsValidationError is the validation error returned by
// MerchantDetails.Validate if the designated constraints aren't met.
type MerchantDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MerchantDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MerchantDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MerchantDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MerchantDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MerchantDetailsValidationError) ErrorName() string { return "MerchantDetailsValidationError" }

// Error satisfies the builtin error interface
func (e MerchantDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMerchantDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MerchantDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MerchantDetailsValidationError{}

// Validate checks the field values on VpaMerchantInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VpaMerchantInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VpaMerchantInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VpaMerchantInfoMultiError, or nil if none found.
func (m *VpaMerchantInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *VpaMerchantInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Vpa

	if all {
		switch v := interface{}(m.GetMerchantDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "MerchantDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "MerchantDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMerchantDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpaMerchantInfoValidationError{
				field:  "MerchantDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpaMerchantInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpaMerchantInfoValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpaMerchantInfoValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRestrictedAccountTypeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "RestrictedAccountTypeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpaMerchantInfoValidationError{
					field:  "RestrictedAccountTypeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRestrictedAccountTypeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpaMerchantInfoValidationError{
				field:  "RestrictedAccountTypeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VpaMerchantInfoMultiError(errors)
	}

	return nil
}

// VpaMerchantInfoMultiError is an error wrapping multiple validation errors
// returned by VpaMerchantInfo.ValidateAll() if the designated constraints
// aren't met.
type VpaMerchantInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VpaMerchantInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VpaMerchantInfoMultiError) AllErrors() []error { return m }

// VpaMerchantInfoValidationError is the validation error returned by
// VpaMerchantInfo.Validate if the designated constraints aren't met.
type VpaMerchantInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VpaMerchantInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VpaMerchantInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VpaMerchantInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VpaMerchantInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VpaMerchantInfoValidationError) ErrorName() string { return "VpaMerchantInfoValidationError" }

// Error satisfies the builtin error interface
func (e VpaMerchantInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVpaMerchantInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VpaMerchantInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VpaMerchantInfoValidationError{}

// Validate checks the field values on RestrictedAccountTypeDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RestrictedAccountTypeDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestrictedAccountTypeDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RestrictedAccountTypeDetailsMultiError, or nil if none found.
func (m *RestrictedAccountTypeDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RestrictedAccountTypeDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FeatureSupportedValues

	if len(errors) > 0 {
		return RestrictedAccountTypeDetailsMultiError(errors)
	}

	return nil
}

// RestrictedAccountTypeDetailsMultiError is an error wrapping multiple
// validation errors returned by RestrictedAccountTypeDetails.ValidateAll() if
// the designated constraints aren't met.
type RestrictedAccountTypeDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestrictedAccountTypeDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestrictedAccountTypeDetailsMultiError) AllErrors() []error { return m }

// RestrictedAccountTypeDetailsValidationError is the validation error returned
// by RestrictedAccountTypeDetails.Validate if the designated constraints
// aren't met.
type RestrictedAccountTypeDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestrictedAccountTypeDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestrictedAccountTypeDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestrictedAccountTypeDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestrictedAccountTypeDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestrictedAccountTypeDetailsValidationError) ErrorName() string {
	return "RestrictedAccountTypeDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RestrictedAccountTypeDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestrictedAccountTypeDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestrictedAccountTypeDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestrictedAccountTypeDetailsValidationError{}
