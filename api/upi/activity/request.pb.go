// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/activity/request.proto

package activity

import (
	activity "github.com/epifi/be-common/api/celestial/activity"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	notification "github.com/epifi/gamma/api/celestial/activity/notification"
	onboarding "github.com/epifi/gamma/api/upi/onboarding"
	enums "github.com/epifi/gamma/api/upi/onboarding/enums"
	payload "github.com/epifi/gamma/api/upi/payload"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateUpiOnboardingEntityForDelinkingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *CreateUpiOnboardingEntityForDelinkingRequest) Reset() {
	*x = CreateUpiOnboardingEntityForDelinkingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUpiOnboardingEntityForDelinkingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUpiOnboardingEntityForDelinkingRequest) ProtoMessage() {}

func (x *CreateUpiOnboardingEntityForDelinkingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUpiOnboardingEntityForDelinkingRequest.ProtoReflect.Descriptor instead.
func (*CreateUpiOnboardingEntityForDelinkingRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{0}
}

func (x *CreateUpiOnboardingEntityForDelinkingRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type DelinkAccountWithVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *DelinkAccountWithVendorRequest) Reset() {
	*x = DelinkAccountWithVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelinkAccountWithVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelinkAccountWithVendorRequest) ProtoMessage() {}

func (x *DelinkAccountWithVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelinkAccountWithVendorRequest.ProtoReflect.Descriptor instead.
func (*DelinkAccountWithVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{1}
}

func (x *DelinkAccountWithVendorRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type DeactivatePiRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *DeactivatePiRequest) Reset() {
	*x = DeactivatePiRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeactivatePiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivatePiRequest) ProtoMessage() {}

func (x *DeactivatePiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivatePiRequest.ProtoReflect.Descriptor instead.
func (*DeactivatePiRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{2}
}

func (x *DeactivatePiRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type MarkAccountAsDelinkedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
}

func (x *MarkAccountAsDelinkedRequest) Reset() {
	*x = MarkAccountAsDelinkedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkAccountAsDelinkedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAccountAsDelinkedRequest) ProtoMessage() {}

func (x *MarkAccountAsDelinkedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAccountAsDelinkedRequest.ProtoReflect.Descriptor instead.
func (*MarkAccountAsDelinkedRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{3}
}

func (x *MarkAccountAsDelinkedRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

type CreateUpiOnboardingEntityForDelinkingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CreateUpiOnboardingEntityForDelinkingResponse) Reset() {
	*x = CreateUpiOnboardingEntityForDelinkingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUpiOnboardingEntityForDelinkingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUpiOnboardingEntityForDelinkingResponse) ProtoMessage() {}

func (x *CreateUpiOnboardingEntityForDelinkingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUpiOnboardingEntityForDelinkingResponse.ProtoReflect.Descriptor instead.
func (*CreateUpiOnboardingEntityForDelinkingResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{4}
}

func (x *CreateUpiOnboardingEntityForDelinkingResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type DelinkAccountWithVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *DelinkAccountWithVendorResponse) Reset() {
	*x = DelinkAccountWithVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelinkAccountWithVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelinkAccountWithVendorResponse) ProtoMessage() {}

func (x *DelinkAccountWithVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelinkAccountWithVendorResponse.ProtoReflect.Descriptor instead.
func (*DelinkAccountWithVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{5}
}

func (x *DelinkAccountWithVendorResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type DeactivatePiResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *DeactivatePiResponse) Reset() {
	*x = DeactivatePiResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeactivatePiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivatePiResponse) ProtoMessage() {}

func (x *DeactivatePiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivatePiResponse.ProtoReflect.Descriptor instead.
func (*DeactivatePiResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{6}
}

func (x *DeactivatePiResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type MarkAccountAsDelinkedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *MarkAccountAsDelinkedResponse) Reset() {
	*x = MarkAccountAsDelinkedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkAccountAsDelinkedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAccountAsDelinkedResponse) ProtoMessage() {}

func (x *MarkAccountAsDelinkedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAccountAsDelinkedResponse.ProtoReflect.Descriptor instead.
func (*MarkAccountAsDelinkedResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{7}
}

func (x *MarkAccountAsDelinkedResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// activity request to upsert upi number pi mapping
type UpsertUpiNumberPiMappingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// latest upi mapper info fetched from vendor
	UpiMapperInfo *onboarding.UpiMapperInfo `protobuf:"bytes,2,opt,name=upi_mapper_info,json=upiMapperInfo,proto3" json:"upi_mapper_info,omitempty"`
	// upi number action taken
	Action enums.UpiOnboardingAction `protobuf:"varint,3,opt,name=action,proto3,enum=upi.onboarding.enums.UpiOnboardingAction" json:"action,omitempty"`
}

func (x *UpsertUpiNumberPiMappingRequest) Reset() {
	*x = UpsertUpiNumberPiMappingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertUpiNumberPiMappingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertUpiNumberPiMappingRequest) ProtoMessage() {}

func (x *UpsertUpiNumberPiMappingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertUpiNumberPiMappingRequest.ProtoReflect.Descriptor instead.
func (*UpsertUpiNumberPiMappingRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{8}
}

func (x *UpsertUpiNumberPiMappingRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpsertUpiNumberPiMappingRequest) GetUpiMapperInfo() *onboarding.UpiMapperInfo {
	if x != nil {
		return x.UpiMapperInfo
	}
	return nil
}

func (x *UpsertUpiNumberPiMappingRequest) GetAction() enums.UpiOnboardingAction {
	if x != nil {
		return x.Action
	}
	return enums.UpiOnboardingAction(0)
}

// activity response
type UpsertUpiNumberPiMappingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *UpsertUpiNumberPiMappingResponse) Reset() {
	*x = UpsertUpiNumberPiMappingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertUpiNumberPiMappingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertUpiNumberPiMappingResponse) ProtoMessage() {}

func (x *UpsertUpiNumberPiMappingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertUpiNumberPiMappingResponse.ProtoReflect.Descriptor instead.
func (*UpsertUpiNumberPiMappingResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{9}
}

func (x *UpsertUpiNumberPiMappingResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CreateUpiOnboardingEntityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// account id of the upi account
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// id corresponding to the actor
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Upi onboarding partner bank
	Vendor vendorgateway.Vendor `protobuf:"varint,4,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// upi onboarding action
	Action enums.UpiOnboardingAction `protobuf:"varint,5,opt,name=action,proto3,enum=upi.onboarding.enums.UpiOnboardingAction" json:"action,omitempty"`
	// vpa to which upi number will be linked
	Vpa string `protobuf:"bytes,6,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// payload to create upi onboarding details
	Payload *onboarding.UpiOnboardingDetailsPayload `protobuf:"bytes,7,opt,name=payload,proto3" json:"payload,omitempty"`
	// vendor_req_id - txn/req id used to initiate call with vendor
	VendorReqId string `protobuf:"bytes,8,opt,name=vendor_req_id,json=vendorReqId,proto3" json:"vendor_req_id,omitempty"`
}

func (x *CreateUpiOnboardingEntityRequest) Reset() {
	*x = CreateUpiOnboardingEntityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUpiOnboardingEntityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUpiOnboardingEntityRequest) ProtoMessage() {}

func (x *CreateUpiOnboardingEntityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUpiOnboardingEntityRequest.ProtoReflect.Descriptor instead.
func (*CreateUpiOnboardingEntityRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{10}
}

func (x *CreateUpiOnboardingEntityRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CreateUpiOnboardingEntityRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CreateUpiOnboardingEntityRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateUpiOnboardingEntityRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *CreateUpiOnboardingEntityRequest) GetAction() enums.UpiOnboardingAction {
	if x != nil {
		return x.Action
	}
	return enums.UpiOnboardingAction(0)
}

func (x *CreateUpiOnboardingEntityRequest) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *CreateUpiOnboardingEntityRequest) GetPayload() *onboarding.UpiOnboardingDetailsPayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *CreateUpiOnboardingEntityRequest) GetVendorReqId() string {
	if x != nil {
		return x.VendorReqId
	}
	return ""
}

type CreateUpiOnboardingEntityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CreateUpiOnboardingEntityResponse) Reset() {
	*x = CreateUpiOnboardingEntityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUpiOnboardingEntityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUpiOnboardingEntityResponse) ProtoMessage() {}

func (x *CreateUpiOnboardingEntityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUpiOnboardingEntityResponse.ProtoReflect.Descriptor instead.
func (*CreateUpiOnboardingEntityResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{11}
}

func (x *CreateUpiOnboardingEntityResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type LinkUpiNumberWithVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// id corresponding to the actor
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *LinkUpiNumberWithVendorRequest) Reset() {
	*x = LinkUpiNumberWithVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkUpiNumberWithVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkUpiNumberWithVendorRequest) ProtoMessage() {}

func (x *LinkUpiNumberWithVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkUpiNumberWithVendorRequest.ProtoReflect.Descriptor instead.
func (*LinkUpiNumberWithVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{12}
}

func (x *LinkUpiNumberWithVendorRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *LinkUpiNumberWithVendorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type LinkUpiNumberWithVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *LinkUpiNumberWithVendorResponse) Reset() {
	*x = LinkUpiNumberWithVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LinkUpiNumberWithVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinkUpiNumberWithVendorResponse) ProtoMessage() {}

func (x *LinkUpiNumberWithVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinkUpiNumberWithVendorResponse.ProtoReflect.Descriptor instead.
func (*LinkUpiNumberWithVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{13}
}

func (x *LinkUpiNumberWithVendorResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CheckActionStatusWithVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// id corresponding to the actor
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// upi onboarding action
	Action enums.UpiOnboardingAction `protobuf:"varint,3,opt,name=action,proto3,enum=upi.onboarding.enums.UpiOnboardingAction" json:"action,omitempty"`
}

func (x *CheckActionStatusWithVendorRequest) Reset() {
	*x = CheckActionStatusWithVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckActionStatusWithVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckActionStatusWithVendorRequest) ProtoMessage() {}

func (x *CheckActionStatusWithVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckActionStatusWithVendorRequest.ProtoReflect.Descriptor instead.
func (*CheckActionStatusWithVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{14}
}

func (x *CheckActionStatusWithVendorRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CheckActionStatusWithVendorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CheckActionStatusWithVendorRequest) GetAction() enums.UpiOnboardingAction {
	if x != nil {
		return x.Action
	}
	return enums.UpiOnboardingAction(0)
}

type CheckActionStatusWithVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// upi mapper info fetched from vendor
	UpiMapperInfo *onboarding.UpiMapperInfo `protobuf:"bytes,2,opt,name=upi_mapper_info,json=upiMapperInfo,proto3" json:"upi_mapper_info,omitempty"`
}

func (x *CheckActionStatusWithVendorResponse) Reset() {
	*x = CheckActionStatusWithVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckActionStatusWithVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckActionStatusWithVendorResponse) ProtoMessage() {}

func (x *CheckActionStatusWithVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckActionStatusWithVendorResponse.ProtoReflect.Descriptor instead.
func (*CheckActionStatusWithVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{15}
}

func (x *CheckActionStatusWithVendorResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CheckActionStatusWithVendorResponse) GetUpiMapperInfo() *onboarding.UpiMapperInfo {
	if x != nil {
		return x.UpiMapperInfo
	}
	return nil
}

type DelinkUpiNumberWithVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// id corresponding to the actor
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *DelinkUpiNumberWithVendorRequest) Reset() {
	*x = DelinkUpiNumberWithVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelinkUpiNumberWithVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelinkUpiNumberWithVendorRequest) ProtoMessage() {}

func (x *DelinkUpiNumberWithVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelinkUpiNumberWithVendorRequest.ProtoReflect.Descriptor instead.
func (*DelinkUpiNumberWithVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{16}
}

func (x *DelinkUpiNumberWithVendorRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *DelinkUpiNumberWithVendorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type DelinkUpiNumberWithVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *DelinkUpiNumberWithVendorResponse) Reset() {
	*x = DelinkUpiNumberWithVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelinkUpiNumberWithVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelinkUpiNumberWithVendorResponse) ProtoMessage() {}

func (x *DelinkUpiNumberWithVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelinkUpiNumberWithVendorResponse.ProtoReflect.Descriptor instead.
func (*DelinkUpiNumberWithVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{17}
}

func (x *DelinkUpiNumberWithVendorResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type GenerateNotificationForUpiNumberActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GenerateNotificationForUpiNumberActionRequest) Reset() {
	*x = GenerateNotificationForUpiNumberActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateNotificationForUpiNumberActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateNotificationForUpiNumberActionRequest) ProtoMessage() {}

func (x *GenerateNotificationForUpiNumberActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateNotificationForUpiNumberActionRequest.ProtoReflect.Descriptor instead.
func (*GenerateNotificationForUpiNumberActionRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{18}
}

func (x *GenerateNotificationForUpiNumberActionRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GenerateNotificationForUpiNumberActionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GenerateNotificationForUpiNumberActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// notification to publish PN to the user
	Notification *notification.Notification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
}

func (x *GenerateNotificationForUpiNumberActionResponse) Reset() {
	*x = GenerateNotificationForUpiNumberActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateNotificationForUpiNumberActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateNotificationForUpiNumberActionResponse) ProtoMessage() {}

func (x *GenerateNotificationForUpiNumberActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateNotificationForUpiNumberActionResponse.ProtoReflect.Descriptor instead.
func (*GenerateNotificationForUpiNumberActionResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{19}
}

func (x *GenerateNotificationForUpiNumberActionResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GenerateNotificationForUpiNumberActionResponse) GetNotification() *notification.Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

type GetDelinkUpiNumberWorkflowPayloadsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// All the upi numbers linked to the vpa need to be delinked
	Vpa string `protobuf:"bytes,2,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// account corresponding to the vpa
	AccountId string `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Upi onboarding partner bank
	Vendor vendorgateway.Vendor `protobuf:"varint,5,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
}

func (x *GetDelinkUpiNumberWorkflowPayloadsRequest) Reset() {
	*x = GetDelinkUpiNumberWorkflowPayloadsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDelinkUpiNumberWorkflowPayloadsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDelinkUpiNumberWorkflowPayloadsRequest) ProtoMessage() {}

func (x *GetDelinkUpiNumberWorkflowPayloadsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDelinkUpiNumberWorkflowPayloadsRequest.ProtoReflect.Descriptor instead.
func (*GetDelinkUpiNumberWorkflowPayloadsRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{20}
}

func (x *GetDelinkUpiNumberWorkflowPayloadsRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GetDelinkUpiNumberWorkflowPayloadsRequest) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *GetDelinkUpiNumberWorkflowPayloadsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetDelinkUpiNumberWorkflowPayloadsRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetDelinkUpiNumberWorkflowPayloadsRequest) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

type GetDelinkUpiNumberWorkflowPayloadsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// while delinking an upi account, all the upi numbers
	// linked to the vpa need to be delinked,
	// delinkUpiNumberWorkflowPayloads stores the payload for all
	// the upi numbers to initiate the delink-upi-number workflow
	DelinkUpiNumberInfos []*payload.DelinkUpiNumberInfo `protobuf:"bytes,2,rep,name=delink_upi_number_infos,json=delinkUpiNumberInfos,proto3" json:"delink_upi_number_infos,omitempty"`
}

func (x *GetDelinkUpiNumberWorkflowPayloadsResponse) Reset() {
	*x = GetDelinkUpiNumberWorkflowPayloadsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDelinkUpiNumberWorkflowPayloadsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDelinkUpiNumberWorkflowPayloadsResponse) ProtoMessage() {}

func (x *GetDelinkUpiNumberWorkflowPayloadsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDelinkUpiNumberWorkflowPayloadsResponse.ProtoReflect.Descriptor instead.
func (*GetDelinkUpiNumberWorkflowPayloadsResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{21}
}

func (x *GetDelinkUpiNumberWorkflowPayloadsResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GetDelinkUpiNumberWorkflowPayloadsResponse) GetDelinkUpiNumberInfos() []*payload.DelinkUpiNumberInfo {
	if x != nil {
		return x.DelinkUpiNumberInfos
	}
	return nil
}

type CheckDelinkUpiNumbersWorkflowsStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// workflow status will be checked for all the client-req-ids
	// If it is not in terminal state for any of it, then
	// it should be retried
	ClientReqIds []string `protobuf:"bytes,2,rep,name=clientReqIds,proto3" json:"clientReqIds,omitempty"`
}

func (x *CheckDelinkUpiNumbersWorkflowsStatusRequest) Reset() {
	*x = CheckDelinkUpiNumbersWorkflowsStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDelinkUpiNumbersWorkflowsStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDelinkUpiNumbersWorkflowsStatusRequest) ProtoMessage() {}

func (x *CheckDelinkUpiNumbersWorkflowsStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDelinkUpiNumbersWorkflowsStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckDelinkUpiNumbersWorkflowsStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{22}
}

func (x *CheckDelinkUpiNumbersWorkflowsStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CheckDelinkUpiNumbersWorkflowsStatusRequest) GetClientReqIds() []string {
	if x != nil {
		return x.ClientReqIds
	}
	return nil
}

type CheckDelinkUpiNumbersWorkflowsStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CheckDelinkUpiNumbersWorkflowsStatusResponse) Reset() {
	*x = CheckDelinkUpiNumbersWorkflowsStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDelinkUpiNumbersWorkflowsStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDelinkUpiNumbersWorkflowsStatusResponse) ProtoMessage() {}

func (x *CheckDelinkUpiNumbersWorkflowsStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDelinkUpiNumbersWorkflowsStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckDelinkUpiNumbersWorkflowsStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{23}
}

func (x *CheckDelinkUpiNumbersWorkflowsStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type UpdateUpiAccountControlsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// client request id of the client for which updation needs to be done
	ClientReqId string `protobuf:"bytes,2,opt,name=clientReqId,proto3" json:"clientReqId,omitempty"`
}

func (x *UpdateUpiAccountControlsRequest) Reset() {
	*x = UpdateUpiAccountControlsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUpiAccountControlsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUpiAccountControlsRequest) ProtoMessage() {}

func (x *UpdateUpiAccountControlsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUpiAccountControlsRequest.ProtoReflect.Descriptor instead.
func (*UpdateUpiAccountControlsRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateUpiAccountControlsRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpdateUpiAccountControlsRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type UpdateUpiAccountControlsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *UpdateUpiAccountControlsResponse) Reset() {
	*x = UpdateUpiAccountControlsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUpiAccountControlsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUpiAccountControlsResponse) ProtoMessage() {}

func (x *UpdateUpiAccountControlsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUpiAccountControlsResponse.ProtoReflect.Descriptor instead.
func (*UpdateUpiAccountControlsResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateUpiAccountControlsResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type GenerateNotificationForIntPaymentActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GenerateNotificationForIntPaymentActionRequest) Reset() {
	*x = GenerateNotificationForIntPaymentActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateNotificationForIntPaymentActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateNotificationForIntPaymentActionRequest) ProtoMessage() {}

func (x *GenerateNotificationForIntPaymentActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateNotificationForIntPaymentActionRequest.ProtoReflect.Descriptor instead.
func (*GenerateNotificationForIntPaymentActionRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{26}
}

func (x *GenerateNotificationForIntPaymentActionRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GenerateNotificationForIntPaymentActionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GenerateNotificationForIntPaymentActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// notification to publish PN to the user
	Notification *notification.Notification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
}

func (x *GenerateNotificationForIntPaymentActionResponse) Reset() {
	*x = GenerateNotificationForIntPaymentActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateNotificationForIntPaymentActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateNotificationForIntPaymentActionResponse) ProtoMessage() {}

func (x *GenerateNotificationForIntPaymentActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateNotificationForIntPaymentActionResponse.ProtoReflect.Descriptor instead.
func (*GenerateNotificationForIntPaymentActionResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{27}
}

func (x *GenerateNotificationForIntPaymentActionResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GenerateNotificationForIntPaymentActionResponse) GetNotification() *notification.Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

type CheckIntlUpiPaymentsActionStatusWithVendorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// client request id of the client for which updation needs to be done
	ClientReqId string `protobuf:"bytes,2,opt,name=clientReqId,proto3" json:"clientReqId,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorRequest) Reset() {
	*x = CheckIntlUpiPaymentsActionStatusWithVendorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIntlUpiPaymentsActionStatusWithVendorRequest) ProtoMessage() {}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIntlUpiPaymentsActionStatusWithVendorRequest.ProtoReflect.Descriptor instead.
func (*CheckIntlUpiPaymentsActionStatusWithVendorRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{28}
}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type CheckIntlUpiPaymentsActionStatusWithVendorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorResponse) Reset() {
	*x = CheckIntlUpiPaymentsActionStatusWithVendorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIntlUpiPaymentsActionStatusWithVendorResponse) ProtoMessage() {}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIntlUpiPaymentsActionStatusWithVendorResponse.ProtoReflect.Descriptor instead.
func (*CheckIntlUpiPaymentsActionStatusWithVendorResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{29}
}

func (x *CheckIntlUpiPaymentsActionStatusWithVendorResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type GenerateNotificationForUpiLiteActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GenerateNotificationForUpiLiteActionRequest) Reset() {
	*x = GenerateNotificationForUpiLiteActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateNotificationForUpiLiteActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateNotificationForUpiLiteActionRequest) ProtoMessage() {}

func (x *GenerateNotificationForUpiLiteActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateNotificationForUpiLiteActionRequest.ProtoReflect.Descriptor instead.
func (*GenerateNotificationForUpiLiteActionRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{30}
}

func (x *GenerateNotificationForUpiLiteActionRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *GenerateNotificationForUpiLiteActionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GenerateNotificationForUpiLiteActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// notification to publish PN to the user
	Notification *notification.Notification `protobuf:"bytes,2,opt,name=notification,proto3" json:"notification,omitempty"`
}

func (x *GenerateNotificationForUpiLiteActionResponse) Reset() {
	*x = GenerateNotificationForUpiLiteActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateNotificationForUpiLiteActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateNotificationForUpiLiteActionResponse) ProtoMessage() {}

func (x *GenerateNotificationForUpiLiteActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateNotificationForUpiLiteActionResponse.ProtoReflect.Descriptor instead.
func (*GenerateNotificationForUpiLiteActionResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{31}
}

func (x *GenerateNotificationForUpiLiteActionResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *GenerateNotificationForUpiLiteActionResponse) GetNotification() *notification.Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

type UpiLitePiCreationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	//	lrn (Lite Reference Number) -
	//   - to be used to store
	//     as reference number while creating upi lite account
	//   - lrn will be used for all future actions on upi lite
	//     account
	Lrn string `protobuf:"bytes,2,opt,name=lrn,proto3" json:"lrn,omitempty"`
	// account id of the upi account for which upi lite activation is going on.
	AccountRefId string `protobuf:"bytes,3,opt,name=account_ref_id,json=accountRefId,proto3" json:"account_ref_id,omitempty"`
	// actor id of the user
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// upi lite account id for which PI
	// needs to be created
	UpiLiteAccountId string `protobuf:"bytes,5,opt,name=upi_lite_account_id,json=upiLiteAccountId,proto3" json:"upi_lite_account_id,omitempty"`
}

func (x *UpiLitePiCreationRequest) Reset() {
	*x = UpiLitePiCreationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiLitePiCreationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiLitePiCreationRequest) ProtoMessage() {}

func (x *UpiLitePiCreationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiLitePiCreationRequest.ProtoReflect.Descriptor instead.
func (*UpiLitePiCreationRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{32}
}

func (x *UpiLitePiCreationRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpiLitePiCreationRequest) GetLrn() string {
	if x != nil {
		return x.Lrn
	}
	return ""
}

func (x *UpiLitePiCreationRequest) GetAccountRefId() string {
	if x != nil {
		return x.AccountRefId
	}
	return ""
}

func (x *UpiLitePiCreationRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpiLitePiCreationRequest) GetUpiLiteAccountId() string {
	if x != nil {
		return x.UpiLiteAccountId
	}
	return ""
}

type UpiLitePiCreationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *UpiLitePiCreationResponse) Reset() {
	*x = UpiLitePiCreationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiLitePiCreationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiLitePiCreationResponse) ProtoMessage() {}

func (x *UpiLitePiCreationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiLitePiCreationResponse.ProtoReflect.Descriptor instead.
func (*UpiLitePiCreationResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{33}
}

func (x *UpiLitePiCreationResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type UpiLitePiDeactivationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// lrn (Lite Reference Number) - used to identify upi lite pi uniquely
	Lrn string `protobuf:"bytes,2,opt,name=lrn,proto3" json:"lrn,omitempty"`
}

func (x *UpiLitePiDeactivationRequest) Reset() {
	*x = UpiLitePiDeactivationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiLitePiDeactivationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiLitePiDeactivationRequest) ProtoMessage() {}

func (x *UpiLitePiDeactivationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiLitePiDeactivationRequest.ProtoReflect.Descriptor instead.
func (*UpiLitePiDeactivationRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{34}
}

func (x *UpiLitePiDeactivationRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpiLitePiDeactivationRequest) GetLrn() string {
	if x != nil {
		return x.Lrn
	}
	return ""
}

type UpiLitePiDeactivationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *UpiLitePiDeactivationResponse) Reset() {
	*x = UpiLitePiDeactivationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiLitePiDeactivationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiLitePiDeactivationResponse) ProtoMessage() {}

func (x *UpiLitePiDeactivationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiLitePiDeactivationResponse.ProtoReflect.Descriptor instead.
func (*UpiLitePiDeactivationResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{35}
}

func (x *UpiLitePiDeactivationResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type EnquireOrderStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// order_id - will be used for identifying any order.
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *EnquireOrderStatusRequest) Reset() {
	*x = EnquireOrderStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireOrderStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireOrderStatusRequest) ProtoMessage() {}

func (x *EnquireOrderStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireOrderStatusRequest.ProtoReflect.Descriptor instead.
func (*EnquireOrderStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{36}
}

func (x *EnquireOrderStatusRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *EnquireOrderStatusRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type EnquireOrderStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common response header across all the celestial activities
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *EnquireOrderStatusResponse) Reset() {
	*x = EnquireOrderStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireOrderStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireOrderStatusResponse) ProtoMessage() {}

func (x *EnquireOrderStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireOrderStatusResponse.ProtoReflect.Descriptor instead.
func (*EnquireOrderStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{37}
}

func (x *EnquireOrderStatusResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type CreateUpiLitePaymentOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor id for which upi lite is getting enabled or disabled
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *CreateUpiLitePaymentOrderRequest) Reset() {
	*x = CreateUpiLitePaymentOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUpiLitePaymentOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUpiLitePaymentOrderRequest) ProtoMessage() {}

func (x *CreateUpiLitePaymentOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUpiLitePaymentOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateUpiLitePaymentOrderRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{38}
}

func (x *CreateUpiLitePaymentOrderRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CreateUpiLitePaymentOrderRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type CreateUpiLitePaymentOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
	// order id of order created for payment transfer
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *CreateUpiLitePaymentOrderResponse) Reset() {
	*x = CreateUpiLitePaymentOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUpiLitePaymentOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUpiLitePaymentOrderResponse) ProtoMessage() {}

func (x *CreateUpiLitePaymentOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUpiLitePaymentOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateUpiLitePaymentOrderResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{39}
}

func (x *CreateUpiLitePaymentOrderResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

func (x *CreateUpiLitePaymentOrderResponse) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type SyncUpiLiteInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	RequestHeader *activity.RequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// account id to sync upi lite data OR
	// upi lite account (with zero balance) disablement
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// signifies the action for which sync upi lite is called
	SyncUpiLiteAction enums.SyncUpiLiteAction `protobuf:"varint,3,opt,name=sync_upi_lite_action,json=syncUpiLiteAction,proto3,enum=upi.onboarding.enums.SyncUpiLiteAction" json:"sync_upi_lite_action,omitempty"`
}

func (x *SyncUpiLiteInfoRequest) Reset() {
	*x = SyncUpiLiteInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncUpiLiteInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncUpiLiteInfoRequest) ProtoMessage() {}

func (x *SyncUpiLiteInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncUpiLiteInfoRequest.ProtoReflect.Descriptor instead.
func (*SyncUpiLiteInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{40}
}

func (x *SyncUpiLiteInfoRequest) GetRequestHeader() *activity.RequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *SyncUpiLiteInfoRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *SyncUpiLiteInfoRequest) GetSyncUpiLiteAction() enums.SyncUpiLiteAction {
	if x != nil {
		return x.SyncUpiLiteAction
	}
	return enums.SyncUpiLiteAction(0)
}

type SyncUpiLiteInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all the celestial activities.
	ResponseHeader *activity.ResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *SyncUpiLiteInfoResponse) Reset() {
	*x = SyncUpiLiteInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_activity_request_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncUpiLiteInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncUpiLiteInfoResponse) ProtoMessage() {}

func (x *SyncUpiLiteInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_activity_request_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncUpiLiteInfoResponse.ProtoReflect.Descriptor instead.
func (*SyncUpiLiteInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_upi_activity_request_proto_rawDescGZIP(), []int{41}
}

func (x *SyncUpiLiteInfoResponse) GetResponseHeader() *activity.ResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_upi_activity_request_proto protoreflect.FileDescriptor

var file_api_upi_activity_request_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0c, 0x75, 0x70, 0x69, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x23,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70,
	0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x75, 0x70, 0x69, 0x5f, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f,
	0x75, 0x70, 0x69, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x2f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x78, 0x0a, 0x2c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x46,
	0x6f, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6a, 0x0a, 0x1e,
	0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x5f, 0x0a, 0x13, 0x44, 0x65, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x50, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x68, 0x0a, 0x1c, 0x4d, 0x61, 0x72,
	0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b,
	0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x22, 0x7c, 0x0a, 0x2d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x46, 0x6f, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x6e, 0x0a, 0x1f, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x63, 0x0a, 0x14, 0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x65, 0x50,
	0x69, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x6c, 0x0a, 0x1d, 0x4d, 0x61, 0x72, 0x6b, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x22, 0xf5, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x55,
	0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x50, 0x69, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x45, 0x0a, 0x0f, 0x75, 0x70, 0x69, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x69,
	0x4d, 0x61, 0x70, 0x70, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x75, 0x70, 0x69, 0x4d,
	0x61, 0x70, 0x70, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x6f, 0x0a, 0x20,
	0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x50,
	0x69, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x95, 0x03,
	0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x41, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x45, 0x0a, 0x07, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x69,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0x22, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55,
	0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x85, 0x01, 0x0a, 0x1e, 0x4c, 0x69, 0x6e, 0x6b,
	0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22,
	0x6e, 0x0a, 0x1f, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22,
	0xcc, 0x01, 0x0a, 0x22, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x75, 0x70,
	0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb9,
	0x01, 0x0a, 0x23, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x0f, 0x75, 0x70, 0x69, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70,
	0x69, 0x4d, 0x61, 0x70, 0x70, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x75, 0x70, 0x69,
	0x4d, 0x61, 0x70, 0x70, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x87, 0x01, 0x0a, 0x20, 0x44,
	0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x57, 0x69,
	0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70,
	0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x94, 0x01, 0x0a, 0x2d, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x6f, 0x72, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xd0, 0x01,
	0x0a, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x51, 0x0a,
	0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xf0, 0x01, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70,
	0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x50,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x22, 0xd2, 0x01, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x6e,
	0x6b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x57, 0x0a, 0x17, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x75, 0x70, 0x69, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x44,
	0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x14, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x2b, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x49, 0x64, 0x73, 0x22, 0x7b, 0x0a, 0x2c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44,
	0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x55, 0x70, 0x69, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x22, 0x8d, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70,
	0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x49, 0x64, 0x22, 0x6f, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x22, 0x95, 0x01, 0x0a, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x49, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xd1, 0x01, 0x0a,
	0x2f, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x49, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x51, 0x0a,
	0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xba, 0x01, 0x0a, 0x31, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x74, 0x6c, 0x55, 0x70,
	0x69, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x81, 0x01,
	0x0a, 0x32, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x74, 0x6c, 0x55, 0x70, 0x69, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x57, 0x69, 0x74, 0x68, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x22, 0x92, 0x01, 0x0a, 0x2b, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x55, 0x70, 0x69,
	0x4c, 0x69, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65,
	0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xce, 0x01, 0x0a, 0x2c, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x6f, 0x72, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x51, 0x0a, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe6, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x69, 0x4c,
	0x69, 0x74, 0x65, 0x50, 0x69, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x72, 0x6e,
	0x12, 0x24, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x2d, 0x0a, 0x13, 0x75, 0x70, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x75, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x68, 0x0a, 0x19, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x50, 0x69, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69,
	0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x7a, 0x0a, 0x1c, 0x55, 0x70,
	0x69, 0x4c, 0x69, 0x74, 0x65, 0x50, 0x69, 0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x72, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6c, 0x72, 0x6e, 0x22, 0x6c, 0x0a, 0x1d, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74,
	0x65, 0x50, 0x69, 0x44, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x22, 0x80, 0x01, 0x0a, 0x19, 0x45, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6c,
	0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x69, 0x0a, 0x1a, 0x45, 0x6e, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x22, 0x87, 0x01, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69,
	0x4c, 0x69, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x8b, 0x01, 0x0a,
	0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x65,
	0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xdb, 0x01, 0x0a, 0x16, 0x53,
	0x79, 0x6e, 0x63, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x58,
	0x0a, 0x14, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x75, 0x70, 0x69, 0x5f, 0x6c, 0x69, 0x74, 0x65, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x11, 0x73, 0x79, 0x6e, 0x63, 0x55, 0x70, 0x69, 0x4c, 0x69,
	0x74, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x66, 0x0a, 0x17, 0x53, 0x79, 0x6e, 0x63,
	0x55, 0x70, 0x69, 0x4c, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63,
	0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x70, 0x69, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5a, 0x27, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_activity_request_proto_rawDescOnce sync.Once
	file_api_upi_activity_request_proto_rawDescData = file_api_upi_activity_request_proto_rawDesc
)

func file_api_upi_activity_request_proto_rawDescGZIP() []byte {
	file_api_upi_activity_request_proto_rawDescOnce.Do(func() {
		file_api_upi_activity_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_activity_request_proto_rawDescData)
	})
	return file_api_upi_activity_request_proto_rawDescData
}

var file_api_upi_activity_request_proto_msgTypes = make([]protoimpl.MessageInfo, 42)
var file_api_upi_activity_request_proto_goTypes = []interface{}{
	(*CreateUpiOnboardingEntityForDelinkingRequest)(nil),       // 0: upi.activity.CreateUpiOnboardingEntityForDelinkingRequest
	(*DelinkAccountWithVendorRequest)(nil),                     // 1: upi.activity.DelinkAccountWithVendorRequest
	(*DeactivatePiRequest)(nil),                                // 2: upi.activity.DeactivatePiRequest
	(*MarkAccountAsDelinkedRequest)(nil),                       // 3: upi.activity.MarkAccountAsDelinkedRequest
	(*CreateUpiOnboardingEntityForDelinkingResponse)(nil),      // 4: upi.activity.CreateUpiOnboardingEntityForDelinkingResponse
	(*DelinkAccountWithVendorResponse)(nil),                    // 5: upi.activity.DelinkAccountWithVendorResponse
	(*DeactivatePiResponse)(nil),                               // 6: upi.activity.DeactivatePiResponse
	(*MarkAccountAsDelinkedResponse)(nil),                      // 7: upi.activity.MarkAccountAsDelinkedResponse
	(*UpsertUpiNumberPiMappingRequest)(nil),                    // 8: upi.activity.UpsertUpiNumberPiMappingRequest
	(*UpsertUpiNumberPiMappingResponse)(nil),                   // 9: upi.activity.UpsertUpiNumberPiMappingResponse
	(*CreateUpiOnboardingEntityRequest)(nil),                   // 10: upi.activity.CreateUpiOnboardingEntityRequest
	(*CreateUpiOnboardingEntityResponse)(nil),                  // 11: upi.activity.CreateUpiOnboardingEntityResponse
	(*LinkUpiNumberWithVendorRequest)(nil),                     // 12: upi.activity.LinkUpiNumberWithVendorRequest
	(*LinkUpiNumberWithVendorResponse)(nil),                    // 13: upi.activity.LinkUpiNumberWithVendorResponse
	(*CheckActionStatusWithVendorRequest)(nil),                 // 14: upi.activity.CheckActionStatusWithVendorRequest
	(*CheckActionStatusWithVendorResponse)(nil),                // 15: upi.activity.CheckActionStatusWithVendorResponse
	(*DelinkUpiNumberWithVendorRequest)(nil),                   // 16: upi.activity.DelinkUpiNumberWithVendorRequest
	(*DelinkUpiNumberWithVendorResponse)(nil),                  // 17: upi.activity.DelinkUpiNumberWithVendorResponse
	(*GenerateNotificationForUpiNumberActionRequest)(nil),      // 18: upi.activity.GenerateNotificationForUpiNumberActionRequest
	(*GenerateNotificationForUpiNumberActionResponse)(nil),     // 19: upi.activity.GenerateNotificationForUpiNumberActionResponse
	(*GetDelinkUpiNumberWorkflowPayloadsRequest)(nil),          // 20: upi.activity.GetDelinkUpiNumberWorkflowPayloadsRequest
	(*GetDelinkUpiNumberWorkflowPayloadsResponse)(nil),         // 21: upi.activity.GetDelinkUpiNumberWorkflowPayloadsResponse
	(*CheckDelinkUpiNumbersWorkflowsStatusRequest)(nil),        // 22: upi.activity.CheckDelinkUpiNumbersWorkflowsStatusRequest
	(*CheckDelinkUpiNumbersWorkflowsStatusResponse)(nil),       // 23: upi.activity.CheckDelinkUpiNumbersWorkflowsStatusResponse
	(*UpdateUpiAccountControlsRequest)(nil),                    // 24: upi.activity.UpdateUpiAccountControlsRequest
	(*UpdateUpiAccountControlsResponse)(nil),                   // 25: upi.activity.UpdateUpiAccountControlsResponse
	(*GenerateNotificationForIntPaymentActionRequest)(nil),     // 26: upi.activity.GenerateNotificationForIntPaymentActionRequest
	(*GenerateNotificationForIntPaymentActionResponse)(nil),    // 27: upi.activity.GenerateNotificationForIntPaymentActionResponse
	(*CheckIntlUpiPaymentsActionStatusWithVendorRequest)(nil),  // 28: upi.activity.CheckIntlUpiPaymentsActionStatusWithVendorRequest
	(*CheckIntlUpiPaymentsActionStatusWithVendorResponse)(nil), // 29: upi.activity.CheckIntlUpiPaymentsActionStatusWithVendorResponse
	(*GenerateNotificationForUpiLiteActionRequest)(nil),        // 30: upi.activity.GenerateNotificationForUpiLiteActionRequest
	(*GenerateNotificationForUpiLiteActionResponse)(nil),       // 31: upi.activity.GenerateNotificationForUpiLiteActionResponse
	(*UpiLitePiCreationRequest)(nil),                           // 32: upi.activity.UpiLitePiCreationRequest
	(*UpiLitePiCreationResponse)(nil),                          // 33: upi.activity.UpiLitePiCreationResponse
	(*UpiLitePiDeactivationRequest)(nil),                       // 34: upi.activity.UpiLitePiDeactivationRequest
	(*UpiLitePiDeactivationResponse)(nil),                      // 35: upi.activity.UpiLitePiDeactivationResponse
	(*EnquireOrderStatusRequest)(nil),                          // 36: upi.activity.EnquireOrderStatusRequest
	(*EnquireOrderStatusResponse)(nil),                         // 37: upi.activity.EnquireOrderStatusResponse
	(*CreateUpiLitePaymentOrderRequest)(nil),                   // 38: upi.activity.CreateUpiLitePaymentOrderRequest
	(*CreateUpiLitePaymentOrderResponse)(nil),                  // 39: upi.activity.CreateUpiLitePaymentOrderResponse
	(*SyncUpiLiteInfoRequest)(nil),                             // 40: upi.activity.SyncUpiLiteInfoRequest
	(*SyncUpiLiteInfoResponse)(nil),                            // 41: upi.activity.SyncUpiLiteInfoResponse
	(*activity.RequestHeader)(nil),                             // 42: celestial.activity.RequestHeader
	(*activity.ResponseHeader)(nil),                            // 43: celestial.activity.ResponseHeader
	(*onboarding.UpiMapperInfo)(nil),                           // 44: upi.onboarding.UpiMapperInfo
	(enums.UpiOnboardingAction)(0),                             // 45: upi.onboarding.enums.UpiOnboardingAction
	(vendorgateway.Vendor)(0),                                  // 46: vendorgateway.Vendor
	(*onboarding.UpiOnboardingDetailsPayload)(nil),             // 47: upi.onboarding.UpiOnboardingDetailsPayload
	(*notification.Notification)(nil),                          // 48: celestial.activity.notification.Notification
	(*payload.DelinkUpiNumberInfo)(nil),                        // 49: upi.payload.DelinkUpiNumberInfo
	(enums.SyncUpiLiteAction)(0),                               // 50: upi.onboarding.enums.SyncUpiLiteAction
}
var file_api_upi_activity_request_proto_depIdxs = []int32{
	42, // 0: upi.activity.CreateUpiOnboardingEntityForDelinkingRequest.request_header:type_name -> celestial.activity.RequestHeader
	42, // 1: upi.activity.DelinkAccountWithVendorRequest.request_header:type_name -> celestial.activity.RequestHeader
	42, // 2: upi.activity.DeactivatePiRequest.request_header:type_name -> celestial.activity.RequestHeader
	42, // 3: upi.activity.MarkAccountAsDelinkedRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 4: upi.activity.CreateUpiOnboardingEntityForDelinkingResponse.response_header:type_name -> celestial.activity.ResponseHeader
	43, // 5: upi.activity.DelinkAccountWithVendorResponse.response_header:type_name -> celestial.activity.ResponseHeader
	43, // 6: upi.activity.DeactivatePiResponse.response_header:type_name -> celestial.activity.ResponseHeader
	43, // 7: upi.activity.MarkAccountAsDelinkedResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 8: upi.activity.UpsertUpiNumberPiMappingRequest.request_header:type_name -> celestial.activity.RequestHeader
	44, // 9: upi.activity.UpsertUpiNumberPiMappingRequest.upi_mapper_info:type_name -> upi.onboarding.UpiMapperInfo
	45, // 10: upi.activity.UpsertUpiNumberPiMappingRequest.action:type_name -> upi.onboarding.enums.UpiOnboardingAction
	43, // 11: upi.activity.UpsertUpiNumberPiMappingResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 12: upi.activity.CreateUpiOnboardingEntityRequest.request_header:type_name -> celestial.activity.RequestHeader
	46, // 13: upi.activity.CreateUpiOnboardingEntityRequest.vendor:type_name -> vendorgateway.Vendor
	45, // 14: upi.activity.CreateUpiOnboardingEntityRequest.action:type_name -> upi.onboarding.enums.UpiOnboardingAction
	47, // 15: upi.activity.CreateUpiOnboardingEntityRequest.payload:type_name -> upi.onboarding.UpiOnboardingDetailsPayload
	43, // 16: upi.activity.CreateUpiOnboardingEntityResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 17: upi.activity.LinkUpiNumberWithVendorRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 18: upi.activity.LinkUpiNumberWithVendorResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 19: upi.activity.CheckActionStatusWithVendorRequest.request_header:type_name -> celestial.activity.RequestHeader
	45, // 20: upi.activity.CheckActionStatusWithVendorRequest.action:type_name -> upi.onboarding.enums.UpiOnboardingAction
	43, // 21: upi.activity.CheckActionStatusWithVendorResponse.response_header:type_name -> celestial.activity.ResponseHeader
	44, // 22: upi.activity.CheckActionStatusWithVendorResponse.upi_mapper_info:type_name -> upi.onboarding.UpiMapperInfo
	42, // 23: upi.activity.DelinkUpiNumberWithVendorRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 24: upi.activity.DelinkUpiNumberWithVendorResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 25: upi.activity.GenerateNotificationForUpiNumberActionRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 26: upi.activity.GenerateNotificationForUpiNumberActionResponse.response_header:type_name -> celestial.activity.ResponseHeader
	48, // 27: upi.activity.GenerateNotificationForUpiNumberActionResponse.notification:type_name -> celestial.activity.notification.Notification
	42, // 28: upi.activity.GetDelinkUpiNumberWorkflowPayloadsRequest.request_header:type_name -> celestial.activity.RequestHeader
	46, // 29: upi.activity.GetDelinkUpiNumberWorkflowPayloadsRequest.vendor:type_name -> vendorgateway.Vendor
	43, // 30: upi.activity.GetDelinkUpiNumberWorkflowPayloadsResponse.response_header:type_name -> celestial.activity.ResponseHeader
	49, // 31: upi.activity.GetDelinkUpiNumberWorkflowPayloadsResponse.delink_upi_number_infos:type_name -> upi.payload.DelinkUpiNumberInfo
	42, // 32: upi.activity.CheckDelinkUpiNumbersWorkflowsStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 33: upi.activity.CheckDelinkUpiNumbersWorkflowsStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 34: upi.activity.UpdateUpiAccountControlsRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 35: upi.activity.UpdateUpiAccountControlsResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 36: upi.activity.GenerateNotificationForIntPaymentActionRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 37: upi.activity.GenerateNotificationForIntPaymentActionResponse.response_header:type_name -> celestial.activity.ResponseHeader
	48, // 38: upi.activity.GenerateNotificationForIntPaymentActionResponse.notification:type_name -> celestial.activity.notification.Notification
	42, // 39: upi.activity.CheckIntlUpiPaymentsActionStatusWithVendorRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 40: upi.activity.CheckIntlUpiPaymentsActionStatusWithVendorResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 41: upi.activity.GenerateNotificationForUpiLiteActionRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 42: upi.activity.GenerateNotificationForUpiLiteActionResponse.response_header:type_name -> celestial.activity.ResponseHeader
	48, // 43: upi.activity.GenerateNotificationForUpiLiteActionResponse.notification:type_name -> celestial.activity.notification.Notification
	42, // 44: upi.activity.UpiLitePiCreationRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 45: upi.activity.UpiLitePiCreationResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 46: upi.activity.UpiLitePiDeactivationRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 47: upi.activity.UpiLitePiDeactivationResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 48: upi.activity.EnquireOrderStatusRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 49: upi.activity.EnquireOrderStatusResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 50: upi.activity.CreateUpiLitePaymentOrderRequest.request_header:type_name -> celestial.activity.RequestHeader
	43, // 51: upi.activity.CreateUpiLitePaymentOrderResponse.response_header:type_name -> celestial.activity.ResponseHeader
	42, // 52: upi.activity.SyncUpiLiteInfoRequest.request_header:type_name -> celestial.activity.RequestHeader
	50, // 53: upi.activity.SyncUpiLiteInfoRequest.sync_upi_lite_action:type_name -> upi.onboarding.enums.SyncUpiLiteAction
	43, // 54: upi.activity.SyncUpiLiteInfoResponse.response_header:type_name -> celestial.activity.ResponseHeader
	55, // [55:55] is the sub-list for method output_type
	55, // [55:55] is the sub-list for method input_type
	55, // [55:55] is the sub-list for extension type_name
	55, // [55:55] is the sub-list for extension extendee
	0,  // [0:55] is the sub-list for field type_name
}

func init() { file_api_upi_activity_request_proto_init() }
func file_api_upi_activity_request_proto_init() {
	if File_api_upi_activity_request_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_activity_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUpiOnboardingEntityForDelinkingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelinkAccountWithVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeactivatePiRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkAccountAsDelinkedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUpiOnboardingEntityForDelinkingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelinkAccountWithVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeactivatePiResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkAccountAsDelinkedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertUpiNumberPiMappingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertUpiNumberPiMappingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUpiOnboardingEntityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUpiOnboardingEntityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LinkUpiNumberWithVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LinkUpiNumberWithVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckActionStatusWithVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckActionStatusWithVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelinkUpiNumberWithVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelinkUpiNumberWithVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateNotificationForUpiNumberActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateNotificationForUpiNumberActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDelinkUpiNumberWorkflowPayloadsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDelinkUpiNumberWorkflowPayloadsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDelinkUpiNumbersWorkflowsStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDelinkUpiNumbersWorkflowsStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUpiAccountControlsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUpiAccountControlsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateNotificationForIntPaymentActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateNotificationForIntPaymentActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIntlUpiPaymentsActionStatusWithVendorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIntlUpiPaymentsActionStatusWithVendorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateNotificationForUpiLiteActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateNotificationForUpiLiteActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiLitePiCreationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiLitePiCreationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiLitePiDeactivationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiLitePiDeactivationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireOrderStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireOrderStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUpiLitePaymentOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUpiLitePaymentOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncUpiLiteInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_activity_request_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncUpiLiteInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_activity_request_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   42,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_activity_request_proto_goTypes,
		DependencyIndexes: file_api_upi_activity_request_proto_depIdxs,
		MessageInfos:      file_api_upi_activity_request_proto_msgTypes,
	}.Build()
	File_api_upi_activity_request_proto = out.File
	file_api_upi_activity_request_proto_rawDesc = nil
	file_api_upi_activity_request_proto_goTypes = nil
	file_api_upi_activity_request_proto_depIdxs = nil
}
