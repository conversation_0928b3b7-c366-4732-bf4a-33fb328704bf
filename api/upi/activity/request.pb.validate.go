// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/upi/activity/request.proto

package activity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/upi/onboarding/enums"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.UpiOnboardingAction(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on
// CreateUpiOnboardingEntityForDelinkingRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateUpiOnboardingEntityForDelinkingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateUpiOnboardingEntityForDelinkingRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateUpiOnboardingEntityForDelinkingRequestMultiError, or nil if none found.
func (m *CreateUpiOnboardingEntityForDelinkingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUpiOnboardingEntityForDelinkingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityForDelinkingRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityForDelinkingRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUpiOnboardingEntityForDelinkingRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateUpiOnboardingEntityForDelinkingRequestMultiError(errors)
	}

	return nil
}

// CreateUpiOnboardingEntityForDelinkingRequestMultiError is an error wrapping
// multiple validation errors returned by
// CreateUpiOnboardingEntityForDelinkingRequest.ValidateAll() if the
// designated constraints aren't met.
type CreateUpiOnboardingEntityForDelinkingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUpiOnboardingEntityForDelinkingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUpiOnboardingEntityForDelinkingRequestMultiError) AllErrors() []error { return m }

// CreateUpiOnboardingEntityForDelinkingRequestValidationError is the
// validation error returned by
// CreateUpiOnboardingEntityForDelinkingRequest.Validate if the designated
// constraints aren't met.
type CreateUpiOnboardingEntityForDelinkingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUpiOnboardingEntityForDelinkingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUpiOnboardingEntityForDelinkingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUpiOnboardingEntityForDelinkingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUpiOnboardingEntityForDelinkingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUpiOnboardingEntityForDelinkingRequestValidationError) ErrorName() string {
	return "CreateUpiOnboardingEntityForDelinkingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUpiOnboardingEntityForDelinkingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUpiOnboardingEntityForDelinkingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUpiOnboardingEntityForDelinkingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUpiOnboardingEntityForDelinkingRequestValidationError{}

// Validate checks the field values on DelinkAccountWithVendorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DelinkAccountWithVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelinkAccountWithVendorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DelinkAccountWithVendorRequestMultiError, or nil if none found.
func (m *DelinkAccountWithVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DelinkAccountWithVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkAccountWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkAccountWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkAccountWithVendorRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DelinkAccountWithVendorRequestMultiError(errors)
	}

	return nil
}

// DelinkAccountWithVendorRequestMultiError is an error wrapping multiple
// validation errors returned by DelinkAccountWithVendorRequest.ValidateAll()
// if the designated constraints aren't met.
type DelinkAccountWithVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelinkAccountWithVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelinkAccountWithVendorRequestMultiError) AllErrors() []error { return m }

// DelinkAccountWithVendorRequestValidationError is the validation error
// returned by DelinkAccountWithVendorRequest.Validate if the designated
// constraints aren't met.
type DelinkAccountWithVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelinkAccountWithVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelinkAccountWithVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelinkAccountWithVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelinkAccountWithVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelinkAccountWithVendorRequestValidationError) ErrorName() string {
	return "DelinkAccountWithVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DelinkAccountWithVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelinkAccountWithVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelinkAccountWithVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelinkAccountWithVendorRequestValidationError{}

// Validate checks the field values on DeactivatePiRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivatePiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivatePiRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivatePiRequestMultiError, or nil if none found.
func (m *DeactivatePiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivatePiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivatePiRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivatePiRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivatePiRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeactivatePiRequestMultiError(errors)
	}

	return nil
}

// DeactivatePiRequestMultiError is an error wrapping multiple validation
// errors returned by DeactivatePiRequest.ValidateAll() if the designated
// constraints aren't met.
type DeactivatePiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivatePiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivatePiRequestMultiError) AllErrors() []error { return m }

// DeactivatePiRequestValidationError is the validation error returned by
// DeactivatePiRequest.Validate if the designated constraints aren't met.
type DeactivatePiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivatePiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivatePiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivatePiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivatePiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivatePiRequestValidationError) ErrorName() string {
	return "DeactivatePiRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivatePiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivatePiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivatePiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivatePiRequestValidationError{}

// Validate checks the field values on MarkAccountAsDelinkedRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MarkAccountAsDelinkedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MarkAccountAsDelinkedRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MarkAccountAsDelinkedRequestMultiError, or nil if none found.
func (m *MarkAccountAsDelinkedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MarkAccountAsDelinkedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarkAccountAsDelinkedRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarkAccountAsDelinkedRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarkAccountAsDelinkedRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MarkAccountAsDelinkedRequestMultiError(errors)
	}

	return nil
}

// MarkAccountAsDelinkedRequestMultiError is an error wrapping multiple
// validation errors returned by MarkAccountAsDelinkedRequest.ValidateAll() if
// the designated constraints aren't met.
type MarkAccountAsDelinkedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarkAccountAsDelinkedRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarkAccountAsDelinkedRequestMultiError) AllErrors() []error { return m }

// MarkAccountAsDelinkedRequestValidationError is the validation error returned
// by MarkAccountAsDelinkedRequest.Validate if the designated constraints
// aren't met.
type MarkAccountAsDelinkedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarkAccountAsDelinkedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarkAccountAsDelinkedRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MarkAccountAsDelinkedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarkAccountAsDelinkedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarkAccountAsDelinkedRequestValidationError) ErrorName() string {
	return "MarkAccountAsDelinkedRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MarkAccountAsDelinkedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarkAccountAsDelinkedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarkAccountAsDelinkedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarkAccountAsDelinkedRequestValidationError{}

// Validate checks the field values on
// CreateUpiOnboardingEntityForDelinkingResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateUpiOnboardingEntityForDelinkingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateUpiOnboardingEntityForDelinkingResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateUpiOnboardingEntityForDelinkingResponseMultiError, or nil if none found.
func (m *CreateUpiOnboardingEntityForDelinkingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUpiOnboardingEntityForDelinkingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityForDelinkingResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityForDelinkingResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUpiOnboardingEntityForDelinkingResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateUpiOnboardingEntityForDelinkingResponseMultiError(errors)
	}

	return nil
}

// CreateUpiOnboardingEntityForDelinkingResponseMultiError is an error wrapping
// multiple validation errors returned by
// CreateUpiOnboardingEntityForDelinkingResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateUpiOnboardingEntityForDelinkingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUpiOnboardingEntityForDelinkingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUpiOnboardingEntityForDelinkingResponseMultiError) AllErrors() []error { return m }

// CreateUpiOnboardingEntityForDelinkingResponseValidationError is the
// validation error returned by
// CreateUpiOnboardingEntityForDelinkingResponse.Validate if the designated
// constraints aren't met.
type CreateUpiOnboardingEntityForDelinkingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUpiOnboardingEntityForDelinkingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUpiOnboardingEntityForDelinkingResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreateUpiOnboardingEntityForDelinkingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUpiOnboardingEntityForDelinkingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUpiOnboardingEntityForDelinkingResponseValidationError) ErrorName() string {
	return "CreateUpiOnboardingEntityForDelinkingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUpiOnboardingEntityForDelinkingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUpiOnboardingEntityForDelinkingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUpiOnboardingEntityForDelinkingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUpiOnboardingEntityForDelinkingResponseValidationError{}

// Validate checks the field values on DelinkAccountWithVendorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DelinkAccountWithVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelinkAccountWithVendorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DelinkAccountWithVendorResponseMultiError, or nil if none found.
func (m *DelinkAccountWithVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DelinkAccountWithVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkAccountWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkAccountWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkAccountWithVendorResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DelinkAccountWithVendorResponseMultiError(errors)
	}

	return nil
}

// DelinkAccountWithVendorResponseMultiError is an error wrapping multiple
// validation errors returned by DelinkAccountWithVendorResponse.ValidateAll()
// if the designated constraints aren't met.
type DelinkAccountWithVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelinkAccountWithVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelinkAccountWithVendorResponseMultiError) AllErrors() []error { return m }

// DelinkAccountWithVendorResponseValidationError is the validation error
// returned by DelinkAccountWithVendorResponse.Validate if the designated
// constraints aren't met.
type DelinkAccountWithVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelinkAccountWithVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelinkAccountWithVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelinkAccountWithVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelinkAccountWithVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelinkAccountWithVendorResponseValidationError) ErrorName() string {
	return "DelinkAccountWithVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DelinkAccountWithVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelinkAccountWithVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelinkAccountWithVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelinkAccountWithVendorResponseValidationError{}

// Validate checks the field values on DeactivatePiResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeactivatePiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeactivatePiResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeactivatePiResponseMultiError, or nil if none found.
func (m *DeactivatePiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeactivatePiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeactivatePiResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeactivatePiResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeactivatePiResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeactivatePiResponseMultiError(errors)
	}

	return nil
}

// DeactivatePiResponseMultiError is an error wrapping multiple validation
// errors returned by DeactivatePiResponse.ValidateAll() if the designated
// constraints aren't met.
type DeactivatePiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeactivatePiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeactivatePiResponseMultiError) AllErrors() []error { return m }

// DeactivatePiResponseValidationError is the validation error returned by
// DeactivatePiResponse.Validate if the designated constraints aren't met.
type DeactivatePiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeactivatePiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeactivatePiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeactivatePiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeactivatePiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeactivatePiResponseValidationError) ErrorName() string {
	return "DeactivatePiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeactivatePiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeactivatePiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeactivatePiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeactivatePiResponseValidationError{}

// Validate checks the field values on MarkAccountAsDelinkedResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MarkAccountAsDelinkedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MarkAccountAsDelinkedResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// MarkAccountAsDelinkedResponseMultiError, or nil if none found.
func (m *MarkAccountAsDelinkedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MarkAccountAsDelinkedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MarkAccountAsDelinkedResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MarkAccountAsDelinkedResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MarkAccountAsDelinkedResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MarkAccountAsDelinkedResponseMultiError(errors)
	}

	return nil
}

// MarkAccountAsDelinkedResponseMultiError is an error wrapping multiple
// validation errors returned by MarkAccountAsDelinkedResponse.ValidateAll()
// if the designated constraints aren't met.
type MarkAccountAsDelinkedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MarkAccountAsDelinkedResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MarkAccountAsDelinkedResponseMultiError) AllErrors() []error { return m }

// MarkAccountAsDelinkedResponseValidationError is the validation error
// returned by MarkAccountAsDelinkedResponse.Validate if the designated
// constraints aren't met.
type MarkAccountAsDelinkedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MarkAccountAsDelinkedResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MarkAccountAsDelinkedResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MarkAccountAsDelinkedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MarkAccountAsDelinkedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MarkAccountAsDelinkedResponseValidationError) ErrorName() string {
	return "MarkAccountAsDelinkedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MarkAccountAsDelinkedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMarkAccountAsDelinkedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MarkAccountAsDelinkedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MarkAccountAsDelinkedResponseValidationError{}

// Validate checks the field values on UpsertUpiNumberPiMappingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpsertUpiNumberPiMappingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertUpiNumberPiMappingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpsertUpiNumberPiMappingRequestMultiError, or nil if none found.
func (m *UpsertUpiNumberPiMappingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertUpiNumberPiMappingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUpiNumberPiMappingRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUpiNumberPiMappingRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUpiNumberPiMappingRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpiMapperInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUpiNumberPiMappingRequestValidationError{
					field:  "UpiMapperInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUpiNumberPiMappingRequestValidationError{
					field:  "UpiMapperInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpiMapperInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUpiNumberPiMappingRequestValidationError{
				field:  "UpiMapperInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	if len(errors) > 0 {
		return UpsertUpiNumberPiMappingRequestMultiError(errors)
	}

	return nil
}

// UpsertUpiNumberPiMappingRequestMultiError is an error wrapping multiple
// validation errors returned by UpsertUpiNumberPiMappingRequest.ValidateAll()
// if the designated constraints aren't met.
type UpsertUpiNumberPiMappingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertUpiNumberPiMappingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertUpiNumberPiMappingRequestMultiError) AllErrors() []error { return m }

// UpsertUpiNumberPiMappingRequestValidationError is the validation error
// returned by UpsertUpiNumberPiMappingRequest.Validate if the designated
// constraints aren't met.
type UpsertUpiNumberPiMappingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertUpiNumberPiMappingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertUpiNumberPiMappingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertUpiNumberPiMappingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertUpiNumberPiMappingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertUpiNumberPiMappingRequestValidationError) ErrorName() string {
	return "UpsertUpiNumberPiMappingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertUpiNumberPiMappingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertUpiNumberPiMappingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertUpiNumberPiMappingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertUpiNumberPiMappingRequestValidationError{}

// Validate checks the field values on UpsertUpiNumberPiMappingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpsertUpiNumberPiMappingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertUpiNumberPiMappingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpsertUpiNumberPiMappingResponseMultiError, or nil if none found.
func (m *UpsertUpiNumberPiMappingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertUpiNumberPiMappingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpsertUpiNumberPiMappingResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpsertUpiNumberPiMappingResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpsertUpiNumberPiMappingResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpsertUpiNumberPiMappingResponseMultiError(errors)
	}

	return nil
}

// UpsertUpiNumberPiMappingResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpsertUpiNumberPiMappingResponse.ValidateAll() if the designated
// constraints aren't met.
type UpsertUpiNumberPiMappingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertUpiNumberPiMappingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertUpiNumberPiMappingResponseMultiError) AllErrors() []error { return m }

// UpsertUpiNumberPiMappingResponseValidationError is the validation error
// returned by UpsertUpiNumberPiMappingResponse.Validate if the designated
// constraints aren't met.
type UpsertUpiNumberPiMappingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertUpiNumberPiMappingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertUpiNumberPiMappingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertUpiNumberPiMappingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertUpiNumberPiMappingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertUpiNumberPiMappingResponseValidationError) ErrorName() string {
	return "UpsertUpiNumberPiMappingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertUpiNumberPiMappingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertUpiNumberPiMappingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertUpiNumberPiMappingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertUpiNumberPiMappingResponseValidationError{}

// Validate checks the field values on CreateUpiOnboardingEntityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateUpiOnboardingEntityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUpiOnboardingEntityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateUpiOnboardingEntityRequestMultiError, or nil if none found.
func (m *CreateUpiOnboardingEntityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUpiOnboardingEntityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUpiOnboardingEntityRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	// no validation rules for ActorId

	// no validation rules for Vendor

	// no validation rules for Action

	// no validation rules for Vpa

	if all {
		switch v := interface{}(m.GetPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityRequestValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityRequestValidationError{
					field:  "Payload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUpiOnboardingEntityRequestValidationError{
				field:  "Payload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorReqId

	if len(errors) > 0 {
		return CreateUpiOnboardingEntityRequestMultiError(errors)
	}

	return nil
}

// CreateUpiOnboardingEntityRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateUpiOnboardingEntityRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateUpiOnboardingEntityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUpiOnboardingEntityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUpiOnboardingEntityRequestMultiError) AllErrors() []error { return m }

// CreateUpiOnboardingEntityRequestValidationError is the validation error
// returned by CreateUpiOnboardingEntityRequest.Validate if the designated
// constraints aren't met.
type CreateUpiOnboardingEntityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUpiOnboardingEntityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUpiOnboardingEntityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUpiOnboardingEntityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUpiOnboardingEntityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUpiOnboardingEntityRequestValidationError) ErrorName() string {
	return "CreateUpiOnboardingEntityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUpiOnboardingEntityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUpiOnboardingEntityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUpiOnboardingEntityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUpiOnboardingEntityRequestValidationError{}

// Validate checks the field values on CreateUpiOnboardingEntityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateUpiOnboardingEntityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUpiOnboardingEntityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateUpiOnboardingEntityResponseMultiError, or nil if none found.
func (m *CreateUpiOnboardingEntityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUpiOnboardingEntityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUpiOnboardingEntityResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUpiOnboardingEntityResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateUpiOnboardingEntityResponseMultiError(errors)
	}

	return nil
}

// CreateUpiOnboardingEntityResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateUpiOnboardingEntityResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateUpiOnboardingEntityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUpiOnboardingEntityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUpiOnboardingEntityResponseMultiError) AllErrors() []error { return m }

// CreateUpiOnboardingEntityResponseValidationError is the validation error
// returned by CreateUpiOnboardingEntityResponse.Validate if the designated
// constraints aren't met.
type CreateUpiOnboardingEntityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUpiOnboardingEntityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUpiOnboardingEntityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUpiOnboardingEntityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUpiOnboardingEntityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUpiOnboardingEntityResponseValidationError) ErrorName() string {
	return "CreateUpiOnboardingEntityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUpiOnboardingEntityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUpiOnboardingEntityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUpiOnboardingEntityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUpiOnboardingEntityResponseValidationError{}

// Validate checks the field values on LinkUpiNumberWithVendorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkUpiNumberWithVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkUpiNumberWithVendorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LinkUpiNumberWithVendorRequestMultiError, or nil if none found.
func (m *LinkUpiNumberWithVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkUpiNumberWithVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LinkUpiNumberWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LinkUpiNumberWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LinkUpiNumberWithVendorRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return LinkUpiNumberWithVendorRequestMultiError(errors)
	}

	return nil
}

// LinkUpiNumberWithVendorRequestMultiError is an error wrapping multiple
// validation errors returned by LinkUpiNumberWithVendorRequest.ValidateAll()
// if the designated constraints aren't met.
type LinkUpiNumberWithVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkUpiNumberWithVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkUpiNumberWithVendorRequestMultiError) AllErrors() []error { return m }

// LinkUpiNumberWithVendorRequestValidationError is the validation error
// returned by LinkUpiNumberWithVendorRequest.Validate if the designated
// constraints aren't met.
type LinkUpiNumberWithVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkUpiNumberWithVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkUpiNumberWithVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkUpiNumberWithVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkUpiNumberWithVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkUpiNumberWithVendorRequestValidationError) ErrorName() string {
	return "LinkUpiNumberWithVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LinkUpiNumberWithVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkUpiNumberWithVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkUpiNumberWithVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkUpiNumberWithVendorRequestValidationError{}

// Validate checks the field values on LinkUpiNumberWithVendorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LinkUpiNumberWithVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinkUpiNumberWithVendorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// LinkUpiNumberWithVendorResponseMultiError, or nil if none found.
func (m *LinkUpiNumberWithVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LinkUpiNumberWithVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LinkUpiNumberWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LinkUpiNumberWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LinkUpiNumberWithVendorResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LinkUpiNumberWithVendorResponseMultiError(errors)
	}

	return nil
}

// LinkUpiNumberWithVendorResponseMultiError is an error wrapping multiple
// validation errors returned by LinkUpiNumberWithVendorResponse.ValidateAll()
// if the designated constraints aren't met.
type LinkUpiNumberWithVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinkUpiNumberWithVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinkUpiNumberWithVendorResponseMultiError) AllErrors() []error { return m }

// LinkUpiNumberWithVendorResponseValidationError is the validation error
// returned by LinkUpiNumberWithVendorResponse.Validate if the designated
// constraints aren't met.
type LinkUpiNumberWithVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinkUpiNumberWithVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinkUpiNumberWithVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinkUpiNumberWithVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinkUpiNumberWithVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinkUpiNumberWithVendorResponseValidationError) ErrorName() string {
	return "LinkUpiNumberWithVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LinkUpiNumberWithVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinkUpiNumberWithVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinkUpiNumberWithVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinkUpiNumberWithVendorResponseValidationError{}

// Validate checks the field values on CheckActionStatusWithVendorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckActionStatusWithVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckActionStatusWithVendorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckActionStatusWithVendorRequestMultiError, or nil if none found.
func (m *CheckActionStatusWithVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckActionStatusWithVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckActionStatusWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckActionStatusWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckActionStatusWithVendorRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for Action

	if len(errors) > 0 {
		return CheckActionStatusWithVendorRequestMultiError(errors)
	}

	return nil
}

// CheckActionStatusWithVendorRequestMultiError is an error wrapping multiple
// validation errors returned by
// CheckActionStatusWithVendorRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckActionStatusWithVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckActionStatusWithVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckActionStatusWithVendorRequestMultiError) AllErrors() []error { return m }

// CheckActionStatusWithVendorRequestValidationError is the validation error
// returned by CheckActionStatusWithVendorRequest.Validate if the designated
// constraints aren't met.
type CheckActionStatusWithVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckActionStatusWithVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckActionStatusWithVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckActionStatusWithVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckActionStatusWithVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckActionStatusWithVendorRequestValidationError) ErrorName() string {
	return "CheckActionStatusWithVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckActionStatusWithVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckActionStatusWithVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckActionStatusWithVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckActionStatusWithVendorRequestValidationError{}

// Validate checks the field values on CheckActionStatusWithVendorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckActionStatusWithVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckActionStatusWithVendorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckActionStatusWithVendorResponseMultiError, or nil if none found.
func (m *CheckActionStatusWithVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckActionStatusWithVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckActionStatusWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckActionStatusWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckActionStatusWithVendorResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpiMapperInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckActionStatusWithVendorResponseValidationError{
					field:  "UpiMapperInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckActionStatusWithVendorResponseValidationError{
					field:  "UpiMapperInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpiMapperInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckActionStatusWithVendorResponseValidationError{
				field:  "UpiMapperInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckActionStatusWithVendorResponseMultiError(errors)
	}

	return nil
}

// CheckActionStatusWithVendorResponseMultiError is an error wrapping multiple
// validation errors returned by
// CheckActionStatusWithVendorResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckActionStatusWithVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckActionStatusWithVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckActionStatusWithVendorResponseMultiError) AllErrors() []error { return m }

// CheckActionStatusWithVendorResponseValidationError is the validation error
// returned by CheckActionStatusWithVendorResponse.Validate if the designated
// constraints aren't met.
type CheckActionStatusWithVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckActionStatusWithVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckActionStatusWithVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckActionStatusWithVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckActionStatusWithVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckActionStatusWithVendorResponseValidationError) ErrorName() string {
	return "CheckActionStatusWithVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckActionStatusWithVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckActionStatusWithVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckActionStatusWithVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckActionStatusWithVendorResponseValidationError{}

// Validate checks the field values on DelinkUpiNumberWithVendorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DelinkUpiNumberWithVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelinkUpiNumberWithVendorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DelinkUpiNumberWithVendorRequestMultiError, or nil if none found.
func (m *DelinkUpiNumberWithVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DelinkUpiNumberWithVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiNumberWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiNumberWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiNumberWithVendorRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return DelinkUpiNumberWithVendorRequestMultiError(errors)
	}

	return nil
}

// DelinkUpiNumberWithVendorRequestMultiError is an error wrapping multiple
// validation errors returned by
// DelinkUpiNumberWithVendorRequest.ValidateAll() if the designated
// constraints aren't met.
type DelinkUpiNumberWithVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelinkUpiNumberWithVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelinkUpiNumberWithVendorRequestMultiError) AllErrors() []error { return m }

// DelinkUpiNumberWithVendorRequestValidationError is the validation error
// returned by DelinkUpiNumberWithVendorRequest.Validate if the designated
// constraints aren't met.
type DelinkUpiNumberWithVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelinkUpiNumberWithVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelinkUpiNumberWithVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelinkUpiNumberWithVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelinkUpiNumberWithVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelinkUpiNumberWithVendorRequestValidationError) ErrorName() string {
	return "DelinkUpiNumberWithVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DelinkUpiNumberWithVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelinkUpiNumberWithVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelinkUpiNumberWithVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelinkUpiNumberWithVendorRequestValidationError{}

// Validate checks the field values on DelinkUpiNumberWithVendorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DelinkUpiNumberWithVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelinkUpiNumberWithVendorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DelinkUpiNumberWithVendorResponseMultiError, or nil if none found.
func (m *DelinkUpiNumberWithVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DelinkUpiNumberWithVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DelinkUpiNumberWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DelinkUpiNumberWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DelinkUpiNumberWithVendorResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DelinkUpiNumberWithVendorResponseMultiError(errors)
	}

	return nil
}

// DelinkUpiNumberWithVendorResponseMultiError is an error wrapping multiple
// validation errors returned by
// DelinkUpiNumberWithVendorResponse.ValidateAll() if the designated
// constraints aren't met.
type DelinkUpiNumberWithVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelinkUpiNumberWithVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelinkUpiNumberWithVendorResponseMultiError) AllErrors() []error { return m }

// DelinkUpiNumberWithVendorResponseValidationError is the validation error
// returned by DelinkUpiNumberWithVendorResponse.Validate if the designated
// constraints aren't met.
type DelinkUpiNumberWithVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelinkUpiNumberWithVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelinkUpiNumberWithVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelinkUpiNumberWithVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelinkUpiNumberWithVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelinkUpiNumberWithVendorResponseValidationError) ErrorName() string {
	return "DelinkUpiNumberWithVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DelinkUpiNumberWithVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelinkUpiNumberWithVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelinkUpiNumberWithVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelinkUpiNumberWithVendorResponseValidationError{}

// Validate checks the field values on
// GenerateNotificationForUpiNumberActionRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GenerateNotificationForUpiNumberActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateNotificationForUpiNumberActionRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GenerateNotificationForUpiNumberActionRequestMultiError, or nil if none found.
func (m *GenerateNotificationForUpiNumberActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateNotificationForUpiNumberActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateNotificationForUpiNumberActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateNotificationForUpiNumberActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateNotificationForUpiNumberActionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GenerateNotificationForUpiNumberActionRequestMultiError(errors)
	}

	return nil
}

// GenerateNotificationForUpiNumberActionRequestMultiError is an error wrapping
// multiple validation errors returned by
// GenerateNotificationForUpiNumberActionRequest.ValidateAll() if the
// designated constraints aren't met.
type GenerateNotificationForUpiNumberActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateNotificationForUpiNumberActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateNotificationForUpiNumberActionRequestMultiError) AllErrors() []error { return m }

// GenerateNotificationForUpiNumberActionRequestValidationError is the
// validation error returned by
// GenerateNotificationForUpiNumberActionRequest.Validate if the designated
// constraints aren't met.
type GenerateNotificationForUpiNumberActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateNotificationForUpiNumberActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateNotificationForUpiNumberActionRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GenerateNotificationForUpiNumberActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateNotificationForUpiNumberActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateNotificationForUpiNumberActionRequestValidationError) ErrorName() string {
	return "GenerateNotificationForUpiNumberActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateNotificationForUpiNumberActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateNotificationForUpiNumberActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateNotificationForUpiNumberActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateNotificationForUpiNumberActionRequestValidationError{}

// Validate checks the field values on
// GenerateNotificationForUpiNumberActionResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GenerateNotificationForUpiNumberActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateNotificationForUpiNumberActionResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GenerateNotificationForUpiNumberActionResponseMultiError, or nil if none found.
func (m *GenerateNotificationForUpiNumberActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateNotificationForUpiNumberActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateNotificationForUpiNumberActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateNotificationForUpiNumberActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateNotificationForUpiNumberActionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateNotificationForUpiNumberActionResponseValidationError{
					field:  "Notification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateNotificationForUpiNumberActionResponseValidationError{
					field:  "Notification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateNotificationForUpiNumberActionResponseValidationError{
				field:  "Notification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateNotificationForUpiNumberActionResponseMultiError(errors)
	}

	return nil
}

// GenerateNotificationForUpiNumberActionResponseMultiError is an error
// wrapping multiple validation errors returned by
// GenerateNotificationForUpiNumberActionResponse.ValidateAll() if the
// designated constraints aren't met.
type GenerateNotificationForUpiNumberActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateNotificationForUpiNumberActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateNotificationForUpiNumberActionResponseMultiError) AllErrors() []error { return m }

// GenerateNotificationForUpiNumberActionResponseValidationError is the
// validation error returned by
// GenerateNotificationForUpiNumberActionResponse.Validate if the designated
// constraints aren't met.
type GenerateNotificationForUpiNumberActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateNotificationForUpiNumberActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateNotificationForUpiNumberActionResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GenerateNotificationForUpiNumberActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateNotificationForUpiNumberActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateNotificationForUpiNumberActionResponseValidationError) ErrorName() string {
	return "GenerateNotificationForUpiNumberActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateNotificationForUpiNumberActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateNotificationForUpiNumberActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateNotificationForUpiNumberActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateNotificationForUpiNumberActionResponseValidationError{}

// Validate checks the field values on
// GetDelinkUpiNumberWorkflowPayloadsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDelinkUpiNumberWorkflowPayloadsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDelinkUpiNumberWorkflowPayloadsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetDelinkUpiNumberWorkflowPayloadsRequestMultiError, or nil if none found.
func (m *GetDelinkUpiNumberWorkflowPayloadsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDelinkUpiNumberWorkflowPayloadsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDelinkUpiNumberWorkflowPayloadsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDelinkUpiNumberWorkflowPayloadsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDelinkUpiNumberWorkflowPayloadsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vpa

	// no validation rules for ActorId

	// no validation rules for AccountId

	// no validation rules for Vendor

	if len(errors) > 0 {
		return GetDelinkUpiNumberWorkflowPayloadsRequestMultiError(errors)
	}

	return nil
}

// GetDelinkUpiNumberWorkflowPayloadsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetDelinkUpiNumberWorkflowPayloadsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDelinkUpiNumberWorkflowPayloadsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDelinkUpiNumberWorkflowPayloadsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDelinkUpiNumberWorkflowPayloadsRequestMultiError) AllErrors() []error { return m }

// GetDelinkUpiNumberWorkflowPayloadsRequestValidationError is the validation
// error returned by GetDelinkUpiNumberWorkflowPayloadsRequest.Validate if the
// designated constraints aren't met.
type GetDelinkUpiNumberWorkflowPayloadsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDelinkUpiNumberWorkflowPayloadsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDelinkUpiNumberWorkflowPayloadsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDelinkUpiNumberWorkflowPayloadsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDelinkUpiNumberWorkflowPayloadsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDelinkUpiNumberWorkflowPayloadsRequestValidationError) ErrorName() string {
	return "GetDelinkUpiNumberWorkflowPayloadsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDelinkUpiNumberWorkflowPayloadsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDelinkUpiNumberWorkflowPayloadsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDelinkUpiNumberWorkflowPayloadsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDelinkUpiNumberWorkflowPayloadsRequestValidationError{}

// Validate checks the field values on
// GetDelinkUpiNumberWorkflowPayloadsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDelinkUpiNumberWorkflowPayloadsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDelinkUpiNumberWorkflowPayloadsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetDelinkUpiNumberWorkflowPayloadsResponseMultiError, or nil if none found.
func (m *GetDelinkUpiNumberWorkflowPayloadsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDelinkUpiNumberWorkflowPayloadsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDelinkUpiNumberWorkflowPayloadsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDelinkUpiNumberWorkflowPayloadsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDelinkUpiNumberWorkflowPayloadsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDelinkUpiNumberInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDelinkUpiNumberWorkflowPayloadsResponseValidationError{
						field:  fmt.Sprintf("DelinkUpiNumberInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDelinkUpiNumberWorkflowPayloadsResponseValidationError{
						field:  fmt.Sprintf("DelinkUpiNumberInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDelinkUpiNumberWorkflowPayloadsResponseValidationError{
					field:  fmt.Sprintf("DelinkUpiNumberInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetDelinkUpiNumberWorkflowPayloadsResponseMultiError(errors)
	}

	return nil
}

// GetDelinkUpiNumberWorkflowPayloadsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetDelinkUpiNumberWorkflowPayloadsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDelinkUpiNumberWorkflowPayloadsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDelinkUpiNumberWorkflowPayloadsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDelinkUpiNumberWorkflowPayloadsResponseMultiError) AllErrors() []error { return m }

// GetDelinkUpiNumberWorkflowPayloadsResponseValidationError is the validation
// error returned by GetDelinkUpiNumberWorkflowPayloadsResponse.Validate if
// the designated constraints aren't met.
type GetDelinkUpiNumberWorkflowPayloadsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDelinkUpiNumberWorkflowPayloadsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDelinkUpiNumberWorkflowPayloadsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDelinkUpiNumberWorkflowPayloadsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDelinkUpiNumberWorkflowPayloadsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDelinkUpiNumberWorkflowPayloadsResponseValidationError) ErrorName() string {
	return "GetDelinkUpiNumberWorkflowPayloadsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDelinkUpiNumberWorkflowPayloadsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDelinkUpiNumberWorkflowPayloadsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDelinkUpiNumberWorkflowPayloadsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDelinkUpiNumberWorkflowPayloadsResponseValidationError{}

// Validate checks the field values on
// CheckDelinkUpiNumbersWorkflowsStatusRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckDelinkUpiNumbersWorkflowsStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckDelinkUpiNumbersWorkflowsStatusRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckDelinkUpiNumbersWorkflowsStatusRequestMultiError, or nil if none found.
func (m *CheckDelinkUpiNumbersWorkflowsStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckDelinkUpiNumbersWorkflowsStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckDelinkUpiNumbersWorkflowsStatusRequestMultiError(errors)
	}

	return nil
}

// CheckDelinkUpiNumbersWorkflowsStatusRequestMultiError is an error wrapping
// multiple validation errors returned by
// CheckDelinkUpiNumbersWorkflowsStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckDelinkUpiNumbersWorkflowsStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckDelinkUpiNumbersWorkflowsStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckDelinkUpiNumbersWorkflowsStatusRequestMultiError) AllErrors() []error { return m }

// CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError is the validation
// error returned by CheckDelinkUpiNumbersWorkflowsStatusRequest.Validate if
// the designated constraints aren't met.
type CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError) ErrorName() string {
	return "CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckDelinkUpiNumbersWorkflowsStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckDelinkUpiNumbersWorkflowsStatusRequestValidationError{}

// Validate checks the field values on
// CheckDelinkUpiNumbersWorkflowsStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckDelinkUpiNumbersWorkflowsStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckDelinkUpiNumbersWorkflowsStatusResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckDelinkUpiNumbersWorkflowsStatusResponseMultiError, or nil if none found.
func (m *CheckDelinkUpiNumbersWorkflowsStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckDelinkUpiNumbersWorkflowsStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckDelinkUpiNumbersWorkflowsStatusResponseMultiError(errors)
	}

	return nil
}

// CheckDelinkUpiNumbersWorkflowsStatusResponseMultiError is an error wrapping
// multiple validation errors returned by
// CheckDelinkUpiNumbersWorkflowsStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type CheckDelinkUpiNumbersWorkflowsStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckDelinkUpiNumbersWorkflowsStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckDelinkUpiNumbersWorkflowsStatusResponseMultiError) AllErrors() []error { return m }

// CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError is the
// validation error returned by
// CheckDelinkUpiNumbersWorkflowsStatusResponse.Validate if the designated
// constraints aren't met.
type CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError) ErrorName() string {
	return "CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckDelinkUpiNumbersWorkflowsStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckDelinkUpiNumbersWorkflowsStatusResponseValidationError{}

// Validate checks the field values on UpdateUpiAccountControlsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUpiAccountControlsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUpiAccountControlsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateUpiAccountControlsRequestMultiError, or nil if none found.
func (m *UpdateUpiAccountControlsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUpiAccountControlsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUpiAccountControlsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUpiAccountControlsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUpiAccountControlsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return UpdateUpiAccountControlsRequestMultiError(errors)
	}

	return nil
}

// UpdateUpiAccountControlsRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateUpiAccountControlsRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateUpiAccountControlsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUpiAccountControlsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUpiAccountControlsRequestMultiError) AllErrors() []error { return m }

// UpdateUpiAccountControlsRequestValidationError is the validation error
// returned by UpdateUpiAccountControlsRequest.Validate if the designated
// constraints aren't met.
type UpdateUpiAccountControlsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUpiAccountControlsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUpiAccountControlsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUpiAccountControlsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUpiAccountControlsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUpiAccountControlsRequestValidationError) ErrorName() string {
	return "UpdateUpiAccountControlsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUpiAccountControlsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUpiAccountControlsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUpiAccountControlsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUpiAccountControlsRequestValidationError{}

// Validate checks the field values on UpdateUpiAccountControlsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateUpiAccountControlsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUpiAccountControlsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateUpiAccountControlsResponseMultiError, or nil if none found.
func (m *UpdateUpiAccountControlsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUpiAccountControlsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUpiAccountControlsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUpiAccountControlsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUpiAccountControlsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateUpiAccountControlsResponseMultiError(errors)
	}

	return nil
}

// UpdateUpiAccountControlsResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateUpiAccountControlsResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateUpiAccountControlsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUpiAccountControlsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUpiAccountControlsResponseMultiError) AllErrors() []error { return m }

// UpdateUpiAccountControlsResponseValidationError is the validation error
// returned by UpdateUpiAccountControlsResponse.Validate if the designated
// constraints aren't met.
type UpdateUpiAccountControlsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUpiAccountControlsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUpiAccountControlsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUpiAccountControlsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUpiAccountControlsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUpiAccountControlsResponseValidationError) ErrorName() string {
	return "UpdateUpiAccountControlsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUpiAccountControlsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUpiAccountControlsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUpiAccountControlsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUpiAccountControlsResponseValidationError{}

// Validate checks the field values on
// GenerateNotificationForIntPaymentActionRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GenerateNotificationForIntPaymentActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateNotificationForIntPaymentActionRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GenerateNotificationForIntPaymentActionRequestMultiError, or nil if none found.
func (m *GenerateNotificationForIntPaymentActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateNotificationForIntPaymentActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateNotificationForIntPaymentActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateNotificationForIntPaymentActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateNotificationForIntPaymentActionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GenerateNotificationForIntPaymentActionRequestMultiError(errors)
	}

	return nil
}

// GenerateNotificationForIntPaymentActionRequestMultiError is an error
// wrapping multiple validation errors returned by
// GenerateNotificationForIntPaymentActionRequest.ValidateAll() if the
// designated constraints aren't met.
type GenerateNotificationForIntPaymentActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateNotificationForIntPaymentActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateNotificationForIntPaymentActionRequestMultiError) AllErrors() []error { return m }

// GenerateNotificationForIntPaymentActionRequestValidationError is the
// validation error returned by
// GenerateNotificationForIntPaymentActionRequest.Validate if the designated
// constraints aren't met.
type GenerateNotificationForIntPaymentActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateNotificationForIntPaymentActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateNotificationForIntPaymentActionRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GenerateNotificationForIntPaymentActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateNotificationForIntPaymentActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateNotificationForIntPaymentActionRequestValidationError) ErrorName() string {
	return "GenerateNotificationForIntPaymentActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateNotificationForIntPaymentActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateNotificationForIntPaymentActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateNotificationForIntPaymentActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateNotificationForIntPaymentActionRequestValidationError{}

// Validate checks the field values on
// GenerateNotificationForIntPaymentActionResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GenerateNotificationForIntPaymentActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateNotificationForIntPaymentActionResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GenerateNotificationForIntPaymentActionResponseMultiError, or nil if none found.
func (m *GenerateNotificationForIntPaymentActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateNotificationForIntPaymentActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateNotificationForIntPaymentActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateNotificationForIntPaymentActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateNotificationForIntPaymentActionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateNotificationForIntPaymentActionResponseValidationError{
					field:  "Notification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateNotificationForIntPaymentActionResponseValidationError{
					field:  "Notification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateNotificationForIntPaymentActionResponseValidationError{
				field:  "Notification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateNotificationForIntPaymentActionResponseMultiError(errors)
	}

	return nil
}

// GenerateNotificationForIntPaymentActionResponseMultiError is an error
// wrapping multiple validation errors returned by
// GenerateNotificationForIntPaymentActionResponse.ValidateAll() if the
// designated constraints aren't met.
type GenerateNotificationForIntPaymentActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateNotificationForIntPaymentActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateNotificationForIntPaymentActionResponseMultiError) AllErrors() []error { return m }

// GenerateNotificationForIntPaymentActionResponseValidationError is the
// validation error returned by
// GenerateNotificationForIntPaymentActionResponse.Validate if the designated
// constraints aren't met.
type GenerateNotificationForIntPaymentActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateNotificationForIntPaymentActionResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GenerateNotificationForIntPaymentActionResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GenerateNotificationForIntPaymentActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateNotificationForIntPaymentActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateNotificationForIntPaymentActionResponseValidationError) ErrorName() string {
	return "GenerateNotificationForIntPaymentActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateNotificationForIntPaymentActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateNotificationForIntPaymentActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateNotificationForIntPaymentActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateNotificationForIntPaymentActionResponseValidationError{}

// Validate checks the field values on
// CheckIntlUpiPaymentsActionStatusWithVendorRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CheckIntlUpiPaymentsActionStatusWithVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckIntlUpiPaymentsActionStatusWithVendorRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CheckIntlUpiPaymentsActionStatusWithVendorRequestMultiError, or nil if none found.
func (m *CheckIntlUpiPaymentsActionStatusWithVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckIntlUpiPaymentsActionStatusWithVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	// no validation rules for ActorId

	if len(errors) > 0 {
		return CheckIntlUpiPaymentsActionStatusWithVendorRequestMultiError(errors)
	}

	return nil
}

// CheckIntlUpiPaymentsActionStatusWithVendorRequestMultiError is an error
// wrapping multiple validation errors returned by
// CheckIntlUpiPaymentsActionStatusWithVendorRequest.ValidateAll() if the
// designated constraints aren't met.
type CheckIntlUpiPaymentsActionStatusWithVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckIntlUpiPaymentsActionStatusWithVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckIntlUpiPaymentsActionStatusWithVendorRequestMultiError) AllErrors() []error { return m }

// CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError is the
// validation error returned by
// CheckIntlUpiPaymentsActionStatusWithVendorRequest.Validate if the
// designated constraints aren't met.
type CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError) ErrorName() string {
	return "CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckIntlUpiPaymentsActionStatusWithVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckIntlUpiPaymentsActionStatusWithVendorRequestValidationError{}

// Validate checks the field values on
// CheckIntlUpiPaymentsActionStatusWithVendorResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CheckIntlUpiPaymentsActionStatusWithVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckIntlUpiPaymentsActionStatusWithVendorResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CheckIntlUpiPaymentsActionStatusWithVendorResponseMultiError, or nil if
// none found.
func (m *CheckIntlUpiPaymentsActionStatusWithVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckIntlUpiPaymentsActionStatusWithVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckIntlUpiPaymentsActionStatusWithVendorResponseMultiError(errors)
	}

	return nil
}

// CheckIntlUpiPaymentsActionStatusWithVendorResponseMultiError is an error
// wrapping multiple validation errors returned by
// CheckIntlUpiPaymentsActionStatusWithVendorResponse.ValidateAll() if the
// designated constraints aren't met.
type CheckIntlUpiPaymentsActionStatusWithVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckIntlUpiPaymentsActionStatusWithVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckIntlUpiPaymentsActionStatusWithVendorResponseMultiError) AllErrors() []error { return m }

// CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError is the
// validation error returned by
// CheckIntlUpiPaymentsActionStatusWithVendorResponse.Validate if the
// designated constraints aren't met.
type CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError) ErrorName() string {
	return "CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckIntlUpiPaymentsActionStatusWithVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckIntlUpiPaymentsActionStatusWithVendorResponseValidationError{}

// Validate checks the field values on
// GenerateNotificationForUpiLiteActionRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GenerateNotificationForUpiLiteActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateNotificationForUpiLiteActionRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GenerateNotificationForUpiLiteActionRequestMultiError, or nil if none found.
func (m *GenerateNotificationForUpiLiteActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateNotificationForUpiLiteActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateNotificationForUpiLiteActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateNotificationForUpiLiteActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateNotificationForUpiLiteActionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GenerateNotificationForUpiLiteActionRequestMultiError(errors)
	}

	return nil
}

// GenerateNotificationForUpiLiteActionRequestMultiError is an error wrapping
// multiple validation errors returned by
// GenerateNotificationForUpiLiteActionRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateNotificationForUpiLiteActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateNotificationForUpiLiteActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateNotificationForUpiLiteActionRequestMultiError) AllErrors() []error { return m }

// GenerateNotificationForUpiLiteActionRequestValidationError is the validation
// error returned by GenerateNotificationForUpiLiteActionRequest.Validate if
// the designated constraints aren't met.
type GenerateNotificationForUpiLiteActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateNotificationForUpiLiteActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateNotificationForUpiLiteActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateNotificationForUpiLiteActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateNotificationForUpiLiteActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateNotificationForUpiLiteActionRequestValidationError) ErrorName() string {
	return "GenerateNotificationForUpiLiteActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateNotificationForUpiLiteActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateNotificationForUpiLiteActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateNotificationForUpiLiteActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateNotificationForUpiLiteActionRequestValidationError{}

// Validate checks the field values on
// GenerateNotificationForUpiLiteActionResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GenerateNotificationForUpiLiteActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GenerateNotificationForUpiLiteActionResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GenerateNotificationForUpiLiteActionResponseMultiError, or nil if none found.
func (m *GenerateNotificationForUpiLiteActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateNotificationForUpiLiteActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateNotificationForUpiLiteActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateNotificationForUpiLiteActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateNotificationForUpiLiteActionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotification()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateNotificationForUpiLiteActionResponseValidationError{
					field:  "Notification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateNotificationForUpiLiteActionResponseValidationError{
					field:  "Notification",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotification()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateNotificationForUpiLiteActionResponseValidationError{
				field:  "Notification",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateNotificationForUpiLiteActionResponseMultiError(errors)
	}

	return nil
}

// GenerateNotificationForUpiLiteActionResponseMultiError is an error wrapping
// multiple validation errors returned by
// GenerateNotificationForUpiLiteActionResponse.ValidateAll() if the
// designated constraints aren't met.
type GenerateNotificationForUpiLiteActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateNotificationForUpiLiteActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateNotificationForUpiLiteActionResponseMultiError) AllErrors() []error { return m }

// GenerateNotificationForUpiLiteActionResponseValidationError is the
// validation error returned by
// GenerateNotificationForUpiLiteActionResponse.Validate if the designated
// constraints aren't met.
type GenerateNotificationForUpiLiteActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateNotificationForUpiLiteActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateNotificationForUpiLiteActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateNotificationForUpiLiteActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateNotificationForUpiLiteActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateNotificationForUpiLiteActionResponseValidationError) ErrorName() string {
	return "GenerateNotificationForUpiLiteActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateNotificationForUpiLiteActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateNotificationForUpiLiteActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateNotificationForUpiLiteActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateNotificationForUpiLiteActionResponseValidationError{}

// Validate checks the field values on UpiLitePiCreationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiLitePiCreationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiLitePiCreationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiLitePiCreationRequestMultiError, or nil if none found.
func (m *UpiLitePiCreationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiLitePiCreationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiLitePiCreationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiLitePiCreationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiLitePiCreationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Lrn

	// no validation rules for AccountRefId

	// no validation rules for ActorId

	// no validation rules for UpiLiteAccountId

	if len(errors) > 0 {
		return UpiLitePiCreationRequestMultiError(errors)
	}

	return nil
}

// UpiLitePiCreationRequestMultiError is an error wrapping multiple validation
// errors returned by UpiLitePiCreationRequest.ValidateAll() if the designated
// constraints aren't met.
type UpiLitePiCreationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiLitePiCreationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiLitePiCreationRequestMultiError) AllErrors() []error { return m }

// UpiLitePiCreationRequestValidationError is the validation error returned by
// UpiLitePiCreationRequest.Validate if the designated constraints aren't met.
type UpiLitePiCreationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiLitePiCreationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiLitePiCreationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiLitePiCreationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiLitePiCreationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiLitePiCreationRequestValidationError) ErrorName() string {
	return "UpiLitePiCreationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpiLitePiCreationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiLitePiCreationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiLitePiCreationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiLitePiCreationRequestValidationError{}

// Validate checks the field values on UpiLitePiCreationResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiLitePiCreationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiLitePiCreationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiLitePiCreationResponseMultiError, or nil if none found.
func (m *UpiLitePiCreationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiLitePiCreationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiLitePiCreationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiLitePiCreationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiLitePiCreationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiLitePiCreationResponseMultiError(errors)
	}

	return nil
}

// UpiLitePiCreationResponseMultiError is an error wrapping multiple validation
// errors returned by UpiLitePiCreationResponse.ValidateAll() if the
// designated constraints aren't met.
type UpiLitePiCreationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiLitePiCreationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiLitePiCreationResponseMultiError) AllErrors() []error { return m }

// UpiLitePiCreationResponseValidationError is the validation error returned by
// UpiLitePiCreationResponse.Validate if the designated constraints aren't met.
type UpiLitePiCreationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiLitePiCreationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiLitePiCreationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiLitePiCreationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiLitePiCreationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiLitePiCreationResponseValidationError) ErrorName() string {
	return "UpiLitePiCreationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpiLitePiCreationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiLitePiCreationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiLitePiCreationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiLitePiCreationResponseValidationError{}

// Validate checks the field values on UpiLitePiDeactivationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiLitePiDeactivationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiLitePiDeactivationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiLitePiDeactivationRequestMultiError, or nil if none found.
func (m *UpiLitePiDeactivationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiLitePiDeactivationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiLitePiDeactivationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiLitePiDeactivationRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiLitePiDeactivationRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Lrn

	if len(errors) > 0 {
		return UpiLitePiDeactivationRequestMultiError(errors)
	}

	return nil
}

// UpiLitePiDeactivationRequestMultiError is an error wrapping multiple
// validation errors returned by UpiLitePiDeactivationRequest.ValidateAll() if
// the designated constraints aren't met.
type UpiLitePiDeactivationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiLitePiDeactivationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiLitePiDeactivationRequestMultiError) AllErrors() []error { return m }

// UpiLitePiDeactivationRequestValidationError is the validation error returned
// by UpiLitePiDeactivationRequest.Validate if the designated constraints
// aren't met.
type UpiLitePiDeactivationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiLitePiDeactivationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiLitePiDeactivationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiLitePiDeactivationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiLitePiDeactivationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiLitePiDeactivationRequestValidationError) ErrorName() string {
	return "UpiLitePiDeactivationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpiLitePiDeactivationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiLitePiDeactivationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiLitePiDeactivationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiLitePiDeactivationRequestValidationError{}

// Validate checks the field values on UpiLitePiDeactivationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiLitePiDeactivationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiLitePiDeactivationResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpiLitePiDeactivationResponseMultiError, or nil if none found.
func (m *UpiLitePiDeactivationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiLitePiDeactivationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiLitePiDeactivationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiLitePiDeactivationResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiLitePiDeactivationResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiLitePiDeactivationResponseMultiError(errors)
	}

	return nil
}

// UpiLitePiDeactivationResponseMultiError is an error wrapping multiple
// validation errors returned by UpiLitePiDeactivationResponse.ValidateAll()
// if the designated constraints aren't met.
type UpiLitePiDeactivationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiLitePiDeactivationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiLitePiDeactivationResponseMultiError) AllErrors() []error { return m }

// UpiLitePiDeactivationResponseValidationError is the validation error
// returned by UpiLitePiDeactivationResponse.Validate if the designated
// constraints aren't met.
type UpiLitePiDeactivationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiLitePiDeactivationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiLitePiDeactivationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiLitePiDeactivationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiLitePiDeactivationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiLitePiDeactivationResponseValidationError) ErrorName() string {
	return "UpiLitePiDeactivationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpiLitePiDeactivationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiLitePiDeactivationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiLitePiDeactivationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiLitePiDeactivationResponseValidationError{}

// Validate checks the field values on EnquireOrderStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnquireOrderStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireOrderStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnquireOrderStatusRequestMultiError, or nil if none found.
func (m *EnquireOrderStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireOrderStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireOrderStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireOrderStatusRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireOrderStatusRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	if len(errors) > 0 {
		return EnquireOrderStatusRequestMultiError(errors)
	}

	return nil
}

// EnquireOrderStatusRequestMultiError is an error wrapping multiple validation
// errors returned by EnquireOrderStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type EnquireOrderStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireOrderStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireOrderStatusRequestMultiError) AllErrors() []error { return m }

// EnquireOrderStatusRequestValidationError is the validation error returned by
// EnquireOrderStatusRequest.Validate if the designated constraints aren't met.
type EnquireOrderStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireOrderStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireOrderStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireOrderStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireOrderStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireOrderStatusRequestValidationError) ErrorName() string {
	return "EnquireOrderStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireOrderStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireOrderStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireOrderStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireOrderStatusRequestValidationError{}

// Validate checks the field values on EnquireOrderStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EnquireOrderStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnquireOrderStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EnquireOrderStatusResponseMultiError, or nil if none found.
func (m *EnquireOrderStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EnquireOrderStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EnquireOrderStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EnquireOrderStatusResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EnquireOrderStatusResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EnquireOrderStatusResponseMultiError(errors)
	}

	return nil
}

// EnquireOrderStatusResponseMultiError is an error wrapping multiple
// validation errors returned by EnquireOrderStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type EnquireOrderStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnquireOrderStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnquireOrderStatusResponseMultiError) AllErrors() []error { return m }

// EnquireOrderStatusResponseValidationError is the validation error returned
// by EnquireOrderStatusResponse.Validate if the designated constraints aren't met.
type EnquireOrderStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnquireOrderStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnquireOrderStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnquireOrderStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnquireOrderStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnquireOrderStatusResponseValidationError) ErrorName() string {
	return "EnquireOrderStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EnquireOrderStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnquireOrderStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnquireOrderStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnquireOrderStatusResponseValidationError{}

// Validate checks the field values on CreateUpiLitePaymentOrderRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateUpiLitePaymentOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUpiLitePaymentOrderRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateUpiLitePaymentOrderRequestMultiError, or nil if none found.
func (m *CreateUpiLitePaymentOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUpiLitePaymentOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUpiLitePaymentOrderRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUpiLitePaymentOrderRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUpiLitePaymentOrderRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return CreateUpiLitePaymentOrderRequestMultiError(errors)
	}

	return nil
}

// CreateUpiLitePaymentOrderRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateUpiLitePaymentOrderRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateUpiLitePaymentOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUpiLitePaymentOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUpiLitePaymentOrderRequestMultiError) AllErrors() []error { return m }

// CreateUpiLitePaymentOrderRequestValidationError is the validation error
// returned by CreateUpiLitePaymentOrderRequest.Validate if the designated
// constraints aren't met.
type CreateUpiLitePaymentOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUpiLitePaymentOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUpiLitePaymentOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUpiLitePaymentOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUpiLitePaymentOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUpiLitePaymentOrderRequestValidationError) ErrorName() string {
	return "CreateUpiLitePaymentOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUpiLitePaymentOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUpiLitePaymentOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUpiLitePaymentOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUpiLitePaymentOrderRequestValidationError{}

// Validate checks the field values on CreateUpiLitePaymentOrderResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateUpiLitePaymentOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUpiLitePaymentOrderResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateUpiLitePaymentOrderResponseMultiError, or nil if none found.
func (m *CreateUpiLitePaymentOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUpiLitePaymentOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUpiLitePaymentOrderResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUpiLitePaymentOrderResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUpiLitePaymentOrderResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	if len(errors) > 0 {
		return CreateUpiLitePaymentOrderResponseMultiError(errors)
	}

	return nil
}

// CreateUpiLitePaymentOrderResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateUpiLitePaymentOrderResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateUpiLitePaymentOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUpiLitePaymentOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUpiLitePaymentOrderResponseMultiError) AllErrors() []error { return m }

// CreateUpiLitePaymentOrderResponseValidationError is the validation error
// returned by CreateUpiLitePaymentOrderResponse.Validate if the designated
// constraints aren't met.
type CreateUpiLitePaymentOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUpiLitePaymentOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUpiLitePaymentOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUpiLitePaymentOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUpiLitePaymentOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUpiLitePaymentOrderResponseValidationError) ErrorName() string {
	return "CreateUpiLitePaymentOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUpiLitePaymentOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUpiLitePaymentOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUpiLitePaymentOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUpiLitePaymentOrderResponseValidationError{}

// Validate checks the field values on SyncUpiLiteInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncUpiLiteInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncUpiLiteInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncUpiLiteInfoRequestMultiError, or nil if none found.
func (m *SyncUpiLiteInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncUpiLiteInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncUpiLiteInfoRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncUpiLiteInfoRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncUpiLiteInfoRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountId

	// no validation rules for SyncUpiLiteAction

	if len(errors) > 0 {
		return SyncUpiLiteInfoRequestMultiError(errors)
	}

	return nil
}

// SyncUpiLiteInfoRequestMultiError is an error wrapping multiple validation
// errors returned by SyncUpiLiteInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type SyncUpiLiteInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncUpiLiteInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncUpiLiteInfoRequestMultiError) AllErrors() []error { return m }

// SyncUpiLiteInfoRequestValidationError is the validation error returned by
// SyncUpiLiteInfoRequest.Validate if the designated constraints aren't met.
type SyncUpiLiteInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncUpiLiteInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncUpiLiteInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncUpiLiteInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncUpiLiteInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncUpiLiteInfoRequestValidationError) ErrorName() string {
	return "SyncUpiLiteInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncUpiLiteInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncUpiLiteInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncUpiLiteInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncUpiLiteInfoRequestValidationError{}

// Validate checks the field values on SyncUpiLiteInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncUpiLiteInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncUpiLiteInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncUpiLiteInfoResponseMultiError, or nil if none found.
func (m *SyncUpiLiteInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncUpiLiteInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SyncUpiLiteInfoResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SyncUpiLiteInfoResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SyncUpiLiteInfoResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SyncUpiLiteInfoResponseMultiError(errors)
	}

	return nil
}

// SyncUpiLiteInfoResponseMultiError is an error wrapping multiple validation
// errors returned by SyncUpiLiteInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type SyncUpiLiteInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncUpiLiteInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncUpiLiteInfoResponseMultiError) AllErrors() []error { return m }

// SyncUpiLiteInfoResponseValidationError is the validation error returned by
// SyncUpiLiteInfoResponse.Validate if the designated constraints aren't met.
type SyncUpiLiteInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncUpiLiteInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncUpiLiteInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncUpiLiteInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncUpiLiteInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncUpiLiteInfoResponseValidationError) ErrorName() string {
	return "SyncUpiLiteInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SyncUpiLiteInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncUpiLiteInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncUpiLiteInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncUpiLiteInfoResponseValidationError{}
