// protolint:disable MAX_LINE_LENGTH
// Defines proto messages linked to an UPI merchant.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/merchant.proto

package upi

import (
	accounts "github.com/epifi/gamma/api/accounts"
	enums "github.com/epifi/gamma/api/upi/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantGenre int32

const (
	MerchantGenre_MERCHANT_GENRE_UNSPECIFIED MerchantGenre = 0
	MerchantGenre_ONLINE                     MerchantGenre = 1
	MerchantGenre_OFFLINE                    MerchantGenre = 2
)

// Enum value maps for MerchantGenre.
var (
	MerchantGenre_name = map[int32]string{
		0: "MERCHANT_GENRE_UNSPECIFIED",
		1: "ONLINE",
		2: "OFFLINE",
	}
	MerchantGenre_value = map[string]int32{
		"MERCHANT_GENRE_UNSPECIFIED": 0,
		"ONLINE":                     1,
		"OFFLINE":                    2,
	}
)

func (x MerchantGenre) Enum() *MerchantGenre {
	p := new(MerchantGenre)
	*p = x
	return p
}

func (x MerchantGenre) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MerchantGenre) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_merchant_proto_enumTypes[0].Descriptor()
}

func (MerchantGenre) Type() protoreflect.EnumType {
	return &file_api_upi_merchant_proto_enumTypes[0]
}

func (x MerchantGenre) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MerchantGenre.Descriptor instead.
func (MerchantGenre) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_merchant_proto_rawDescGZIP(), []int{0}
}

type MerchantType int32

const (
	MerchantType_MERCHANT_TYPE_UNSPECIFIED MerchantType = 0
	MerchantType_SMALL                     MerchantType = 1
	MerchantType_LARGE                     MerchantType = 2
)

// Enum value maps for MerchantType.
var (
	MerchantType_name = map[int32]string{
		0: "MERCHANT_TYPE_UNSPECIFIED",
		1: "SMALL",
		2: "LARGE",
	}
	MerchantType_value = map[string]int32{
		"MERCHANT_TYPE_UNSPECIFIED": 0,
		"SMALL":                     1,
		"LARGE":                     2,
	}
)

func (x MerchantType) Enum() *MerchantType {
	p := new(MerchantType)
	*p = x
	return p
}

func (x MerchantType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MerchantType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_merchant_proto_enumTypes[1].Descriptor()
}

func (MerchantType) Type() protoreflect.EnumType {
	return &file_api_upi_merchant_proto_enumTypes[1]
}

func (x MerchantType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MerchantType.Descriptor instead.
func (MerchantType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_merchant_proto_rawDescGZIP(), []int{1}
}

type MerchantOnboardingType int32

const (
	MerchantOnboardingType_MERCHANT_ONBOARDING_TYPE_UNSPECIFIED MerchantOnboardingType = 0
	MerchantOnboardingType_BANK                                 MerchantOnboardingType = 1
	MerchantOnboardingType_AGGREGATOR                           MerchantOnboardingType = 2
	MerchantOnboardingType_NETWORK                              MerchantOnboardingType = 3
	MerchantOnboardingType_TPAP                                 MerchantOnboardingType = 4
)

// Enum value maps for MerchantOnboardingType.
var (
	MerchantOnboardingType_name = map[int32]string{
		0: "MERCHANT_ONBOARDING_TYPE_UNSPECIFIED",
		1: "BANK",
		2: "AGGREGATOR",
		3: "NETWORK",
		4: "TPAP",
	}
	MerchantOnboardingType_value = map[string]int32{
		"MERCHANT_ONBOARDING_TYPE_UNSPECIFIED": 0,
		"BANK":                                 1,
		"AGGREGATOR":                           2,
		"NETWORK":                              3,
		"TPAP":                                 4,
	}
)

func (x MerchantOnboardingType) Enum() *MerchantOnboardingType {
	p := new(MerchantOnboardingType)
	*p = x
	return p
}

func (x MerchantOnboardingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MerchantOnboardingType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_merchant_proto_enumTypes[2].Descriptor()
}

func (MerchantOnboardingType) Type() protoreflect.EnumType {
	return &file_api_upi_merchant_proto_enumTypes[2]
}

func (x MerchantOnboardingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MerchantOnboardingType.Descriptor instead.
func (MerchantOnboardingType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_merchant_proto_rawDescGZIP(), []int{2}
}

type MerchantOwnershipType int32

const (
	MerchantOwnershipType_MERCHANT_OWNERSHIP_UNSPECIFIED MerchantOwnershipType = 0
	MerchantOwnershipType_PROPRIETARY                    MerchantOwnershipType = 1
	MerchantOwnershipType_PARTNERSHIP                    MerchantOwnershipType = 2
	MerchantOwnershipType_PRIVATE                        MerchantOwnershipType = 3
	MerchantOwnershipType_PUBLIC                         MerchantOwnershipType = 4
	MerchantOwnershipType_OTHERS                         MerchantOwnershipType = 5
)

// Enum value maps for MerchantOwnershipType.
var (
	MerchantOwnershipType_name = map[int32]string{
		0: "MERCHANT_OWNERSHIP_UNSPECIFIED",
		1: "PROPRIETARY",
		2: "PARTNERSHIP",
		3: "PRIVATE",
		4: "PUBLIC",
		5: "OTHERS",
	}
	MerchantOwnershipType_value = map[string]int32{
		"MERCHANT_OWNERSHIP_UNSPECIFIED": 0,
		"PROPRIETARY":                    1,
		"PARTNERSHIP":                    2,
		"PRIVATE":                        3,
		"PUBLIC":                         4,
		"OTHERS":                         5,
	}
)

func (x MerchantOwnershipType) Enum() *MerchantOwnershipType {
	p := new(MerchantOwnershipType)
	*p = x
	return p
}

func (x MerchantOwnershipType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MerchantOwnershipType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_merchant_proto_enumTypes[3].Descriptor()
}

func (MerchantOwnershipType) Type() protoreflect.EnumType {
	return &file_api_upi_merchant_proto_enumTypes[3]
}

func (x MerchantOwnershipType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MerchantOwnershipType.Descriptor instead.
func (MerchantOwnershipType) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_merchant_proto_rawDescGZIP(), []int{3}
}

// Enum to specify the fields that can be updated in VpaMerchantInfo
// Currently use-case is to only update restricted_account_type_details only. It can be extended in the future.
type VpaMerchantInfoUpdateMask int32

const (
	VpaMerchantInfoUpdateMask_VPA_MERCHANT_INFO_UPDATE_MASK_UNSPECIFIED                     VpaMerchantInfoUpdateMask = 0
	VpaMerchantInfoUpdateMask_VPA_MERCHANT_INFO_UPDATE_MASK_RESTRICTED_ACCOUNT_TYPE_DETAILS VpaMerchantInfoUpdateMask = 1
)

// Enum value maps for VpaMerchantInfoUpdateMask.
var (
	VpaMerchantInfoUpdateMask_name = map[int32]string{
		0: "VPA_MERCHANT_INFO_UPDATE_MASK_UNSPECIFIED",
		1: "VPA_MERCHANT_INFO_UPDATE_MASK_RESTRICTED_ACCOUNT_TYPE_DETAILS",
	}
	VpaMerchantInfoUpdateMask_value = map[string]int32{
		"VPA_MERCHANT_INFO_UPDATE_MASK_UNSPECIFIED":                     0,
		"VPA_MERCHANT_INFO_UPDATE_MASK_RESTRICTED_ACCOUNT_TYPE_DETAILS": 1,
	}
)

func (x VpaMerchantInfoUpdateMask) Enum() *VpaMerchantInfoUpdateMask {
	p := new(VpaMerchantInfoUpdateMask)
	*p = x
	return p
}

func (x VpaMerchantInfoUpdateMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VpaMerchantInfoUpdateMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_merchant_proto_enumTypes[4].Descriptor()
}

func (VpaMerchantInfoUpdateMask) Type() protoreflect.EnumType {
	return &file_api_upi_merchant_proto_enumTypes[4]
}

func (x VpaMerchantInfoUpdateMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VpaMerchantInfoUpdateMask.Descriptor instead.
func (VpaMerchantInfoUpdateMask) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_merchant_proto_rawDescGZIP(), []int{4}
}

type MerchantDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// merchant id
	MerchantId string `protobuf:"bytes,1,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	// merchant store id
	MerchantStoreId string `protobuf:"bytes,2,opt,name=merchant_store_id,json=merchantStoreId,proto3" json:"merchant_store_id,omitempty"`
	// merchant terminal id
	MerchantTerminalId string `protobuf:"bytes,3,opt,name=merchant_terminal_id,json=merchantTerminalId,proto3" json:"merchant_terminal_id,omitempty"`
	// genre of the merchant eg. Online, offline
	Genre MerchantGenre `protobuf:"varint,4,opt,name=genre,proto3,enum=upi.MerchantGenre" json:"genre,omitempty"`
	// merchant brand name e.g. McDonald's
	BrandName string `protobuf:"bytes,5,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	// legal registered name for the merchant
	LegalName string `protobuf:"bytes,6,opt,name=legal_name,json=legalName,proto3" json:"legal_name,omitempty"`
	// franchise name correspond to a given merchant brand
	FranchiseName string `protobuf:"bytes,7,opt,name=franchise_name,json=franchiseName,proto3" json:"franchise_name,omitempty"`
	// merchant ownership type
	OwnershipType  MerchantOwnershipType  `protobuf:"varint,8,opt,name=ownership_type,json=ownershipType,proto3,enum=upi.MerchantOwnershipType" json:"ownership_type,omitempty"`
	OnboardingType MerchantOnboardingType `protobuf:"varint,9,opt,name=onboarding_type,json=onboardingType,proto3,enum=upi.MerchantOnboardingType" json:"onboarding_type,omitempty"`
	MerchantType   MerchantType           `protobuf:"varint,10,opt,name=merchant_type,json=merchantType,proto3,enum=upi.MerchantType" json:"merchant_type,omitempty"`
	// Can be empty even if VPA belongs to a merchant
	// In such cases, MCC will represent the category of the merchant
	SubCode string `protobuf:"bytes,11,opt,name=sub_code,json=subCode,proto3" json:"sub_code,omitempty"`
	// Merchant category code
	// If VPAs are acquired through aggregators, MCC can represent aggregator's MCC
	// In such cases(i.e., mcc and sub_code are different), sub_code will represent the actual MCC of the VPA
	Mcc string `protobuf:"bytes,12,opt,name=mcc,proto3" json:"mcc,omitempty"`
	// Registration id of the merchant
	RegId string `protobuf:"bytes,13,opt,name=reg_id,json=regId,proto3" json:"reg_id,omitempty"`
	// pin code of the merchant location
	PinCode string `protobuf:"bytes,14,opt,name=pin_code,json=pinCode,proto3" json:"pin_code,omitempty"`
	// tier category of the merchant
	Tier string `protobuf:"bytes,15,opt,name=tier,proto3" json:"tier,omitempty"`
	// location of the merchant
	MerchantLocation string `protobuf:"bytes,16,opt,name=merchant_location,json=merchantLocation,proto3" json:"merchant_location,omitempty"`
	// id of the institute connected to the merchant
	MerchantInstituteCode string `protobuf:"bytes,17,opt,name=merchant_institute_code,json=merchantInstituteCode,proto3" json:"merchant_institute_code,omitempty"`
}

func (x *MerchantDetails) Reset() {
	*x = MerchantDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_merchant_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantDetails) ProtoMessage() {}

func (x *MerchantDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_merchant_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantDetails.ProtoReflect.Descriptor instead.
func (*MerchantDetails) Descriptor() ([]byte, []int) {
	return file_api_upi_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantDetails) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *MerchantDetails) GetMerchantStoreId() string {
	if x != nil {
		return x.MerchantStoreId
	}
	return ""
}

func (x *MerchantDetails) GetMerchantTerminalId() string {
	if x != nil {
		return x.MerchantTerminalId
	}
	return ""
}

func (x *MerchantDetails) GetGenre() MerchantGenre {
	if x != nil {
		return x.Genre
	}
	return MerchantGenre_MERCHANT_GENRE_UNSPECIFIED
}

func (x *MerchantDetails) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *MerchantDetails) GetLegalName() string {
	if x != nil {
		return x.LegalName
	}
	return ""
}

func (x *MerchantDetails) GetFranchiseName() string {
	if x != nil {
		return x.FranchiseName
	}
	return ""
}

func (x *MerchantDetails) GetOwnershipType() MerchantOwnershipType {
	if x != nil {
		return x.OwnershipType
	}
	return MerchantOwnershipType_MERCHANT_OWNERSHIP_UNSPECIFIED
}

func (x *MerchantDetails) GetOnboardingType() MerchantOnboardingType {
	if x != nil {
		return x.OnboardingType
	}
	return MerchantOnboardingType_MERCHANT_ONBOARDING_TYPE_UNSPECIFIED
}

func (x *MerchantDetails) GetMerchantType() MerchantType {
	if x != nil {
		return x.MerchantType
	}
	return MerchantType_MERCHANT_TYPE_UNSPECIFIED
}

func (x *MerchantDetails) GetSubCode() string {
	if x != nil {
		return x.SubCode
	}
	return ""
}

func (x *MerchantDetails) GetMcc() string {
	if x != nil {
		return x.Mcc
	}
	return ""
}

func (x *MerchantDetails) GetRegId() string {
	if x != nil {
		return x.RegId
	}
	return ""
}

func (x *MerchantDetails) GetPinCode() string {
	if x != nil {
		return x.PinCode
	}
	return ""
}

func (x *MerchantDetails) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *MerchantDetails) GetMerchantLocation() string {
	if x != nil {
		return x.MerchantLocation
	}
	return ""
}

func (x *MerchantDetails) GetMerchantInstituteCode() string {
	if x != nil {
		return x.MerchantInstituteCode
	}
	return ""
}

// DB model proto
// Merchant Info that is registered with NPCI against a VPA
//
//go:generate gen_sql -types=RestrictedAccountTypeDetails
type VpaMerchantInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Vpa string `protobuf:"bytes,2,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// merchant vpa details JSONB
	MerchantDetails *MerchantDetails `protobuf:"bytes,3,opt,name=merchant_details,json=merchantDetails,proto3" json:"merchant_details,omitempty"`
	// standard timestamp fields
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// restricted account type details
	// 1. feature_supported_values: storing plain string (raw data) of feature supported values which we receive from the validate Address Vendor Call
	// 2. restricted_account_types: List of account types which are restricted to initiate payment for particular merchant.
	// 3. upi_account_restriction_exemptions: List of exemptions from UPI account restrictions applicable for the merchant under certain conditions.
	RestrictedAccountTypeDetails *RestrictedAccountTypeDetails `protobuf:"bytes,7,opt,name=restricted_account_type_details,json=restrictedAccountTypeDetails,proto3" json:"restricted_account_type_details,omitempty"`
}

func (x *VpaMerchantInfo) Reset() {
	*x = VpaMerchantInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_merchant_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VpaMerchantInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VpaMerchantInfo) ProtoMessage() {}

func (x *VpaMerchantInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_merchant_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VpaMerchantInfo.ProtoReflect.Descriptor instead.
func (*VpaMerchantInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *VpaMerchantInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VpaMerchantInfo) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *VpaMerchantInfo) GetMerchantDetails() *MerchantDetails {
	if x != nil {
		return x.MerchantDetails
	}
	return nil
}

func (x *VpaMerchantInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VpaMerchantInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *VpaMerchantInfo) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *VpaMerchantInfo) GetRestrictedAccountTypeDetails() *RestrictedAccountTypeDetails {
	if x != nil {
		return x.RestrictedAccountTypeDetails
	}
	return nil
}

type RestrictedAccountTypeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// storing plain string (raw data) of feature supported values which we receive from the validate Address Vendor Call
	FeatureSupportedValues string `protobuf:"bytes,1,opt,name=feature_supported_values,json=featureSupportedValues,proto3" json:"feature_supported_values,omitempty"`
	// List of account types which are restricted to initiate payment for particular merchant.
	// We deduce this from the feature_supported_values using map which we store at our end (reference from doc - https://drive.google.com/drive/folders/1WsUVRBTCxCu55ktPbGpKqYx0q25eLUzk).
	// Note: If list is empty, then we assume that all Account Types are Accepted.
	RestrictedAccountTypes []accounts.Type `protobuf:"varint,2,rep,packed,name=restricted_account_types,json=restrictedAccountTypes,proto3,enum=accounts.Type" json:"restricted_account_types,omitempty"`
	// List of exemptions from UPI account restrictions applicable to the merchant.
	// These exemptions allow specific payment methods (e.g., RuPay Credit Card, PPI, Credit Line) under certain conditions.
	// For e.g. Exemptions applicable for small and offline merchants by allowing payments upto INR 2000 for different restricted account-types.
	// This field should always be used in conjunction with restricted_account_types.
	UpiAccountRestrictionExemptions []enums.UpiAccountRestrictionExemptionType `protobuf:"varint,3,rep,packed,name=upi_account_restriction_exemptions,json=upiAccountRestrictionExemptions,proto3,enum=upi.enums.UpiAccountRestrictionExemptionType" json:"upi_account_restriction_exemptions,omitempty"`
}

func (x *RestrictedAccountTypeDetails) Reset() {
	*x = RestrictedAccountTypeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_merchant_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RestrictedAccountTypeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestrictedAccountTypeDetails) ProtoMessage() {}

func (x *RestrictedAccountTypeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_merchant_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestrictedAccountTypeDetails.ProtoReflect.Descriptor instead.
func (*RestrictedAccountTypeDetails) Descriptor() ([]byte, []int) {
	return file_api_upi_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *RestrictedAccountTypeDetails) GetFeatureSupportedValues() string {
	if x != nil {
		return x.FeatureSupportedValues
	}
	return ""
}

func (x *RestrictedAccountTypeDetails) GetRestrictedAccountTypes() []accounts.Type {
	if x != nil {
		return x.RestrictedAccountTypes
	}
	return nil
}

func (x *RestrictedAccountTypeDetails) GetUpiAccountRestrictionExemptions() []enums.UpiAccountRestrictionExemptionType {
	if x != nil {
		return x.UpiAccountRestrictionExemptions
	}
	return nil
}

var File_api_upi_merchant_proto protoreflect.FileDescriptor

var file_api_upi_merchant_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x75, 0x70, 0x69, 0x1a, 0x1f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3a,
	0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x70,
	0x69, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x05, 0x0a, 0x0f,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x05, 0x67, 0x65, 0x6e, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x72,
	0x65, 0x52, 0x05, 0x67, 0x65, 0x6e, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x65, 0x67, 0x61, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x65, 0x67,
	0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x69, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x66, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x69, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a,
	0x0e, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0d, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x44, 0x0a, 0x0f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e,
	0x75, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x63, 0x63,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x63, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x67,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x69, 0x65,
	0x72, 0x12, 0x2b, 0x0a, 0x11, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36,
	0x0a, 0x17, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x69,
	0x74, 0x75, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75,
	0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x8f, 0x03, 0x0a, 0x0f, 0x56, 0x70, 0x61, 0x4d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x3f, 0x0a, 0x10,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x68,
	0x0a, 0x1f, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x52, 0x65,
	0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x1c, 0x72, 0x65, 0x73, 0x74,
	0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x9e, 0x02, 0x0a, 0x1c, 0x52, 0x65, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x18, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x16, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x65,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x7a, 0x0a,
	0x22, 0x75, 0x70, 0x69, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x55, 0x70, 0x69, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x65, 0x6d, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x1f, 0x75, 0x70, 0x69, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x78, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2a, 0x48, 0x0a, 0x0d, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x47, 0x65, 0x6e, 0x72, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x4d, 0x45,
	0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f, 0x47, 0x45, 0x4e, 0x52, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x4e,
	0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e,
	0x45, 0x10, 0x02, 0x2a, 0x43, 0x0a, 0x0c, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x4d, 0x41, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x09, 0x0a,
	0x05, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x10, 0x02, 0x2a, 0x73, 0x0a, 0x16, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x28, 0x0a, 0x24, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04,
	0x42, 0x41, 0x4e, 0x4b, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47,
	0x41, 0x54, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x4b, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x50, 0x41, 0x50, 0x10, 0x04, 0x2a, 0x82, 0x01,
	0x0a, 0x15, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x4d, 0x45, 0x52, 0x43, 0x48,
	0x41, 0x4e, 0x54, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x50,
	0x52, 0x4f, 0x50, 0x52, 0x49, 0x45, 0x54, 0x41, 0x52, 0x59, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b,
	0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x53, 0x48, 0x49, 0x50, 0x10, 0x02, 0x12, 0x0b, 0x0a,
	0x07, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x55,
	0x42, 0x4c, 0x49, 0x43, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53,
	0x10, 0x05, 0x2a, 0x8d, 0x01, 0x0a, 0x19, 0x56, 0x70, 0x61, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x2d, 0x0a, 0x29, 0x56, 0x50, 0x41, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x41, 0x0a, 0x3d, 0x56, 0x50, 0x41, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x10, 0x01, 0x42, 0x40, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x70, 0x69, 0x5a, 0x1e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x75, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_merchant_proto_rawDescOnce sync.Once
	file_api_upi_merchant_proto_rawDescData = file_api_upi_merchant_proto_rawDesc
)

func file_api_upi_merchant_proto_rawDescGZIP() []byte {
	file_api_upi_merchant_proto_rawDescOnce.Do(func() {
		file_api_upi_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_merchant_proto_rawDescData)
	})
	return file_api_upi_merchant_proto_rawDescData
}

var file_api_upi_merchant_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_upi_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_upi_merchant_proto_goTypes = []interface{}{
	(MerchantGenre)(0),                            // 0: upi.MerchantGenre
	(MerchantType)(0),                             // 1: upi.MerchantType
	(MerchantOnboardingType)(0),                   // 2: upi.MerchantOnboardingType
	(MerchantOwnershipType)(0),                    // 3: upi.MerchantOwnershipType
	(VpaMerchantInfoUpdateMask)(0),                // 4: upi.VpaMerchantInfoUpdateMask
	(*MerchantDetails)(nil),                       // 5: upi.MerchantDetails
	(*VpaMerchantInfo)(nil),                       // 6: upi.VpaMerchantInfo
	(*RestrictedAccountTypeDetails)(nil),          // 7: upi.RestrictedAccountTypeDetails
	(*timestamppb.Timestamp)(nil),                 // 8: google.protobuf.Timestamp
	(accounts.Type)(0),                            // 9: accounts.Type
	(enums.UpiAccountRestrictionExemptionType)(0), // 10: upi.enums.UpiAccountRestrictionExemptionType
}
var file_api_upi_merchant_proto_depIdxs = []int32{
	0,  // 0: upi.MerchantDetails.genre:type_name -> upi.MerchantGenre
	3,  // 1: upi.MerchantDetails.ownership_type:type_name -> upi.MerchantOwnershipType
	2,  // 2: upi.MerchantDetails.onboarding_type:type_name -> upi.MerchantOnboardingType
	1,  // 3: upi.MerchantDetails.merchant_type:type_name -> upi.MerchantType
	5,  // 4: upi.VpaMerchantInfo.merchant_details:type_name -> upi.MerchantDetails
	8,  // 5: upi.VpaMerchantInfo.created_at:type_name -> google.protobuf.Timestamp
	8,  // 6: upi.VpaMerchantInfo.updated_at:type_name -> google.protobuf.Timestamp
	8,  // 7: upi.VpaMerchantInfo.deleted_at:type_name -> google.protobuf.Timestamp
	7,  // 8: upi.VpaMerchantInfo.restricted_account_type_details:type_name -> upi.RestrictedAccountTypeDetails
	9,  // 9: upi.RestrictedAccountTypeDetails.restricted_account_types:type_name -> accounts.Type
	10, // 10: upi.RestrictedAccountTypeDetails.upi_account_restriction_exemptions:type_name -> upi.enums.UpiAccountRestrictionExemptionType
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_upi_merchant_proto_init() }
func file_api_upi_merchant_proto_init() {
	if File_api_upi_merchant_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_upi_merchant_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerchantDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_merchant_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VpaMerchantInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_merchant_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RestrictedAccountTypeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_merchant_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_merchant_proto_goTypes,
		DependencyIndexes: file_api_upi_merchant_proto_depIdxs,
		EnumInfos:         file_api_upi_merchant_proto_enumTypes,
		MessageInfos:      file_api_upi_merchant_proto_msgTypes,
	}.Build()
	File_api_upi_merchant_proto = out.File
	file_api_upi_merchant_proto_rawDesc = nil
	file_api_upi_merchant_proto_goTypes = nil
	file_api_upi_merchant_proto_depIdxs = nil
}
