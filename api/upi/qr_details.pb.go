// protolint:disable MAX_LINE_LENGTH
// Defines proto messages linked to a QR.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/upi/qr_details.proto

package upi

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Types of Medium through which QR can be created
type QrMedium int32

const (
	QrMedium_QR_MEDIUM_UNSPECIFIED       QrMedium = 0
	QrMedium_QR_MEDIUM_PICK_FROM_GALLERY QrMedium = 1
	QrMedium_QR_MEDIUM_APP               QrMedium = 2
	QrMedium_QR_MEDIUM_POS               QrMedium = 3
	QrMedium_QR_MEDIUM_PHYSICAL          QrMedium = 4
	QrMedium_QR_MEDIUM_ATM               QrMedium = 5
	QrMedium_QR_MEDIUM_WEB               QrMedium = 6
)

// Enum value maps for QrMedium.
var (
	QrMedium_name = map[int32]string{
		0: "QR_MEDIUM_UNSPECIFIED",
		1: "QR_MEDIUM_PICK_FROM_GALLERY",
		2: "QR_MEDIUM_APP",
		3: "QR_MEDIUM_POS",
		4: "QR_MEDIUM_PHYSICAL",
		5: "QR_MEDIUM_ATM",
		6: "QR_MEDIUM_WEB",
	}
	QrMedium_value = map[string]int32{
		"QR_MEDIUM_UNSPECIFIED":       0,
		"QR_MEDIUM_PICK_FROM_GALLERY": 1,
		"QR_MEDIUM_APP":               2,
		"QR_MEDIUM_POS":               3,
		"QR_MEDIUM_PHYSICAL":          4,
		"QR_MEDIUM_ATM":               5,
		"QR_MEDIUM_WEB":               6,
	}
)

func (x QrMedium) Enum() *QrMedium {
	p := new(QrMedium)
	*p = x
	return p
}

func (x QrMedium) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QrMedium) Descriptor() protoreflect.EnumDescriptor {
	return file_api_upi_qr_details_proto_enumTypes[0].Descriptor()
}

func (QrMedium) Type() protoreflect.EnumType {
	return &file_api_upi_qr_details_proto_enumTypes[0]
}

func (x QrMedium) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QrMedium.Descriptor instead.
func (QrMedium) EnumDescriptor() ([]byte, []int) {
	return file_api_upi_qr_details_proto_rawDescGZIP(), []int{0}
}

type QRDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Version of the QR
	QrVersion string `protobuf:"bytes,1,opt,name=qr_version,json=qrVersion,proto3" json:"qr_version,omitempty"`
	// timestamp when the qr was created (corresponds to ts in NPCI document)
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// medium through which qr was created
	QrMedium QrMedium `protobuf:"varint,3,opt,name=qr_medium,json=qrMedium,proto3,enum=upi.QrMedium" json:"qr_medium,omitempty"`
	// timestamp when the qr will expire (corresponds to expireTs in NPCI document)
	ExpireAt *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	// For future use in JSON format, details still not specified by NPCI
	Query string `protobuf:"bytes,5,opt,name=query,proto3" json:"query,omitempty"`
	// Todo:(Abhinit) Description to be added based on NPCI doc update
	VersionToken string `protobuf:"bytes,6,opt,name=version_token,json=versionToken,proto3" json:"version_token,omitempty"`
	Stan         string `protobuf:"bytes,7,opt,name=stan,proto3" json:"stan,omitempty"`
}

func (x *QRDetails) Reset() {
	*x = QRDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_qr_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QRDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QRDetails) ProtoMessage() {}

func (x *QRDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_qr_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QRDetails.ProtoReflect.Descriptor instead.
func (*QRDetails) Descriptor() ([]byte, []int) {
	return file_api_upi_qr_details_proto_rawDescGZIP(), []int{0}
}

func (x *QRDetails) GetQrVersion() string {
	if x != nil {
		return x.QrVersion
	}
	return ""
}

func (x *QRDetails) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *QRDetails) GetQrMedium() QrMedium {
	if x != nil {
		return x.QrMedium
	}
	return QrMedium_QR_MEDIUM_UNSPECIFIED
}

func (x *QRDetails) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

func (x *QRDetails) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *QRDetails) GetVersionToken() string {
	if x != nil {
		return x.VersionToken
	}
	return ""
}

func (x *QRDetails) GetStan() string {
	if x != nil {
		return x.Stan
	}
	return ""
}

// InternationalQrInfo - object store qrDetails and forex details in Redis as a key-val pair
type UpiInternationalQrInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QrDetails       *QRDetails   `protobuf:"bytes,1,opt,name=qr_details,json=qrDetails,proto3" json:"qr_details,omitempty"`
	ForexDetailList []*FxDetail  `protobuf:"bytes,2,rep,name=forex_detail_list,json=forexDetailList,proto3" json:"forex_detail_list,omitempty"`
	Institution     *Institution `protobuf:"bytes,3,opt,name=institution,proto3" json:"institution,omitempty"`
}

func (x *UpiInternationalQrInfo) Reset() {
	*x = UpiInternationalQrInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_upi_qr_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpiInternationalQrInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpiInternationalQrInfo) ProtoMessage() {}

func (x *UpiInternationalQrInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_upi_qr_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpiInternationalQrInfo.ProtoReflect.Descriptor instead.
func (*UpiInternationalQrInfo) Descriptor() ([]byte, []int) {
	return file_api_upi_qr_details_proto_rawDescGZIP(), []int{1}
}

func (x *UpiInternationalQrInfo) GetQrDetails() *QRDetails {
	if x != nil {
		return x.QrDetails
	}
	return nil
}

func (x *UpiInternationalQrInfo) GetForexDetailList() []*FxDetail {
	if x != nil {
		return x.ForexDetailList
	}
	return nil
}

func (x *UpiInternationalQrInfo) GetInstitution() *Institution {
	if x != nil {
		return x.Institution
	}
	return nil
}

var File_api_upi_qr_details_proto protoreflect.FileDescriptor

var file_api_upi_qr_details_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x71, 0x72, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x75, 0x70, 0x69, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x02, 0x0a, 0x09, 0x51, 0x52, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x72, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x72, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x2a, 0x0a, 0x09, 0x71, 0x72, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x51, 0x72, 0x4d, 0x65, 0x64, 0x69,
	0x75, 0x6d, 0x52, 0x08, 0x71, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x12, 0x37, 0x0a, 0x09,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x41, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x61, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x73, 0x74, 0x61, 0x6e, 0x22, 0xb6, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x69, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x51, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x2d, 0x0a, 0x0a, 0x71, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x75, 0x70, 0x69, 0x2e, 0x51, 0x52, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x09, 0x71, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39,
	0x0a, 0x11, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x70, 0x69, 0x2e,
	0x46, 0x78, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0f, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x0b, 0x69, 0x6e, 0x73,
	0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x75, 0x70, 0x69, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x69, 0x74, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0xaa, 0x01,
	0x0a, 0x08, 0x51, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x52,
	0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x51, 0x52, 0x5f, 0x4d, 0x45, 0x44, 0x49,
	0x55, 0x4d, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x47, 0x41, 0x4c,
	0x4c, 0x45, 0x52, 0x59, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x51, 0x52, 0x5f, 0x4d, 0x45, 0x44,
	0x49, 0x55, 0x4d, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x51, 0x52, 0x5f,
	0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x5f, 0x50, 0x4f, 0x53, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12,
	0x51, 0x52, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43,
	0x41, 0x4c, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x51, 0x52, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55,
	0x4d, 0x5f, 0x41, 0x54, 0x4d, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x51, 0x52, 0x5f, 0x4d, 0x45,
	0x44, 0x49, 0x55, 0x4d, 0x5f, 0x57, 0x45, 0x42, 0x10, 0x06, 0x42, 0x40, 0x0a, 0x1e, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x70, 0x69, 0x5a, 0x1e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_upi_qr_details_proto_rawDescOnce sync.Once
	file_api_upi_qr_details_proto_rawDescData = file_api_upi_qr_details_proto_rawDesc
)

func file_api_upi_qr_details_proto_rawDescGZIP() []byte {
	file_api_upi_qr_details_proto_rawDescOnce.Do(func() {
		file_api_upi_qr_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_upi_qr_details_proto_rawDescData)
	})
	return file_api_upi_qr_details_proto_rawDescData
}

var file_api_upi_qr_details_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_upi_qr_details_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_upi_qr_details_proto_goTypes = []interface{}{
	(QrMedium)(0),                  // 0: upi.QrMedium
	(*QRDetails)(nil),              // 1: upi.QRDetails
	(*UpiInternationalQrInfo)(nil), // 2: upi.UpiInternationalQrInfo
	(*timestamppb.Timestamp)(nil),  // 3: google.protobuf.Timestamp
	(*FxDetail)(nil),               // 4: upi.FxDetail
	(*Institution)(nil),            // 5: upi.Institution
}
var file_api_upi_qr_details_proto_depIdxs = []int32{
	3, // 0: upi.QRDetails.created_at:type_name -> google.protobuf.Timestamp
	0, // 1: upi.QRDetails.qr_medium:type_name -> upi.QrMedium
	3, // 2: upi.QRDetails.expire_at:type_name -> google.protobuf.Timestamp
	1, // 3: upi.UpiInternationalQrInfo.qr_details:type_name -> upi.QRDetails
	4, // 4: upi.UpiInternationalQrInfo.forex_detail_list:type_name -> upi.FxDetail
	5, // 5: upi.UpiInternationalQrInfo.institution:type_name -> upi.Institution
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_upi_qr_details_proto_init() }
func file_api_upi_qr_details_proto_init() {
	if File_api_upi_qr_details_proto != nil {
		return
	}
	file_api_upi_customer_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_upi_qr_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QRDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_upi_qr_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpiInternationalQrInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_upi_qr_details_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_upi_qr_details_proto_goTypes,
		DependencyIndexes: file_api_upi_qr_details_proto_depIdxs,
		EnumInfos:         file_api_upi_qr_details_proto_enumTypes,
		MessageInfos:      file_api_upi_qr_details_proto_msgTypes,
	}.Build()
	File_api_upi_qr_details_proto = out.File
	file_api_upi_qr_details_proto_rawDesc = nil
	file_api_upi_qr_details_proto_goTypes = nil
	file_api_upi_qr_details_proto_depIdxs = nil
}
