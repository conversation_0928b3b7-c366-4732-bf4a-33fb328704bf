package recurringpayment

func (r RecurringPaymentState) IsAnyActionInProgress() bool {
	return r == RecurringPaymentState_CREATION_QUEUED || r == RecurringPaymentState_MODIFY_QUEUED ||
		r == RecurringPaymentState_REVOKE_QUEUED || r == RecurringPaymentState_CREATION_INITIATED ||
		r == RecurringPaymentState_MODIFY_INITIATED || r == RecurringPaymentState_REVOKE_INITIATED ||
		r == RecurringPaymentState_CREATION_AUTHORISED || r == RecurringPaymentState_MODIFY_AUTHORISED ||
		r == RecurringPaymentState_REVOKE_AUTHORISED
}

func (r RecurringPaymentState) IsModificationInProgress() bool {
	return r == RecurringPaymentState_MODIFY_QUEUED || r == RecurringPaymentState_MODIFY_INITIATED ||
		r == RecurringPaymentState_MODIFY_AUTHORISED
}

func (r RecurringPaymentState) IsCreationInProgress() bool {
	return r == RecurringPaymentState_CREATION_QUEUED || r == RecurringPaymentState_CREATION_INITIATED ||
		r == RecurringPaymentState_CREATION_AUTHORISED
}

func (r RecurringPaymentState) IsRevokeInProgress() bool {
	return r == RecurringPaymentState_REVOKE_QUEUED || r == RecurringPaymentState_REVOKE_AUTHORISED ||
		r == RecurringPaymentState_REVOKE_INITIATED
}
