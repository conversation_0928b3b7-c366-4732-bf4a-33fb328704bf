// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendornotification/openbanking/payment/federal/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	federal "github.com/epifi/gamma/api/vendornotification/openbanking/payment/federal"
	federal0 "github.com/epifi/gamma/api/vendors/federal"
	addFunds "github.com/epifi/gamma/api/vendors/federal/payment/addFunds"
	b2c "github.com/epifi/gamma/api/vendors/federal/payment/b2c"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockPaymentClient is a mock of PaymentClient interface.
type MockPaymentClient struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentClientMockRecorder
}

// MockPaymentClientMockRecorder is the mock recorder for MockPaymentClient.
type MockPaymentClientMockRecorder struct {
	mock *MockPaymentClient
}

// NewMockPaymentClient creates a new mock instance.
func NewMockPaymentClient(ctrl *gomock.Controller) *MockPaymentClient {
	mock := &MockPaymentClient{ctrl: ctrl}
	mock.recorder = &MockPaymentClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentClient) EXPECT() *MockPaymentClientMockRecorder {
	return m.recorder
}

// ProcessAddFundsCallbackEvent mocks base method.
func (m *MockPaymentClient) ProcessAddFundsCallbackEvent(ctx context.Context, in *addFunds.AddFundsCallbackRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAddFundsCallbackEvent", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAddFundsCallbackEvent indicates an expected call of ProcessAddFundsCallbackEvent.
func (mr *MockPaymentClientMockRecorder) ProcessAddFundsCallbackEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAddFundsCallbackEvent", reflect.TypeOf((*MockPaymentClient)(nil).ProcessAddFundsCallbackEvent), varargs...)
}

// ProcessInboundTxn mocks base method.
func (m *MockPaymentClient) ProcessInboundTxn(ctx context.Context, in *federal0.ProcessInboundTxnRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessInboundTxn", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessInboundTxn indicates an expected call of ProcessInboundTxn.
func (mr *MockPaymentClientMockRecorder) ProcessInboundTxn(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessInboundTxn", reflect.TypeOf((*MockPaymentClient)(nil).ProcessInboundTxn), varargs...)
}

// ProcessInboundTxnV1 mocks base method.
func (m *MockPaymentClient) ProcessInboundTxnV1(ctx context.Context, in *federal0.ProcessInboundTxnRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessInboundTxnV1", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessInboundTxnV1 indicates an expected call of ProcessInboundTxnV1.
func (mr *MockPaymentClientMockRecorder) ProcessInboundTxnV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessInboundTxnV1", reflect.TypeOf((*MockPaymentClient)(nil).ProcessInboundTxnV1), varargs...)
}

// ProcessUpiEvent mocks base method.
func (m *MockPaymentClient) ProcessUpiEvent(ctx context.Context, in *federal.ProcessUpiEventRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessUpiEvent", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessUpiEvent indicates an expected call of ProcessUpiEvent.
func (mr *MockPaymentClientMockRecorder) ProcessUpiEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessUpiEvent", reflect.TypeOf((*MockPaymentClient)(nil).ProcessUpiEvent), varargs...)
}

// UpdateB2CTransaction mocks base method.
func (m *MockPaymentClient) UpdateB2CTransaction(ctx context.Context, in *b2c.UpdateB2CTransactionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateB2CTransaction", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateB2CTransaction indicates an expected call of UpdateB2CTransaction.
func (mr *MockPaymentClientMockRecorder) UpdateB2CTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateB2CTransaction", reflect.TypeOf((*MockPaymentClient)(nil).UpdateB2CTransaction), varargs...)
}

// UpdateTransaction mocks base method.
func (m *MockPaymentClient) UpdateTransaction(ctx context.Context, in *federal.UpdateTransactionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTransaction", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTransaction indicates an expected call of UpdateTransaction.
func (mr *MockPaymentClientMockRecorder) UpdateTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransaction", reflect.TypeOf((*MockPaymentClient)(nil).UpdateTransaction), varargs...)
}

// MockPaymentServer is a mock of PaymentServer interface.
type MockPaymentServer struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentServerMockRecorder
}

// MockPaymentServerMockRecorder is the mock recorder for MockPaymentServer.
type MockPaymentServerMockRecorder struct {
	mock *MockPaymentServer
}

// NewMockPaymentServer creates a new mock instance.
func NewMockPaymentServer(ctrl *gomock.Controller) *MockPaymentServer {
	mock := &MockPaymentServer{ctrl: ctrl}
	mock.recorder = &MockPaymentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentServer) EXPECT() *MockPaymentServerMockRecorder {
	return m.recorder
}

// ProcessAddFundsCallbackEvent mocks base method.
func (m *MockPaymentServer) ProcessAddFundsCallbackEvent(arg0 context.Context, arg1 *addFunds.AddFundsCallbackRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAddFundsCallbackEvent", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAddFundsCallbackEvent indicates an expected call of ProcessAddFundsCallbackEvent.
func (mr *MockPaymentServerMockRecorder) ProcessAddFundsCallbackEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAddFundsCallbackEvent", reflect.TypeOf((*MockPaymentServer)(nil).ProcessAddFundsCallbackEvent), arg0, arg1)
}

// ProcessInboundTxn mocks base method.
func (m *MockPaymentServer) ProcessInboundTxn(arg0 context.Context, arg1 *federal0.ProcessInboundTxnRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessInboundTxn", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessInboundTxn indicates an expected call of ProcessInboundTxn.
func (mr *MockPaymentServerMockRecorder) ProcessInboundTxn(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessInboundTxn", reflect.TypeOf((*MockPaymentServer)(nil).ProcessInboundTxn), arg0, arg1)
}

// ProcessInboundTxnV1 mocks base method.
func (m *MockPaymentServer) ProcessInboundTxnV1(arg0 context.Context, arg1 *federal0.ProcessInboundTxnRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessInboundTxnV1", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessInboundTxnV1 indicates an expected call of ProcessInboundTxnV1.
func (mr *MockPaymentServerMockRecorder) ProcessInboundTxnV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessInboundTxnV1", reflect.TypeOf((*MockPaymentServer)(nil).ProcessInboundTxnV1), arg0, arg1)
}

// ProcessUpiEvent mocks base method.
func (m *MockPaymentServer) ProcessUpiEvent(arg0 context.Context, arg1 *federal.ProcessUpiEventRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessUpiEvent", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessUpiEvent indicates an expected call of ProcessUpiEvent.
func (mr *MockPaymentServerMockRecorder) ProcessUpiEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessUpiEvent", reflect.TypeOf((*MockPaymentServer)(nil).ProcessUpiEvent), arg0, arg1)
}

// UpdateB2CTransaction mocks base method.
func (m *MockPaymentServer) UpdateB2CTransaction(arg0 context.Context, arg1 *b2c.UpdateB2CTransactionRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateB2CTransaction", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateB2CTransaction indicates an expected call of UpdateB2CTransaction.
func (mr *MockPaymentServerMockRecorder) UpdateB2CTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateB2CTransaction", reflect.TypeOf((*MockPaymentServer)(nil).UpdateB2CTransaction), arg0, arg1)
}

// UpdateTransaction mocks base method.
func (m *MockPaymentServer) UpdateTransaction(arg0 context.Context, arg1 *federal.UpdateTransactionRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTransaction", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTransaction indicates an expected call of UpdateTransaction.
func (mr *MockPaymentServerMockRecorder) UpdateTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransaction", reflect.TypeOf((*MockPaymentServer)(nil).UpdateTransaction), arg0, arg1)
}

// MockUnsafePaymentServer is a mock of UnsafePaymentServer interface.
type MockUnsafePaymentServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafePaymentServerMockRecorder
}

// MockUnsafePaymentServerMockRecorder is the mock recorder for MockUnsafePaymentServer.
type MockUnsafePaymentServerMockRecorder struct {
	mock *MockUnsafePaymentServer
}

// NewMockUnsafePaymentServer creates a new mock instance.
func NewMockUnsafePaymentServer(ctrl *gomock.Controller) *MockUnsafePaymentServer {
	mock := &MockUnsafePaymentServer{ctrl: ctrl}
	mock.recorder = &MockUnsafePaymentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafePaymentServer) EXPECT() *MockUnsafePaymentServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedPaymentServer mocks base method.
func (m *MockUnsafePaymentServer) mustEmbedUnimplementedPaymentServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPaymentServer")
}

// mustEmbedUnimplementedPaymentServer indicates an expected call of mustEmbedUnimplementedPaymentServer.
func (mr *MockUnsafePaymentServerMockRecorder) mustEmbedUnimplementedPaymentServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPaymentServer", reflect.TypeOf((*MockUnsafePaymentServer)(nil).mustEmbedUnimplementedPaymentServer))
}
