// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendornotification/openbanking/dispute/dispute.proto

package dispute

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AttachCorrespondenceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AttachCorrespondenceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttachCorrespondenceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttachCorrespondenceRequestMultiError, or nil if none found.
func (m *AttachCorrespondenceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AttachCorrespondenceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DisputeCaseNumber

	// no validation rules for CorrespondenceText

	// no validation rules for AgentEmail

	if len(errors) > 0 {
		return AttachCorrespondenceRequestMultiError(errors)
	}

	return nil
}

// AttachCorrespondenceRequestMultiError is an error wrapping multiple
// validation errors returned by AttachCorrespondenceRequest.ValidateAll() if
// the designated constraints aren't met.
type AttachCorrespondenceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttachCorrespondenceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttachCorrespondenceRequestMultiError) AllErrors() []error { return m }

// AttachCorrespondenceRequestValidationError is the validation error returned
// by AttachCorrespondenceRequest.Validate if the designated constraints
// aren't met.
type AttachCorrespondenceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttachCorrespondenceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttachCorrespondenceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttachCorrespondenceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttachCorrespondenceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttachCorrespondenceRequestValidationError) ErrorName() string {
	return "AttachCorrespondenceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AttachCorrespondenceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttachCorrespondenceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttachCorrespondenceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttachCorrespondenceRequestValidationError{}

// Validate checks the field values on AttachCorrespondenceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AttachCorrespondenceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttachCorrespondenceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttachCorrespondenceResponseMultiError, or nil if none found.
func (m *AttachCorrespondenceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AttachCorrespondenceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StatusCode

	if len(errors) > 0 {
		return AttachCorrespondenceResponseMultiError(errors)
	}

	return nil
}

// AttachCorrespondenceResponseMultiError is an error wrapping multiple
// validation errors returned by AttachCorrespondenceResponse.ValidateAll() if
// the designated constraints aren't met.
type AttachCorrespondenceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttachCorrespondenceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttachCorrespondenceResponseMultiError) AllErrors() []error { return m }

// AttachCorrespondenceResponseValidationError is the validation error returned
// by AttachCorrespondenceResponse.Validate if the designated constraints
// aren't met.
type AttachCorrespondenceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttachCorrespondenceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttachCorrespondenceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttachCorrespondenceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttachCorrespondenceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttachCorrespondenceResponseValidationError) ErrorName() string {
	return "AttachCorrespondenceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AttachCorrespondenceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttachCorrespondenceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttachCorrespondenceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttachCorrespondenceResponseValidationError{}
