package user_preference

import (
	"database/sql/driver"
	"fmt"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x Preference) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *Preference) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := Preference_value[val]
	*x = Preference(valInt)
	return nil
}
