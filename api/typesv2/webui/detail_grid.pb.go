// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/webui/detail_grid.proto

package webui

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ValueType int32

const (
	ValueType_VALUE_TYPE_UNSPECIFIED ValueType = 0
	ValueType_VALUE_TYPE_TEXT        ValueType = 1
	ValueType_VALUE_TYPE_KEY_VALUE   ValueType = 2
)

// Enum value maps for ValueType.
var (
	ValueType_name = map[int32]string{
		0: "VALUE_TYPE_UNSPECIFIED",
		1: "VALUE_TYPE_TEXT",
		2: "VALUE_TYPE_KEY_VALUE",
	}
	ValueType_value = map[string]int32{
		"VALUE_TYPE_UNSPECIFIED": 0,
		"VALUE_TYPE_TEXT":        1,
		"VALUE_TYPE_KEY_VALUE":   2,
	}
)

func (x ValueType) Enum() *ValueType {
	p := new(ValueType)
	*p = x
	return p
}

func (x ValueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValueType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_webui_detail_grid_proto_enumTypes[0].Descriptor()
}

func (ValueType) Type() protoreflect.EnumType {
	return &file_api_typesv2_webui_detail_grid_proto_enumTypes[0]
}

func (x ValueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValueType.Descriptor instead.
func (ValueType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_webui_detail_grid_proto_rawDescGZIP(), []int{0}
}

type KeyValueList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyValueList []*KeyValueList_KeyValue `protobuf:"bytes,1,rep,name=key_value_list,json=keyValueList,proto3" json:"key_value_list,omitempty"`
}

func (x *KeyValueList) Reset() {
	*x = KeyValueList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeyValueList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyValueList) ProtoMessage() {}

func (x *KeyValueList) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyValueList.ProtoReflect.Descriptor instead.
func (*KeyValueList) Descriptor() ([]byte, []int) {
	return file_api_typesv2_webui_detail_grid_proto_rawDescGZIP(), []int{0}
}

func (x *KeyValueList) GetKeyValueList() []*KeyValueList_KeyValue {
	if x != nil {
		return x.KeyValueList
	}
	return nil
}

type Cell struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type ValueType `protobuf:"varint,1,opt,name=type,proto3,enum=api.typesv2.webui.ValueType" json:"type,omitempty"`
	// cell style is applied to higher div
	Style *Style `protobuf:"bytes,2,opt,name=style,proto3" json:"style,omitempty"`
	// Types that are assignable to Value:
	//
	//	*Cell_TextValue
	//	*Cell_KeyValuePairs
	Value isCell_Value `protobuf_oneof:"value"`
}

func (x *Cell) Reset() {
	*x = Cell{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cell) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cell) ProtoMessage() {}

func (x *Cell) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cell.ProtoReflect.Descriptor instead.
func (*Cell) Descriptor() ([]byte, []int) {
	return file_api_typesv2_webui_detail_grid_proto_rawDescGZIP(), []int{1}
}

func (x *Cell) GetType() ValueType {
	if x != nil {
		return x.Type
	}
	return ValueType_VALUE_TYPE_UNSPECIFIED
}

func (x *Cell) GetStyle() *Style {
	if x != nil {
		return x.Style
	}
	return nil
}

func (m *Cell) GetValue() isCell_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *Cell) GetTextValue() string {
	if x, ok := x.GetValue().(*Cell_TextValue); ok {
		return x.TextValue
	}
	return ""
}

func (x *Cell) GetKeyValuePairs() *KeyValueList {
	if x, ok := x.GetValue().(*Cell_KeyValuePairs); ok {
		return x.KeyValuePairs
	}
	return nil
}

type isCell_Value interface {
	isCell_Value()
}

type Cell_TextValue struct {
	TextValue string `protobuf:"bytes,3,opt,name=text_value,json=textValue,proto3,oneof"`
}

type Cell_KeyValuePairs struct {
	KeyValuePairs *KeyValueList `protobuf:"bytes,4,opt,name=key_value_pairs,json=keyValuePairs,proto3,oneof"`
}

func (*Cell_TextValue) isCell_Value() {}

func (*Cell_KeyValuePairs) isCell_Value() {}

type GridRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label       string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	DetailValue *Cell  `protobuf:"bytes,2,opt,name=detail_value,json=detailValue,proto3" json:"detail_value,omitempty"`
}

func (x *GridRow) Reset() {
	*x = GridRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GridRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GridRow) ProtoMessage() {}

func (x *GridRow) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GridRow.ProtoReflect.Descriptor instead.
func (*GridRow) Descriptor() ([]byte, []int) {
	return file_api_typesv2_webui_detail_grid_proto_rawDescGZIP(), []int{2}
}

func (x *GridRow) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *GridRow) GetDetailValue() *Cell {
	if x != nil {
		return x.DetailValue
	}
	return nil
}

type DetailView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title string     `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Rows  []*GridRow `protobuf:"bytes,2,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *DetailView) Reset() {
	*x = DetailView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DetailView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailView) ProtoMessage() {}

func (x *DetailView) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailView.ProtoReflect.Descriptor instead.
func (*DetailView) Descriptor() ([]byte, []int) {
	return file_api_typesv2_webui_detail_grid_proto_rawDescGZIP(), []int{3}
}

func (x *DetailView) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DetailView) GetRows() []*GridRow {
	if x != nil {
		return x.Rows
	}
	return nil
}

type KeyValueList_KeyValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// key value pair style takes priority over the cell style
	// if the style is not set, the cell style will be used
	Style *Style `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
}

func (x *KeyValueList_KeyValue) Reset() {
	*x = KeyValueList_KeyValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeyValueList_KeyValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyValueList_KeyValue) ProtoMessage() {}

func (x *KeyValueList_KeyValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_webui_detail_grid_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyValueList_KeyValue.ProtoReflect.Descriptor instead.
func (*KeyValueList_KeyValue) Descriptor() ([]byte, []int) {
	return file_api_typesv2_webui_detail_grid_proto_rawDescGZIP(), []int{0, 0}
}

func (x *KeyValueList_KeyValue) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *KeyValueList_KeyValue) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *KeyValueList_KeyValue) GetStyle() *Style {
	if x != nil {
		return x.Style
	}
	return nil
}

var File_api_typesv2_webui_detail_grid_proto protoreflect.FileDescriptor

var file_api_typesv2_webui_detail_grid_proto_rawDesc = []byte{
	0x0a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x77, 0x65,
	0x62, 0x75, 0x69, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x67, 0x72, 0x69, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2f, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc2, 0x01, 0x0a, 0x0c, 0x4b, 0x65, 0x79, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x0e, 0x6b, 0x65, 0x79, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77,
	0x65, 0x62, 0x75, 0x69, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x6b, 0x65, 0x79, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x62, 0x0a, 0x08, 0x4b, 0x65, 0x79, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2e, 0x0a, 0x05,
	0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e,
	0x53, 0x74, 0x79, 0x6c, 0x65, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x22, 0xdd, 0x01, 0x0a,
	0x04, 0x43, 0x65, 0x6c, 0x6c, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x74, 0x65, 0x78, 0x74, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x74,
	0x65, 0x78, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x49, 0x0a, 0x0f, 0x6b, 0x65, 0x79, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x6b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61,
	0x69, 0x72, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5b, 0x0a, 0x07,
	0x47, 0x72, 0x69, 0x64, 0x52, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x3a, 0x0a,
	0x0c, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x43, 0x65, 0x6c, 0x6c, 0x52, 0x0b, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x52, 0x0a, 0x0a, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2e, 0x0a,
	0x04, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e,
	0x47, 0x72, 0x69, 0x64, 0x52, 0x6f, 0x77, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x2a, 0x56, 0x0a,
	0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x41,
	0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x56,
	0x41, 0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x45, 0x59, 0x5f, 0x56, 0x41,
	0x4c, 0x55, 0x45, 0x10, 0x02, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x77, 0x65, 0x62, 0x75,
	0x69, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x77, 0x65, 0x62, 0x75, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_webui_detail_grid_proto_rawDescOnce sync.Once
	file_api_typesv2_webui_detail_grid_proto_rawDescData = file_api_typesv2_webui_detail_grid_proto_rawDesc
)

func file_api_typesv2_webui_detail_grid_proto_rawDescGZIP() []byte {
	file_api_typesv2_webui_detail_grid_proto_rawDescOnce.Do(func() {
		file_api_typesv2_webui_detail_grid_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_webui_detail_grid_proto_rawDescData)
	})
	return file_api_typesv2_webui_detail_grid_proto_rawDescData
}

var file_api_typesv2_webui_detail_grid_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_webui_detail_grid_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_typesv2_webui_detail_grid_proto_goTypes = []interface{}{
	(ValueType)(0),                // 0: api.typesv2.webui.ValueType
	(*KeyValueList)(nil),          // 1: api.typesv2.webui.KeyValueList
	(*Cell)(nil),                  // 2: api.typesv2.webui.Cell
	(*GridRow)(nil),               // 3: api.typesv2.webui.GridRow
	(*DetailView)(nil),            // 4: api.typesv2.webui.DetailView
	(*KeyValueList_KeyValue)(nil), // 5: api.typesv2.webui.KeyValueList.KeyValue
	(*Style)(nil),                 // 6: api.typesv2.webui.Style
}
var file_api_typesv2_webui_detail_grid_proto_depIdxs = []int32{
	5, // 0: api.typesv2.webui.KeyValueList.key_value_list:type_name -> api.typesv2.webui.KeyValueList.KeyValue
	0, // 1: api.typesv2.webui.Cell.type:type_name -> api.typesv2.webui.ValueType
	6, // 2: api.typesv2.webui.Cell.style:type_name -> api.typesv2.webui.Style
	1, // 3: api.typesv2.webui.Cell.key_value_pairs:type_name -> api.typesv2.webui.KeyValueList
	2, // 4: api.typesv2.webui.GridRow.detail_value:type_name -> api.typesv2.webui.Cell
	3, // 5: api.typesv2.webui.DetailView.rows:type_name -> api.typesv2.webui.GridRow
	6, // 6: api.typesv2.webui.KeyValueList.KeyValue.style:type_name -> api.typesv2.webui.Style
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_typesv2_webui_detail_grid_proto_init() }
func file_api_typesv2_webui_detail_grid_proto_init() {
	if File_api_typesv2_webui_detail_grid_proto != nil {
		return
	}
	file_api_typesv2_webui_table_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_webui_detail_grid_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeyValueList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_webui_detail_grid_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cell); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_webui_detail_grid_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GridRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_webui_detail_grid_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DetailView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_webui_detail_grid_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeyValueList_KeyValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_webui_detail_grid_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Cell_TextValue)(nil),
		(*Cell_KeyValuePairs)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_webui_detail_grid_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_webui_detail_grid_proto_goTypes,
		DependencyIndexes: file_api_typesv2_webui_detail_grid_proto_depIdxs,
		EnumInfos:         file_api_typesv2_webui_detail_grid_proto_enumTypes,
		MessageInfos:      file_api_typesv2_webui_detail_grid_proto_msgTypes,
	}.Build()
	File_api_typesv2_webui_detail_grid_proto = out.File
	file_api_typesv2_webui_detail_grid_proto_rawDesc = nil
	file_api_typesv2_webui_detail_grid_proto_goTypes = nil
	file_api_typesv2_webui_detail_grid_proto_depIdxs = nil
}
