syntax = "proto3";

package derivedattributes;

option go_package = "github.com/epifi/gamma/api/user/credit_report/derivedattributes";
option java_package = "com.github.epifi.gamma.api.user.credit_report.derivedattributes";


enum TimeUnit {
  TIME_UNIT_UNSPECIFIED = 0;
  TIME_UNIT_DAY = 1;
  TIME_UNIT_MONTH = 2;
  TIME_UNIT_YEAR = 3;
}

message TimePeriod {
  uint32 length = 1;
  TimeUnit unit = 2;
}
