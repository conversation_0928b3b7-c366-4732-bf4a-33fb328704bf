package dao

import (
	"context"
	"testing"

	"google.golang.org/protobuf/proto"
	gormV2 "gorm.io/gorm"

	"github.com/epifi/gamma/api/frontend/analytics"
	"github.com/epifi/gamma/api/inapphelp/feedback_engine"
	"github.com/epifi/gamma/inapphelp/config"
	"github.com/epifi/gamma/inapphelp/test"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
)

type FeedbackSurveyMappingsDaoTestSuite struct {
	db                        *gormV2.DB
	conf                      *config.Config
	FeedbackSurveyMappingsDao *FeedbackSurveyMappingsDao
}

var (
	fsmdts FeedbackSurveyMappingsDaoTestSuite
)

func TestFeedbackSurveyMappingsDao_Create(t *testing.T) {
	sampleFeedbackSurveyMapping := &feedback_engine.FeedbackSurveyMapping{
		AnalyticsScreenName:    analytics.AnalyticsScreenName_CX_CHAT_SCREEN,
		FlowId:                 "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
		SurveyId:               "157a1ebe-4f91-470a-bf8d-1dab7deacfa1",
		IsSurveyMappingEnabled: true,
	}
	type args struct {
		ctx                   context.Context
		feedbackSurveyMapping *feedback_engine.FeedbackSurveyMapping
	}
	tests := []struct {
		name    string
		args    args
		want    *feedback_engine.FeedbackSurveyMapping
		wantErr bool
	}{
		{
			name: "invalid input fields",
			args: args{
				ctx:                   context.Background(),
				feedbackSurveyMapping: &feedback_engine.FeedbackSurveyMapping{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "update on conflict",
			args: args{
				ctx: context.Background(),
				feedbackSurveyMapping: &feedback_engine.FeedbackSurveyMapping{
					AnalyticsScreenName:    analytics.AnalyticsScreenName_ANALYZER,
					FlowId:                 "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
					SurveyId:               "157a1ebe-4f91-470a-bf8d-1dab7deacfa1",
					IsSurveyMappingEnabled: true,
				},
			},
			want: &feedback_engine.FeedbackSurveyMapping{
				AnalyticsScreenName:    analytics.AnalyticsScreenName_ANALYZER,
				FlowId:                 "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
				SurveyId:               "157a1ebe-4f91-470a-bf8d-1dab7deacfa1",
				IsSurveyMappingEnabled: true,
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx:                   context.Background(),
				feedbackSurveyMapping: sampleFeedbackSurveyMapping,
			},
			want:    sampleFeedbackSurveyMapping,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		pkgTestV2.TruncateAndPopulateRdsFixtures(t, fqdts.db, fqdts.conf.EpifiDb.GetName(), test.AffectedTestTables)
		got, err := fsmdts.FeedbackSurveyMappingsDao.Create(tt.args.ctx, tt.args.feedbackSurveyMapping)
		if (err != nil) != tt.wantErr {
			t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
			return
		}
		if !isFeedbackSurveyMappingEqual(got, tt.want) {
			t.Errorf("Create() got = %v, want %v", got, tt.want)
		}
	}
}

func TestFeedbackSurveyMappingsDao_GetMappings(t *testing.T) {
	sampleFeedbackSurveyMapping1 := &feedback_engine.FeedbackSurveyMapping{
		AnalyticsScreenName:    analytics.AnalyticsScreenName_ANALYZER,
		FlowId:                 "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
		SurveyId:               "157a1ebe-4f91-470a-bf8d-1dab7deacfa1",
		IsSurveyMappingEnabled: true,
	}
	type args struct {
		ctx                 context.Context
		analyticsScreenName analytics.AnalyticsScreenName
		flowId              string
	}
	tests := []struct {
		name    string
		args    args
		want    []*feedback_engine.FeedbackSurveyMapping
		wantErr bool
	}{
		{
			name: "invalid input",
			args: args{
				ctx:                 context.Background(),
				analyticsScreenName: analytics.AnalyticsScreenName_ANALYTICS_SCREEN_NAME_UNSPECIFIED,
				flowId:              "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "record not found",
			args: args{
				ctx:                 context.Background(),
				analyticsScreenName: analytics.AnalyticsScreenName_APP_SHORTCUTS,
				flowId:              "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success - based on only analytics screen name",
			args: args{
				ctx:                 context.Background(),
				analyticsScreenName: analytics.AnalyticsScreenName_ANALYZER,
			},
			want:    []*feedback_engine.FeedbackSurveyMapping{sampleFeedbackSurveyMapping1},
			wantErr: false,
		},
		{
			name: "success - based on both analytics screen name and flow id",
			args: args{
				ctx:                 context.Background(),
				analyticsScreenName: analytics.AnalyticsScreenName_ANALYZER,
				flowId:              "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
			},
			want:    []*feedback_engine.FeedbackSurveyMapping{sampleFeedbackSurveyMapping1},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		pkgTestV2.TruncateAndPopulateRdsFixtures(t, fqdts.db, fqdts.conf.EpifiDb.GetName(), test.AffectedTestTables)
		got, err := fsmdts.FeedbackSurveyMappingsDao.GetMappings(tt.args.ctx, tt.args.analyticsScreenName, tt.args.flowId)
		if (err != nil) != tt.wantErr {
			t.Errorf("GetMappings() error = %v, wantErr %v", err, tt.wantErr)
			return
		}
		if !isListOfFeedbackSurveyMappingsEqual(got, tt.want) {
			t.Errorf("GetMappings() got = %v, want %v", got, tt.want)
		}
	}
}

func TestFeedbackSurveyMappingsDao_UpdateIsSurveyEnabled(t *testing.T) {
	sampleFeedbackSurveyMapping1 := &feedback_engine.FeedbackSurveyMapping{
		AnalyticsScreenName:    analytics.AnalyticsScreenName_ANALYZER,
		FlowId:                 "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
		SurveyId:               "157a1ebe-4f91-470a-bf8d-1dab7deacfa1",
		IsSurveyMappingEnabled: false,
	}
	type args struct {
		ctx                   context.Context
		feedbackSurveyMapping *feedback_engine.FeedbackSurveyMapping
	}
	tests := []struct {
		name    string
		args    args
		want    *feedback_engine.FeedbackSurveyMapping
		wantErr bool
	}{
		{
			name: "invalid input fields",
			args: args{
				ctx:                   context.Background(),
				feedbackSurveyMapping: &feedback_engine.FeedbackSurveyMapping{AnalyticsScreenName: analytics.AnalyticsScreenName_ANALYZER},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "record not found",
			args: args{
				ctx: context.Background(),
				feedbackSurveyMapping: &feedback_engine.FeedbackSurveyMapping{
					AnalyticsScreenName:    analytics.AnalyticsScreenName_CX_CHAT_SCREEN,
					FlowId:                 "FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING",
					SurveyId:               "157a1ebe-4f91-470a-bf8d-1dab7deacfa1",
					IsSurveyMappingEnabled: false,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx:                   context.Background(),
				feedbackSurveyMapping: sampleFeedbackSurveyMapping1,
			},
			want:    sampleFeedbackSurveyMapping1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		pkgTestV2.TruncateAndPopulateRdsFixtures(t, fqdts.db, fqdts.conf.EpifiDb.GetName(), test.AffectedTestTables)
		got, err := fsmdts.FeedbackSurveyMappingsDao.UpdateIsSurveyEnabled(tt.args.ctx, tt.args.feedbackSurveyMapping)
		if (err != nil) != tt.wantErr {
			t.Errorf("UpdateIsSurveyEnabled() error = %v, wantErr %v", err, tt.wantErr)
			return
		}
		if !isFeedbackSurveyMappingEqual(got, tt.want) {
			t.Errorf("Create() got = %v, want %v", got, tt.want)
		}
	}
}

func isFeedbackSurveyMappingEqual(actual *feedback_engine.FeedbackSurveyMapping, expected *feedback_engine.FeedbackSurveyMapping) bool {
	if actual != nil && expected != nil {
		expected.Id = actual.GetId()
		expected.CreatedAt = actual.GetCreatedAt()
		expected.UpdatedAt = actual.GetUpdatedAt()
	}
	return proto.Equal(actual, expected)
}

func isListOfFeedbackSurveyMappingsEqual(actual []*feedback_engine.FeedbackSurveyMapping, expected []*feedback_engine.FeedbackSurveyMapping) bool {
	if len(actual) != len(expected) {
		return false
	}
	for index, actualMapping := range actual {
		if !isFeedbackSurveyMappingEqual(actualMapping, expected[index]) {
			return false
		}
	}
	return true
}
