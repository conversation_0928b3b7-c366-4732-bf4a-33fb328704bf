groups:
  - name: rules/aggregation.rules
    rules:
      - record: node:capacity
        expr: sum without(store) (capacity{job="cockroachdb", service="prod"})
      - record: cluster:capacity
        expr: sum without(instance) (node:capacity{job="cockroachdb", service="prod"})
      - record: node:capacity_available
        expr: sum without(store) (capacity_available{job="cockroachdb", service="prod"})
      - record: cluster:capacity_available
        expr: sum without(instance) (node:capacity_available{job="cockroachdb", service="prod"})
      - record: capacity_available:ratio
        expr: capacity_available{job="cockroachdb", service="prod"} / capacity{job="cockroachdb", service="prod"}
      - record: node:capacity_available:ratio
        expr: node:capacity_available{job="cockroachdb", service="prod"} / node:capacity{job="cockroachdb", service="prod"}
      - record: cluster:capacity_available:ratio
        expr: cluster:capacity_available{job="cockroachdb", service="prod"} / cluster:capacity{job="cockroachdb", service="prod"}
      # Histogram rules: these are fairly expensive to compute live, so we precompute a few percetiles.
      - record: sql_exec_latency_bucket:rate1m
        expr: rate(sql_exec_latency_bucket{job="cockroachdb", service="prod"}[1m])
      - record: sql_exec_latency:rate1m:quantile_99
        expr: histogram_quantile(0.99, sql_exec_latency_bucket:rate1m)
  - name: rules/alerts.rules
    rules:
      # Alert for any instance that is unreachable for >15 minutes.
      - alert: InstanceDead
        expr: up{job="cockroachdb", service="prod"} == 0
        for: 5m
        labels:
          severity: P0
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          description: '{{ $labels.instance }} for cluster {{ $labels.cluster }} has been down for more than 15 minutes.'
          summary: CRDB Instance {{ $labels.instance }} dead

      # Alert on instance restarts.
      - alert: InstanceRestart
        expr: resets(sys_uptime{job="cockroachdb", service="prod"}[15m]) > 1
        labels:
          severity: P0
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          description: '{{ $labels.instance }} for cluster {{ $labels.cluster }} restarted {{ $value }} time(s) in 24h'
          summary: CRDB Instance {{ $labels.instance }} restarted
      # Alert on flapping instances (frequent restarts).
      - alert: InstancesFlapping
        # Aggregated.
        # This alert assumes that rolling restarts or rolling upgrades leave at least 3 minutes between each node being updated or restarted.
        expr: sum by (environment)(resets(sys_uptime{job="cockroachdb", service="prod"}[5m])) > 2
        labels:
          severity: P0
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          description: 'instances in cluster {{ $labels.cluster }} restarted  {{ $value }} time(s) in 5m'
          summary: CRDB Instances in {{ $labels.environment }} flapping
      # Alert on version mismatch.
      # This alert is intentionally loose (4 hours) to allow for rolling upgrades.
      # This may need to be adjusted for large clusters.
      - alert: VersionMismatch
        expr: count by(environment) (count_values by(tag, environment) ("version", build_timestamp{job="cockroachdb", service="prod"}))  > 1
        for: 4h
        labels:
          severity: P0
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          description: Environment {{ $labels.environment }} running {{ $value }} different versions
          summary: CRDB Binary version mismatch on {{ $labels.environment }}
      # Available capacity alerts.
      - alert: StoreDiskLow
        expr: capacity_available:ratio{job="cockroachdb", service="prod"} < 0.20
        for: 12h
        labels:
          severity: P1
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          summary: CRDB Storage {{ $labels.store }} on node {{ $labels.instance }} at {{ $value }} available disk fraction
          description: "CRDB Node storage on node {{ $labels.instance }} is less than 25%, please check and raise in db-ops for capacity planning"
      - alert: ClusterDiskLow
        expr: cluster:capacity_available:ratio{job="cockroachdb", service="prod"} < 0.20
        for: 12h
        labels:
          severity: P1
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          summary: CRDB Cluster {{ $labels.cluster }} at {{ $value }} available disk fraction
          description: "CRDB cluster storage remaining is less than 25%, please check and raise in db-ops for capacity planning"
      # Unavailable ranges.
      - alert: UnavailableRanges
        expr: (sum by(instance, cluster) (ranges_unavailable{job="cockroachdb", service="prod"})) > 0
        for: 10m
        labels:
          severity: P0
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          summary: CRDB Instance {{ $labels.instance }} has {{ $value }} unavailable ranges
      # Scheduled backup job failures
      - alert: ScheduledBackupJobFailed
        expr: sum by(cluster) (increase(schedules_BACKUP_failed{job="cockroachdb", service="prod"}[10m])) > 0
        for: 10m
        labels:
          severity: P0
          team: "devops"
          alert_type: "cockroachdb"
        annotations:
          summary: Some scheduled backup jobs in the Cluster {{ $labels.cluster }} failed
      # Cockroach-measured clock offset nearing limit (by default, servers kill themselves at 400ms from the mean, so alert at 300ms)
      - alert: ClockOffsetNearMax
        expr: clock_offset_meannanos{job="cockroachdb", service="prod"} > 300 * 1000 * 1000
        for: 5m
        labels:
          severity: P0
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          summary: Clock on CRDB Instance {{ $labels.instance }} as measured by cockroach is offset by {{ $value }} nanoseconds from the cluster mean  # Certificate expiration. Alerts are per node.
      - alert: CACertificateExpiresSoon
        expr: (security_certificate_expiration_ca{job="cockroachdb", service="prod"} > 0) and (security_certificate_expiration_ca{job="cockroachdb", service="prod"}
          - time()) < 86400 * 366
        labels:
          severity: P1
          team: "platform"
          frequency: daily
          alert_type: "cockroachdb"
        annotations:
          summary: CA certificate for CRDB Instance {{ $labels.instance }} expires in less than a year
      - alert: ClientCACertificateExpiresSoon
        expr: (security_certificate_expiration_client_ca{job="cockroachdb", service="prod"} > 0) and (security_certificate_expiration_client_ca{job="cockroachdb", service="prod"}
          - time()) < 86400 * 366
        labels:
          severity: P1
          team: "platform"
          frequency: daily
          alert_type: "cockroachdb"
        annotations:
          summary: Client CA certificate for CRDB Instance {{ $labels.instance }} expires in less than a year
      - alert: UICACertificateExpiresSoon
        expr: (security_certificate_expiration_ui_ca{job="cockroachdb", service="prod"} > 0) and (security_certificate_expiration_ui_ca{job="cockroachdb", service="prod"}
          - time()) < 86400 * 366
        labels:
          severity: P1
          team: "platform"
          frequency: daily
          alert_type: "cockroachdb"
        annotations:
          summary: UI CA certificate for CRDB Instance {{ $labels.instance }} expires in less than a year
      - alert: NodeCertificateExpiresSoon
        expr: (security_certificate_expiration_node{job="cockroachdb", service="prod"} > 0) and (security_certificate_expiration_node{job="cockroachdb", service="prod"}
          - time()) < 86400 * 183
        labels:
          severity: P1
          team: "platform"
          frequency: daily
          alert_type: "cockroachdb"
        annotations:
          summary: Node certificate for CRDB Instance {{ $labels.instance }} expires in less than six months
      - alert: NodeClientCertificateExpiresSoon
        expr: (security_certificate_expiration_node_client{job="cockroachdb", service="prod"} > 0) and (security_certificate_expiration_node_client{job="cockroachdb", service="prod"}
          - time()) < 86400 * 183
        labels:
          severity: P0
          team: "platform"
          frequency: daily
          alert_type: "cockroachdb"
        annotations:
          summary: Client certificate for CRDB Instance {{ $labels.instance }} expires in less than six months
      - alert: UICertificateExpiresSoon
        expr: (security_certificate_expiration_ui{job="cockroachdb", service="prod"} > 0) and (security_certificate_expiration_ui{job="cockroachdb", service="prod"}
          - time()) < 86400 * 20
        labels:
          severity: P0
          team: "platform"
          frequency: daily
          alert_type: "cockroachdb"
        annotations:
          summary: UI certificate for CRDB Instance {{ $labels.instance }} expires in less than 20 days
      # Slow Latch/Lease/Raft requests.
      - alert: SlowLatchRequest
        expr: requests_slow_latch{job="cockroachdb", service="prod"} > 0
        for: 5m
        labels:
          severity: P1
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          summary: '{{ $value }} slow latch requests on CRDB Instance {{ $labels.instance }}'
      - alert: SlowLeaseRequest
        expr: requests_slow_lease{job="cockroachdb", service="prod"} > 0
        for: 5m
        labels:
          severity: P0
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          summary: '{{ $value }} slow lease requests on CRDB Instance {{ $labels.instance }}'
      - alert: SlowRaftRequest
        expr: requests_slow_raft{job="cockroachdb", service="prod"} > 0
        for: 5m
        labels:
          severity: P1
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          summary: '{{ $value }} slow raft requests on CRDB Instance {{ $labels.instance }}'
      # Getting close to open file descriptor limit.
      - alert: HighOpenFDCount
        expr: sys_fd_open{job="cockroachdb", service="prod"} / sys_fd_softlimit{job="cockroachdb", service="prod"} > 0.85
        for: 10m
        labels:
          severity: P1
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          summary: 'Too many open file descriptors on CRDB Instance {{ $labels.instance }}: {{ $value }} fraction used'
      # Alert for any instance with 99% sql latency more than 100 milliseconds.
      # TODO(Sundeep): Disabling 100ms Latency alert for CRDB since the cluster is reduced to half.
      # Re-enable it once pay & onboarding queries are optimised.
#      - alert: CRDB100MsLatency
#        expr: sql_exec_latency:rate1m:quantile_99 / 1000000 > 100
#        for: 5m
#        labels:
#          severity: P1
#          team: "platform"
#          alert_type: "cockroachdb"
#        annotations:
#          description: '{{ $labels.instance }} in cluster {{ $labels.cluster }} has 99th percentile SQL latency above 100ms (Current value: {{ $value }}ms)'
#          summary: CRDB Instance {{ $labels.instance }} exceeds 99th percentile SQL latency
      # TODO(Sundeep): Increasing 200ms Latency alert for CRDB to 300ms since the cluster is reduced to half.
      # Reset it once pay & onboarding queries are optimised.
      # Alert for any instance with 99% sql latency more than 300 milliseconds.
      - alert: CRDB300MsLatency
        expr: sql_exec_latency:rate1m:quantile_99 / 1000000 > 300
        for: 5m
        labels:
          severity: P0
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          description: '{{ $labels.instance }} in cluster {{ $labels.cluster }} has 99th percentile SQL latency above 300ms (Current value: {{ $value }}ms)'
          summary: CRDB Instance {{ $labels.instance }} exceeds 99th percentile SQL latency
      # Alert if the cluster executed full scan queries more than 50
      # [sakthi] commenting lower limit threshold. Since queries to new tables and small tables end up in full scan, this alert is frequent
      # has no AIs.
#      - alert: CRDBFullScanMoreThan50
#        expr: sum(rate(sql_full_scan_count{job="cockroachdb", service="prod"}[5m])) by (environment) > 50
#        for: 5m
#        labels:
#          severity: P1
#          team: "platform"
#          alert_type: "cockroachdb"
#        annotations:
#          description: 'Cluster {{ $labels.cluster }} is executing more than 50 full scan queries (Current value: {{ $value }})'
#          summary: CRDB Instance {{ $labels.instance }} exceeds 50 Full Scan query limit
      # Alert if the cluster executed full scan queries more than 500
      - alert: CRDBFullScanMoreThan500
        expr: sum(rate(sql_full_scan_count{job="cockroachdb", service="prod"}[5m])) by (environment) > 500
        for: 5m
        labels:
          severity: P0
          team: "platform"
          alert_type: "cockroachdb"
        annotations:
          description: 'Cluster {{ $labels.cluster }} is executing more than 500 full scan queries (Current value: {{ $value }})'
          summary: CRDB Instance {{ $labels.instance }} exceeds 500 Full Scan query limit
