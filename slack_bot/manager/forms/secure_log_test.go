package forms_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/epifi/gamma/slack_bot/manager/forms"
)

func Test_RedactEsResponse(t *testing.T) {

	tests := []struct {
		name    string
		body    []map[string]interface{}
		want    []map[string]interface{}
		wantErr bool
	}{
		{
			name: "body contain xml content",
			body: []map[string]interface{}{
				{"payload": "<fed:MerchantHeader><SENDER_USER_ID>FI23</SENDER_USER_ID></fed:MerchantHeader>"},
			},
			want: []map[string]interface{}{
				{"payload": "<fed:MerchantHeader><SENDER_USER_ID>XXXX</SENDER_USER_ID></fed:MerchantHeader>"},
			},
			wantErr: false,
		},
		{
			name: "body contain json content",
			body: []map[string]interface{}{
				{
					"msg":     "Raw request:",
					"payload": "{\"SENDER_USER_ID\":\"FI23\"}",
				},
			},
			want: []map[string]interface{}{
				{
					"msg":     "Raw request:",
					"payload": "{\"SENDER_USER_ID\":\"XXXX\"}",
				},
			},
			wantErr: false,
		},
		{
			name: "body contain both xml and JSON content",
			body: []map[string]interface{}{
				{
					"msg":     "<fed:MerchantHeader><SENDER_USER_ID>FI23</SENDER_USER_ID></fed:MerchantHeader>",
					"payload": "{\"SENDER_USER_ID\":\"FI23\"}",
				},
			},
			want: []map[string]interface{}{
				{
					"msg":     "<fed:MerchantHeader><SENDER_USER_ID>XXXX</SENDER_USER_ID></fed:MerchantHeader>",
					"payload": "{\"SENDER_USER_ID\":\"XXXX\"}",
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := forms.RedactEsResponse(context.Background(), tt.body, true)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedactEsResponse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				assert.Equal(t, tt.want, got, "RedactEsResponse() got = %v, want %v", got, tt.want)
			}
		})
	}
}
