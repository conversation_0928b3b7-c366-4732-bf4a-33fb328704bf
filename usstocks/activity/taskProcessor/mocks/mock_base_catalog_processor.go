// Code generated by MockGen. DO NOT EDIT.
// Source: base_catalog_processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	catalog "github.com/epifi/gamma/api/usstocks/catalog"
	gomock "github.com/golang/mock/gomock"
)

// MockIBaseCatalogProcessor is a mock of IBaseCatalogProcessor interface.
type MockIBaseCatalogProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockIBaseCatalogProcessorMockRecorder
}

// MockIBaseCatalogProcessorMockRecorder is the mock recorder for MockIBaseCatalogProcessor.
type MockIBaseCatalogProcessorMockRecorder struct {
	mock *MockIBaseCatalogProcessor
}

// NewMockIBaseCatalogProcessor creates a new mock instance.
func NewMockIBaseCatalogProcessor(ctrl *gomock.Controller) *MockIBaseCatalogProcessor {
	mock := &MockIBaseCatalogProcessor{ctrl: ctrl}
	mock.recorder = &MockIBaseCatalogProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBaseCatalogProcessor) EXPECT() *MockIBaseCatalogProcessorMockRecorder {
	return m.recorder
}

// UpdateCollectionWithStkMapping mocks base method.
func (m *MockIBaseCatalogProcessor) UpdateCollectionWithStkMapping(ctx context.Context, collectionStockMappings []*catalog.CollectionStockMapping, collectionId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCollectionWithStkMapping", ctx, collectionStockMappings, collectionId)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCollectionWithStkMapping indicates an expected call of UpdateCollectionWithStkMapping.
func (mr *MockIBaseCatalogProcessorMockRecorder) UpdateCollectionWithStkMapping(ctx, collectionStockMappings, collectionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCollectionWithStkMapping", reflect.TypeOf((*MockIBaseCatalogProcessor)(nil).UpdateCollectionWithStkMapping), ctx, collectionStockMappings, collectionId)
}
