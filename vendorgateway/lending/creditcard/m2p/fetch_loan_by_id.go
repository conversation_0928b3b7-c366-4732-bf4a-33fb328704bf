package m2p

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	m2pLendingLms "github.com/epifi/gamma/api/vendors/m2p/lending/lms"
	"github.com/epifi/be-common/pkg/aws/json"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/m2p"
	m2pCryptor "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/m2p"
)

type FetchLoanByIdRequest struct {
	*m2pCryptor.DefaultM2PSecuredExchange
	*m2p.DefaultHeaderAdder
	Method string
	Req    *creditcard.FetchLoanByIdRequest
	Conf   *config.CreditCard
}

type FetchLoanByIdResp struct{}

func (f *FetchLoanByIdRequest) HTTPMethod() string {
	return f.Method
}

func (f *FetchLoanByIdRequest) URL() string {
	return f.Conf.M2P.M2PLMSHost + "loan/getDetails"
}

func (f *FetchLoanByIdRequest) GetResponse() vendorapi.Response {
	return &FetchLoanByIdResp{}
}

func (f *FetchLoanByIdRequest) Marshal() ([]byte, error) {
	err := f.validateRequest()
	if err != nil {
		return nil, errors.Wrap(err, "Unable to Validate Request")
	}

	res, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(
		&m2pLendingLms.GetLoanByIdRequest{
			EntityId: f.Req.GetCustomerId(),
			LoanId:   f.Req.GetVendorLoanId(),
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "Error in Marshalling the request")
	}

	ret, _ := json.SanitiseJSON(context.Background(), res)
	return ret, nil
}

func (f *FetchLoanByIdRequest) validateRequest() error {
	if f.Req.GetCustomerId() == "" {
		return errors.New("CustomerID is required")
	}
	if f.Req.GetVendorLoanId() == "" {
		return errors.New("VendorLoanId is required")
	}
	return nil
}

func (f *FetchLoanByIdResp) Unmarshal(b []byte) (proto.Message, error) {
	res := &m2pLendingLms.GetLoanByIdResponse{}

	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res)
	if err != nil {
		return nil, errors.Wrap(err, "Error in unmarshalling the response")
	}

	if res.GetException() != nil {
		status, ok := m2pCodesVsRpcCodes[res.GetException().GetErrorCode()]
		if !ok {
			errMsg := fmt.Sprint(res.GetException().GetDetailMessage(), res.GetException().GetMessage())
			status = rpc.StatusInternalWithDebugMsg(errMsg)
		}
		return &creditcard.FetchLoanByIdResponse{Status: status}, nil
	}

	loanInfo, err := f.getLoanInfo(res.GetResult())
	if err != nil {
		return nil, errors.Wrap(err, "Error in parsing loan info from fetch loan by Id API ")
	}

	return &creditcard.FetchLoanByIdResponse{
		Status:   rpc.StatusOk(),
		LoanInfo: loanInfo,
	}, nil
}

func (f *FetchLoanByIdResp) getLoanInfo(result *m2pLendingLms.GetLoanByIdResponseResult) (*creditcard.LoanInfo, error) {
	tenureInMonths, err := strconv.ParseInt(result.GetTenure(), 10, 64)
	if err != nil {
		return nil, errors.Wrap(err, "Error in parsing tenure in months in fetch Loan by Id API")
	}
	principalInfo, _ := f.getPrincipalInfo(result)
	repaymentInfo, err := f.getRepaymentInfo(result)
	if err != nil {
		return nil, errors.Wrap(err, "Error in parsing repayment Info in fetch Loan by Id API")
	}

	interestInfo, err := f.getInterestInfo(result)
	if err != nil {
		return nil, errors.Wrap(err, "Error in parsing interest Info in fetch Loan by Id API")
	}

	feeInfo, err := f.getFeeInfo(result)
	if err != nil {
		return nil, errors.Wrap(err, "Error in parsing fee Info in fetch Loan by Id API")
	}

	summary, _ := f.getSummary(result)
	amortizations, err := f.getAmortizations(result)
	if err != nil {
		return nil, errors.Wrap(err, "Error in parsing amortizations in fetch Loan by Id API")
	}

	loanStatus, ok := mapVendorLoanStatusToVgLoanStatus[result.GetLoanStatus()]
	if !ok {
		return nil, errors.New(fmt.Sprintf("invalid loan status from vendor, loan_status: %v", result.GetLoanStatus()))
	}

	return &creditcard.LoanInfo{
		VendorLoanId:      result.GetLoanId(),
		TenureInMonths:    tenureInMonths,
		LoanStatus:        loanStatus,
		DisbursedDate:     datetime.DateFromString(result.GetDisbursedDate()),
		AmountInfo:        f.getAmountInfo(result),
		ApplicableActions: f.getApplicableActions(result),
		PrincipalInfo:     principalInfo,
		RepaymentInfo:     repaymentInfo,
		InterestInfo:      interestInfo,
		FeeInfo:           feeInfo,
		Summary:           summary,
		Amortizations:     amortizations,
		Transactions:      f.getTransactions(result.GetTransactions()),
	}, nil
}

func (f *FetchLoanByIdResp) getAmountInfo(result *m2pLendingLms.GetLoanByIdResponseResult) *creditcard.LoanInfo_AmountInfo {
	return &creditcard.LoanInfo_AmountInfo{
		LoanAmount: money.ParseFloat(result.GetLoanAmount(), money.RupeeCurrencyCode),
		EmiAmount:  money.ParseFloat(result.GetEmiAmount(), money.RupeeCurrencyCode),
	}
}

func (f *FetchLoanByIdResp) getApplicableActions(result *m2pLendingLms.GetLoanByIdResponseResult) *creditcard.LoanInfo_ApplicableActions {
	return &creditcard.LoanInfo_ApplicableActions{
		IsLoanCancelApplicable:     result.GetIsLoanCancelApplicable(),
		IsLoanPreClosureApplicable: result.GetIsLoanPreClosureApplicable(),
		IsLoanRescheduleApplicable: result.GetIsLoanRescheduleApplicable(),
	}
}
func (f *FetchLoanByIdResp) getPrincipalInfo(result *m2pLendingLms.GetLoanByIdResponseResult) (*creditcard.LoanInfo_PrincipalInfo, error) {
	totalPrincipal := money.ParseFloat(result.GetTotalPrincipal(), money.RupeeCurrencyCode)
	principalOverdue := money.ParseFloat(result.GetPrincipalOverDue(), money.RupeeCurrencyCode)
	principalInfo := &creditcard.LoanInfo_PrincipalInfo{
		TotalPrincipal:       totalPrincipal,
		PrincipalOverdue:     principalOverdue,
		PrincipalOutstanding: money.ParseFloat(result.GetPrincipalOutstanding(), money.RupeeCurrencyCode),
	}

	return principalInfo, nil
}

func (f *FetchLoanByIdResp) getRepaymentInfo(result *m2pLendingLms.GetLoanByIdResponseResult) (*creditcard.LoanInfo_RepaymentInfo, error) {
	totalExpectedRepayment := money.ParseFloat(result.GetTotalExpectedRepayment(), money.RupeeCurrencyCode)
	numberOfDueRepayments, err := strconv.ParseInt(result.GetNumberOfDueRepayments(), 10, 64)
	if err != nil && result.GetNumberOfDueRepayments() != "" {
		return nil, errors.Wrap(err, "Error in parsing number Of Due Repayments in fetch Loan by Id API")
	}

	numberOfPaidRepayments, _ := strconv.ParseInt(result.GetNumberOfPaidRepayments(), 10, 64)

	repaymentInfo := &creditcard.LoanInfo_RepaymentInfo{
		TotalExpectedRepayment: totalExpectedRepayment,
		NumberOfDueRepayments:  numberOfDueRepayments,
		NumberOfPaidRepayments: numberOfPaidRepayments,
	}

	return repaymentInfo, nil
}

func (f *FetchLoanByIdResp) getInterestInfo(result *m2pLendingLms.GetLoanByIdResponseResult) (*creditcard.LoanInfo_InterestInfo, error) {
	brokenPeriodInterest, err := money.ParseString(result.GetBrokenPeriodInterest(), money.RupeeCurrencyCode)
	if err != nil {
		return nil, errors.Wrap(err, "Error in parsing broken Period Interest in fetch Loan by Id API")
	}

	interestInfo := &creditcard.LoanInfo_InterestInfo{
		InterestRate:         result.GetInterestRate(),
		TotalInterest:        money.ParseFloat(result.GetTotalInterest(), money.RupeeCurrencyCode),
		InterestOutstanding:  money.ParseFloat(result.GetInterestOutstanding(), money.RupeeCurrencyCode),
		InterestOverdue:      money.ParseFloat(result.GetInterestOverDue(), money.RupeeCurrencyCode),
		InterestPaid:         money.ParseFloat(result.GetInterestPaid(), money.RupeeCurrencyCode),
		InterestWaived:       money.ParseFloat(result.TotalWaivedOff, money.RupeeCurrencyCode),
		BrokenPeriodInterest: brokenPeriodInterest,
	}

	return interestInfo, nil
}

func (f *FetchLoanByIdResp) getFeeInfo(result *m2pLendingLms.GetLoanByIdResponseResult) (*creditcard.LoanInfo_FeeInfo, error) {
	totalFee := money.ParseFloat(result.GetTotalFee(), money.RupeeCurrencyCode)
	feeInfo := &creditcard.LoanInfo_FeeInfo{
		TotalFee:         totalFee,
		FeeOutstanding:   money.ParseFloat(result.GetFeeOutstanding(), money.RupeeCurrencyCode),
		FeeOverdue:       money.ParseFloat(result.GetFeeOverDue(), money.RupeeCurrencyCode),
		TotalFeePaid:     money.ParseFloat(result.GetTotalFeePaid(), money.RupeeCurrencyCode),
		FeeWaived:        money.ParseFloat(result.GetFeeWaived(), money.RupeeCurrencyCode),
		ProcessingFee:    money.ParseFloat(result.GetProcessingFee(), money.RupeeCurrencyCode),
		ProcessingFeeTax: money.ParseFloat(result.GetProcessingFeeTax(), money.RupeeCurrencyCode),
	}
	return feeInfo, nil
}

func (f *FetchLoanByIdResp) getSummary(result *m2pLendingLms.GetLoanByIdResponseResult) (*creditcard.LoanInfo_Summary, error) {
	totalTax := money.ParseFloat(result.GetTotalTax(), money.RupeeCurrencyCode)

	summary := &creditcard.LoanInfo_Summary{
		TotalTax:         totalTax,
		TotalRepayment:   money.ParseFloat(result.GetTotalRepayment(), money.RupeeCurrencyCode),
		TotalOutstanding: money.ParseFloat(result.GetTotalOutstanding(), money.RupeeCurrencyCode),
		TotalOverdue:     money.ParseFloat(result.GetTotalOverDue(), money.RupeeCurrencyCode),
		TotalWaivedOff:   money.ParseFloat(result.GetTotalWaivedOff(), money.RupeeCurrencyCode),
	}

	return summary, nil
}

func (f *FetchLoanByIdResp) getAmortizations(result *m2pLendingLms.GetLoanByIdResponseResult) ([]*creditcard.Amortization, error) {
	amortizations := make([]*creditcard.Amortization, 0)
	for _, amortizationDetails := range result.GetAmortizations() {
		principalInfo := &creditcard.Amortization_PrincipalInfo{
			Principal:              money.ParseFloat(amortizationDetails.GetPrincipal(), money.RupeeCurrencyCode),
			PrincipalPaid:          money.ParseFloat(amortizationDetails.GetPrincipalPaid(), money.RupeeCurrencyCode),
			PrincipalOutstanding:   money.ParseFloat(amortizationDetails.GetPrincipalOutstanding(), money.RupeeCurrencyCode),
			ClosingPrincipalAmount: money.ParseFloat(amortizationDetails.GetClosingPrincipalAmount(), money.RupeeCurrencyCode),
		}

		interestInfo := &creditcard.Amortization_InterestInfo{
			Interest:            money.ParseFloat(amortizationDetails.GetInterest(), money.RupeeCurrencyCode),
			InterestPaid:        money.ParseFloat(amortizationDetails.GetInterestPaid(), money.RupeeCurrencyCode),
			InterestOutstanding: money.ParseFloat(amortizationDetails.GetInterestOutstanding(), money.RupeeCurrencyCode),
			InterestWaived:      money.ParseFloat(amortizationDetails.GetInterestWaived(), money.RupeeCurrencyCode),
		}

		feeInfo := &creditcard.Amortization_FeeInfo{
			Fee:            money.ParseFloat(amortizationDetails.GetFee(), money.RupeeCurrencyCode),
			FeePaid:        money.ParseFloat(amortizationDetails.GetFeePaid(), money.RupeeCurrencyCode),
			FeeOutstanding: money.ParseFloat(amortizationDetails.GetFeeOutstanding(), money.RupeeCurrencyCode),
			FeeWaived:      money.ParseFloat(amortizationDetails.GetFeeWaived(), money.RupeeCurrencyCode),
		}

		summary := &creditcard.Amortization_Summary{
			TotalDue:         money.ParseFloat(amortizationDetails.GetTotalDue(), money.RupeeCurrencyCode),
			TotalPaid:        money.ParseFloat(amortizationDetails.GetTotalPaid(), money.RupeeCurrencyCode),
			TotalOutstanding: money.ParseFloat(amortizationDetails.GetTotalOutstanding(), money.RupeeCurrencyCode),
			TotalWaived:      money.ParseFloat(amortizationDetails.GetTotalWaived(), money.RupeeCurrencyCode),
		}

		amortization := &creditcard.Amortization{
			InstallmentNumber: amortizationDetails.GetInstallmentNumber(),
			DueDate:           datetime.DateFromString(amortizationDetails.GetDueDate()),
			PrincipalInfo:     principalInfo,
			InterestInfo:      interestInfo,
			FeeInfo:           feeInfo,
			Summary:           summary,
			Tax:               money.ParseFloat(amortizationDetails.GetTax(), money.RupeeCurrencyCode),
		}
		amortizations = append(amortizations, amortization)
	}
	return amortizations, nil
}

func (f *FetchLoanByIdResp) getTransactions(result []*m2pLendingLms.Transactions) []*creditcard.LoanInfo_Transaction {
	transactions := make([]*creditcard.LoanInfo_Transaction, 0)
	for _, transactionDetails := range result {
		transaction := &creditcard.LoanInfo_Transaction{
			VendorExternalTransactionId: transactionDetails.GetExtTransactionId(),
			TransactionAmount:           money.ParseFloat(transactionDetails.GetTransactionAmount(), money.RupeeCurrencyCode),
			Amount:                      money.ParseFloat(transactionDetails.GetAmount(), money.RupeeCurrencyCode),
			Description:                 transactionDetails.GetDescription(),
			MerchantCategoryCode:        transactionDetails.GetMerchantCategoryCode(),
			TransactionTimestamp:        timestamppb.New(time.UnixMilli(transactionDetails.GetTransactionDate())),
			TransactionType:             transactionTypeMap[transactionDetails.GetTransactionType()],
			SubTransactionType:          transactionDetails.GetSubTransactionType(),
			TransactionOrigin:           transactionDetails.GetTransactionOrigin(),
			MerchantName:                transactionDetails.GetMerchantName(),
		}
		transactions = append(transactions, transaction)
	}

	return transactions
}

func (f *FetchLoanByIdResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	if len(b) == 0 {
		return &creditcard.FetchLoanByIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("no response from fetch loan by Id API"),
		}, nil
	}

	res := m2pLendingLms.GetLoanByIdResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v, err = %v", httpStatus, string(b), err))
	}

	status, ok := m2pCodesVsRpcCodes[res.GetException().GetErrorCode()]
	if !ok {
		errMsg := fmt.Sprint(res.GetException().GetDetailMessage(), res.GetException().GetMessage())
		status = rpc.StatusInternalWithDebugMsg(errMsg)
	}

	return &creditcard.FetchLoanByIdResponse{Status: status}, nil
}

func (f *FetchLoanByIdRequest) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}
