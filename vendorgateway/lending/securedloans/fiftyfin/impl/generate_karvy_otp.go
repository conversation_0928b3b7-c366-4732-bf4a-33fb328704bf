// nolint:dupl
package impl

import (
	"context"
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	vgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	vendorPb "github.com/epifi/gamma/api/vendors/fiftyfin"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/fiftyfin"
)

var SebiDebarredPanStatus = func() *rpc.Status {
	return &rpc.Status{
		Code:         uint32(vgPb.GenerateKarvyOtpResponse_SEBI_DEBARRED_PAN),
		ShortMessage: "sebi debarred pan",
		DebugMessage: "sebi debarred pan",
	}
}

var generateKarvyOtpStatusMapping = map[string]rpc.StatusFactory{
	"User not found":                                    rpc.StatusRecordNotFound,
	"PAN not found":                                     rpc.StatusFailedPrecondition,
	"Invalid PAN status":                                rpc.StatusFailedPrecondition,
	"Karvy (KFin) OTP sent successfully":                rpc.StatusOk,
	"No KFin portfolio found related to PAN":            rpc.StatusRecordNotFound,
	"Data Inserted Successfully":                        rpc.StatusOk,
	"Data Does not Exist":                               rpc.StatusRecordNotFound,
	"Data Doesnot Exists":                               rpc.StatusRecordNotFound,
	"KYC Status not verified":                           rpc.StatusRecordNotFound,
	"SEBI Debarred PAN are restricted for Lien Marking": SebiDebarredPanStatus,
}

type GenerateKarvyOtpRequest struct {
	Method string
	Conf   *config.SecuredLoans
	Req    *vgPb.GenerateKarvyOtpRequest
	*fiftyfin.DefaultHeaderSetter
	*fiftyfin.HeaderContentSetter
	*RequestBodyRedactor
}

type GenerateKarvyOtpResponse struct {
	*ResponseBodyRedactor
}

func (u *GenerateKarvyOtpRequest) Marshal() ([]byte, error) {
	user := &vendorPb.GenerateCamsOtpRequest{
		UserId: strconv.FormatInt(int64(u.Req.GetUserId()), 10),
	}
	res, err := protojson.Marshal(user)
	if err != nil {
		return nil, fmt.Errorf("error in marshalling generate karvy otp request. err: %v", zap.Error(err))
	}
	return res, nil
}

func (u *GenerateKarvyOtpRequest) GetResponse() vendorapi.Response {
	return &GenerateKarvyOtpResponse{}
}

func (u *GenerateKarvyOtpRequest) HTTPMethod() string {
	return u.Method
}

func (u *GenerateKarvyOtpRequest) URL() string {
	return u.Conf.Url + "/portfolio/api/v2/generate_karvy_otp/"
}

func (u *GenerateKarvyOtpResponse) Unmarshal(b []byte) (proto.Message, error) {
	res := vendorPb.GenerateKarvyOtpResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, fmt.Errorf("unable to unmarshal byte array to proto vendor response. err: %v", zap.Error(err))
	}
	if res.GetCode() != 200 && res.GetCode() != 201 {
		return &vgPb.GenerateKarvyOtpResponse{
			Status: getStatusForMsg(context.Background(), res.GetCode(), res.GetDetail(), generateKarvyOtpStatusMapping, fiftyfinStatusForMsgReqParams("GenerateKarvyOtp")),
		}, nil
	}
	vgResponse := &vgPb.GenerateKarvyOtpResponse{
		Status:    rpc.StatusOkWithDebugMsg(res.GetDetail()),
		RequestId: res.GetData().GetRequestId(),
	}
	return vgResponse, nil
}

func (u *GenerateKarvyOtpResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	res := vendorPb.GenerateKarvyOtpResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to proto vendor response")
	}
	return &vgPb.GenerateKarvyOtpResponse{
		Status: getStatusForMsg(ctx, res.GetCode(), res.GetDetail(), generateKarvyOtpStatusMapping, fiftyfinStatusForMsgReqParams("GenerateKarvyOtp")),
	}, nil
}
