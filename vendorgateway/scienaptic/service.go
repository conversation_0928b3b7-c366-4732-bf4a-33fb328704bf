//nolint:dupl
package scienaptic

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	vgScienapticPb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
)

var requestFactoryMap = map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
	commonvgpb.Vendor_SCIENAPTIC: NewScienapticRequest,
}

// Ensure Service implements the ScienapticServer interface
var _ vgScienapticPb.ScienapticServer = &Service{}

type Service struct {
	Handler *vendorapi.HTTPRequestHandler
}

func NewService(h *vendorapi.HTTPRequestHandler) *Service {
	return &Service{Handler: h}
}

func (s *Service) GenerateSmsFeatures(ctx context.Context, req *vgScienapticPb.GenerateSmsFeaturesRequest) (*vgScienapticPb.GenerateSmsFeaturesResponse, error) {
	vendorReq, err := vendorapi.NewVendorRequest(req, requestFactoryMap)
	if err != nil {
		return &vgScienapticPb.GenerateSmsFeaturesResponse{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}, err
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in GenerateSmsFeatures", zap.Error(err))
	}
	if res != nil {
		return res.(*vgScienapticPb.GenerateSmsFeaturesResponse), nil
	}
	return &vgScienapticPb.GenerateSmsFeaturesResponse{Status: rpcPb.StatusInternalWithDebugMsg(err.Error())}, nil
}
