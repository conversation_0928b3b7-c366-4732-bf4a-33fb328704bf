package dao

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgconn"
	"go.uber.org/zap"
	"gorm.io/gorm"

	dbTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	cibilReportPb "github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/creditreportv2/dao/model"
)

type CibilReportFlattenDaoPGDB struct {
	DB dbTypes.FeatureEngineeringPGDB
}

func NewCibilReportFlattenDaoPGDB(db dbTypes.FeatureEngineeringPGDB) *CibilReportFlattenDaoPGDB {
	return &CibilReportFlattenDaoPGDB{DB: db}
}
func (c *CibilReportFlattenDaoPGDB) FlattenCibilReport(ctx context.Context, report *cibilReportPb.CustomerAssetsResponse, actorId, creditReportId string, downloadtime time.Time) error {
	downloadtime = downloadtime.In(datetime.IST)
	defer metric_util.TrackDuration("creditreportv2/dao", "CibilReportFlattenDaoPGDB", "FlattenCibilReport", time.Now())
	if report == nil {
		return errors.New("report is mandatory")
	}
	if actorId == "" || creditReportId == "" {
		return errors.New("actor id and credit_report id can not be empty")
	}
	pgdb := gormctxv2.FromContextOrDefault(ctx, c.DB)
	err := pgdb.Transaction(func(db *gorm.DB) error {
		cibilAssetModel, err := model.GetCibilAssetModel(report, actorId, creditReportId, downloadtime)
		if err != nil {
			logger.Error(ctx, "error in CibilReportFlatten (cibil_assets) model")
			return err
		}
		resp := db.Create(cibilAssetModel)
		if resp.Error != nil {
			logger.Error(ctx, "error in CibilReportFlatten (cibil_assets) dao")
			return resp.Error
		}

		cibilBorrowerDobModel := model.GetCibilBorrowerDobModel(report, actorId, creditReportId, downloadtime)
		resp = db.Create(cibilBorrowerDobModel)
		if resp.Error != nil {
			logger.Error(ctx, "error in CibilReportFlatten (cibil_borrower_dob) dao")
			return resp.Error
		}

		cibilBorrowerNameModel := model.GetCibilBorrowerNameModel(report, actorId, creditReportId, downloadtime)
		resp = db.Create(cibilBorrowerNameModel)
		if resp.Error != nil {
			logger.Error(ctx, "error in CibilReportFlatten (cibil_borrower_name) dao")
			return resp.Error
		}

		cibilBorrowerAddressModel := model.GetCibilBorrowerAddressModel(report, actorId, creditReportId, downloadtime)
		if len(cibilBorrowerAddressModel) == 0 {
			logger.Warn("empty slice cibilBorrowerAddressModel", zap.String(logger.CREDIT_REPORT_ID, creditReportId))
		} else {
			resp = db.Create(cibilBorrowerAddressModel)
			if resp.Error != nil {
				logger.Error(ctx, "error in CibilReportFlatten (ciil_borrower_address) dao")
				return resp.Error
			}
		}

		cibilBorrowerEmailModel := model.GetCibilBorrowerEmailModel(report, actorId, creditReportId, downloadtime)
		if len(cibilBorrowerEmailModel) == 0 {
			logger.Warn("empty slice cibilBorrowerEmailModel", zap.String(logger.CREDIT_REPORT_ID, creditReportId))
		} else {
			resp = db.Create(cibilBorrowerEmailModel)
			if resp.Error != nil {
				logger.Error(ctx, "error in CibilReportFlatten (cibil_borrower_email) dao")
				return resp.Error
			}
		}

		cibilBorrowerTelephoneModel := model.GetCibilBorrowerTelephoneModel(report, actorId, creditReportId, downloadtime)
		if len(cibilBorrowerTelephoneModel) == 0 {
			logger.Warn("empty slice cibilBorrowerTelephoneModel", zap.String(logger.CREDIT_REPORT_ID, creditReportId))
		} else {
			resp = db.Create(cibilBorrowerTelephoneModel)
			if resp.Error != nil {
				logger.Error(ctx, "error in CibilReportFlatten (cibil_borrower_telephone) dao")
				return resp.Error
			}
		}

		cibilBorrowerIdentifierModel := model.GetCibilBorrowerIdentifierModel(report, actorId, creditReportId, downloadtime)
		if len(cibilBorrowerIdentifierModel) == 0 {
			logger.Warn("empty slice cibilBorrowerIdentifierModel", zap.String(logger.CREDIT_REPORT_ID, creditReportId))
		} else {
			resp = db.Create(cibilBorrowerIdentifierModel)
			if resp.Error != nil {
				logger.Error(ctx, "error in CibilReportFlatten (cibil_borrower_identifier) dao")
				return resp.Error
			}
		}

		cibilCreditScore := model.GetCibilCreditScoreModel(report, actorId, creditReportId, downloadtime)
		resp = db.Create(cibilCreditScore)
		if resp.Error != nil {
			logger.Error(ctx, "error in CibilReportFlatten (cibil_credit_score) dao")
			return resp.Error
		}

		cibilTradeLines := model.GetCibilTradelinesModel(report, actorId, creditReportId, downloadtime)
		if len(cibilTradeLines) == 0 {
			logger.Warn("empty slice cibilTradeLines", zap.String(logger.CREDIT_REPORT_ID, creditReportId))
		} else {
			resp = db.Create(cibilTradeLines)
			if resp.Error != nil {
				logger.Error(ctx, "error in CibilReportFlatten (cibil_tradelines) dao")
				return resp.Error
			}
		}

		cibilTradelinesHistoriesModel := model.GetCibilTradelinesHistoriesModel(report, actorId, creditReportId, downloadtime)
		if len(cibilTradelinesHistoriesModel) == 0 {
			logger.Warn("empty slice cibilTradelinesHistoriesModel", zap.String(logger.CREDIT_REPORT_ID, creditReportId))
		} else {
			resp = db.Create(cibilTradelinesHistoriesModel)
			if resp.Error != nil {
				logger.Error(ctx, "error in CibilReportFlatten (cibil_tradeline_histories) dao")
				return resp.Error
			}
		}

		cibilInqiries := model.GetCibilInquiriesModel(report, actorId, creditReportId, downloadtime)
		if len(cibilInqiries) == 0 {
			logger.Warn("empty slice cibilInqiries", zap.String(logger.CREDIT_REPORT_ID, creditReportId))
		} else {
			resp = db.Create(cibilInqiries)
			if resp.Error != nil {
				logger.Error(ctx, "error in CibilReportFlatten (cibil_inquiries) dao")
				return resp.Error
			}
		}

		cibilCreditStatement := model.GetCibilCreditStatementModel(report, actorId, creditReportId, downloadtime)
		resp = db.Create(cibilCreditStatement)
		if resp.Error != nil {
			logger.Error(ctx, "error in CibilReportFlatten (cibil_credit_statement) dao")
			return resp.Error
		}

		cibilCreditScoreFactor := model.GetCibilCreditScoreFactorModel(report, actorId, creditReportId, downloadtime)
		if len(cibilCreditScoreFactor) == 0 {
			logger.Warn("empty slice cibilCreditScoreFactor", zap.String(logger.CREDIT_REPORT_ID, creditReportId))
		} else {
			resp = db.Create(cibilCreditScoreFactor)
			if resp.Error != nil {
				logger.Error(ctx, "error in CibilReportFlatten (cibil_credit_score) dao")
				return resp.Error
			}
		}

		cibilBorrowerEmployer := model.GetCibilBorrowerEmployerModel(report, actorId, creditReportId, downloadtime)
		if len(cibilBorrowerEmployer) == 0 {
			logger.Warn("empty slice cibilBorrowerEmployer", zap.String(logger.CREDIT_REPORT_ID, creditReportId))
		} else {
			resp = db.Create(cibilBorrowerEmployer)
			if resp.Error != nil {
				logger.Error(ctx, "error in CibilReportFlatten (cibil_tradelines) dao")
				return resp.Error
			}
		}
		return nil
	})
	if err != nil {
		var pgErr *pgconn.PgError
		if ok := errors.As(err, &pgErr); !ok {
			return err
		}
		if pgErr.Code == "23505" {
			return epifierrors.ErrDuplicateEntry
		}
		return err
	}
	return nil
}
