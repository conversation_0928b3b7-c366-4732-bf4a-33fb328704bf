package control

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	actorMock "github.com/epifi/gamma/api/actor/mocks"
	authMock "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bankcust/mocks"
	cardPb "github.com/epifi/gamma/api/card"
	ccPb "github.com/epifi/gamma/api/card/control"
	savingsMock "github.com/epifi/gamma/api/savings/mocks"
	userMock "github.com/epifi/gamma/api/user/mocks"
	vgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	cardVgMock "github.com/epifi/gamma/api/vendorgateway/openbanking/card/mocks"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	daoMock "github.com/epifi/gamma/card/dao/mocks"
	"github.com/epifi/be-common/pkg/mock"
)

func TestService_ConsolidatedCardControlOnOff(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCardDao := daoMock.NewMockCardDao(ctrl)
	mockVgCardClient := cardVgMock.NewMockCardProvisioningClient(ctrl)
	mockAuthClient := authMock.NewMockAuthClient(ctrl)
	mockActorClient := actorMock.NewMockActorClient(ctrl)
	mockSavingsClient := savingsMock.NewMockSavingsClient(ctrl)
	mockUserClient := userMock.NewMockUsersClient(ctrl)
	mockVgPciClient := cardVgMock.NewMockCardProvisioningClient(ctrl)
	mockBcClient := mocks.NewMockBankCustomerServiceClient(ctrl)

	mockAuthClient.EXPECT().GetDeviceAuth(gomock.Any(), gomock.Any()).Return(getMockedDeviceDetailsResponse()).AnyTimes()
	mockActorClient.EXPECT().GetEntityDetailsByActorId(gomock.Any(), gomock.Any()).Return(getMockedEntityDetailsResponse()).AnyTimes()
	mockBcClient.EXPECT().GetBankCustomer(gomock.Any(), mock.NewProtoMatcher(&bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_UserId{
			UserId: "dummy-entity-id",
		},
	})).Return(&bankcust.GetBankCustomerResponse{
		Status: rpc.StatusOk(),
		BankCustomer: &bankcust.BankCustomer{
			VendorCustomerId: "dummy-cust-id",
			Status:           bankcust.Status_STATUS_ACTIVE,
		},
	}, nil).AnyTimes()
	mockSavingsClient.EXPECT().GetAccount(ctx, gomock.Any()).Return(getMockedAccountDetailsResponse()).AnyTimes()

	svc := NewService(svcTS.conf, mockCardDao, mockVgCardClient, mockAuthClient, mockActorClient, mockUserClient,
		mockSavingsClient, nil, nil, nil, nil, nil,
		nil, nil, svcTS.dynamicConf, mockVgPciClient, nil, mockBcClient, nil, nil)

	tests := []struct {
		name           string
		req            *ccPb.ConsolidatedCardControlOnOffRequest
		setupMockCalls func()
		want           *ccPb.ConsolidatedCardControlOnOffResponse
		wantErr        bool
	}{
		{
			name: "successfully enabled all controls",
			req: &ccPb.ConsolidatedCardControlOnOffRequest{
				CardId: "card-id-1",
				ControlActions: map[int32]cardPb.CardControlAction{
					int32(cardPb.CardControlType_SUSPEND):              cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_DOMESTIC_ON_OFF):      cardPb.CardControlAction_ENABLE,
					int32(cardPb.CardControlType_INTERNATIONAL_ON_OFF): cardPb.CardControlAction_ENABLE,
					int32(cardPb.CardControlType_ECOMM_ON_OFF):         cardPb.CardControlAction_ENABLE,
					int32(cardPb.CardControlType_POS_ON_OFF):           cardPb.CardControlAction_ENABLE,
					int32(cardPb.CardControlType_ATM_ON_OFF):           cardPb.CardControlAction_ENABLE,
					int32(cardPb.CardControlType_NFC_ON_OFF):           cardPb.CardControlAction_ENABLE,
				},
				RequestId:             "request-id-1",
				CredBlock:             "pin",
				ControlWorkflow:       ccPb.CardControlWorkflow_CARD_CTRL_SECURE_PIN_WF,
				ControlActionWorkflow: ccPb.ControlActionWorkflow_INTERNAL,
			},
			setupMockCalls: func() {
				mockCardDao.EXPECT().GetByID(gomock.Any(), "card-id-1").Return(&cardPb.Card{
					Id:             "card-id-1",
					ActorId:        "actor-id-1",
					State:          cardPb.CardState_ACTIVATED,
					Type:           cardPb.CardType_DEBIT,
					Form:           cardPb.CardForm_PHYSICAL,
					NetworkType:    cardPb.CardNetworkType_VISA,
					IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					IssueType:      cardPb.CardIssueType_FRESH,
					BankIdentifier: "federal-card-1",
					BasicInfo: &cardPb.BasicCardInfo{
						MaskedCardNumber: "1234********5678",
						CustomerName:     "epifi user",
					},
					GroupId:          "group-id-1",
					SavingsAccountId: "savings-account-id-1",
				}, nil)
				mockVgPciClient.EXPECT().ConsolidatedCardControlOnOff(gomock.Any(), &vgPb.ConsolidatedCardControlOnOffRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Auth: &header.Auth{
						DeviceToken:   "dummy-token",
						EncryptedPin:  "pin",
						UserProfileId: "dummy-user-profile-id",
						CustomerId:    "dummy-cust-id",
						DeviceId:      "device-id",
					},
					VendorCardId:           "federal-card-1",
					DeviceBiometricEnabled: false,
					AccountDetails: &vgPb.AccountDetails{
						AccountNumber: "**********",
						CustomerId:    "dummy-cust-id",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
					},
					ControlDetails: map[int32]vgPb.CardControlAction{
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_SUSPEND): vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_ECOM):    vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_NFC):     vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_ATM):     vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_POS):     vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_DOM):     vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_INTL):    vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE,
					},
					RequestId: "request-id-1",
				}).Return(&vgPb.ConsolidatedCardControlOnOffResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockCardDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &cardPb.Card{
					Id:             "card-id-1",
					ActorId:        "actor-id-1",
					State:          cardPb.CardState_ACTIVATED,
					Type:           cardPb.CardType_DEBIT,
					Form:           cardPb.CardForm_PHYSICAL,
					NetworkType:    cardPb.CardNetworkType_VISA,
					IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					IssueType:      cardPb.CardIssueType_FRESH,
					BankIdentifier: "federal-card-1",
					BasicInfo: &cardPb.BasicCardInfo{
						MaskedCardNumber: "1234********5678",
						CustomerName:     "epifi user",
					},
					GroupId:          "group-id-1",
					SavingsAccountId: "savings-account-id-1",
					Controls: &cardPb.ControlData{
						TxnStates: map[string]cardPb.CardControlAction{
							cardPb.CardTransactionType_ECOMMERCE.String(): cardPb.CardControlAction_ENABLE,
							cardPb.CardTransactionType_ATM.String():       cardPb.CardControlAction_ENABLE,
							cardPb.CardTransactionType_NFC.String():       cardPb.CardControlAction_ENABLE,
							cardPb.CardTransactionType_POS.String():       cardPb.CardControlAction_ENABLE,
						},
						LocStates: map[string]cardPb.CardControlAction{
							cardPb.CardUsageLocationType_DOMESTIC.String():      cardPb.CardControlAction_ENABLE,
							cardPb.CardUsageLocationType_INTERNATIONAL.String(): cardPb.CardControlAction_ENABLE,
						},
					},
				}, []cardPb.CardFieldMask{cardPb.CardFieldMask_CARD_CONTROLS}, cardPb.CardState_ACTIVATED, cardPb.CardState_ACTIVATED).Return(nil, nil)
			},
			want: &ccPb.ConsolidatedCardControlOnOffResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "successfully disabled all controls",
			req: &ccPb.ConsolidatedCardControlOnOffRequest{
				CardId: "card-id-1",
				ControlActions: map[int32]cardPb.CardControlAction{
					int32(cardPb.CardControlType_SUSPEND):              cardPb.CardControlAction_ENABLE,
					int32(cardPb.CardControlType_DOMESTIC_ON_OFF):      cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_INTERNATIONAL_ON_OFF): cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_ECOMM_ON_OFF):         cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_POS_ON_OFF):           cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_ATM_ON_OFF):           cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_NFC_ON_OFF):           cardPb.CardControlAction_DISABLE,
				},
				RequestId:             "request-id-1",
				CredBlock:             "pin",
				ControlWorkflow:       ccPb.CardControlWorkflow_CARD_CTRL_SECURE_PIN_WF,
				ControlActionWorkflow: ccPb.ControlActionWorkflow_INTERNAL,
			},
			setupMockCalls: func() {
				mockCardDao.EXPECT().GetByID(gomock.Any(), "card-id-1").Return(&cardPb.Card{
					Id:             "card-id-1",
					ActorId:        "actor-id-1",
					State:          cardPb.CardState_ACTIVATED,
					Type:           cardPb.CardType_DEBIT,
					Form:           cardPb.CardForm_PHYSICAL,
					NetworkType:    cardPb.CardNetworkType_VISA,
					IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					IssueType:      cardPb.CardIssueType_FRESH,
					BankIdentifier: "federal-card-1",
					BasicInfo: &cardPb.BasicCardInfo{
						MaskedCardNumber: "1234********5678",
						CustomerName:     "epifi user",
					},
					GroupId:          "group-id-1",
					SavingsAccountId: "savings-account-id-1",
					Controls: &cardPb.ControlData{
						TxnStates: map[string]cardPb.CardControlAction{
							cardPb.CardTransactionType_ECOMMERCE.String(): cardPb.CardControlAction_ENABLE,
							cardPb.CardTransactionType_ATM.String():       cardPb.CardControlAction_ENABLE,
							cardPb.CardTransactionType_NFC.String():       cardPb.CardControlAction_ENABLE,
							cardPb.CardTransactionType_POS.String():       cardPb.CardControlAction_ENABLE,
						},
						LocStates: map[string]cardPb.CardControlAction{
							cardPb.CardUsageLocationType_DOMESTIC.String():      cardPb.CardControlAction_ENABLE,
							cardPb.CardUsageLocationType_INTERNATIONAL.String(): cardPb.CardControlAction_ENABLE,
						},
					},
				}, nil)
				mockVgPciClient.EXPECT().ConsolidatedCardControlOnOff(gomock.Any(), &vgPb.ConsolidatedCardControlOnOffRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Auth: &header.Auth{
						DeviceToken:   "dummy-token",
						EncryptedPin:  "pin",
						UserProfileId: "dummy-user-profile-id",
						CustomerId:    "dummy-cust-id",
						DeviceId:      "device-id",
					},
					VendorCardId:           "federal-card-1",
					DeviceBiometricEnabled: false,
					AccountDetails: &vgPb.AccountDetails{
						AccountNumber: "**********",
						CustomerId:    "dummy-cust-id",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
					},
					ControlDetails: map[int32]vgPb.CardControlAction{
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_SUSPEND): vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_ECOM):    vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_NFC):     vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_ATM):     vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_POS):     vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_DOM):     vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_INTL):    vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
					},
					RequestId: "request-id-1",
				}).Return(&vgPb.ConsolidatedCardControlOnOffResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockCardDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &cardPb.Card{
					Id:             "card-id-1",
					ActorId:        "actor-id-1",
					State:          cardPb.CardState_ACTIVATED,
					Type:           cardPb.CardType_DEBIT,
					Form:           cardPb.CardForm_PHYSICAL,
					NetworkType:    cardPb.CardNetworkType_VISA,
					IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					IssueType:      cardPb.CardIssueType_FRESH,
					BankIdentifier: "federal-card-1",
					BasicInfo: &cardPb.BasicCardInfo{
						MaskedCardNumber: "1234********5678",
						CustomerName:     "epifi user",
					},
					GroupId:          "group-id-1",
					SavingsAccountId: "savings-account-id-1",
					Controls: &cardPb.ControlData{
						TxnStates: map[string]cardPb.CardControlAction{
							cardPb.CardTransactionType_ECOMMERCE.String(): cardPb.CardControlAction_DISABLE,
							cardPb.CardTransactionType_ATM.String():       cardPb.CardControlAction_DISABLE,
							cardPb.CardTransactionType_NFC.String():       cardPb.CardControlAction_DISABLE,
							cardPb.CardTransactionType_POS.String():       cardPb.CardControlAction_DISABLE,
						},
						LocStates: map[string]cardPb.CardControlAction{
							cardPb.CardUsageLocationType_DOMESTIC.String():      cardPb.CardControlAction_DISABLE,
							cardPb.CardUsageLocationType_INTERNATIONAL.String(): cardPb.CardControlAction_DISABLE,
						},
					},
				}, []cardPb.CardFieldMask{cardPb.CardFieldMask_CARD_CONTROLS}, cardPb.CardState_SUSPENDED, cardPb.CardState_ACTIVATED).Return(nil, nil)
			},
			want: &ccPb.ConsolidatedCardControlOnOffResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "successfully enabled/disabled few controls",
			req: &ccPb.ConsolidatedCardControlOnOffRequest{
				CardId: "card-id-1",
				ControlActions: map[int32]cardPb.CardControlAction{
					int32(cardPb.CardControlType_INTERNATIONAL_ON_OFF): cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_ECOMM_ON_OFF):         cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_POS_ON_OFF):           cardPb.CardControlAction_ENABLE,
				},
				RequestId:             "request-id-1",
				CredBlock:             "pin",
				ControlWorkflow:       ccPb.CardControlWorkflow_CARD_CTRL_SECURE_PIN_WF,
				ControlActionWorkflow: ccPb.ControlActionWorkflow_INTERNAL,
			},
			setupMockCalls: func() {
				mockCardDao.EXPECT().GetByID(gomock.Any(), "card-id-1").Return(&cardPb.Card{
					Id:             "card-id-1",
					ActorId:        "actor-id-1",
					State:          cardPb.CardState_ACTIVATED,
					Type:           cardPb.CardType_DEBIT,
					Form:           cardPb.CardForm_PHYSICAL,
					NetworkType:    cardPb.CardNetworkType_VISA,
					IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					IssueType:      cardPb.CardIssueType_FRESH,
					BankIdentifier: "federal-card-1",
					BasicInfo: &cardPb.BasicCardInfo{
						MaskedCardNumber: "1234********5678",
						CustomerName:     "epifi user",
					},
					GroupId:          "group-id-1",
					SavingsAccountId: "savings-account-id-1",
					Controls: &cardPb.ControlData{
						TxnStates: map[string]cardPb.CardControlAction{
							cardPb.CardTransactionType_ECOMMERCE.String(): cardPb.CardControlAction_ENABLE,
							cardPb.CardTransactionType_POS.String():       cardPb.CardControlAction_DISABLE,
							cardPb.CardTransactionType_NFC.String():       cardPb.CardControlAction_DISABLE,
							cardPb.CardTransactionType_ATM.String():       cardPb.CardControlAction_DISABLE,
						},
						LocStates: map[string]cardPb.CardControlAction{
							cardPb.CardUsageLocationType_DOMESTIC.String():      cardPb.CardControlAction_ENABLE,
							cardPb.CardUsageLocationType_INTERNATIONAL.String(): cardPb.CardControlAction_ENABLE,
						},
					},
				}, nil)
				mockVgPciClient.EXPECT().ConsolidatedCardControlOnOff(gomock.Any(), &vgPb.ConsolidatedCardControlOnOffRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Auth: &header.Auth{
						DeviceToken:   "dummy-token",
						EncryptedPin:  "pin",
						UserProfileId: "dummy-user-profile-id",
						CustomerId:    "dummy-cust-id",
						DeviceId:      "device-id",
					},
					VendorCardId:           "federal-card-1",
					DeviceBiometricEnabled: false,
					AccountDetails: &vgPb.AccountDetails{
						AccountNumber: "**********",
						CustomerId:    "dummy-cust-id",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
					},
					ControlDetails: map[int32]vgPb.CardControlAction{
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_ECOM): vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_POS):  vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_INTL): vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
					},
					RequestId: "request-id-1",
				}).Return(&vgPb.ConsolidatedCardControlOnOffResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockCardDao.EXPECT().UpdateIfStateMatches(gomock.Any(), &cardPb.Card{
					Id:             "card-id-1",
					ActorId:        "actor-id-1",
					State:          cardPb.CardState_ACTIVATED,
					Type:           cardPb.CardType_DEBIT,
					Form:           cardPb.CardForm_PHYSICAL,
					NetworkType:    cardPb.CardNetworkType_VISA,
					IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					IssueType:      cardPb.CardIssueType_FRESH,
					BankIdentifier: "federal-card-1",
					BasicInfo: &cardPb.BasicCardInfo{
						MaskedCardNumber: "1234********5678",
						CustomerName:     "epifi user",
					},
					GroupId:          "group-id-1",
					SavingsAccountId: "savings-account-id-1",
					Controls: &cardPb.ControlData{
						TxnStates: map[string]cardPb.CardControlAction{
							cardPb.CardTransactionType_ECOMMERCE.String(): cardPb.CardControlAction_DISABLE,
							cardPb.CardTransactionType_POS.String():       cardPb.CardControlAction_ENABLE,
							cardPb.CardTransactionType_NFC.String():       cardPb.CardControlAction_DISABLE,
							cardPb.CardTransactionType_ATM.String():       cardPb.CardControlAction_DISABLE,
						},
						LocStates: map[string]cardPb.CardControlAction{
							cardPb.CardUsageLocationType_DOMESTIC.String():      cardPb.CardControlAction_ENABLE,
							cardPb.CardUsageLocationType_INTERNATIONAL.String(): cardPb.CardControlAction_DISABLE,
						},
					},
				}, []cardPb.CardFieldMask{cardPb.CardFieldMask_CARD_CONTROLS}, cardPb.CardState_CARD_STATE_UNSPECIFIED, cardPb.CardState_ACTIVATED).Return(nil, nil)
			},
			want: &ccPb.ConsolidatedCardControlOnOffResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failed to fetch card",
			req: &ccPb.ConsolidatedCardControlOnOffRequest{
				CardId: "card-id-1",
				ControlActions: map[int32]cardPb.CardControlAction{
					int32(cardPb.CardControlType_INTERNATIONAL_ON_OFF): cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_ECOMM_ON_OFF):         cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_POS_ON_OFF):           cardPb.CardControlAction_ENABLE,
				},
				RequestId:             "request-id-1",
				CredBlock:             "pin",
				ControlWorkflow:       ccPb.CardControlWorkflow_CARD_CTRL_SECURE_PIN_WF,
				ControlActionWorkflow: ccPb.ControlActionWorkflow_INTERNAL,
			},
			setupMockCalls: func() {
				mockCardDao.EXPECT().GetByID(gomock.Any(), "card-id-1").Return(nil, fmt.Errorf("error"))
			},
			want: &ccPb.ConsolidatedCardControlOnOffResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "vg request failed",
			req: &ccPb.ConsolidatedCardControlOnOffRequest{
				CardId: "card-id-1",
				ControlActions: map[int32]cardPb.CardControlAction{
					int32(cardPb.CardControlType_INTERNATIONAL_ON_OFF): cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_ECOMM_ON_OFF):         cardPb.CardControlAction_DISABLE,
					int32(cardPb.CardControlType_POS_ON_OFF):           cardPb.CardControlAction_ENABLE,
				},
				RequestId:             "request-id-1",
				CredBlock:             "pin",
				ControlWorkflow:       ccPb.CardControlWorkflow_CARD_CTRL_SECURE_PIN_WF,
				ControlActionWorkflow: ccPb.ControlActionWorkflow_INTERNAL,
			},
			setupMockCalls: func() {
				mockCardDao.EXPECT().GetByID(gomock.Any(), "card-id-1").Return(&cardPb.Card{
					Id:             "card-id-1",
					ActorId:        "actor-id-1",
					State:          cardPb.CardState_ACTIVATED,
					Type:           cardPb.CardType_DEBIT,
					Form:           cardPb.CardForm_PHYSICAL,
					NetworkType:    cardPb.CardNetworkType_VISA,
					IssuerBank:     commonvgpb.Vendor_FEDERAL_BANK,
					IssueType:      cardPb.CardIssueType_FRESH,
					BankIdentifier: "federal-card-1",
					BasicInfo: &cardPb.BasicCardInfo{
						MaskedCardNumber: "1234********5678",
						CustomerName:     "epifi user",
					},
					GroupId:          "group-id-1",
					SavingsAccountId: "savings-account-id-1",
					Controls: &cardPb.ControlData{
						TxnStates: map[string]cardPb.CardControlAction{
							cardPb.CardTransactionType_ECOMMERCE.String(): cardPb.CardControlAction_ENABLE,
							cardPb.CardTransactionType_POS.String():       cardPb.CardControlAction_DISABLE,
							cardPb.CardTransactionType_NFC.String():       cardPb.CardControlAction_DISABLE,
							cardPb.CardTransactionType_ATM.String():       cardPb.CardControlAction_DISABLE,
						},
						LocStates: map[string]cardPb.CardControlAction{
							cardPb.CardUsageLocationType_DOMESTIC.String():      cardPb.CardControlAction_ENABLE,
							cardPb.CardUsageLocationType_INTERNATIONAL.String(): cardPb.CardControlAction_ENABLE,
						},
					},
				}, nil)
				mockVgPciClient.EXPECT().ConsolidatedCardControlOnOff(gomock.Any(), &vgPb.ConsolidatedCardControlOnOffRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					Auth: &header.Auth{
						DeviceToken:   "dummy-token",
						EncryptedPin:  "pin",
						UserProfileId: "dummy-user-profile-id",
						CustomerId:    "dummy-cust-id",
						DeviceId:      "device-id",
					},
					VendorCardId:           "federal-card-1",
					DeviceBiometricEnabled: false,
					AccountDetails: &vgPb.AccountDetails{
						AccountNumber: "**********",
						CustomerId:    "dummy-cust-id",
						PhoneNumber: &commontypes.PhoneNumber{
							CountryCode:    91,
							NationalNumber: **********,
						},
					},
					ControlDetails: map[int32]vgPb.CardControlAction{
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_ECOM): vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_POS):  vgPb.CardControlAction_CARD_CONTROL_ACTION_ENABLE,
						int32(vgPb.CardControlType_CARD_CONTROL_TYPE_INTL): vgPb.CardControlAction_CARD_CONTROL_ACTION_DISABLE,
					},
					RequestId: "request-id-1",
				}).Return(&vgPb.ConsolidatedCardControlOnOffResponse{
					Status:               rpc.StatusInternal(),
					InternalResponseCode: "internal-code-1",
				}, nil)
			},
			want: &ccPb.ConsolidatedCardControlOnOffResponse{
				Status:               rpc.StatusInternal(),
				InternalResponseCode: "internal-code-1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := svc.ConsolidatedCardControlOnOff(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConsolidatedCardControlOnOff() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("ConsolidatedCardControlOnOff() diff %v", diff)
			}
		})
	}
}
